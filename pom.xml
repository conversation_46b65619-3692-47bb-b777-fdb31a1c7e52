<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.wormhole</groupId>
        <artifactId>wormhole-parent</artifactId>
        <version>1.3.15</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>wormhole-agent</artifactId>
    <version>1.1.12</version>

    <name>wormhole-agent</name>
    <packaging>pom</packaging>

    <modules>
        <module>wormhole-agent-client</module>
        <module>wormhole-agent-service</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!--只跳过测试执行，但仍会编译测试代码-->
        <skipTests>true</skipTests>

        <wormhole-channel-client.version>1.1.13</wormhole-channel-client.version>
        <wormhole-hotelds-api-client.version>1.2.3</wormhole-hotelds-api-client.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-agent-client</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-channel-client</artifactId>
                <version>${wormhole-channel-client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.wormhole</groupId>
                <artifactId>wormhole-hotelds-api-client</artifactId>
                <version>${wormhole-hotelds-api-client.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/test/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
        </testResources>
    </build>

</project>
