FROM docker.cnb.delonix.work/arch/base-images/openjdk17:B1
ADD /wormhole-agent-service/target/*.jar app.jar

# 检查 jar 文件
RUN jar tf app.jar | grep MANIFEST.MF
RUN jar xf app.jar META-INF/MANIFEST.MF && cat META-INF/MANIFEST.MF
# RUN microdnf install iputils net-tools telnet
# 设置时区与语言环境变量
ENV TIME_ZONE=Asia/Shanghai \
    LANG=en_US.UTF-8 \
    LANGUAGE=en_US.UTF-8 \
    LC_ALL=en_US.UTF-8

# 执行以下命令设置时区
RUN echo "${TIME_ZONE}" > /etc/timezone && \
    ln -sf /usr/share/zoneinfo/${TIME_ZONE} /etc/localtime



ARG argName
ENV envName=$argName
RUN echo $envName

# 指定容器启动程序及参数
ENTRYPOINT exec java -Dfile.encoding=UTF-8 -Dsun.jnu.encoding=UTF-8 -Dnative.encoding=UTF-8 -jar /app.jar --spring.profiles.active=$envName $JAVA_OPTS