server:
  port: 8080

management:
  endpoints:
    web:
      exposure:
        include: health
      base-path: /actuator
  endpoint:
    health:
      probes:
        enabled: true
  metrics:
    tags:
      application: ${spring.application.name}
  tracing:
    enabled: true

spring:
  main:
    allow-circular-references: true
  application:
    name: wormhole-agent
  reactor:
    context-propagation: auto
  freemarker:
    check-template-location: false
  elasticsearch:
    uris: http://***********:9200
    username: es-user
    password: Abcd234@#$
  data:
    redis:
      host: ************
      database: 8
      password: 6iaEg*fXjl6Rzxe*

  r2dbc:
    url: r2dbc:mysql://***********:3306/wormhole?characterEncoding=UTF8&autoReconnect=true&allowMultiQueries=true&rewriteBatchedStatements=true&serverTimezone=GMT%2B8&zeroDateTimeBehavior=convertToNull&useSSL=false
    username: dev_rw
    password: z8E3SuIy.1MlCril8

wormhole:
  auth:
    api-key:
      excluded-paths:
        - "/actuator/**"
        - "/feishu/**"
        - "/chat/**"
        - "/llm/chat/**"
        - "/amap/**"
        - "/debug/**"
      valid-api-keys:
        "sk-delonix-office"
  ai:
    azure:
      openai:
        ai-configs:
          - region: japan
            enabled: true
            default-config: true
            api-key: ********************************
            endpoint: https://dl-japan-prod.openai.azure.com
          - region: east-us
            enabled: true
            default-config: false
            api-key: ********************************
            endpoint: https://dl-openai-east-us.openai.azure.com
          - region: west-us
            enabled: true
            default-config: false
            api-key: ********************************
            endpoint: https://dl-openai-west-us.openai.azure.com
    aliyun:
      qwen:
        api-key: sk-********************************
        endpoint: https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions
        embedding-endpoint: https://dashscope.aliyuncs.com/api/v1/services/embeddings/text-embedding/text-embedding
    jina:
      access-key: jina_a9171dfe3aa74bfa9379040fa4a6c0f3gBz-clP52PTD0aebm1n0WQtEuzJm
      rerank-endpoint: https://api.jina.ai/v1/rerank
      embeddings-endpoint: https://api.jina.ai/v1/embeddings
      classify-endpoint: https://api.jina.ai/v1/classify
      reader-endpoint: https://r.jina.ai
      segment-endpoint: https://segment.jina.ai
      param:
        segment:
          return-tokens: true
    zilliz:
      endpoint: https://in05-7dbe69e2faa4607.serverless.ali-cn-hangzhou.cloud.zilliz.com.cn
      token: ea6b048d11a5d4c67bcf11c8c4e7000752da60ef74d7dc2dace66df1bad7e9952158873bc7e34305310cc8b91e69c4704a80eebc
    bge:
      url: http://bge-rerank.delonix.group/compare_sentences
    doubao:
      api-key: 6591bbc0-23ad-423e-a34e-1de58f9a80d6
      base-url: https://ark.cn-beijing.volces.com/api/v3/
    aws:
      region: us-west-2
      access-key-id: ********************
      secret-access-key: GtsJEU3yq5TanRanl1FkjuX2pmjKAGrrT5CB6t+m
    deepseek:
      api-key: ***********************************
      endpoint: https://api.deepseek.com/chat/completions
    siliconflow:
      api-key: sk-mushpmbauahlxaxrnepigskjskvsjqjjxedmcidgyfyeehnz
      endpoint: https://api.siliconflow.cn/v1
      chat-endpoint: https://api.siliconflow.cn/v1/chat/completions
      rerank-endpoint: https://api.siliconflow.cn/v1/rerank
  chat:
    local:
      read-local-workflow: true
  feishu:
    robot:
      encrypt-key: L5PvO2KSEs8MKhxymZeAFSwUHXgReZGy
      robot-list:
        - app-id: cli_a7bc4f7d23e7900e
          secret-key: XyNVOXqpPwvsyWKshstHLcioEZqoMBp8
          verification-token: r3z0nVpVSeKeNyoASINFngkWHoK3CRlw
        - app-id: cli_a7bc5122b2f81013
          secret-key: srM0XAFLXmQ4mVvG5ohxahrqDsPXBQVw
          verification-token: r3z0nVpVSeKeNyoASINFngkWHoK3CRlw1
          default-bot-code: hotel_reply_bot
        - app-id: cli_a7d3f016e879900b
          secret-key: X3lbKFPMG0khfFLuwjg88g0UKzI5BVDb
          verification-token: Xhml0sIuzsRhlOdELsAqveThpZHVKZAv

  voicechat:
    bot-code: bot_f0782ce378d0c590efeda27aae6c7a79
    voice-type: zh_male_beijingxiaoye_moon_bigtts
  tts:
    doubao:
      app-id: 7962686137
      token: XY5V1VbaX3iUSGNpoGBtK45GqvMjqd8L
      base-url: https://openspeech.bytedance.com/api/v1/tts
      voice: zh_female_xiaoxin_moon_bigtts
      format: mp3
      speed: "1.0"
      emotion: normal
      language: zh
      enabled: true
logging:
  level:
    io.asyncer.r2dbc.mysql.client: ERROR

langchain4j:
  azure-open-ai:
    chat-model:
      api-key: ********************************
      endpoint: https://dl-japan-prod.openai.azure.com
      deployment-name: gpt-4o


