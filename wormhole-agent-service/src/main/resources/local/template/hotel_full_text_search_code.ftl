def runScript(Map<String, Object> ctx) {
    def outputList = ctx.output_list
    def resultMap = [:]
    def output = ""
    if (outputList instanceof List) {
        def index = 1
        def parts = []
        outputList.each { item ->
            def metadata = getItemMetadata(item)
            if (metadata && metadata.containsKey("title") && metadata.containsKey("text")) {
                def title = metadata.get("title")
                def text = metadata.get("text")
                parts << "${index}、title:${title}, content:${text}\n"
                index++
            }
        }
        output = parts.join(" ")
    }
    resultMap['output'] = output
    return resultMap
}

    def getItemMetadata(item) {
        def metadata
        def metaClass = item.getMetaClass()
        if (metaClass.respondsTo(item, "getMetadata")) {
            metadata = metaClass.invokeMethod(item, "getMetadata")
        } else if (item.hasProperty("metadata")) {
            metadata = item.getProperty("metadata")
        } else if (item instanceof Map && item.containsKey("metadata")) {
            metadata = item.get("metadata")
        }
        return metadata
    }
return this.&runScript(ctx)