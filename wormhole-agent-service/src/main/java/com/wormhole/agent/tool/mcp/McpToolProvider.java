package com.wormhole.agent.tool.mcp;

import com.wormhole.agent.client.chat.mcp.dto.McpServiceStatus;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.mcp.cache.McpToolCacheManager;
import com.wormhole.agent.tool.mcp.config.McpConfigAdapter;
import com.wormhole.agent.tool.mcp.config.McpProperties;
import com.wormhole.agent.tool.mcp.event.McpClientEvent;
import com.wormhole.agent.tool.mcp.publisher.McpServerInfoPublisher;
import com.wormhole.agent.tool.mcp.redis.config.GlobalMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.McpRedisProperties;
import com.wormhole.agent.tool.mcp.redis.manager.McpGlobalConfigManager;
import com.wormhole.agent.tool.mcp.service.McpCreateHandler;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import dev.langchain4j.mcp.client.McpClient;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.DependsOn;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * MCP工具提供者
 * 负责管理MCP客户端连接和工具发现
 * 现在从Redis读取配置，不再依赖JSON文件
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@Slf4j
@Component
@RequiredArgsConstructor
@DependsOn("rocketMQMessageListenerContainerRegistrar") // 等待RocketMQ容器启动
public class McpToolProvider {

    // Redis 相关依赖
    private final McpGlobalConfigManager mcpGlobalConfigManager;
    private final McpConfigAdapter mcpConfigAdapter;
    private final McpRedisProperties mcpRedisProperties;
    private final McpServerInfoPublisher mcpServerInfoPublisher;

    // 保留现有依赖
    private final McpToolAdapter mcpToolAdapter;
    private final McpToolCacheManager cacheManager;
    
    // 事件发布器
    private final ApplicationEventPublisher eventPublisher;
    
    // ApplicationContext 用于懒加载 McpServiceManager 以避免循环依赖
    private final ApplicationContext applicationContext;
    @Resource
    private McpCreateHandler mcpCreateHandler;
    /**
     * MCP客户端缓存：客户端名称 -> MCP客户端
     */
    private final Map<String, McpClient> mcpClients = new ConcurrentHashMap<>();
    
    /**
     * 工具缓存：客户端名称 -> 工具列表
     */
    private final Map<String, List<OpenAiTool>> toolCache = new ConcurrentHashMap<>();

    /**
     * 初始化状态枚举
     */
    public enum InitializationStatus {
        NOT_STARTED,    // 未开始
        IN_PROGRESS,    // 进行中
        PARTIALLY_READY, // 部分就绪
        COMPLETED,      // 完全完成
        FAILED          // 失败
    }

    /**
     * 初始化状态详情
     */
    public static class InitializationStatusDetail {
        private volatile long startTime = 0;
        private volatile long endTime = 0;
        private volatile int totalConfigs = 0;
        private volatile int successfulClients = 0;
        private volatile int failedClients = 0;
        private volatile String lastError = null;
        private volatile boolean configLoaded = false;
        private volatile boolean clientsCreated = false;

        // Getters
        public long getStartTime() { return startTime; }
        public long getEndTime() { return endTime; }
        public int getTotalConfigs() { return totalConfigs; }
        public int getSuccessfulClients() { return successfulClients; }
        public int getFailedClients() { return failedClients; }
        public String getLastError() { return lastError; }
        public boolean isConfigLoaded() { return configLoaded; }
        public boolean isClientsCreated() { return clientsCreated; }

        // Setters
        public void setStartTime(long startTime) { this.startTime = startTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        public void setTotalConfigs(int totalConfigs) { this.totalConfigs = totalConfigs; }
        public void setSuccessfulClients(int successfulClients) { this.successfulClients = successfulClients; }
        public void setFailedClients(int failedClients) { this.failedClients = failedClients; }
        public void setLastError(String lastError) { this.lastError = lastError; }
        public void setConfigLoaded(boolean configLoaded) { this.configLoaded = configLoaded; }
        public void setClientsCreated(boolean clientsCreated) { this.clientsCreated = clientsCreated; }

        // 计算属性
        public long getDurationMs() {
            if (startTime == 0) return 0;
            return (endTime == 0 ? System.currentTimeMillis() : endTime) - startTime;
        }

        public double getSuccessRate() {
            if (totalConfigs == 0) return 0.0;
            return (double) successfulClients / totalConfigs;
        }

        public String getSummary() {
            if (totalConfigs == 0) {
                return "无配置";
            }
            return String.format("成功: %d/%d (%.1f%%), 耗时: %dms", 
                successfulClients, totalConfigs, getSuccessRate() * 100, getDurationMs());
        }
    }

    /**
     * 初始化状态
     * -- GETTER --
     *  检查 McpToolProvider 初始化状态
     *
     * @return 初始化状态
     */
    @Getter
    private volatile InitializationStatus initializationStatus = InitializationStatus.NOT_STARTED;

    /**
     * 向后兼容的初始化完成标志
     * -- GETTER --
     *  检查 McpToolProvider 是否已完成初始化
     *
     * @return 是否已初始化完成
     */
    @Getter
    private volatile boolean initialized = false;

    /**
     * 已加载的客户端配置列表（用于替代 mcpProperties）
     */
    private volatile List<McpProperties.McpClientConfig> loadedConfigs = new ArrayList<>();

    /**
     * 初始化状态详情
     */
    @Getter
    private volatile InitializationStatusDetail statusDetail = new InitializationStatusDetail();
    
    /**
     * 连接池实例（懒加载）
     */
//    private volatile McpConnectionPool mcpConnectionPool;
    
    /**
     * 懒加载获取 McpServiceManager 实例
     * 用于避免循环依赖问题
     */
    private com.wormhole.agent.tool.mcp.service.McpServiceManager getMcpServiceManager() {
        return applicationContext.getBean(com.wormhole.agent.tool.mcp.service.McpServiceManager.class);
    }


    @PostConstruct
    public void init() {
        log.info("MCP工具提供者开始快速启动模式，异步初始化中...");
        initializationStatus = InitializationStatus.IN_PROGRESS;
        statusDetail.setStartTime(System.currentTimeMillis());


        // 异步执行实际的初始化逻辑，不阻塞应用启动
        initializeAsync();
    }

    

    /**
     * 异步初始化MCP客户端
     */
    @Async("mcpTaskExecutor")
    public CompletableFuture<Void> initializeAsync() {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始异步加载MCP配置和初始化客户端");
                
                // 异步加载全局配置
                GlobalMcpConfig globalConfig = loadGlobalConfigAsync();
                statusDetail.setConfigLoaded(true);
                
                if (globalConfig == null || globalConfig.isEmpty()) {
                    log.info("未找到有效的MCP配置，初始化完成但无客户端");
                    completeInitialization(InitializationStatus.COMPLETED, null);
                    return;
                }

                // 转换为 LangChain4j 格式
                List<McpProperties.McpClientConfig> configs =
                    mcpConfigAdapter.convertStandardToLangChain4j(globalConfig.toStandardMcpConfig());

                if (configs.isEmpty()) {
                    log.info("转换后的客户端配置为空，初始化完成但无客户端");
                    completeInitialization(InitializationStatus.COMPLETED, null);
                    return;
                }

                statusDetail.setTotalConfigs(configs.size());
                log.info("开始并行初始化{}个MCP客户端", configs.size());

                // 并行初始化客户端
                ClientInitResult result = initializeClientsParallelWithResult(configs);
                
                // 更新状态详情
                statusDetail.setSuccessfulClients(result.getSuccessCount());
                statusDetail.setFailedClients(result.getFailedCount());
                statusDetail.setClientsCreated(true);

                // 标记部分就绪状态
                initializationStatus = InitializationStatus.PARTIALLY_READY;
                initialized = true;
                
                log.info("MCP客户端并行初始化已启动，提供者现在可用 - {}", statusDetail.getSummary());

                // 发布初始化消息
                publishInitialServerInfo();

                // 最终标记完成
                completeInitialization(InitializationStatus.COMPLETED, null);
                
            } catch (Exception e) {
                log.error("MCP异步初始化失败", e);
                completeInitialization(InitializationStatus.FAILED, e.getMessage());
            }
        }, getAsyncExecutor());
    }
    
    /**
     * 完成初始化，设置最终状态
     */
    private void completeInitialization(InitializationStatus finalStatus, String error) {
        initializationStatus = finalStatus;
        initialized = true;
        statusDetail.setEndTime(System.currentTimeMillis());
        if (error != null) {
            statusDetail.setLastError(error);
        }
        
        log.info("MCP初始化完成 - 状态: {}, {}", finalStatus, statusDetail.getSummary());
    }

    /**
     * 客户端初始化结果
     */
    private static class ClientInitResult {
        private final int successCount;
        private final int failedCount;

        public ClientInitResult(int successCount, int failedCount) {
            this.successCount = successCount;
            this.failedCount = failedCount;
        }

        public int getSuccessCount() { return successCount; }
        public int getFailedCount() { return failedCount; }
    }
    
    /**
     * 异步加载全局配置，带超时控制
     */
    private GlobalMcpConfig loadGlobalConfigAsync() {
        try {
            return mcpGlobalConfigManager.loadGlobalConfig()
                    .doOnNext(config -> { /* 加载MCP配置成功忽略 */ })
                    .timeout(Duration.ofSeconds(5)) // 5秒超时
                    .onErrorResume(throwable -> {
                        log.info("加载MCP配置超时或失败，使用空配置");
                        return Mono.just(new GlobalMcpConfig());
                    })
                    .block();
        } catch (Exception e) {
            log.info("加载MCP配置异常，使用空配置");
            return new GlobalMcpConfig();
        }


    }
    
    /**
     * 获取异步执行器
     */
    private Executor getAsyncExecutor() {
        // 使用ForkJoinPool的公共池，或者可以注入自定义的线程池
        return ForkJoinPool.commonPool();
    }
    
    @PreDestroy
    public void destroy() {
        log.info("开始销毁MCP客户端和连接池");

        // 关闭剩余的直接管理的客户端（向后兼容）
        mcpClients.values().forEach(client -> {
            try {
                client.close();
            } catch (Exception e) {
                log.error("关闭MCP客户端时发生异常", e);
            }
        });
        mcpClients.clear();
        toolCache.clear();
        
        log.info("MCP工具提供者销毁完成");
    }

    /**
     * 并行初始化客户端并返回结果（新版本）
     * 使用CompletableFuture并行创建所有客户端，显著提升启动速度
     *
     * @param configs 客户端配置列表
     * @return 初始化结果
     */
    private ClientInitResult initializeClientsParallelWithResult(List<McpProperties.McpClientConfig> configs) {
        // 保存配置列表
        this.loadedConfigs = new ArrayList<>(configs);

        // 过滤出启用的配置
        List<McpProperties.McpClientConfig> enabledConfigs = configs.stream()
            .filter(McpProperties.McpClientConfig::isEnabled)
            .collect(Collectors.toList());

        if (enabledConfigs.isEmpty()) {
            log.info("没有启用的MCP客户端配置");
            return new ClientInitResult(0, 0);
        }

        log.info("开始并行创建{}个MCP客户端", enabledConfigs.size());

        // 创建并行任务列表
        List<CompletableFuture<ClientCreationResult>> creationTasks = enabledConfigs.stream()
            .map(this::createClientAsync)
            .collect(Collectors.toList());

        // 等待所有客户端创建完成（设置合理超时）
        CompletableFuture<Void> allCreationTasks = CompletableFuture.allOf(
            creationTasks.toArray(new CompletableFuture[0])
        );

        try {
            // 等待所有任务完成，最多等待30秒
            allCreationTasks.get(30, java.util.concurrent.TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("等待客户端创建任务完成时发生异常，部分客户端可能仍在创建中: {}", e.getMessage());
        }

        // 收集创建结果
        int successCount = 0;
        int failedCount = 0;
        
        for (CompletableFuture<ClientCreationResult> task : creationTasks) {
            try {
                ClientCreationResult result = task.get(1, java.util.concurrent.TimeUnit.SECONDS); // 快速获取已完成的结果
                if (result.isSuccess()) {
                    successCount++;
                    // 客户端已在createClientAsync中加入mcpClients
                } else {
                    failedCount++;
                    publishClientCreationFailureMessage(result.getClientName(), 
                        result.getErrorMessage(), result.getConfig(), 0);
                }
            } catch (Exception e) {
                failedCount++;
                log.error("获取客户端创建结果时发生异常: {}", e.getMessage());
            }
        }

        log.info("MCP客户端并行创建完成 - 成功: {}, 失败: {}, 总计: {}", 
            successCount, failedCount, enabledConfigs.size());

        // 注意：不再在这里预加载工具，改为延迟加载
        log.info("客户端创建完成，工具将在首次使用时发现");
        
        return new ClientInitResult(successCount, failedCount);
    }


    /**
     * 异步创建单个MCP客户端
     *
     * @param config 客户端配置
     * @return 创建结果的CompletableFuture
     */
    private CompletableFuture<ClientCreationResult> createClientAsync(McpProperties.McpClientConfig config) {
        return CompletableFuture.supplyAsync(() -> {
            String clientName = config.getName();
            
            try {
                
                // 委托给 McpServiceManager 创建客户端
                McpClient client = mcpCreateHandler.createMcpClient(config);
                
                // 加入客户端缓存
                mcpClients.put(clientName, client);
                
                log.info("成功创建MCP客户端: {}", clientName);
                
                // 立即异步加载工具列表
                loadToolsForNewClient(clientName, client);
                
                return ClientCreationResult.success(clientName, config, client);
                
            } catch (Exception e) {
                log.error("创建MCP客户端失败: {} - {}", clientName, e.getMessage());
                return ClientCreationResult.failure(clientName, config, e.getMessage());
            }
        }, getAsyncExecutor());
    }

    /**
     * 为新创建的客户端异步加载工具列表
     * 
     * @param clientName 客户端名称
     * @param client MCP客户端
     */
    private void loadToolsForNewClient(String clientName, McpClient client) {
        CompletableFuture.runAsync(() -> {
            try {
                
                List<OpenAiTool> tools = mcpToolAdapter.discoverTools(client);
                
                // 缓存工具列表到本地缓存
                toolCache.put(clientName, tools);
                
                // 异步缓存到Redis
                cacheManager.putTools(clientName, tools)
                    .subscribe(
                        success -> {
                            if (success) {
                            }
                        },
                        error -> { /* Redis缓存失败忽略 */ }
                    );
                
                log.info("客户端{}工具加载完成，发现{}个工具", clientName, tools.size());
                
                // 打印工具列表（调试用）
                if (log.isDebugEnabled() && !tools.isEmpty()) {
                }
                
            } catch (Exception e) {
                // 即使失败也要设置空列表，避免后续查询时重复尝试
                toolCache.put(clientName, Collections.emptyList());
            }
        }, getAsyncExecutor()).exceptionally(throwable -> {
            log.error("为客户端{}异步加载工具时发生异常", clientName, throwable);
            return null;
        });
    }
    

    /**
     * 客户端创建结果
     */
    private static class ClientCreationResult {
        private final String clientName;
        private final McpProperties.McpClientConfig config;
        private final boolean success;
        private final McpClient client;
        private final String errorMessage;

        private ClientCreationResult(String clientName, McpProperties.McpClientConfig config, 
                                   boolean success, McpClient client, String errorMessage) {
            this.clientName = clientName;
            this.config = config;
            this.success = success;
            this.client = client;
            this.errorMessage = errorMessage;
        }

        public static ClientCreationResult success(String clientName, McpProperties.McpClientConfig config, McpClient client) {
            return new ClientCreationResult(clientName, config, true, client, null);
        }

        public static ClientCreationResult failure(String clientName, McpProperties.McpClientConfig config, String errorMessage) {
            return new ClientCreationResult(clientName, config, false, null, errorMessage);
        }

        public String getClientName() { return clientName; }
        public McpProperties.McpClientConfig getConfig() { return config; }
        public boolean isSuccess() { return success; }
        public McpClient getClient() { return client; }
        public String getErrorMessage() { return errorMessage; }
    }


    /**
     * 发现MCP工具（优化版本 - 支持并行发现和延迟加载）
     * 
     * @param params MCP功能调用参数
     * @param context 工具链上下文
     * @return 工具列表的Mono
     */
    public Mono<List<OpenAiTool>> discoverMcpTools(List<LlmInputs.McpFunctionCallParam> params,
                                                   ToolChainContext context) {
        if (CollectionUtils.isEmpty(params)) {
            return Mono.just(Collections.emptyList());
        }
        
        
        // 使用并行流处理多个客户端的工具发现
        return Mono.fromCallable(() -> {
            List<CompletableFuture<List<OpenAiTool>>> discoveryTasks = params.stream()
                .map(param -> discoverToolsForClientAsync(param, context))
                .collect(Collectors.toList());
            
            // 等待所有发现任务完成
            List<OpenAiTool> allTools = new ArrayList<>();
            for (CompletableFuture<List<OpenAiTool>> task : discoveryTasks) {
                try {
                    List<OpenAiTool> tools = task.get(10, java.util.concurrent.TimeUnit.SECONDS); // 10秒超时
                    allTools.addAll(tools);
                } catch (Exception e) {
                    log.error("等待工具发现任务完成时发生异常: {}", e.getMessage());
                }
            }
            
            return allTools;
            
        }).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 异步为单个客户端发现工具
     */
    private CompletableFuture<List<OpenAiTool>> discoverToolsForClientAsync(
            LlmInputs.McpFunctionCallParam param, ToolChainContext context) {
        
        return CompletableFuture.supplyAsync(() -> {
            String clientName = param.getMcpServerName();
            McpClient client = mcpClients.get(clientName);
            
            if (client == null) {
                return Collections.<OpenAiTool>emptyList();
            }
            
            try {
                
                List<OpenAiTool> tools = getOrDiscoverToolsLazy(clientName, client, context);
                
                // 按工具名称过滤（如果指定了具体工具）
                if (CollectionUtils.isNotEmpty(param.getToolNames())) {
                    List<String> toolNames = param.getToolNames();
                    tools = tools.stream()
                        .filter(item -> toolNames.contains(item.getFunction().getName()))
                        .collect(Collectors.toList());
                }

                return tools;
                
            } catch (Exception e) {
                log.error("从MCP客户端{}发现工具时发生异常", clientName, e);
                return Collections.<OpenAiTool>emptyList();
            }
        }, getAsyncExecutor());
    }
    
    /**
     * 执行MCP工具
     * 
     * @param toolName 工具名称
     * @param arguments 工具参数
     * @param context 工具链上下文
     * @return 执行结果的Mono
     */
    public Mono<String> executeMcpTool(String toolName, Map<String, Object> arguments, 
                                       ToolChainContext context) {
        // 从上下文中获取工具对应的客户端名称
        String clientName = context.getMcpToolClientMap().get(toolName);
        if (clientName == null) {
            return Mono.error(new RuntimeException("未找到工具对应的MCP客户端：" + toolName));
        }
        
        // 使用连接池获取客户端
        return getClient(clientName)
            .flatMap(client -> Mono.fromCallable(() -> 
                mcpToolAdapter.executeTool(client, toolName, arguments))
                .subscribeOn(Schedulers.boundedElastic()))
            .retryWhen(createRetrySpec(toolName))
            .onErrorMap(throwable -> handleMcpToolError(throwable, toolName, context));
    }

    // 简化客户端获取逻辑
    private Mono<McpClient> getClient(String clientName) {
        return Mono.fromCallable(() -> {
            McpClient client = mcpClients.get(clientName);
            if (client == null) {
                throw new RuntimeException("MCP客户端不存在: " + clientName);
            }
            return client;
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 根据客户端名称获取配置
     * 
     * @param clientName 客户端名称
     * @return 客户端配置
     */
    private McpProperties.McpClientConfig getConfigByName(String clientName) {
        return loadedConfigs.stream()
            .filter(config -> clientName.equals(config.getName()))
            .findFirst()
            .orElse(null);
    }
    
    /**
     * 延迟加载模式获取或发现工具（优化版本）
     * 优先从缓存获取，按需发现工具，显著减少启动时间
     */
    private List<OpenAiTool> getOrDiscoverToolsLazy(String clientName, McpClient client,
                                                    ToolChainContext context) {
        // 1. 优先从本地内存缓存获取（最快）
        List<OpenAiTool> localCachedTools = toolCache.get(clientName);
        if (localCachedTools != null && !localCachedTools.isEmpty()) {
            updateToolClientMapping(localCachedTools, clientName, context);
            return localCachedTools;
        }

        // 2. 从Redis缓存获取（较快）
        try {
            List<OpenAiTool> redisCachedTools = cacheManager.getTools(clientName)
                .timeout(Duration.ofSeconds(2)) // 2秒超时，避免阻塞
                .onErrorReturn(Collections.emptyList())
                .block();
                
            if (redisCachedTools != null && !redisCachedTools.isEmpty()) {
                // 同步到本地缓存以备下次使用
                toolCache.put(clientName, redisCachedTools);
                updateToolClientMapping(redisCachedTools, clientName, context);
                return redisCachedTools;
            }
        } catch (Exception e) {
            log.error("从Redis缓存获取工具失败，将直接发现: {} - {}", clientName, e.getMessage());
        }

        // 3. 缓存都未命中，首次发现工具（最慢但必要）
        log.info("首次为客户端{}发现工具...", clientName);
        
        try {
            List<OpenAiTool> discoveredTools = mcpToolAdapter.discoverTools(client);
            
            log.info("客户端{}工具发现成功，发现{}个工具", clientName, discoveredTools.size());
            
            // 异步缓存到Redis，不阻塞当前操作
            cacheManager.putTools(clientName, discoveredTools)
                .subscribe(
                    success -> {
                        if (success) {
                            log.debug("客户端{}工具已异步缓存到Redis", clientName);
                        }
                    },
                    error -> log.debug("客户端{}工具Redis缓存失败: {}", clientName, error.getMessage())
                );

            // 立即缓存到本地内存
            toolCache.put(clientName, discoveredTools);

            // 更新工具与客户端的映射关系
            updateToolClientMapping(discoveredTools, clientName, context);

            return discoveredTools;
            
        } catch (Exception e) {
            log.error("为客户端{}发现工具失败: {}", clientName, e.getMessage());
            return Collections.emptyList();
        }
    }

    
    /**
     * 更新工具与客户端的映射关系
     */
    private void updateToolClientMapping(List<OpenAiTool> tools, String clientName,
                                         ToolChainContext context) {
        for (OpenAiTool tool : tools) {
            String toolName = tool.getFunction().getName();
            context.getMcpToolClientMap().put(toolName, clientName);
        }
    }

    /**
     * 获取所有预加载的工具
     *
     * @return 所有可用工具的映射：客户端名称 -> 工具列表
     */
    public Map<String, List<OpenAiTool>> getAllPreloadedTools() {
        return new HashMap<>(toolCache);
    }

    /**
     * 获取指定客户端的预加载工具
     *
     * @param clientName 客户端名称
     * @return 工具列表，如果客户端不存在或没有工具则返回空列表
     */
    public List<OpenAiTool> getPreloadedTools(String clientName) {
        return toolCache.getOrDefault(clientName, Collections.emptyList());
    }

    /**
     * 动态添加MCP客户端
     * 由McpServiceManager调用，用于运行时添加新的客户端
     *
     * @param clientName 客户端名称
     * @param client MCP客户端实例
     * @param tools 客户端工具列表
     */
    public synchronized void addClientDynamically(String clientName, McpClient client, List<OpenAiTool> tools) {
        if (clientName == null || client == null) {
            throw new IllegalArgumentException("客户端名称和客户端实例不能为空");
        }

        log.info("动态添加MCP客户端到工具提供者: {} (工具数: {})", clientName, tools != null ? tools.size() : 0);

        try {
            // 添加客户端到缓存
            mcpClients.put(clientName, client);

            // 添加工具到多级缓存
            if (tools != null && !tools.isEmpty()) {
                toolCache.put(clientName, tools);
                cacheManager.putTools(clientName, tools).subscribe();
                log.info("客户端 {} 的工具已缓存，工具数: {}", clientName, tools.size());
            } else {
                toolCache.put(clientName, Collections.emptyList());
                cacheManager.putTools(clientName, Collections.emptyList()).subscribe();
            }

            log.info("动态添加MCP客户端成功: {}", clientName);

        } catch (Exception e) {
            log.error("动态添加MCP客户端失败: {}", clientName, e);
            // 回滚操作
            mcpClients.remove(clientName);
            toolCache.remove(clientName);
            throw new RuntimeException("动态添加MCP客户端失败: " + clientName, e);
        }
    }

    /**
     * 动态删除MCP客户端
     * 专注于工具提供者层面的清理，不处理连接池等复杂逻辑
     * 主要由McpServiceManager调用，用于运行时删除客户端
     *
     * @param clientName 客户端名称
     * @return 是否删除成功
     */
    public synchronized boolean removeClientDynamically(String clientName) {
        if (clientName == null || clientName.trim().isEmpty()) {
            log.warn("客户端名称为空，跳过删除操作");
            return false;
        }

        log.info("从工具提供者中删除MCP客户端: {}", clientName);

        try {
            // 获取并关闭客户端连接
            McpClient client = mcpClients.remove(clientName);
            if (client != null) {
                try {
                    client.close();
                    log.debug("客户端 {} 连接已关闭", clientName);
                } catch (Exception e) {
                    log.warn("关闭客户端 {} 连接时发生异常", clientName, e);
                }
            } else {
                log.debug("客户端 {} 在工具提供者中不存在", clientName);
            }

            // 清理本地工具缓存
            List<OpenAiTool> removedTools = toolCache.remove(clientName);
            if (removedTools != null) {
                log.debug("从本地缓存移除客户端 {} 的 {} 个工具", clientName, removedTools.size());
            }

            // 清理Redis工具缓存
            cacheManager.evictTools(clientName).subscribe(
                success -> {
                    if (success) {
                        log.debug("客户端 {} Redis缓存清理成功", clientName);
                    } else {
                        log.warn("客户端 {} Redis缓存清理失败", clientName);
                    }
                },
                error -> log.warn("客户端 {} Redis缓存清理异常", clientName, error)
            );

            log.info("工具提供者中的客户端删除成功: {}", clientName);
            return true;

        } catch (Exception e) {
            log.error("工具提供者删除客户端失败: {}", clientName, e);
            return false;
        }
    }

    /**
     * 检查客户端是否存在
     *
     * @param clientName 客户端名称
     * @return 是否存在
     */
    public boolean hasClient(String clientName) {
        return mcpClients.containsKey(clientName);
    }


    /**
     * 获取所有可用的客户端名称
     *
     * @return 客户端名称集合
     */
    public Set<String> getAvailableClientNames() {
        return new HashSet<>(mcpClients.keySet());
    }

    /**
     * 检查指定客户端是否可用
     *
     * @param clientName 客户端名称
     * @return 是否可用
     */
    public boolean isClientAvailable(String clientName) {
        return mcpClients.containsKey(clientName);
    }

    // ==================== 数据访问接口（供 McpServiceManager 使用）====================

    /**
     * 获取所有已加载的MCP客户端
     * 返回客户端名称到客户端实例的映射副本，确保外部无法修改内部状态
     *
     * @return 客户端映射的副本
     */
    public Map<String, McpClient> getAllClients() {
        return new HashMap<>(mcpClients);
    }

    /**
     * 获取所有工具缓存
     * 返回客户端名称到工具列表的映射副本，确保外部无法修改内部状态
     *
     * @return 工具缓存的副本
     */
    public Map<String, List<OpenAiTool>> getAllToolCache() {
        return new HashMap<>(toolCache);
    }

    /**
     * 获取已加载的客户端配置列表
     * 从已保存的配置中获取所有已启用且成功加载的客户端配置
     *
     * @return 已加载的配置列表
     */
    public List<McpProperties.McpClientConfig> getLoadedConfigs() {
        if (loadedConfigs == null || loadedConfigs.isEmpty()) {
            return Collections.emptyList();
        }

        return loadedConfigs.stream()
            .filter(config -> config.isEnabled() && mcpClients.containsKey(config.getName()))
            .collect(Collectors.toList());
    }

    /**
     * 获取MCP集成状态摘要
     *
     * @return 状态摘要信息
     */
    public McpStatus getStatus() {
        int totalClients = mcpClients.size();
        int totalTools = toolCache.values().stream()
                .mapToInt(List::size)
                .sum();

        Map<String, Integer> clientToolCounts = new HashMap<>();
        toolCache.forEach((clientName, tools) -> {
            clientToolCounts.put(clientName, tools.size());
        });

        return McpStatus.builder()
                .totalClients(totalClients)
                .totalTools(totalTools)
                .clientToolCounts(clientToolCounts)
                .availableClients(new HashSet<>(mcpClients.keySet()))
                .build();
    }

    /**
     * 发布初始化服务器信息
     * 系统启动时发布所有成功初始化的MCP服务器信息
     */
    private void publishInitialServerInfo() {
        if (!mcpRedisProperties.isServerInfoEnabled()) {
            log.debug("服务器信息发布功能已禁用，跳过初始化消息发布");
            return;
        }

        if (mcpClients.isEmpty()) {
            log.debug("没有活跃的MCP客户端，跳过初始化消息发布");
            return;
        }

        log.info("开始发布MCP服务器初始化信息，客户端数量: {}", mcpClients.size());

        mcpClients.forEach((clientName, client) -> {
            try {
                // 获取工具列表
                List<OpenAiTool> tools = toolCache.getOrDefault(clientName, Collections.emptyList());

                // 构建服务状态
                McpServiceStatus status = createServiceStatus(clientName, tools);

                // 异步发布初始化消息
                mcpServerInfoPublisher.publishInitMessage(clientName, status, tools)
                    .subscribe(
                        null, // onNext为空，因为返回Mono<Void>
                        error -> log.warn("发布MCP服务器初始化消息失败: {}", clientName, error),
                        () -> log.debug("成功发布MCP服务器初始化消息: {}", clientName)
                    );

            } catch (Exception e) {
                log.warn("处理MCP服务器初始化消息时发生异常: {}", clientName, e);
            }
        });

        log.info("MCP服务器初始化消息发布请求已提交");
    }

    /**
     * 创建服务状态信息
     * 根据客户端名称和工具列表创建McpServiceStatus
     *
     * @param clientName 客户端名称
     * @param tools 工具列表
     * @return 服务状态信息
     */
    private McpServiceStatus createServiceStatus(String clientName, List<OpenAiTool> tools) {
        // 从配置中获取传输方式和端点信息
        String transport = "unknown";
        String endpoint = null;

        // 查找对应的配置
        for (McpProperties.McpClientConfig config : loadedConfigs) {
            if (clientName.equals(config.getName())) {
                transport = config.getTransport();
                if ("sse".equalsIgnoreCase(transport)) {
                    endpoint = config.getUrl();
                } else if ("stdio".equalsIgnoreCase(transport)) {
                    endpoint = config.getCommand() != null && !config.getCommand().isEmpty()
                        ? String.join(" ", config.getCommand())
                        : null;
                }
                break;
            }
        }

        // 创建活跃状态
        return McpServiceStatus.active(
            clientName,
            transport,
            endpoint,
            tools != null ? tools.size() : 0
        );
    }

    
    /**
     * 发布客户端创建失败消息
     */
    private void publishClientCreationFailureMessage(String clientName, String errorMessage, 
                                                   McpProperties.McpClientConfig config, 
                                                   int totalRetries) {
        try {
            mcpServerInfoPublisher.publishRetryExhaustedMessage(
                clientName, 
                String.format("创建客户端失败: %s (传输: %s)", errorMessage, config.getTransport()),
                totalRetries
            ).subscribe(
                null,
                error -> log.warn("发布客户端创建失败消息失败: {}", clientName, error),
                () -> log.debug("成功发布客户端创建失败消息: {}", clientName)
            );
        } catch (Exception e) {
            log.warn("发布客户端创建失败消息时发生异常: {}", clientName, e);
        }
    }
    
    
    
    /**
     * 创建重试策略
     * 
     * @param toolName 工具名称
     * @return 重试规范
     */
    private Retry createRetrySpec(String toolName) {
        return Retry.backoff(3, Duration.ofMillis(500))
            .maxBackoff(Duration.ofSeconds(5))
            .filter(throwable -> isRetriableError(throwable))
            .doBeforeRetry(retrySignal -> {
                log.warn("重试执行MCP工具: {} (第{}次尝试) - {}", 
                    toolName, 
                    retrySignal.totalRetries() + 1,
                    retrySignal.failure().getMessage());
            })
            .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                log.error("MCP工具 {} 重试次数已耗尽，停止重试", toolName);
                return retrySignal.failure();
            });
    }
    
    /**
     * 判断错误是否可重试
     * 
     * @param throwable 异常
     * @return 是否可重试
     */
    private boolean isRetriableError(Throwable throwable) {
        String message = throwable.getMessage();
        String causeMessage = throwable.getCause() != null ? throwable.getCause().getMessage() : "";
        
        // 不可重试的错误类型
        if (message != null && (
            message.contains("404") ||
            message.contains("401") ||
            message.contains("403") ||
            message.contains("tool not found") ||
            message.contains("invalid argument") ||
            message.contains("parameter"))) {
            return false;
        }
        
        if (causeMessage.contains("404") ||
            causeMessage.contains("Unexpected status code: 404") ||
            causeMessage.contains("401") ||
            causeMessage.contains("403")) {
            return false;
        }
        
        // 可重试的错误类型
        return message != null && (
            message.contains("timeout") ||
            message.contains("Connection reset") ||
            message.contains("Connection aborted") ||
            message.contains("Connection refused") ||
            message.contains("500") ||
            message.contains("502") ||
            message.contains("503")
        ) || causeMessage.contains("timeout") ||
           causeMessage.contains("Connection") ||
           causeMessage.contains("500") ||
           causeMessage.contains("502") ||
           causeMessage.contains("503");
    }
    
    /**
     * 处理MCP工具执行错误，包括自动清理和推送通知
     * 
     * @param throwable 原始异常
     * @param toolName 工具名称
     * @param context 工具链上下文
     * @return 增强后的异常
     */
    private Throwable handleMcpToolError(Throwable throwable, String toolName, ToolChainContext context) {
        // 获取客户端名称
        String clientName = context != null ? context.getMcpToolClientMap().get(toolName) : null;
        
        // 分析错误类型并决定是否需要清理客户端
        boolean shouldCleanupClient = shouldCleanupClientOnError(throwable);
        
        // 执行客户端清理（如果需要）
        if (shouldCleanupClient && clientName != null) {
            cleanupClientOnError(clientName, throwable);
        }
        
        // 发布工具执行错误事件
        publishToolExecutionError(clientName, toolName, throwable);
        
        // 返回增强的错误信息
        return enhanceErrorMessage(throwable, toolName);
    }
    
    /**
     * 判断是否需要根据错误类型清理客户端
     * 
     * @param throwable 异常信息
     * @return 是否需要清理客户端
     */
    private boolean shouldCleanupClientOnError(Throwable throwable) {
        if (throwable == null) {
            return false;
        }
        
        String message = throwable.getMessage();
        String causeMessage = throwable.getCause() != null ? throwable.getCause().getMessage() : "";
        
        // 致命错误，需要清理客户端
        if (message != null && (
            message.contains("404") ||
            message.contains("401") ||
            message.contains("403") ||
            message.contains("Connection refused") ||
            message.contains("Connection reset") ||
            message.contains("No route to host") ||
            message.contains("Network is unreachable"))) {
            return true;
        }
        
        if (causeMessage.contains("404") ||
            causeMessage.contains("401") ||
            causeMessage.contains("403") ||
            causeMessage.contains("Connection refused") ||
            causeMessage.contains("Connection reset")) {
            return true;
        }
        
        // 临时错误，不清理客户端
        return false;
    }
    
    /**
     * 在错误时清理客户端
     * 
     * @param clientName 客户端名称
     * @param throwable 错误信息
     */
    private void cleanupClientOnError(String clientName, Throwable throwable) {
        try {
            log.warn("检测到MCP客户端致命错误，开始自动清理: {} - {}", clientName, throwable.getMessage());
            
            // 发布客户端错误事件，触发统一清理流程
            eventPublisher.publishEvent(McpClientEvent.clientError(
                this, 
                clientName, 
                "工具执行致命错误: " + throwable.getMessage(),
                throwable
            ));
            
            // 本地立即清理（降级方案）
            boolean cleanupResult = removeClientDynamically(clientName);
            if (cleanupResult) {
                log.info("MCP客户端自动清理成功: {}", clientName);
                
                // 发布下线状态消息
                publishClientOfflineMessage(clientName, throwable.getMessage());
            } else {
                log.warn("MCP客户端自动清理失败: {}", clientName);
            }
            
        } catch (Exception e) {
            log.error("清理MCP客户端时发生异常: {}", clientName, e);
        }
    }
    
    /**
     * 发布工具执行错误事件
     * 
     * @param clientName 客户端名称
     * @param toolName 工具名称
     * @param throwable 异常信息
     */
    private void publishToolExecutionError(String clientName, String toolName, Throwable throwable) {
        try {
            if (clientName != null) {
                // 发布工具执行错误消息
                mcpServerInfoPublisher.publishErrorMessage(
                    clientName,
                    String.format("工具 %s 执行失败: %s", toolName, throwable.getMessage()),
                    "工具执行错误"
                ).subscribe(
                    null,
                    error -> log.warn("发布工具执行错误消息失败: {}", clientName, error),
                    () -> log.debug("成功发布工具执行错误消息: {}", clientName)
                );
            }
        } catch (Exception e) {
            log.warn("发布工具执行错误消息时发生异常: {}", clientName, e);
        }
    }
    
    /**
     * 发布客户端下线消息
     * 
     * @param clientName 客户端名称
     * @param reason 下线原因
     */
    private void publishClientOfflineMessage(String clientName, String reason) {
        try {
            // 创建离线状态（使用ERROR状态表示因错误而下线）
            McpServiceStatus offlineStatus = McpServiceStatus.error(clientName, "自动清理: " + reason);
            
            mcpServerInfoPublisher.publishDeleteMessage(clientName, offlineStatus)
                .subscribe(
                    null,
                    error -> log.warn("发布客户端下线消息失败: {}", clientName, error),
                    () -> log.debug("成功发布客户端下线消息: {}", clientName)
                );
        } catch (Exception e) {
            log.warn("发布客户端下线消息时发生异常: {}", clientName, e);
        }
    }
    
    /**
     * 增强错误消息，提供更友好的用户提示
     * 
     * @param throwable 原始异常
     * @param toolName 工具名称
     * @return 增强后的异常
     */
    private Throwable enhanceErrorMessage(Throwable throwable, String toolName) {
        String originalMessage = throwable.getMessage();
        
        // 分析错误类型并提供友好的错误消息
        if (originalMessage != null) {
            if (originalMessage.contains("404") || 
                (throwable.getCause() != null && throwable.getCause().getMessage().contains("404"))) {
                
                return new RuntimeException(
                    String.format("MCP工具调用失败: %s - 服务不可达(404)。\n" +
                        "可能原因：\n" +
                        "1. MCP服务已停止运行\n" +
                        "2. 服务端点URL已变更\n" +
                        "3. 网络连接问题\n" +
                        "建议解决方案：\n" +
                        "1. 检查MCP服务是否正常运行\n" +
                        "2. 验证配置中的服务端点URL\n" +
                        "3. 重新配置MCP连接或联系服务提供方", toolName), 
                    throwable);
            }
            
            if (originalMessage.contains("Connection refused")) {
                return new RuntimeException(
                    String.format("MCP工具调用失败: %s - 连接被拒绝。\n" +
                        "可能原因：MCP服务未启动或端口不可访问\n" +
                        "建议解决方案：检查MCP服务状态并确保网络连接正常", toolName), 
                    throwable);
            }
            
            if (originalMessage.contains("timeout")) {
                return new RuntimeException(
                    String.format("MCP工具调用失败: %s - 执行超时。\n" +
                        "可能原因：网络延迟或服务响应缓慢\n" +
                        "建议解决方案：稍后重试或增加超时时间配置", toolName), 
                    throwable);
            }
            
            if (originalMessage.contains("401") || originalMessage.contains("403")) {
                return new RuntimeException(
                    String.format("MCP工具调用失败: %s - 认证失败。\n" +
                        "可能原因：访问凭据无效或已过期\n" +
                        "建议解决方案：检查并更新MCP服务的认证配置", toolName), 
                    throwable);
            }
        }
        
        // 通用错误处理
        return new RuntimeException(
            String.format("MCP工具调用失败: %s - %s\n" +
                "如果问题持续存在，请检查MCP服务状态或联系技术支持", 
                toolName, originalMessage), 
            throwable);
    }

    /**
     * MCP状态信息
     */
    @lombok.Data
    @lombok.Builder
    public static class McpStatus {
        private boolean enabled;
        private int totalClients;
        private int totalTools;
        private Map<String, Integer> clientToolCounts;
        private Set<String> availableClients;
    }
}
