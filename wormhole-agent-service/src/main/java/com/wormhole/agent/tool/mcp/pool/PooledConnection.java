package com.wormhole.agent.tool.mcp.pool;

import dev.langchain4j.mcp.client.McpClient;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;

/**
 * 池化连接包装类
 * 包装MCP客户端连接，提供生命周期管理和状态跟踪
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@Getter
public class PooledConnection implements McpConnection {
    
    /**
     * MCP客户端实例
     */
    private final McpClient client;
    
    /**
     * 客户端名称
     */
    private final String clientName;
    
    /**
     * 连接创建时间
     */
    private final Instant createdAt;
    
    /**
     * 最后使用时间
     */
    private volatile Instant lastUsedAt;
    
    /**
     * 连接最大生存时间
     */
    private final Duration maxLifetime;
    
    /**
     * 连接空闲超时时间
     */
    private final Duration idleTimeout;
    
    /**
     * 连接是否已关闭
     */
    private volatile boolean closed = false;
    
    /**
     * 构造函数
     * 
     * @param client MCP客户端
     * @param clientName 客户端名称
     */
    public PooledConnection(McpClient client, String clientName) {
        this(client, clientName, Duration.ofMinutes(30), Duration.ofMinutes(5));
    }
    
    /**
     * 构造函数
     * 
     * @param client MCP客户端
     * @param clientName 客户端名称
     * @param maxLifetime 最大生存时间
     * @param idleTimeout 空闲超时时间
     */
    public PooledConnection(McpClient client, String clientName, 
                          Duration maxLifetime, Duration idleTimeout) {
        this.client = client;
        this.clientName = clientName;
        this.maxLifetime = maxLifetime;
        this.idleTimeout = idleTimeout;
        this.createdAt = Instant.now();
        this.lastUsedAt = this.createdAt;
    }
    
    /**
     * 更新最后使用时间
     */
    public void updateLastUsed() {
        this.lastUsedAt = Instant.now();
    }
    
    /**
     * 检查连接是否已过期
     * 
     * @return true如果连接已过期
     */
    public boolean isExpired() {
        if (closed) {
            return true;
        }
        
        Instant now = Instant.now();
        
        // 检查最大生存时间
        if (Duration.between(createdAt, now).compareTo(maxLifetime) > 0) {
            log.debug("连接 {} 超过最大生存时间，已过期", clientName);
            return true;
        }
        
        // 检查空闲超时时间
        if (Duration.between(lastUsedAt, now).compareTo(idleTimeout) > 0) {
            log.debug("连接 {} 超过空闲超时时间，已过期", clientName);
            return true;
        }
        
        return false;
    }
    
    /**
     * 检查连接是否健康
     *
     * @return 健康检查结果的Mono
     */
    @Override
    public Mono<Boolean> isHealthy() {
        return Mono.fromCallable(() -> {
            if (closed || isExpired()) {
                return false;
            }

            try {
                // 这里可以添加具体的健康检查逻辑
                // 例如发送ping请求或检查连接状态
                return client != null;
            } catch (Exception e) {
                log.warn("连接健康检查失败: {}", clientName, e);
                return false;
            }
        });
    }

    /**
     * 同步健康检查方法（向后兼容）
     *
     * @return true如果连接健康
     */
    public boolean isHealthySync() {
        if (closed || isExpired()) {
            return false;
        }

        try {
            // 这里可以添加具体的健康检查逻辑
            // 例如发送ping请求或检查连接状态
            return client != null;
        } catch (Exception e) {
            log.warn("连接健康检查失败: {}", clientName, e);
            return false;
        }
    }
    
    /**
     * 获取连接年龄
     * 
     * @return 连接年龄
     */
    public Duration getAge() {
        return Duration.between(createdAt, Instant.now());
    }
    
    /**
     * 获取空闲时间
     * 
     * @return 空闲时间
     */
    public Duration getIdleTime() {
        return Duration.between(lastUsedAt, Instant.now());
    }
    
    /**
     * 检查连接是否已关闭
     *
     * @return true如果连接已关闭
     */
    @Override
    public boolean isClosed() {
        return closed;
    }

    /**
     * 关闭连接
     */
    @Override
    public void close() {
        if (closed) {
            return;
        }

        closed = true;

        try {
            if (client != null) {
                client.close();
                log.debug("连接 {} 已关闭", clientName);
            }
        } catch (Exception e) {
            log.warn("关闭连接异常: {}", clientName, e);
        }
    }

    /**
     * 异步关闭连接
     *
     * @return 关闭操作的Mono
     */
    @Override
    public Mono<Void> closeAsync() {
        return Mono.fromRunnable(this::close);
    }

    /**
     * 重置连接状态
     * 用于连接复用时清理状态
     */
    @Override
    public void reset() {
        updateLastUsed();
        // 这里可以添加其他重置逻辑，如清理缓存等
    }

    /**
     * 获取连接的详细信息
     *
     * @return 连接信息
     */
    @Override
    public ConnectionInfo getConnectionInfo() {
        return new PooledConnectionInfo();
    }
    
    /**
     * 获取连接信息字符串
     *
     * @return 连接信息
     */
    @Override
    public String toString() {
        return String.format("PooledConnection{clientName='%s', age=%s, idle=%s, expired=%s, healthy=%s}",
            clientName, getAge(), getIdleTime(), isExpired(), isHealthy());
    }

    /**
     * 池化连接信息实现类
     */
    private class PooledConnectionInfo implements ConnectionInfo {

        @Override
        public String getClientName() {
            return PooledConnection.this.clientName;
        }

        @Override
        public Instant getCreatedAt() {
            return PooledConnection.this.createdAt;
        }

        @Override
        public Instant getLastUsedAt() {
            return PooledConnection.this.lastUsedAt;
        }

        @Override
        public Duration getAge() {
            return PooledConnection.this.getAge();
        }

        @Override
        public Duration getIdleTime() {
            return PooledConnection.this.getIdleTime();
        }

        @Override
        public boolean isExpired() {
            return PooledConnection.this.isExpired();
        }

        @Override
        public boolean isClosed() {
            return PooledConnection.this.closed;
        }

        @Override
        public String getStatus() {
            if (closed) {
                return "CLOSED";
            } else if (isExpired()) {
                return "EXPIRED";
            } else if (isHealthySync()) {
                return "HEALTHY";
            } else {
                return "UNHEALTHY";
            }
        }

        @Override
        public String toString() {
            return String.format("ConnectionInfo{clientName='%s', status='%s', age=%s, idle=%s}",
                getClientName(), getStatus(), getAge(), getIdleTime());
        }
    }
}
