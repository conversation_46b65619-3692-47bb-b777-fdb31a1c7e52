package com.wormhole.agent.tool.mcp.redis.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MCP Redis配置属性类
 * 支持Spring Boot配置绑定，提供MCP Redis功能的配置选项
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@Data
@Component
@ConfigurationProperties(prefix = "mcp.redis")
public class McpRedisProperties {
    
    /**
     * 是否启用MCP Redis配置管理
     * 默认false，保持向后兼容
     */
    private boolean enabled = true;

    /**
     * 全局配置Redis key名称
     */
    private String globalConfigKey = "mcp:config:global";
    /**
     * 客户端消息Redis pub/sub频道名称
     */
    private String clientMessageChannel = "mcp:server:msg";

    /**
     * 服务器信息Redis pub/sub频道名称
     * 用于向wormhole-platform推送MCP服务器状态信息
     */
    private String serverInfoChannel = "mcp:server:info";

    /**
     * 已处理消息Redis Set key名称（用于去重）
     */
    private String processedMessagesKey = "mcp:processed:messages";
    
    /**
     * 分布式锁超时时间（毫秒）
     */
    private long lockTimeoutMs = 30000L;
    
    /**
     * 分布式锁等待时间（毫秒）
     */
    private long lockWaitTimeMs = 1000L;
    
    /**
     * 消息去重过期时间（秒）
     */
    private long messageDeduplicationExpireSeconds = 3600L;
    
    /**
     * 配置同步超时时间（秒）
     */
    private long configSyncTimeoutSeconds = 30L;

    
    /**
     * 是否启用配置变更日志
     */
    private boolean enableChangeLogging = true;
    
    /**
     * 消息处理重试次数
     */
    private int messageRetryCount = 3;
    
    /**
     * 消息处理重试间隔（毫秒）
     */
    private long messageRetryIntervalMs = 1000L;
    
    /**
     * 配置验证是否严格模式
     * true: 配置验证失败时抛出异常
     * false: 配置验证失败时记录警告并跳过
     */
    private boolean strictValidation = false;
    
    /**
     * 是否启用配置缓存
     */
    private boolean enableConfigCache = true;

    /**
     * 配置缓存过期时间（秒）
     */
    private long configCacheExpireSeconds = 300L;

    /**
     * 是否启用服务器信息发布
     * 控制是否向wormhole-platform推送MCP服务器状态信息
     */
    private boolean serverInfoEnabled = true;
    
    /**
     * 是否启用连接重试
     */
    private boolean connectionRetryEnabled = true;
    
    /**
     * 连接重试次数
     */
    private int connectionRetryCount = 1;
    
    /**
     * 连接重试间隔（毫秒）
     */
    private long connectionRetryIntervalMs = 1000L;
    
    /**
     * 连接超时时间（毫秒）
     */
    private long connectionTimeoutMs = 10000L;
    
    /**
     * 工具发现超时时间（毫秒）
     */
    private long toolDiscoveryTimeoutMs = 15000L;
    
    /**
     * 获取分布式锁键名前缀
     */
    public String getLockKeyPrefix() {
        return "wormhole-agent:mcp:";
    }
    
    /**
     * 构建完整的分布式锁键名
     */
    public String buildLockKey(String suffix) {
        return getLockKeyPrefix() + suffix;
    }
    
    /**
     * 验证配置的有效性
     */
    public boolean isValidConfig() {

        
        // 检查必要的配置项
        if (globalConfigKey == null || globalConfigKey.trim().isEmpty()) {
            return false;
        }
        
        if (clientMessageChannel == null || clientMessageChannel.trim().isEmpty()) {
            return false;
        }

        if (serverInfoChannel == null || serverInfoChannel.trim().isEmpty()) {
            return false;
        }

        if (processedMessagesKey == null || processedMessagesKey.trim().isEmpty()) {
            return false;
        }
        
        // 检查超时配置的合理性
        if (lockTimeoutMs <= 0 || lockWaitTimeMs < 0) {
            return false;
        }
        
        if (messageDeduplicationExpireSeconds <= 0) {
            return false;
        }
        
        if (configSyncTimeoutSeconds <= 0) {
            return false;
        }
        
        if (messageRetryCount < 0 || messageRetryIntervalMs < 0) {
            return false;
        }
        
        if (configCacheExpireSeconds <= 0) {
            return false;
        }
        
        // 检查连接重试配置的合理性
        if (connectionRetryCount < 0 || connectionRetryIntervalMs < 0) {
            return false;
        }
        
        if (connectionTimeoutMs <= 0 || toolDiscoveryTimeoutMs <= 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取配置摘要信息（用于日志）
     */
    public String getConfigSummary() {
        return String.format(
            "McpRedisProperties{ globalConfigKey='%s', clientMessageChannel='%s', " +
            "serverInfoChannel='%s', serverInfoEnabled=%s, lockTimeoutMs=%d, " +
            "messageDeduplicationExpireSeconds=%d, strictValidation=%s}",
             globalConfigKey, clientMessageChannel, serverInfoChannel, serverInfoEnabled,
            lockTimeoutMs, messageDeduplicationExpireSeconds, strictValidation
        );
    }
}
