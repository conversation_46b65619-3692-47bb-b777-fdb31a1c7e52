package com.wormhole.agent.tool.mcp.pool;

import dev.langchain4j.mcp.client.McpClient;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.PreDestroy;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MCP连接池管理器
 * 提供连接复用、超时控制、资源管理等功能
 * 参考WebClientSupport的连接池设计模式
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@Builder
public class McpConnectionPool {
    
    /**
     * 连接池名称
     */
    @Builder.Default
    private final String poolName = "mcp-connection-pool";
    
    /**
     * 最大连接数
     */
    @Builder.Default
    private final Integer maxConnections = 50;
    
    /**
     * 连接获取超时时间
     */
    @Builder.Default
    private final Duration acquireTimeout = Duration.ofSeconds(10);
    
    /**
     * 连接空闲超时时间
     */
    @Builder.Default
    private final Duration idleTimeout = Duration.ofMinutes(5);
    
    /**
     * 连接最大生存时间
     */
    @Builder.Default
    private final Duration maxLifetime = Duration.ofMinutes(30);
    
    /**
     * 等待队列大小
     */
    @Builder.Default
    private final Integer pendingAcquireMaxCount = 100;
    
    /**
     * 活跃连接池：客户端名称 -> 连接信息
     */
    private final ConcurrentHashMap<String, PooledConnection> activeConnections = new ConcurrentHashMap<>();
    
    /**
     * 空闲连接队列：客户端名称 -> 空闲连接队列
     */
    private final ConcurrentHashMap<String, ConcurrentLinkedQueue<PooledConnection>> idleConnections = new ConcurrentHashMap<>();
    
    /**
     * 连接统计
     */
    private final AtomicInteger totalConnections = new AtomicInteger(0);
    private final AtomicInteger activeConnectionCount = new AtomicInteger(0);
    private final AtomicLong connectionRequests = new AtomicLong(0);
    private final AtomicLong connectionHits = new AtomicLong(0);
    
    /**
     * 获取连接
     * 
     * @param clientName 客户端名称
     * @param clientFactory 客户端工厂方法
     * @return 连接的Mono
     */
    public Mono<McpClient> acquireConnection(String clientName, 
                                           java.util.function.Supplier<McpClient> clientFactory) {
        connectionRequests.incrementAndGet();
        
        return Mono.fromCallable(() -> {
            // 尝试从活跃连接获取
            PooledConnection activeConn = activeConnections.get(clientName);
            if (activeConn != null && !activeConn.isExpired()) {
                activeConn.updateLastUsed();
                connectionHits.incrementAndGet();
                log.debug("从活跃连接池获取连接: {}", clientName);
                return activeConn.getClient();
            }
            
            // 尝试从空闲连接获取
            ConcurrentLinkedQueue<PooledConnection> idleQueue = idleConnections.get(clientName);
            if (idleQueue != null) {
                PooledConnection idleConn = idleQueue.poll();
                if (idleConn != null && !idleConn.isExpired()) {
                    activeConnections.put(clientName, idleConn);
                    idleConn.updateLastUsed();
                    activeConnectionCount.incrementAndGet();
                    connectionHits.incrementAndGet();
                    log.debug("从空闲连接池获取连接: {}", clientName);
                    return idleConn.getClient();
                }
            }
            
            // 检查连接数限制
            if (totalConnections.get() >= maxConnections) {
                throw new RuntimeException("连接池已满，无法创建新连接: " + clientName);
            }
            
            // 创建新连接
            McpClient newClient = clientFactory.get();
            PooledConnection newConn = new PooledConnection(newClient, clientName, maxLifetime, idleTimeout);
            
            activeConnections.put(clientName, newConn);
            totalConnections.incrementAndGet();
            activeConnectionCount.incrementAndGet();
            
            log.info("创建新连接: {}, 总连接数: {}", clientName, totalConnections.get());
            return newClient;
            
        })
        .timeout(acquireTimeout)
        .subscribeOn(Schedulers.boundedElastic())
        .onErrorMap(throwable -> {
            log.error("获取连接失败: {}", clientName, throwable);
            return new RuntimeException("获取连接失败: " + clientName, throwable);
        });
    }
    
    /**
     * 释放连接到空闲池
     * 
     * @param clientName 客户端名称
     * @return 释放结果
     */
    public Mono<Boolean> releaseConnection(String clientName) {
        return Mono.fromCallable(() -> {
            PooledConnection conn = activeConnections.remove(clientName);
            if (conn == null) {
                log.debug("要释放的连接不存在: {}", clientName);
                return false;
            }
            
            if (conn.isExpired()) {
                // 连接已过期，直接关闭
                closeConnection(conn);
                totalConnections.decrementAndGet();
                log.debug("连接已过期，直接关闭: {}", clientName);
            } else {
                // 放入空闲池
                idleConnections.computeIfAbsent(clientName, k -> new ConcurrentLinkedQueue<>())
                    .offer(conn);
                log.debug("连接释放到空闲池: {}", clientName);
            }
            
            activeConnectionCount.decrementAndGet();
            return true;
            
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 移除客户端的所有连接
     * 
     * @param clientName 客户端名称
     * @return 移除结果
     */
    public Mono<Boolean> removeClientConnections(String clientName) {
        return Mono.fromCallable(() -> {
            // 移除活跃连接
            PooledConnection activeConn = activeConnections.remove(clientName);
            if (activeConn != null) {
                closeConnection(activeConn);
                totalConnections.decrementAndGet();
                activeConnectionCount.decrementAndGet();
            }
            
            // 移除空闲连接
            ConcurrentLinkedQueue<PooledConnection> idleQueue = idleConnections.remove(clientName);
            if (idleQueue != null) {
                int removedCount = 0;
                PooledConnection conn;
                while ((conn = idleQueue.poll()) != null) {
                    closeConnection(conn);
                    removedCount++;
                }
                totalConnections.addAndGet(-removedCount);
                log.info("移除客户端 {} 的连接，活跃: {}, 空闲: {}", 
                    clientName, activeConn != null ? 1 : 0, removedCount);
            }
            
            return true;
            
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 清理过期连接
     * 
     * @return 清理的连接数
     */
    public Mono<Integer> cleanupExpiredConnections() {
        return Mono.fromCallable(() -> {
            int cleanedCount = 0;
            
            // 清理过期的活跃连接
            activeConnections.entrySet().removeIf(entry -> {
                if (entry.getValue().isExpired()) {
                    closeConnection(entry.getValue());
                    activeConnectionCount.decrementAndGet();
                    return true;
                }
                return false;
            });
            
            // 清理过期的空闲连接
            for (ConcurrentLinkedQueue<PooledConnection> queue : idleConnections.values()) {
                queue.removeIf(conn -> {
                    if (conn.isExpired()) {
                        closeConnection(conn);
                        return true;
                    }
                    return false;
                });
            }
            
            // 更新总连接数
            int currentTotal = activeConnectionCount.get() + 
                idleConnections.values().stream().mapToInt(ConcurrentLinkedQueue::size).sum();
            totalConnections.set(currentTotal);
            
            if (cleanedCount > 0) {
                log.info("清理过期连接: {} 个，当前总连接数: {}", cleanedCount, currentTotal);
            }
            
            return cleanedCount;
            
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 获取连接池统计信息
     * 
     * @return 统计信息
     */
    public ConnectionPoolStats getStats() {
        int idleCount = idleConnections.values().stream().mapToInt(ConcurrentLinkedQueue::size).sum();
        
        return ConnectionPoolStats.builder()
            .poolName(poolName)
            .totalConnections(totalConnections.get())
            .activeConnections(activeConnectionCount.get())
            .idleConnections(idleCount)
            .maxConnections(maxConnections)
            .connectionRequests(connectionRequests.get())
            .connectionHits(connectionHits.get())
            .hitRate(connectionRequests.get() > 0 ? 
                (double) connectionHits.get() / connectionRequests.get() : 0.0)
            .build();
    }
    
    /**
     * 关闭连接
     */
    private void closeConnection(PooledConnection conn) {
        try {
            if (conn.getClient() != null) {
                conn.getClient().close();
            }
        } catch (Exception e) {
            log.warn("关闭连接异常: {}", conn.getClientName(), e);
        }
    }
    
    /**
     * 销毁连接池
     */
    @PreDestroy
    public void destroy() {
        log.info("开始销毁MCP连接池: {}", poolName);
        
        // 关闭所有活跃连接
        activeConnections.values().forEach(this::closeConnection);
        activeConnections.clear();
        
        // 关闭所有空闲连接
        idleConnections.values().forEach(queue -> {
            PooledConnection conn;
            while ((conn = queue.poll()) != null) {
                closeConnection(conn);
            }
        });
        idleConnections.clear();
        
        totalConnections.set(0);
        activeConnectionCount.set(0);
        
        log.info("MCP连接池销毁完成: {}", poolName);
    }
}
