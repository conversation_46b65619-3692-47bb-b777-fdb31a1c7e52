package com.wormhole.agent.tool.core.dispatcher.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.tool.core.dispatcher.ToolExecutionStrategy;
import com.wormhole.agent.tool.core.dispatcher.ToolStrategy;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.core.model.ToolType;
import com.wormhole.agent.tool.mcp.McpToolProvider;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * MCP工具执行策略
 * 负责执行MCP工具调用
 * 
 * <AUTHOR>
 * @version 2025-07-16
 */
@ToolStrategy(ToolType.MCP)
@Slf4j
@Component
@ConditionalOnBean(McpToolProvider.class)
@RequiredArgsConstructor
public class McpStrategy implements ToolExecutionStrategy {
    
    private final McpToolProvider mcpToolProvider;

    @Override
    public Mono<Map<String, Object>> execute(ChatToolCall toolCall, 
                                             Map<String, Object> inputMap, 
                                             ToolChainContext context) {
        String toolName = toolCall.getFunction().getName();
        String argumentsJson = toolCall.getFunction().getArguments();
        
        log.info("开始执行MCP工具：{}, 参数：{}", toolName, argumentsJson);
        
        return parseArguments(argumentsJson)
                .flatMap(arguments -> mcpToolProvider.executeMcpTool(toolName, arguments, context))
                .map(this::buildSuccessResult)
                .onErrorResume(throwable -> {
                    log.error("MCP工具执行失败：{}", toolName, throwable);
                    return Mono.just(buildErrorResult(throwable));
                });
    }
    
    /**
     * 解析工具参数
     * 
     * @param argumentsJson 参数JSON字符串
     * @return 参数Map的Mono
     */
    private Mono<Map<String, Object>> parseArguments(String argumentsJson) {
        return Mono.fromCallable(() -> {
            if (argumentsJson == null || argumentsJson.trim().isEmpty()) {
                return new HashMap<>();
            }
            
            try {
                return JacksonUtils.readValue(argumentsJson, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                log.warn("解析工具参数失败，使用空参数：{}", argumentsJson, e);
                return new HashMap<>();
            }
        });
    }
    
    /**
     * 构建成功结果
     * 
     * @param result 执行结果
     * @return 结果Map
     */
    private Map<String, Object> buildSuccessResult(String result) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", true);
        resultMap.put("result", result);
        resultMap.put("type", "mcp");
        return resultMap;
    }
    
    /**
     * 构建错误结果
     * 
     * @param throwable 异常
     * @return 错误结果Map
     */
    private Map<String, Object> buildErrorResult(Throwable throwable) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("success", false);
        resultMap.put("error", throwable.getMessage());
        resultMap.put("type", "mcp");
        return resultMap;
    }
}
