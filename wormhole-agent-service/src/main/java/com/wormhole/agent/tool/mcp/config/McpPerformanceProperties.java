package com.wormhole.agent.tool.mcp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.time.Duration;

/**
 * MCP性能配置属性
 * 用于配置MCP连接池、线程池等性能参数
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "wormhole.mcp.performance")
public class McpPerformanceProperties {
    
    /**
     * 线程池配置
     */
    @NestedConfigurationProperty
    private ThreadPoolConfig threadPool = new ThreadPoolConfig();
    
    /**
     * 连接池配置
     */
    @NestedConfigurationProperty
    private ConnectionPoolConfig connectionPool = new ConnectionPoolConfig();
    
    /**
     * 批量操作配置
     */
    @NestedConfigurationProperty
    private BatchConfig batch = new BatchConfig();
    
    /**
     * 超时配置
     */
    @NestedConfigurationProperty
    private TimeoutConfig timeout = new TimeoutConfig();
    
    /**
     * 线程池配置
     */
    @Data
    public static class ThreadPoolConfig {
        /**
         * 核心线程数
         */
        private Integer coreSize = 10;
        
        /**
         * 最大队列大小
         */
        private Integer queuedMax = 1000;
        
        /**
         * 线程名前缀
         */
        private String threadNamePrefix = "mcp-pool";
        
        /**
         * 是否启用线程池
         */
        private Boolean enabled = true;
    }
    
    /**
     * 连接池配置
     */
    @Data
    public static class ConnectionPoolConfig {
        /**
         * 最大连接数
         */
        private Integer maxConnections = 50;
        
        /**
         * 连接获取超时时间
         */
        private Duration acquireTimeout = Duration.ofSeconds(10);
        
        /**
         * 连接空闲超时时间
         */
        private Duration idleTimeout = Duration.ofMinutes(5);
        
        /**
         * 连接最大生存时间
         */
        private Duration maxLifetime = Duration.ofMinutes(30);
        
        /**
         * 等待队列大小
         */
        private Integer pendingAcquireMaxCount = 100;
        
        /**
         * 是否启用连接池
         */
        private Boolean enabled = false;
        
        /**
         * 连接池名称
         */
        private String poolName = "mcp-connection-pool";
    }
    
    /**
     * 批量操作配置
     */
    @Data
    public static class BatchConfig {
        /**
         * 批量操作最大大小
         */
        private Integer maxBatchSize = 10;
        
        /**
         * 批量操作超时时间
         */
        private Duration batchTimeout = Duration.ofSeconds(30);
        
        /**
         * 是否启用批量操作
         */
        private Boolean enabled = true;
        
        /**
         * 批量操作并发度
         */
        private Integer concurrency = 5;
    }
    
    /**
     * 超时配置
     */
    @Data
    public static class TimeoutConfig {
        /**
         * 工具执行超时时间
         */
        private Duration toolExecution = Duration.ofSeconds(30);
        
        /**
         * 工具发现超时时间
         */
        private Duration toolDiscovery = Duration.ofSeconds(15);
        
        /**
         * 客户端初始化超时时间
         */
        private Duration clientInitialization = Duration.ofSeconds(60);
        
        /**
         * 健康检查超时时间
         */
        private Duration healthCheck = Duration.ofSeconds(10);
        
        /**
         * 连接建立超时时间
         */
        private Duration connectionEstablish = Duration.ofSeconds(15);
    }
    
    /**
     * 获取线程池核心大小
     */
    public int getThreadPoolCoreSize() {
        return threadPool.getCoreSize() != null ? threadPool.getCoreSize() : 10;
    }
    
    /**
     * 获取线程池最大队列大小
     */
    public int getThreadPoolQueuedMax() {
        return threadPool.getQueuedMax() != null ? threadPool.getQueuedMax() : 1000;
    }
    
    /**
     * 获取连接池最大连接数
     */
    public int getConnectionPoolMaxConnections() {
        return connectionPool.getMaxConnections() != null ? connectionPool.getMaxConnections() : 50;
    }
    
    /**
     * 获取批量操作最大大小
     */
    public int getBatchMaxSize() {
        return batch.getMaxBatchSize() != null ? batch.getMaxBatchSize() : 10;
    }
    
    /**
     * 检查线程池是否启用
     */
    public boolean isThreadPoolEnabled() {
        return threadPool.getEnabled() != null ? threadPool.getEnabled() : true;
    }
    
    /**
     * 检查连接池是否启用
     */
    public boolean isConnectionPoolEnabled() {
        return connectionPool.getEnabled() != null ? connectionPool.getEnabled() : true;
    }
    
    /**
     * 检查批量操作是否启用
     */
    public boolean isBatchEnabled() {
        return batch.getEnabled() != null ? batch.getEnabled() : true;
    }
}
