package com.wormhole.agent.tool.mcp.service;

import com.wormhole.agent.client.chat.mcp.dto.McpServiceStatus;
import com.wormhole.agent.client.chat.mcp.dto.McpToolCallRequest;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.mcp.McpToolAdapter;
import com.wormhole.agent.tool.mcp.McpToolProvider;
import com.wormhole.agent.tool.mcp.cache.McpToolCacheManager;
import com.wormhole.agent.tool.mcp.config.McpProperties;
import com.wormhole.agent.tool.mcp.event.McpClientEvent;
import com.wormhole.agent.tool.mcp.model.McpClientInfo;
import com.wormhole.agent.tool.mcp.publisher.McpServerInfoPublisher;
import dev.langchain4j.mcp.client.McpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ForkJoinPool;
import java.util.stream.Collectors;

/**
 * MCP服务动态管理器
 * 负责MCP客户端的动态创建、销毁、状态管理
 * 与McpToolProvider协调工作，避免功能重复
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@Service
public class McpServiceManager {

    private final McpToolProvider mcpToolProvider;
    private final McpToolAdapter mcpToolAdapter;
    private final McpHealthChecker healthChecker;
    private final McpToolCacheManager cacheManager;
    private final McpServerInfoPublisher mcpServerInfoPublisher;
    private final McpCreateHandler mcpCreateHandler;
    // 事件发布器
    private final ApplicationEventPublisher eventPublisher;
    /**
     * 构造函数
     */
    public McpServiceManager(McpToolProvider mcpToolProvider,
                           McpToolAdapter mcpToolAdapter,
                           McpHealthChecker healthChecker,
                           McpToolCacheManager cacheManager,
                           McpServerInfoPublisher mcpServerInfoPublisher,
                             McpCreateHandler mcpCreateHandler,
                             ApplicationEventPublisher eventPublisher) {
        this.mcpToolProvider = mcpToolProvider;
        this.mcpToolAdapter = mcpToolAdapter;
        this.healthChecker = healthChecker;
        this.cacheManager = cacheManager;
        this.mcpServerInfoPublisher = mcpServerInfoPublisher;
        this.mcpCreateHandler = mcpCreateHandler;
        this.eventPublisher = eventPublisher;
    }

    /**
     * 异步初始化同步方法
     * 不再强依赖 McpToolProvider 完成初始化，改为异步监控和同步
     */
    @PostConstruct
    public void initAsync() {
        log.info("McpServiceManager开始异步初始化，不等待McpToolProvider完成");
        
        // 异步监控并同步McpToolProvider的状态
        scheduleProviderSync();
    }
    
    /**
     * 定期检查并同步McpToolProvider状态
     */
    private void scheduleProviderSync() {
        CompletableFuture.runAsync(() -> {
            int maxAttempts = 30; // 最多等待30次，每次1秒，总共30秒
            int attempt = 0;
            
            while (attempt < maxAttempts) {
                try {
                    // 检查 McpToolProvider 是否已部分就绪
                    if (mcpToolProvider.isInitialized() || 
                        mcpToolProvider.getInitializationStatus() == McpToolProvider.InitializationStatus.PARTIALLY_READY) {
                        
                        log.info("检测到McpToolProvider可用，开始同步客户端数据 (尝试 {}/{})", attempt + 1, maxAttempts);
                        syncFromToolProvider();
                        return; // 同步成功，退出循环
                    }
                    
                    // 如果Provider初始化失败，但仍然可以尝试同步
                    if (mcpToolProvider.getInitializationStatus() == McpToolProvider.InitializationStatus.FAILED) {
                        log.info("McpToolProvider初始化失败，尝试同步现有数据 (尝试 {}/{})", attempt + 1, maxAttempts);
                        syncFromToolProvider();
                        return;
                    }
                    
                    attempt++;
                    
                    if (attempt % 10 == 0) { // 每10次记录一次日志
                    }
                    
                    Thread.sleep(1000); // 等待1秒
                    
                } catch (Exception e) {
                    log.warn("检查McpToolProvider状态时发生异常 (尝试 {}/{}): {}", attempt + 1, maxAttempts, e.getMessage());
                    attempt++;
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        return;
                    }
                }
            }
            
            log.info("等待McpToolProvider就绪超时，McpServiceManager将以空状态运行");
            
        }, ForkJoinPool.commonPool());
    }

    /**
     * 从 McpToolProvider 同步已加载的客户端和工具到 activeClients 中
     */
    private void syncFromToolProvider() {
        long startTime = System.currentTimeMillis();
        log.info("开始从McpToolProvider同步已加载的客户端");

        try {
            // 现在不再强制要求完全初始化，支持部分初始化状态
            McpToolProvider.InitializationStatus status = mcpToolProvider.getInitializationStatus();
            if (status == McpToolProvider.InitializationStatus.NOT_STARTED) {
                log.info("McpToolProvider尚未开始初始化，稍后重试");
                return;
            }


            // 获取所有已加载的客户端和工具
            Map<String, McpClient> allClients;
            Map<String, List<OpenAiTool>> allTools;
            List<McpProperties.McpClientConfig> loadedConfigs;

            try {
                allClients = mcpToolProvider.getAllClients();
                allTools = mcpToolProvider.getAllToolCache();
                loadedConfigs = mcpToolProvider.getLoadedConfigs();


            } catch (Exception e) {
                log.error("获取McpToolProvider数据时发生异常", e);
                return;
            }

            if (allClients.isEmpty()) {
                log.info("McpToolProvider中没有已加载的客户端，同步完成");
                return;
            }

            log.info("发现{}个已加载的客户端，开始同步", allClients.size());

            int syncedCount = 0;
            int totalTools = 0;
            int failedCount = 0;
            int skippedCount = 0;

            // 为每个客户端创建 McpClientInfo 并添加到 activeClients

            for (Map.Entry<String, McpClient> entry : allClients.entrySet()) {
                String clientName = entry.getKey();
                McpClient client = entry.getValue();
                List<OpenAiTool> tools = allTools.getOrDefault(clientName, Collections.emptyList());


                // 查找对应的配置
                McpProperties.McpClientConfig config = loadedConfigs.stream()
                    .filter(c -> clientName.equals(c.getName()))
                    .findFirst()
                    .orElse(null);

                if (config == null) {
                    log.warn("未找到客户端{}的配置信息，跳过同步", clientName);
                    skippedCount++;
                    continue;
                }

                try {

                    // 使用便捷构造方法创建 McpClientInfo
                    McpClientInfo clientInfo = McpClientInfo.fromExistingClient(config, client, tools);

                    // 添加到活跃客户端列表
                    activeClients.put(clientName, clientInfo);

                    syncedCount++;
                    totalTools += tools.size();

                    log.info("同步客户端成功: {} (工具数: {}, 传输: {}, 端点: {})",
                        clientName, tools.size(), config.getTransport(),
                        clientInfo.getEndpoint() != null ? clientInfo.getEndpoint() : "N/A");

                    // 如果工具数量为0，给出提示
                    if (tools.isEmpty()) {
                        log.info("客户端{}没有可用工具，这可能是正常情况", clientName);

                    }

                } catch (Exception e) {
                    failedCount++;
                    log.error("同步客户端失败: {} - {}", clientName, e.getMessage());
                }
            }

            // 计算同步耗时
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            // 输出详细的同步结果统计
            log.info("从McpToolProvider同步完成，耗时: {}ms", duration);
            log.info("同步统计:");
            log.info("   成功同步: {}个客户端", syncedCount);
            log.info("   同步失败: {}个客户端", failedCount);
            log.info("   跳过处理: {}个客户端", skippedCount);
            log.info("   总客户端数: {}个", allClients.size());
            log.info("   同步工具总数: {}个", totalTools);

            // 根据结果给出不同级别的提示
            if (syncedCount == 0) {
                log.info("没有成功同步任何客户端，请检查配置和日志");
            } else if (failedCount > 0) {
                log.info("有{}个客户端同步失败，请检查上述错误日志", failedCount);
            } else {
                log.info("所有客户端同步成功，MCP服务管理器已就绪");
            }

        } catch (Exception e) {
            log.error("从McpToolProvider同步过程中发生严重异常", e);
            log.error("异常详情: {}", e.getMessage());


        }
    }

    /**
     * 活跃的客户端信息：客户端名称 -> 客户端信息
     */
    private final Map<String, McpClientInfo> activeClients = new ConcurrentHashMap<>();
    
    /**
     * 客户端初始化任务：客户端名称 -> 初始化任务
     */
    private final Map<String, CompletableFuture<Void>> clientInitTasks = new ConcurrentHashMap<>();
    
    /**
     * 统一的内部添加客户端方法
     * 
     * @param config 客户端配置
     * @param shouldPublishMessage 是否发布MQ消息
     * @return 服务状态的Mono
     */
    private Mono<McpServiceStatus> addClientInternal(McpProperties.McpClientConfig config, boolean shouldPublishMessage) {
        if (config == null || config.getName() == null) {
            return Mono.error(new IllegalArgumentException("客户端配置或名称不能为空"));
        }

        String clientName = config.getName();
        log.info("开始动态添加MCP客户端: {} (发布消息: {})", clientName, shouldPublishMessage);

        return Mono.fromCallable(() -> {
                    // 检查客户端是否已存在
                    if (activeClients.containsKey(clientName)) {
                        removeClientSync(clientName, shouldPublishMessage);
                    }

                    // 创建客户端信息
                    McpClientInfo clientInfo = McpClientInfo.create(config);
                    activeClients.put(clientName, clientInfo);

                    try {
                        // 直接同步初始化客户端
                        initializeClientDirect(clientName, config, clientInfo, shouldPublishMessage);
                        log.info("MCP客户端添加成功: {}", clientName);
                    } catch (Exception e) {
                        log.error("初始化MCP客户端失败: {}", clientName, e);
                        clientInfo.setError("初始化失败: " + e.getMessage());
                        // 清理失败的客户端信息
                        activeClients.remove(clientName);
                    }

                    return clientInfo.getStatus();
                })
                .subscribeOn(Schedulers.boundedElastic())
                .doOnError(error -> {
                    log.error("添加MCP客户端失败: {}", clientName, error);
                    // 清理失败的客户端信息
                    activeClients.remove(clientName);
                });

    }

    /**
     * 动态添加MCP客户端
     * 
     * @param config 客户端配置
     * @return 服务状态的Mono
     */
    public Mono<McpServiceStatus> addClient(McpProperties.McpClientConfig config) {
        return addClientInternal(config, true);
    }
    
    /**
     * 动态删除MCP客户端
     * 
     * @param clientName 客户端名称
     * @return 删除结果的Mono
     */
    public Mono<Boolean> removeClient(String clientName) {
        if (clientName == null || clientName.trim().isEmpty()) {
            return Mono.error(new IllegalArgumentException("客户端名称不能为空"));
        }
        
        log.info("开始动态删除MCP客户端: {}", clientName);
        
        return Mono.fromCallable(() -> {
            return removeClientSync(clientName, true);
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 更新MCP客户端配置
     * 
     * @param config 新的客户端配置
     * @return 更新后的服务状态
     */
    public Mono<McpServiceStatus> updateClient(McpProperties.McpClientConfig config) {
        if (config == null || config.getName() == null) {
            return Mono.error(new IllegalArgumentException("客户端配置或名称不能为空"));
        }

        String clientName = config.getName();
        log.info("开始更新MCP客户端配置: {}", clientName);

        // 先删除再添加，确保配置更新的原子性
        return Mono.fromCallable(() -> removeClientSync(clientName, true))
            .then(addClientInternal(config, true))
            .doOnSuccess(status -> {
                log.info("MCP客户端配置更新完成: {}", clientName);
                
                // 发布UPDATE消息
                McpClientInfo clientInfo = activeClients.get(clientName);
                if (clientInfo != null) {
                    publishUpdateMessage(clientName, clientInfo.getStatus(), clientInfo.getTools());
                }
            })
            .doOnError(error -> log.error("更新MCP客户端配置失败: {}", clientName, error));
    }
    
    /**
     * 获取客户端状态
     * 
     * @param clientName 客户端名称
     * @return 客户端状态
     */
    public Mono<McpServiceStatus> getClientStatus(String clientName) {
        return Mono.fromCallable(() -> {
            McpClientInfo clientInfo = activeClients.get(clientName);
            if (clientInfo == null) {
                throw new IllegalArgumentException("客户端不存在: " + clientName);
            }
            return clientInfo.getStatus();
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 获取所有客户端状态
     *
     * @return 所有客户端状态列表
     */
    public Mono<List<McpServiceStatus>> getAllClientStatus() {
        return Mono.fromCallable(() -> {
            return activeClients.values().stream()
                .map(McpClientInfo::getStatus)
                .collect(Collectors.toList());
        }).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 获取活跃客户端列表
     * 
     * @return 活跃客户端名称列表
     */
    public Mono<List<String>> getActiveClients() {
        return Mono.fromCallable(() -> {
            return activeClients.values().stream()
                .filter(McpClientInfo::isHealthy)
                .map(McpClientInfo::getClientName)
                .collect(Collectors.toList());
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 获取客户端工具列表
     *
     * @param clientName 客户端名称
     * @return 工具列表
     */
    public Mono<List<OpenAiTool>> getClientTools(String clientName) {
        return Mono.fromCallable(() -> {
            McpClientInfo clientInfo = activeClients.get(clientName);
            if (clientInfo == null) {
                throw new IllegalArgumentException("客户端不存在: " + clientName);
            }
            return clientInfo.getTools() != null ? clientInfo.getTools() : Collections.<OpenAiTool>emptyList();
        }).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 执行MCP工具调用
     *
     * @param request MCP工具调用请求，包含服务器名称、工具名称和参数
     * @return 异步返回工具执行结果
     */
    public Mono<String> toolCall(McpToolCallRequest request) {
        // 创建工具链执行上下文
        ToolChainContext context = new ToolChainContext();

        // 建立工具与MCP服务器的映射关系
        // 用于工具执行时路由到正确的MCP服务实例
        context.getMcpToolClientMap().put(request.getToolName(), request.getMcpServerName());
        // 委托给MCP工具提供者执行具体的工具调用
        // 返回异步执行结果
        return mcpToolProvider.executeMcpTool(
                request.getToolName(),
                request.getArguments(),
                context
        );
    }



    /**
     * 执行客户端健康检查
     *
     * @param clientName 客户端名称
     * @return 健康检查结果
     */
    public Mono<Boolean> checkClientHealth(String clientName) {
        return Mono.fromCallable(() -> {
            McpClientInfo clientInfo = activeClients.get(clientName);
            if (clientInfo == null) {
                throw new IllegalArgumentException("客户端不存在: " + clientName);
            }

            // 执行健康检查
            return healthChecker.checkHealth(clientInfo)
                .thenApply(result -> result.isHealthy())
                .join(); // 同步等待结果

        }).subscribeOn(Schedulers.boundedElastic());
    }

    /**
     * 执行所有客户端的健康检查
     *
     * @return 健康检查完成的Mono
     */
    public Mono<Void> checkAllClientsHealth() {
        return Mono.fromRunnable(() -> {
            if (activeClients.isEmpty()) {
                return;
            }

            log.info("开始执行所有客户端的健康检查，客户端数量: {}", activeClients.size());

            healthChecker.batchHealthCheck(activeClients.values())
                .thenRun(() -> log.info("所有客户端健康检查完成"))
                .exceptionally(throwable -> {
                    log.error("批量健康检查失败", throwable);
                    return null;
                });

        }).subscribeOn(Schedulers.boundedElastic()).then();
    }
    
    /**
     * 同步删除客户端
     * 
     * @param clientName 客户端名称
     * @param shouldPublishMessage 是否发布MQ消息
     * @return 删除是否成功
     */
    private boolean removeClientSync(String clientName, boolean shouldPublishMessage) {
        try {
            // 在删除前获取客户端信息，根据参数决定是否发布DELETE消息
            McpClientInfo clientInfo = activeClients.get(clientName);
            if (clientInfo != null && shouldPublishMessage) {
                publishDeleteMessage(clientName, clientInfo.getStatus());
            }

            // 从activeClients中移除
            clientInfo = activeClients.remove(clientName);
            if (clientInfo == null) {
                return false;
            }
            
            // 取消初始化任务
            CompletableFuture<Void> initTask = clientInitTasks.remove(clientName);
            if (initTask != null && !initTask.isDone()) {
                initTask.cancel(true);
            }
            
            // 从McpToolProvider中删除客户端
            mcpToolProvider.removeClientDynamically(clientName);

            // 清理缓存
            cacheManager.evictTools(clientName).subscribe(
                success -> {
                    if (success) {
                        log.info("客户端 {} 缓存清理成功", clientName);
                    } else {
                        log.info("客户端 {} 缓存清理失败", clientName);
                    }
                },
                error -> { /* 缓存清理异常忽略 */ }
            );


            // 清理客户端资源
            clientInfo.cleanup();
            
            log.info("MCP客户端删除成功: {}", clientName);
            return true;
            
        } catch (Exception e) {
            log.error("删除MCP客户端失败: {}", clientName, e);
            return false;
        }
    }
    
    /**
     * 异步初始化客户端
     * 
     * @param clientName 客户端名称
     * @param config 客户端配置
     * @param clientInfo 客户端信息
     * @param shouldPublishMessage 是否发布MQ消息
     */
    private void initializeClientAsync(String clientName, McpProperties.McpClientConfig config,
                                     McpClientInfo clientInfo, boolean shouldPublishMessage) {
        log.info("开始初始化MCP客户端: {}", clientName);
        initializeClientDirect(clientName, config, clientInfo, shouldPublishMessage);

    }


    private void initializeClientDirect(String clientName, McpProperties.McpClientConfig config,
                                        McpClientInfo clientInfo, boolean shouldPublishMessage) {
        try {
            // 直接创建MCP客户端
            McpClient client = mcpCreateHandler.createMcpClient(config);

            // 发现工具
            List<OpenAiTool> tools = mcpToolAdapter.discoverTools(client);

            // 设置客户端实例和工具
            clientInfo.setClientInstance(client, tools);

            // 添加到McpToolProvider
            mcpToolProvider.addClientDynamically(clientName, client, tools);

            log.info("MCP客户端初始化成功: {} (工具数: {})", clientName, tools.size());

            if (shouldPublishMessage) {
                publishAddMessage(clientName, clientInfo.getStatus(), tools);
            }

        } catch (Exception e) {
            log.error("MCP客户端初始化失败: {}", clientName, e);
            clientInfo.setError("初始化失败: " + e.getMessage());

            // 清理失败的客户端
            removeClientSync(clientName, shouldPublishMessage);
        }
    }


    
    /**
     * 判断是否为网络相关错误
     * 
     * @param errorMessage 错误信息
     * @return 是否为网络相关错误
     */
    private boolean isNetworkRelatedError(String errorMessage) {
        if (errorMessage == null) {
            return false;
        }
        
        String lowerMessage = errorMessage.toLowerCase();
        return lowerMessage.contains("connection refused") ||
               lowerMessage.contains("connection reset") ||
               lowerMessage.contains("connection timeout") ||
               lowerMessage.contains("network") ||
               lowerMessage.contains("timeout") ||
               lowerMessage.contains("unreachable") ||
               lowerMessage.contains("dns") ||
               lowerMessage.contains("500") ||
               lowerMessage.contains("502") ||
               lowerMessage.contains("503");
    }
    
    /**
     * 传统方式初始化客户端（向后兼容）
     * 
     * @param clientName 客户端名称
     * @param config 客户端配置
     * @param clientInfo 客户端信息
     * @param shouldPublishMessage 是否发布MQ消息
     */
    private void initializeClientLegacy(String clientName, McpProperties.McpClientConfig config,
                                      McpClientInfo clientInfo, boolean shouldPublishMessage) {

        
        try {
            // 创建MCP客户端
            McpClient client = mcpCreateHandler.createMcpClient(config);

            // 发现工具
            List<OpenAiTool> tools = mcpToolAdapter.discoverTools(client);

            // 设置客户端实例和工具
            clientInfo.setClientInstance(client, tools);

            // 添加到McpToolProvider
            mcpToolProvider.addClientDynamically(clientName, client, tools);

            // 预热缓存
            cacheManager.preloadCache(clientName, tools).subscribe(
                success -> {
                    if (success) {
                        log.debug("客户端 {} 缓存预热成功", clientName);
                    } else {
                        log.warn("客户端 {} 缓存预热失败", clientName);
                    }
                },
                error -> log.warn("客户端 {} 缓存预热异常", clientName, error)
            );

            log.info("MCP客户端初始化成功: {} (工具数: {})", clientName, tools.size());

            // 根据参数决定是否发布ADD消息
            if (shouldPublishMessage) {
                publishAddMessage(clientName, clientInfo.getStatus(), tools);
            }

        } catch (Exception e) {
            log.error("MCP客户端初始化失败: {}", clientName, e);
            clientInfo.setError("初始化失败: " + e.getMessage());
            throw e;
        }
    }



    /**
     * 销毁所有客户端
     */
    @PreDestroy
    public void destroy() {
        log.info("开始销毁所有动态MCP客户端");
        
        // 取消所有初始化任务
        clientInitTasks.values().forEach(task -> {
            if (!task.isDone()) {
                task.cancel(true);
            }
        });
        clientInitTasks.clear();
        
        // 清理所有客户端
        activeClients.values().forEach(McpClientInfo::cleanup);
        activeClients.clear();


        log.info("所有动态MCP客户端销毁完成");
    }


    /**
     * 同步状态报告数据类
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class SyncStatusReport {
        /**
         * 检查时间
         */
        private Instant checkTime;

        /**
         * 是否完全同步
         */
        private boolean fullySynced;

        /**
         * McpToolProvider 是否已初始化
         */
        private boolean providerInitialized;

        /**
         * Provider 中的客户端数量
         */
        private int providerClientCount;

        /**
         * Manager 中的客户端数量
         */
        private int managerClientCount;

        /**
         * 共同客户端数量
         */
        private int commonClientCount;

        /**
         * 仅在 Provider 中存在的客户端
         */
        private List<String> onlyInProvider;

        /**
         * 仅在 Manager 中存在的客户端
         */
        private List<String> onlyInManager;

        /**
         * 工具数量不匹配的客户端
         */
        private List<String> toolMismatches;

        /**
         * Provider 中的工具总数
         */
        private int totalProviderTools;

        /**
         * Manager 中的工具总数
         */
        private int totalManagerTools;

        /**
         * 错误信息（如果检查过程中发生异常）
         */
        private String errorMessage;

        /**
         * 获取同步状态摘要
         */
        public String getSummary() {
            if (errorMessage != null) {
                return "检查失败: " + errorMessage;
            }

            if (fullySynced) {
                return String.format("完全同步 - %d个客户端，%d个工具",
                    commonClientCount, totalManagerTools);
            } else {
                StringBuilder summary = new StringBuilder("同步不一致 - ");
                if (!onlyInProvider.isEmpty()) {
                    summary.append(String.format("Provider独有%d个, ", onlyInProvider.size()));
                }
                if (!onlyInManager.isEmpty()) {
                    summary.append(String.format("Manager独有%d个, ", onlyInManager.size()));
                }
                if (!toolMismatches.isEmpty()) {
                    summary.append(String.format("工具不匹配%d个", toolMismatches.size()));
                }
                return summary.toString();
            }
        }
    }

    // ==================== 消息发布相关方法 ====================

    /**
     * 发布ADD消息
     */
    private void publishAddMessage(String clientName, McpServiceStatus status, List<OpenAiTool> tools) {
        try {
            mcpServerInfoPublisher.publishAddMessage(clientName, status, tools)
                .subscribe(
                    null,
                    error -> log.warn("发布MCP服务器添加消息失败: {}", clientName, error),
                    () -> log.debug("成功发布MCP服务器添加消息: {}", clientName)
                );
        } catch (Exception e) {
            log.warn("发布MCP服务器添加消息时发生异常: {}", clientName, e);
        }
    }

    /**
     * 发布UPDATE消息
     */
    private void publishUpdateMessage(String clientName, McpServiceStatus status, List<OpenAiTool> tools) {
        try {
            mcpServerInfoPublisher.publishUpdateMessage(clientName, status, tools)
                .subscribe(
                    null,
                    error -> log.warn("发布MCP服务器更新消息失败: {}", clientName, error),
                    () -> log.debug("成功发布MCP服务器更新消息: {}", clientName)
                );
        } catch (Exception e) {
            log.warn("发布MCP服务器更新消息时发生异常: {}", clientName, e);
        }
    }

    /**
     * 发布DELETE消息
     */
    private void publishDeleteMessage(String clientName, McpServiceStatus status) {
        try {
            mcpServerInfoPublisher.publishDeleteMessage(clientName, status)
                .subscribe(
                    null,
                    error -> log.warn("发布MCP服务器删除消息失败: {}", clientName, error),
                    () -> log.debug("成功发布MCP服务器删除消息: {}", clientName)
                );
        } catch (Exception e) {
            log.warn("发布MCP服务器删除消息时发生异常: {}", clientName, e);
        }
    }

    
    // ==================== 重试相关消息发布方法 ====================
    
    /**
     * 发布客户端初始化重试消息
     */
    private void publishClientInitializationRetryMessage(String clientName, String errorMessage, 
                                                        McpProperties.McpClientConfig config, 
                                                        int currentRetry, int maxRetries) {
        try {
            mcpServerInfoPublisher.publishConnectionFailureMessage(
                clientName, 
                errorMessage, 
                config.getTransport(), 
                getEndpointFromConfig(config), 
                currentRetry, 
                maxRetries
            ).subscribe(
                null,
                error -> log.warn("发布客户端初始化重试消息失败: {}", clientName, error),
                () -> log.debug("成功发布客户端初始化重试消息: {}", clientName)
            );
        } catch (Exception e) {
            log.warn("发布客户端初始化重试消息时发生异常: {}", clientName, e);
        }
    }
    
    /**
     * 发布客户端初始化失败消息
     */
    private void publishClientInitializationFailureMessage(String clientName, String errorMessage, 
                                                          McpProperties.McpClientConfig config, 
                                                          int totalRetries) {
        try {
            mcpServerInfoPublisher.publishRetryExhaustedMessage(
                clientName, 
                String.format("初始化失败: %s (传输: %s)", errorMessage, config.getTransport()),
                totalRetries
            ).subscribe(
                null,
                error -> log.warn("发布客户端初始化失败消息失败: {}", clientName, error),
                () -> log.debug("成功发布客户端初始化失败消息: {}", clientName)
            );
        } catch (Exception e) {
            log.warn("发布客户端初始化失败消息时发生异常: {}", clientName, e);
        }
    }
    
    /**
     * 从配置获取端点信息
     */
    private String getEndpointFromConfig(McpProperties.McpClientConfig config) {
        if ("sse".equalsIgnoreCase(config.getTransport())) {
            return config.getUrl();
        } else if ("stdio".equalsIgnoreCase(config.getTransport())) {
            return config.getCommand() != null && !config.getCommand().isEmpty()
                ? String.join(" ", config.getCommand())
                : null;
        }
        return null;
    }

    // ==================== 事件监听器 ====================

    /**
     * 监听MCP客户端事件
     * 当接收到客户端连接失败事件时，自动进行客户端清理
     *
     * @param event MCP客户端事件
     */
    @EventListener
    public void handleMcpClientEvent(McpClientEvent event) {
        String clientName = event.getClientName();
        McpClientEvent.EventType eventType = event.getEventType();
        String errorMessage = event.getErrorMessage();

        log.info("收到MCP客户端事件: {} - 类型: {}, 错误: {}", clientName, eventType, errorMessage);

        try {
            switch (eventType) {
                case RETRY:
                    handleConnectionRetryEvent(clientName, event.getConfig());
                    break;
                case CONNECTION_FAILED:
                    handleConnectionFailedEvent(clientName, errorMessage);
                    break;
                case CLIENT_ERROR:
                    handleClientErrorEvent(clientName, errorMessage);
                    break;
                default:
                    log.warn("未知的MCP客户端事件类型: {} - 客户端: {}", eventType, clientName);
                    break;
            }
        } catch (Exception e) {
            log.error("处理MCP客户端事件时发生异常: {} - 事件类型: {}", clientName, eventType, e);
        }
    }

    /**
     * 处理连接失败事件
     *
     * @param clientName   客户端名称
     * @param errorMessage 错误信息
     */
    private void handleConnectionFailedEvent(String clientName, String errorMessage) {
        log.warn("处理连接失败事件: {} - {}", clientName, errorMessage);

        // 检查客户端是否存在于活跃列表中
        if (!activeClients.containsKey(clientName)) {
            log.debug("客户端 {} 不在活跃列表中，可能已被清理", clientName);
            return;
        }

        log.info("开始异步清理连接失败的客户端: {}", clientName);

        // 异步执行客户端删除，避免阻塞事件处理
        removeClient(clientName)
            .subscribeOn(Schedulers.boundedElastic())
            .doOnSubscribe(subscription -> log.debug("开始执行客户端删除操作: {}", clientName))
            .subscribe(
                success -> {
                    if (success) {
                        log.info("连接失败事件处理完成，客户端已清理: {}", clientName);
                        // 验证清理完整性
                        ClientCleanupValidation validation = validateClientCleanup(clientName);
                        log.info("清理验证结果: {} - {}", clientName, validation.getValidationMessage());
                    } else {
                        log.warn("连接失败事件处理失败，客户端清理未成功: {}", clientName);
                        // 记录当前状态用于调试
                        log.warn("调试信息 - 客户端仍在活跃列表: {}, 工具提供者中存在: {}", 
                            activeClients.containsKey(clientName),
                            mcpToolProvider.hasClient(clientName));
                    }
                },
                error -> {
                    log.error("处理连接失败事件时发生异常: {}", clientName, error);
                    // 记录异常时的状态
                    log.error("异常时调试信息 - 客户端仍在活跃列表: {}, 工具提供者中存在: {}", 
                        activeClients.containsKey(clientName),
                        mcpToolProvider.hasClient(clientName));
                }
            );
    }


    /**
     * 处理重试事件
     *
     * @param clientName   客户端名称
     */
    private void handleConnectionRetryEvent(String clientName, McpProperties.McpClientConfig config) {
        log.info("处理连接重试事件: {} ", clientName);

        // 检查客户端是否存在于活跃列表中
        if (!activeClients.containsKey(clientName)) {
            log.info("客户端 {} 不在活跃列表中，可能已被清理", clientName);
            return;
        }

        // 订阅执行异步操作
        addClient(config)
                .doOnSuccess(result -> log.info("客户端 {} 重连成功", clientName))
                .doOnError(error -> log.error("客户端 {} 重连失败: {}", clientName, error.getMessage()))
                .subscribe(); // 关键：订阅执行
    }

    /**
     * 处理客户端错误事件
     *
     * @param clientName   客户端名称
     * @param errorMessage 错误信息
     */
    private void handleClientErrorEvent(String clientName, String errorMessage) {
        log.warn("处理客户端错误事件: {} - {}", clientName, errorMessage);

        // 检查客户端是否存在
        McpClientInfo clientInfo = activeClients.get(clientName);
        if (clientInfo == null) {
            log.debug("客户端 {} 不存在，跳过错误处理", clientName);
            return;
        }

        // 记录错误信息到客户端状态
        clientInfo.setError("客户端错误: " + errorMessage);

        // 根据错误严重程度决定是否需要删除客户端
        // 这里可以扩展更复杂的错误处理策略
        if (isClientErrorRecoverable(errorMessage)) {
            log.info("客户端错误可恢复，标记错误状态: {}", clientName);
            // 发布错误状态更新消息
            publishClientErrorStatusMessage(clientName, clientInfo.getStatus(), errorMessage);
        } else {
            log.warn("客户端错误不可恢复，执行客户端清理: {}", clientName);
            handleConnectionFailedEvent(clientName, errorMessage);
        }
    }

    /**
     * 判断客户端错误是否可恢复
     *
     * @param errorMessage 错误信息
     * @return true 如果错误可恢复
     */
    private boolean isClientErrorRecoverable(String errorMessage) {
        if (errorMessage == null) {
            return false;
        }

        // 定义可恢复的错误类型
        String[] recoverableErrors = {
            "timeout",
            "temporary",
            "retry",
            "connection reset"
        };

        String lowerErrorMessage = errorMessage.toLowerCase();
        for (String recoverableError : recoverableErrors) {
            if (lowerErrorMessage.contains(recoverableError)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 发布客户端错误状态消息
     *
     * @param clientName   客户端名称
     * @param status       客户端状态
     * @param errorMessage 错误信息
     */
    private void publishClientErrorStatusMessage(String clientName, McpServiceStatus status, String errorMessage) {
        try {
            mcpServerInfoPublisher.publishErrorMessage(clientName, errorMessage, "客户端运行时错误")
                .subscribe(
                    null,
                    error -> log.warn("发布客户端错误状态消息失败: {}", clientName, error),
                    () -> log.debug("成功发布客户端错误状态消息: {}", clientName)
                );
        } catch (Exception e) {
            log.warn("发布客户端错误状态消息时发生异常: {}", clientName, e);
        }
    }

    // ==================== 客户端信息查询方法 ====================
    
    /**
     * 获取所有客户端信息
     *
     * @return 客户端信息映射
     */
    public Map<String, McpClientInfo> getAllClients() {
        return new HashMap<>(activeClients);
    }
    
    /**
     * 获取指定客户端信息
     *
     * @param clientName 客户端名称
     * @return 客户端信息，不存在时返回null
     */
    public McpClientInfo getClient(String clientName) {
        return activeClients.get(clientName);
    }

    // ==================== 清理验证方法 ====================

    /**
     * 验证客户端清理的完整性
     * 检查指定客户端是否已从所有相关组件中完全清理
     *
     * @param clientName 客户端名称
     * @return 清理验证结果
     */
    public ClientCleanupValidation validateClientCleanup(String clientName) {
        if (clientName == null || clientName.trim().isEmpty()) {
            return ClientCleanupValidation.builder()
                .clientName(clientName)
                .isFullyCleanedUp(false)
                .validationMessage("客户端名称为空")
                .build();
        }

        log.debug("验证客户端清理完整性: {}", clientName);

        boolean managerCleanedUp = !activeClients.containsKey(clientName);
        boolean providerCleanedUp = !mcpToolProvider.hasClient(clientName);
        boolean noActiveInitTask = !clientInitTasks.containsKey(clientName);

        // 检查连接池清理状态
        boolean connectionPoolCleanedUp = true;

        boolean isFullyCleanedUp = managerCleanedUp && providerCleanedUp && noActiveInitTask && connectionPoolCleanedUp;

        StringBuilder messageBuilder = new StringBuilder();
        if (!managerCleanedUp) {
            messageBuilder.append("管理器中仍存在客户端; ");
        }
        if (!providerCleanedUp) {
            messageBuilder.append("工具提供者中仍存在客户端; ");
        }
        if (!noActiveInitTask) {
            messageBuilder.append("仍存在活跃的初始化任务; ");
        }
        if (!connectionPoolCleanedUp) {
            messageBuilder.append("连接池中仍存在连接; ");
        }

        String validationMessage = isFullyCleanedUp ? 
            "客户端已完全清理" : 
            "客户端清理不完整: " + messageBuilder.toString();

        ClientCleanupValidation result = ClientCleanupValidation.builder()
            .clientName(clientName)
            .isFullyCleanedUp(isFullyCleanedUp)
            .managerCleanedUp(managerCleanedUp)
            .providerCleanedUp(providerCleanedUp)
            .noActiveInitTask(noActiveInitTask)
            .connectionPoolCleanedUp(connectionPoolCleanedUp)
            .validationMessage(validationMessage)
            .validationTime(Instant.now())
            .build();

        if (isFullyCleanedUp) {
            log.debug("客户端清理验证通过: {}", clientName);
        } else {
            log.warn("客户端清理验证失败: {} - {}", clientName, validationMessage);
        }

        return result;
    }

    /**
     * 客户端清理验证结果
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ClientCleanupValidation {
        /**
         * 客户端名称
         */
        private String clientName;

        /**
         * 是否完全清理
         */
        private boolean isFullyCleanedUp;

        /**
         * 管理器中是否已清理
         */
        private boolean managerCleanedUp;

        /**
         * 工具提供者中是否已清理
         */
        private boolean providerCleanedUp;

        /**
         * 是否没有活跃的初始化任务
         */
        private boolean noActiveInitTask;

        /**
         * 连接池是否已清理
         */
        private boolean connectionPoolCleanedUp;

        /**
         * 验证消息
         */
        private String validationMessage;

        /**
         * 验证时间
         */
        private Instant validationTime;
    }

}
