package com.wormhole.agent.tool.core.dispatcher.impl;

import com.wormhole.agent.model.openai.ChatFunctionCall;
import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.plugin.entity.PluginToolUniqueKey;
import com.wormhole.agent.plugin.service.PluginService;
import com.wormhole.agent.tool.core.dispatcher.ToolExecutionStrategy;
import com.wormhole.agent.tool.core.dispatcher.ToolStrategy;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.core.model.ToolType;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-04-07 19:23:01
 * @Description:
 */
@ToolStrategy(ToolType.PLUGIN)
@Component
@RequiredArgsConstructor
public class PluginStrategy implements ToolExecutionStrategy {

    @Resource
    private PluginService pluginService;

    @Override
    public Mono<Map<String, Object>> execute(ChatToolCall toolCall, Map<String, Object> inputMap, ToolChainContext context) {
        ChatFunctionCall function = toolCall.getFunction();

        String pluginUniqueKey = context.getOriginCodeMap().get(function.getName());
        PluginToolUniqueKey pluginToolUniqueKey = PluginToolUniqueKey.fromUniqueKey(pluginUniqueKey);

        Map<String, Object> argsMap = Optional.ofNullable(function.getArguments())
                .map(JacksonUtils::readValue)
                .orElse(new HashMap<>());
        argsMap.putAll(inputMap);

        return pluginService.execute(pluginToolUniqueKey, argsMap);
    }
}
