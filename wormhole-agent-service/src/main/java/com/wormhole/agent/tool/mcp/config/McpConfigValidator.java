package com.wormhole.agent.tool.mcp.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * MCP配置验证器
 * 提供MCP配置的完整性和有效性验证
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@Component
public class McpConfigValidator {
    
    // URL格式验证正则
    private static final Pattern URL_PATTERN = Pattern.compile(
        "^(https?|wss?)://[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]"
    );
    
    // 客户端名称验证正则
    private static final Pattern CLIENT_NAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_-]+$");
    
    /**
     * 验证MCP客户端配置
     * 
     * @param config 客户端配置
     * @return 验证结果
     */
    public ValidationResult validateClientConfig(McpProperties.McpClientConfig config) {
        ValidationResult result = new ValidationResult();
        
        if (config == null) {
            result.addError("配置对象不能为空");
            return result;
        }
        
        // 基础验证已通过自定义逻辑实现
        
        // 自定义验证逻辑
        validateClientName(config.getName(), result);
        validateTransportConfig(config, result);
        validateTimeout(config.getTimeout(), result);
        
        return result;
    }
    
    /**
     * 验证标准MCP配置
     * 
     * @param config 标准MCP配置
     * @return 验证结果
     */
    public ValidationResult validateStandardConfig(StandardMcpConfig config) {
        ValidationResult result = new ValidationResult();
        
        if (config == null) {
            result.addError("配置对象不能为空");
            return result;
        }
        
        // 基础验证已通过自定义逻辑实现
        
        // 验证服务器配置
        if (config.getMcpServers() != null) {
            for (String serverName : config.getMcpServers().keySet()) {
                validateClientName(serverName, result);
                StandardMcpConfig.StandardMcpServer server = config.getMcpServers().get(serverName);
                validateStandardServer(serverName, server, result);
            }
        }
        
        return result;
    }
    
    /**
     * 验证Nacos配置
     * 
     * @param config Nacos配置
     * @return 验证结果
     */
    public ValidationResult validateNacosConfig(McpNacosConfig config) {
        ValidationResult result = new ValidationResult();
        
        if (config == null) {
            result.addError("配置对象不能为空");
            return result;
        }
        
        // 基础验证已通过自定义逻辑实现
        
        // 验证客户端配置列表
        if (config.getClients() != null) {
            Set<String> clientNames = new HashSet<>();
            for (McpNacosConfig.McpClientConfigItem client : config.getClients()) {
                // 检查重复名称
                if (clientNames.contains(client.getName())) {
                    result.addError("客户端名称重复: " + client.getName());
                } else {
                    clientNames.add(client.getName());
                }
                
                // 验证单个客户端配置
                validateNacosClientItem(client, result);
            }
        }
        
        return result;
    }
    
    /**
     * 验证配置列表的一致性
     * 
     * @param configs 配置列表
     * @return 验证结果
     */
    public ValidationResult validateConfigList(List<McpProperties.McpClientConfig> configs) {
        ValidationResult result = new ValidationResult();
        
        if (configs == null || configs.isEmpty()) {
            result.addWarning("配置列表为空");
            return result;
        }
        
        Set<String> clientNames = new HashSet<>();
        for (McpProperties.McpClientConfig config : configs) {
            // 验证单个配置
            ValidationResult singleResult = validateClientConfig(config);
            result.merge(singleResult);
            
            // 检查重复名称
            if (clientNames.contains(config.getName())) {
                result.addError("客户端名称重复: " + config.getName());
            } else {
                clientNames.add(config.getName());
            }
        }
        
        return result;
    }
    
    /**
     * 验证客户端名称
     */
    private void validateClientName(String name, ValidationResult result) {
        if (name == null || name.trim().isEmpty()) {
            result.addError("客户端名称不能为空");
            return;
        }
        
        if (!CLIENT_NAME_PATTERN.matcher(name).matches()) {
            result.addError("客户端名称格式无效: " + name + "，只能包含字母、数字、下划线和连字符");
        }
        
        if (name.length() > 50) {
            result.addError("客户端名称过长: " + name + "，最大长度为50个字符");
        }
    }
    
    /**
     * 验证传输配置
     */
    private void validateTransportConfig(McpProperties.McpClientConfig config, ValidationResult result) {
        String transport = config.getTransport();
        
        if ("stdio".equals(transport)) {
            if (config.getCommand() == null || config.getCommand().isEmpty()) {
                result.addError("stdio传输方式必须配置command");
            } else {
                // 验证命令格式
                for (String cmd : config.getCommand()) {
                    if (cmd == null || cmd.trim().isEmpty()) {
                        result.addError("command中不能包含空值");
                        break;
                    }
                }
            }
        } else if ("sse".equals(transport)) {
            if (config.getUrl() == null || config.getUrl().trim().isEmpty()) {
                result.addError("sse传输方式必须配置url");
            } else {
                validateUrl(config.getUrl(), result);
            }
        }
    }
    
    /**
     * 验证标准服务器配置
     */
    private void validateStandardServer(String serverName, StandardMcpConfig.StandardMcpServer server, 
                                      ValidationResult result) {
        if (server == null) {
            result.addError("服务器配置不能为空: " + serverName);
            return;
        }
        
        String type = server.getType();
        if ("stdio".equals(type)) {
            if (server.getCommand() == null || server.getCommand().trim().isEmpty()) {
                result.addError("stdio类型必须配置command: " + serverName);
            }
        } else if ("sse".equals(type) || "http".equals(type)) {
            if (server.getUrl() == null || server.getUrl().trim().isEmpty()) {
                result.addError(type + "类型必须配置url: " + serverName);
            } else {
                validateUrl(server.getUrl(), result);
            }
        }
        
        if (server.getTimeout() != null && server.getTimeout() <= 0) {
            result.addError("超时时间必须大于0: " + serverName);
        }
        
        // 验证args字段
        validateServerArgs(serverName, server.getArgs(), result);
    }
    
    /**
     * 验证Nacos客户端配置项
     */
    private void validateNacosClientItem(McpNacosConfig.McpClientConfigItem client, ValidationResult result) {
        if (client == null) {
            result.addError("客户端配置项不能为空");
            return;
        }
        
        validateClientName(client.getName(), result);
        
        if ("stdio".equals(client.getTransport())) {
            if (client.getCommand() == null || client.getCommand().isEmpty()) {
                result.addError("stdio传输方式必须配置command: " + client.getName());
            }
        } else if ("sse".equals(client.getTransport())) {
            if (client.getUrl() == null || client.getUrl().trim().isEmpty()) {
                result.addError("sse传输方式必须配置url: " + client.getName());
            } else {
                validateUrl(client.getUrl(), result);
            }
        }
        
        validateTimeout(client.getTimeout(), result);
    }
    
    /**
     * 验证URL格式
     */
    private void validateUrl(String url, ValidationResult result) {
        if (!URL_PATTERN.matcher(url).matches()) {
            result.addError("URL格式无效: " + url);
            return;
        }
        
        try {
            new URL(url);
        } catch (MalformedURLException e) {
            result.addError("URL格式错误: " + url + " - " + e.getMessage());
        }
    }
    
    /**
     * 验证超时时间
     */
    private void validateTimeout(Integer timeout, ValidationResult result) {
        if (timeout != null) {
            if (timeout <= 0) {
                result.addError("超时时间必须大于0秒");
            } else if (timeout > 300) {
                result.addWarning("超时时间过长: " + timeout + "秒，建议不超过300秒");
            }
        }
    }
    
    /**
     * 验证服务器args字段
     * 检查args格式的合理性和安全性
     * 
     * @param serverName 服务器名称
     * @param args 参数列表
     * @param result 验证结果
     */
    private void validateServerArgs(String serverName, List<String> args, ValidationResult result) {
        if (args == null || args.isEmpty()) {
            return; // 空参数是合法的
        }
        
        // 检查参数数量
        if (args.size() > 50) {
            result.addWarning("服务器 " + serverName + " 的参数过多: " + args.size() + " 个，可能影响性能");
        }
        
        // 检查每个参数
        for (int i = 0; i < args.size(); i++) {
            String arg = args.get(i);
            
            if (arg == null) {
                result.addError("服务器 " + serverName + " 的第 " + (i + 1) + " 个参数为null");
                continue;
            }
            
            if (arg.trim().isEmpty()) {
                result.addError("服务器 " + serverName + " 的第 " + (i + 1) + " 个参数为空字符串");
                continue;
            }
            
            // 检查参数长度
            if (arg.length() > 1000) {
                result.addWarning("服务器 " + serverName + " 的第 " + (i + 1) + " 个参数过长: " + arg.length() + " 字符");
            }
            
            // 检查潜在的安全问题
            validateArgSecurity(serverName, i + 1, arg, result);
        }
        
        log.debug("验证服务器 {} 的args字段完成，共 {} 个参数", serverName, args.size());
    }
    
    /**
     * 验证参数的安全性
     * 检查是否包含潜在的安全风险
     * 
     * @param serverName 服务器名称
     * @param argIndex 参数索引
     * @param arg 参数值
     * @param result 验证结果
     */
    private void validateArgSecurity(String serverName, int argIndex, String arg, ValidationResult result) {
        // 检查是否包含敏感信息模式（密码、token等）
        String lowerArg = arg.toLowerCase();
        if (lowerArg.contains("password") || lowerArg.contains("token") || 
            lowerArg.contains("secret") || lowerArg.contains("key=")) {
            result.addWarning("服务器 " + serverName + " 的第 " + argIndex + " 个参数可能包含敏感信息");
        }
        
        // 检查是否包含危险的shell字符
        if (arg.contains(";") || arg.contains("&&") || arg.contains("||") || 
            arg.contains("|") || arg.contains(">") || arg.contains("<")) {
            result.addWarning("服务器 " + serverName + " 的第 " + argIndex + " 个参数包含shell特殊字符，请确保安全");
        }
        
        // 检查是否是文件路径且存在
        if (arg.startsWith("/") || arg.startsWith("./") || arg.startsWith("../")) {
            result.addWarning("服务器 " + serverName + " 的第 " + argIndex + " 个参数看起来是文件路径: " + arg);
        }
    }
    
    /**
     * 验证args字段格式建议
     * 用于提供配置修复建议
     * 
     * @param rawArgsValue 原始args值（可能是字符串格式）
     * @return 修复建议
     */
    public String suggestArgsFormat(Object rawArgsValue) {
        if (rawArgsValue == null) {
            return "args字段为null，这是正常的";
        }
        
        if (rawArgsValue instanceof List) {
            return "args字段格式正确（数组格式）";
        }
        
        if (rawArgsValue instanceof String) {
            String strValue = (String) rawArgsValue;
            if (strValue.trim().startsWith("[") && strValue.trim().endsWith("]")) {
                return "检测到字符串格式的数组: " + strValue + 
                       "\n建议修改为标准JSON数组格式，例如: [\"--ignore-robots-txt\"]";
            } else {
                return "检测到单个字符串参数: " + strValue +
                       "\n建议修改为数组格式，例如: [\"" + strValue + "\"]";
            }
        }
        
        return "args字段格式异常，建议使用数组格式，例如: [\"--ignore-robots-txt\"]";
    }
    
    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final List<String> errors = new ArrayList<>();
        private final List<String> warnings = new ArrayList<>();
        
        public void addError(String error) {
            errors.add(error);
        }
        
        public void addWarning(String warning) {
            warnings.add(warning);
        }
        
        public boolean isValid() {
            return errors.isEmpty();
        }
        
        public List<String> getErrors() {
            return new ArrayList<>(errors);
        }
        
        public List<String> getWarnings() {
            return new ArrayList<>(warnings);
        }
        
        public void merge(ValidationResult other) {
            if (other != null) {
                this.errors.addAll(other.errors);
                this.warnings.addAll(other.warnings);
            }
        }
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            if (!errors.isEmpty()) {
                sb.append("错误: ").append(String.join(", ", errors));
            }
            if (!warnings.isEmpty()) {
                if (sb.length() > 0) sb.append("; ");
                sb.append("警告: ").append(String.join(", ", warnings));
            }
            return sb.toString();
        }
    }
}
