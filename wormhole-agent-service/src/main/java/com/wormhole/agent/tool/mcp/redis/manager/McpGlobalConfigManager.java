package com.wormhole.agent.tool.mcp.redis.manager;

import com.wormhole.agent.tool.mcp.config.StandardMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.GlobalMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.McpRedisProperties;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * MCP全局配置管理器
 * 负责mcp:config:global的读写操作，提供配置的CRUD功能
 * 支持配置优先级管理（Redis > JSON文件 > 默认配置）
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@Slf4j
@Service
public class McpGlobalConfigManager {
    
    @Resource
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;
    
    @Resource
    private McpRedisProperties mcpRedisProperties;

    
    /**
     * 加载全局配置
     */
    public Mono<GlobalMcpConfig> loadGlobalConfig() {
        return getGlobalConfigFromRedis()
            .onErrorResume(error -> {
                log.error("加载全局MCP配置失败，使用默认配置", error);
                return Mono.just(createDefaultConfig());
            });
    }
    
    /**
     * 从Redis获取全局配置
     */
    public Mono<GlobalMcpConfig> getGlobalConfigFromRedis() {
        String key = mcpRedisProperties.getGlobalConfigKey();
        
        return reactiveStringRedisTemplate.opsForValue()
            .get(key)
            .flatMap(json -> {
                if (json == null || json.trim().isEmpty()) {
                    return Mono.empty();
                }
                
                try {
                    GlobalMcpConfig config = JacksonUtils.readValue(json, GlobalMcpConfig.class);
                    return Mono.just(config);
                } catch (Exception e) {
                    // 增强错误日志，提供更多调试信息
                    String errorType = e.getClass().getSimpleName();
                    String errorMsg = e.getMessage();
                    
                    log.error("解析Redis中的全局MCP配置失败 [{}]: {}", errorType, errorMsg);
                    log.error("失败的JSON内容: {}", truncateJson(json, 500)); // 截断避免日志过长
                    
                    // 特殊处理常见错误类型
                    if (e instanceof com.fasterxml.jackson.databind.exc.MismatchedInputException) {
                        logMismatchedInputError((com.fasterxml.jackson.databind.exc.MismatchedInputException) e);
                    }
                    
                    log.info("尝试错误隔离解析以挽救部分配置...");
                    // 尝试错误隔离解析
                    return parseConfigWithErrorIsolation(json)
                        .switchIfEmpty(Mono.empty());
                }
            })
            .doOnError(error -> log.error("从Redis读取全局MCP配置失败: {}", key, error));
    }
    
    /**
     * 保存全局配置到Redis
     */
    public Mono<Boolean> saveGlobalConfig(GlobalMcpConfig config) {
        if (config == null) {
            return Mono.just(false);
        }
        
        if (!config.isValid()) {
            log.error("全局MCP配置验证失败，拒绝保存: {}", config);
            return Mono.just(false);
        }
        
        // 更新最后修改时间
        config.updateLastModified();
        
        String key = mcpRedisProperties.getGlobalConfigKey();
        
        try {
            String json = JacksonUtils.writeValueAsString(config);
            
            return reactiveStringRedisTemplate.opsForValue()
                .set(key, json)
                .doOnSuccess(success -> {
                    if (success) {
                        log.info("成功保存全局MCP配置到Redis: {} 个服务器", config.getServerCount());
                    }
                })
                .doOnError(error -> log.error("保存全局MCP配置到Redis时发生异常: {}", key, error))
                .onErrorReturn(false);
                
        } catch (Exception e) {
            log.error("序列化全局MCP配置失败", e);
            return Mono.just(false);
        }
    }
    
    /**
     * 更新单个服务器配置
     */
    public Mono<Boolean> updateServerConfig(String serverName, StandardMcpConfig.StandardMcpServer serverConfig) {
        if (serverName == null || serverName.trim().isEmpty()) {
            return Mono.just(false);
        }
        
        if (serverConfig == null || !serverConfig.isValid()) {
            log.error("服务器配置无效: {}", serverConfig);
            return Mono.just(false);
        }
        
        return getGlobalConfigFromRedis()
            .switchIfEmpty(Mono.just(createDefaultConfig()))
            .flatMap(globalConfig -> {
                // 更新服务器配置
                Map<String, StandardMcpConfig.StandardMcpServer> servers = globalConfig.getMcpServers();
                if (servers == null) {
                    servers = new java.util.HashMap<>();
                    globalConfig.setMcpServers(servers);
                }
                
                servers.put(serverName, serverConfig);
                
                // 保存更新后的配置
                return saveGlobalConfig(globalConfig);
            })
            .doOnNext(success -> {
                if (success) {
                    log.info("成功更新服务器配置: {}", serverName);
                }
            });
    }
    
    /**
     * 删除服务器配置
     */
    public Mono<Boolean> removeServerConfig(String serverName) {
        if (serverName == null || serverName.trim().isEmpty()) {
            return Mono.just(false);
        }
        
        return getGlobalConfigFromRedis()
            .flatMap(globalConfig -> {
                Map<String, StandardMcpConfig.StandardMcpServer> servers = globalConfig.getMcpServers();
                if (servers == null || !servers.containsKey(serverName)) {
                    return Mono.just(false);
                }
                
                servers.remove(serverName);
                
                // 保存更新后的配置
                return saveGlobalConfig(globalConfig);
            })
            .switchIfEmpty(Mono.just(false))
            .doOnNext(success -> {
                if (success) {
                    log.info("成功删除服务器配置: {}", serverName);
                }
            });
    }
    
    /**
     * 检查全局配置是否存在
     */
    public Mono<Boolean> hasGlobalConfig() {
        String key = mcpRedisProperties.getGlobalConfigKey();
        
        return reactiveStringRedisTemplate.hasKey(key)
            .onErrorReturn(false);
    }
    
    /**
     * 删除全局配置
     */
    public Mono<Boolean> deleteGlobalConfig() {
        String key = mcpRedisProperties.getGlobalConfigKey();
        
        return reactiveStringRedisTemplate.delete(key)
            .map(count -> count > 0)
            .doOnNext(success -> {
                if (success) {
                    log.info("成功删除全局MCP配置");
                }
            })
            .onErrorReturn(false);
    }
    
    /**
     * 验证配置有效性
     */
    public boolean validateConfig(GlobalMcpConfig config) {
        if (config == null) {
            return false;
        }
        
        if (!config.isValid()) {
            return false;
        }
        
        return true;
    }

    
    /**
     * 创建默认配置
     */
    private GlobalMcpConfig createDefaultConfig() {
        return GlobalMcpConfig.builder()
            .lastModified(Instant.now().toEpochMilli())
            .mcpServers(Collections.emptyMap())
            .build();
    }
    
    /**
     * 错误隔离解析配置
     * 当整体配置解析失败时，逐个解析服务器配置，跳过错误的配置项
     * 
     * @param json 原始JSON字符串
     * @return 部分解析成功的配置，如果完全失败则返回空
     */
    private Mono<GlobalMcpConfig> parseConfigWithErrorIsolation(String json) {
        try {
            log.info("开始错误隔离解析MCP配置");
            
            // 使用Jackson解析为JsonNode以便逐个处理
            com.fasterxml.jackson.databind.JsonNode rootNode = 
                com.wormhole.common.util.JacksonUtils.mapper().readTree(json);
            
            if (!rootNode.has("mcpServers")) {
                return Mono.just(createDefaultConfig());
            }
            
            com.fasterxml.jackson.databind.JsonNode serversNode = rootNode.get("mcpServers");
            Map<String, StandardMcpConfig.StandardMcpServer> validServers = new java.util.HashMap<>();
            List<String> errorServers = new ArrayList<>();
            int totalServers = 0;
            
            // 逐个解析每个服务器配置
            serversNode.fieldNames().forEachRemaining(serverName -> {
                try {
                    com.fasterxml.jackson.databind.JsonNode serverNode = serversNode.get(serverName);
                    StandardMcpConfig.StandardMcpServer server = 
                        JacksonUtils.mapper().treeToValue(serverNode, StandardMcpConfig.StandardMcpServer.class);
                    
                    // 验证服务器配置的基本有效性
                    if (server != null && server.isValid()) {
                        validServers.put(serverName, server);
                    } else {
                        errorServers.add(serverName + "(配置无效)");
                    }
                } catch (Exception e) {
                    errorServers.add(serverName + "(" + e.getClass().getSimpleName() + ")");
                }
            });
            
            // 统计总数
            serversNode.fieldNames().forEachRemaining(name -> {
                // 这里用来计数，实际上在上面的循环中已经处理过了
            });
            serversNode.size(); // 获取总服务器数量
            
            // 构建部分成功的配置
            GlobalMcpConfig partialConfig = GlobalMcpConfig.builder()
                .lastModified(rootNode.has("lastModified") ? 
                    rootNode.get("lastModified").asLong() : Instant.now().toEpochMilli())
                .mcpServers(validServers)
                .build();
            
            if (!validServers.isEmpty()) {
                log.info("错误隔离解析完成，成功: {}, 失败: {}, 失败列表: {}", 
                    validServers.size(), errorServers.size(), errorServers);
                return Mono.just(partialConfig);
            } else {
                return Mono.just(createDefaultConfig());
            }
            
        } catch (Exception e) {
            log.error("错误隔离解析也失败了，使用默认配置", e);
            return Mono.just(createDefaultConfig());
        }
    }
    
    /**
     * 记录MismatchedInputException的详细信息
     * 特别针对类型不匹配的错误提供更好的调试信息
     */
    private void logMismatchedInputError(com.fasterxml.jackson.databind.exc.MismatchedInputException e) {
        String path = e.getPath() != null && !e.getPath().isEmpty() ? 
            e.getPath().stream()
                .map(ref -> ref.getFieldName() != null ? ref.getFieldName() : "[" + ref.getIndex() + "]")
                .reduce((a, b) -> a + "." + b)
                .orElse("unknown") : "root";
        
        log.error("类型不匹配错误详情:");
        log.error("  - 错误路径: {}", path);

        // 特殊提示args字段错误
        if (path.contains("args")) {
            log.error("  - 问题提示: args字段应该是数组格式 [\"arg1\", \"arg2\"]，而不是字符串格式");
            log.error("  - 修复建议: 将 \"args\": \"['--ignore-robots-txt']\" 改为 \"args\": [\"--ignore-robots-txt\"]");
        }
    }
    
    /**
     * 截断JSON字符串以避免日志过长
     */
    private String truncateJson(String json, int maxLength) {
        if (json == null) return "null";
        if (json.length() <= maxLength) return json;
        return json.substring(0, maxLength) + "... (截断，总长度: " + json.length() + ")";
    }

}
