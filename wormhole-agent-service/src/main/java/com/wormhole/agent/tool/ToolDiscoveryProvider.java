package com.wormhole.agent.tool;

import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.nacos.listener.WorkflowListener;
import com.wormhole.agent.plugin.entity.PluginToolDetailInfo;
import com.wormhole.agent.plugin.entity.PluginToolUniqueKey;
import com.wormhole.agent.plugin.service.PluginService;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.core.model.ToolType;
import com.wormhole.agent.tool.mcp.McpToolProvider;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

/**
 * 工具发现服务提供者，负责动态加载和管理可用的AI工具
 * <p>
 * 核心功能：
 * - 根据输入参数发现工作流和插件工具
 * - 处理工具描述的模板变量替换
 * - 维护工具类型与原始代码的映射关系
 *
 * @see OpenAiTool
 * @see ToolChainContext
 */
@Service
@RequiredArgsConstructor
public class ToolDiscoveryProvider {
    // 工作流配置监听器
    private final WorkflowListener workflowListener;

    // 策略模式映射表：输入参数类型 -> 处理函数（同步版本）
    private final Map<Class<?>, BiFunction<Object, ToolChainContext, List<OpenAiTool>>> strategyMap = new HashMap<>();
    // 异步策略模式映射表：输入参数类型 -> 异步处理函数
    private final Map<Class<?>, BiFunction<Object, ToolChainContext, Mono<List<OpenAiTool>>>> asyncStrategyMap = new HashMap<>();
    
    private final PluginService pluginService;
    private final McpToolProvider mcpToolProvider;

    /**
     * 初始化策略映射表
     */
    @PostConstruct
    public void init() {
        // 同步策略映射表
        strategyMap.put(LlmInputs.WorkflowFunctionCallParam.class, this::processWorkflow);
        strategyMap.put(LlmInputs.PluginFunctionCallParam.class, this::processPlugin);
        strategyMap.put(LlmInputs.McpFunctionCallParam.class, this::processMcpSync);
        
        // 异步策略映射表
        asyncStrategyMap.put(LlmInputs.WorkflowFunctionCallParam.class, this::processWorkflowAsync);
        asyncStrategyMap.put(LlmInputs.PluginFunctionCallParam.class, this::processPluginAsync);
        asyncStrategyMap.put(LlmInputs.McpFunctionCallParam.class, this::processMcpAsync);
    }

    /**
     * 获取可用工具列表
     *
     * @param params  功能调用参数
     * @param vars    模板变量替换表
     * @param context 工具链上下文
     * @return 经过处理的OpenAI工具列表
     */
    public List<OpenAiTool> getTools(LlmInputs.FunctionCallParams params,
                                     Map<String, Object> vars,
                                     ToolChainContext context) {
        List<OpenAiTool> result = new ArrayList<>();

        // 处理工作流类型工具
        if (CollectionUtils.isNotEmpty(params.getWorkflowList())) {
            result.addAll(processIfNotEmpty(
                    params.getWorkflowList(),
                    LlmInputs.WorkflowFunctionCallParam.class,
                    vars,
                    ToolType.WORKFLOW,
                    context
            ));
        }

        // 处理插件类型工具
        if (CollectionUtils.isNotEmpty(params.getPluginList())) {
            result.addAll(processIfNotEmpty(
                    params.getPluginList(),
                    LlmInputs.PluginFunctionCallParam.class,
                    vars,
                    ToolType.PLUGIN,
                    context
            ));
        }

        // 处理MCP类型工具
        if (CollectionUtils.isNotEmpty(params.getMcpServerList())) {
            result.addAll(processIfNotEmpty(
                    params.getMcpServerList(),
                    LlmInputs.McpFunctionCallParam.class,
                    vars,
                    ToolType.MCP,
                    context
            ));
        }

        return result;
    }

    /**
     * 获取可用工具列表（异步版本）
     *
     * @param params  功能调用参数
     * @param vars    模板变量替换表
     * @param context 工具链上下文
     * @return 经过处理的OpenAI工具列表的Mono
     */
    public Mono<List<OpenAiTool>> getToolsAsync(LlmInputs.FunctionCallParams params,
                                                 Map<String, Object> vars,
                                                 ToolChainContext context) {
        List<Mono<List<OpenAiTool>>> monos = new ArrayList<>();

        // 处理工作流类型工具
        if (CollectionUtils.isNotEmpty(params.getWorkflowList())) {
            monos.add(processIfNotEmptyAsync(
                    params.getWorkflowList(),
                    LlmInputs.WorkflowFunctionCallParam.class,
                    vars,
                    ToolType.WORKFLOW,
                    context
            ));
        }

        // 处理插件类型工具
        if (CollectionUtils.isNotEmpty(params.getPluginList())) {
            monos.add(processIfNotEmptyAsync(
                    params.getPluginList(),
                    LlmInputs.PluginFunctionCallParam.class,
                    vars,
                    ToolType.PLUGIN,
                    context
            ));
        }

        // 处理MCP类型工具
        if (CollectionUtils.isNotEmpty(params.getMcpServerList())) {
            monos.add(processIfNotEmptyAsync(
                    params.getMcpServerList(),
                    LlmInputs.McpFunctionCallParam.class,
                    vars,
                    ToolType.MCP,
                    context
            ));
        }

        // 如果没有任何工具需要处理，直接返回空列表
        if (monos.isEmpty()) {
            return Mono.just(Collections.emptyList());
        }

        // 并行执行所有操作，然后合并结果
        return Flux.merge(monos)
                .collectList()
                .map(lists -> lists.stream()
                        .flatMap(List::stream)
                        .collect(Collectors.toList()))
                .onErrorReturn(Collections.emptyList());
    }

    /**
     * 通用工具处理流程
     *
     * @param items    待处理的原始工具列表
     * @param type     输入参数类型
     * @param vars     模板变量替换表
     * @param toolType 工具类型枚举
     * @param context  工具链上下文
     * @return 处理后的工具列表
     */
    private <T> List<OpenAiTool> processIfNotEmpty(List<T> items, Class<T> type,
                                                   Map<String, Object> vars,
                                                   ToolType toolType,
                                                   ToolChainContext context) {
        if (CollectionUtils.isEmpty(items)) {
            return Collections.emptyList();
        }

        // 通过策略模式选择处理器
        List<OpenAiTool> tools = strategyMap.get(type).apply(items, context);
        List<OpenAiTool> processedTools = new ArrayList<>();

        // 逐个处理工具定义
        for (OpenAiTool tool : tools) {
            // 应用模板变量到工具描述
            OpenAiTool processedTool = processTemplate(tool, vars);
            // 记录工具类型映射
            context.getToolTypeMap().put(processedTool.getFunction().getName(), toolType);
            processedTools.add(processedTool);
        }

        return processedTools;
    }

    /**
     * 通用工具处理流程（异步版本）
     *
     * @param items    待处理的原始工具列表
     * @param type     输入参数类型
     * @param vars     模板变量替换表
     * @param toolType 工具类型枚举
     * @param context  工具链上下文
     * @return 处理后的工具列表的Mono
     */
    private <T> Mono<List<OpenAiTool>> processIfNotEmptyAsync(List<T> items, Class<T> type,
                                                              Map<String, Object> vars,
                                                              ToolType toolType,
                                                              ToolChainContext context) {
        if (CollectionUtils.isEmpty(items)) {
            return Mono.just(Collections.emptyList());
        }

        // 通过异步策略模式选择处理器
        return asyncStrategyMap.get(type).apply(items, context)
                .map(tools -> {
                    List<OpenAiTool> processedTools = new ArrayList<>();
                    // 逐个处理工具定义
                    for (OpenAiTool tool : tools) {
                        // 应用模板变量到工具描述
                        OpenAiTool processedTool = processTemplate(tool, vars);
                        // 记录工具类型映射
                        context.getToolTypeMap().put(processedTool.getFunction().getName(), toolType);
                        processedTools.add(processedTool);
                    }
                    return processedTools;
                })
                .onErrorReturn(Collections.emptyList());
    }

    /**
     * 处理工作流类型工具
     *
     * @param params  工作流参数列表
     * @param context 工具链上下文
     * @return 原始工作流工具定义列表
     */
    private List<OpenAiTool> processWorkflow(Object params, ToolChainContext context) {
        List<LlmInputs.WorkflowFunctionCallParam> workflowParams = (List<LlmInputs.WorkflowFunctionCallParam>) params;
        List<String> workflowCodes = new ArrayList<>();

        // 提取工作流代码
        for (LlmInputs.WorkflowFunctionCallParam param : workflowParams) {
            workflowCodes.add(param.getWorkflowCode());
        }

        // 从监听器获取工作流定义
        List<Workflow> workflows = workflowListener.getWorkflowByCodeList(workflowCodes);
        List<OpenAiTool> tools = new ArrayList<>();

        // 转换工作流定义为工具格式
        for (Workflow workflow : workflows) {
            WorkflowDefinition definition = workflow.getWorkflowDefinition();
            // 记录原始代码映射
            context.getOriginCodeMap().put(
                    definition.getTool().getFunction().getName(),
                    definition.getWorkflowCode()
            );
            tools.add(definition.getTool());
        }

        return tools;
    }

    /**
     * 处理工作流类型工具（异步版本）
     *
     * @param params  工作流参数列表
     * @param context 工具链上下文
     * @return 原始工作流工具定义列表的Mono
     */
    private Mono<List<OpenAiTool>> processWorkflowAsync(Object params, ToolChainContext context) {
        return Mono.fromCallable(() -> processWorkflow(params, context))
                .onErrorReturn(Collections.emptyList());
    }

    /**
     * 处理插件类型工具
     *
     * @param codes   插件代码列表
     * @param context 工具链上下文
     * @return 空工具列表
     */
    private List<OpenAiTool> processPlugin(Object codes, ToolChainContext context) {
        List<LlmInputs.PluginFunctionCallParam> pluginFunctionCallParams = (List<LlmInputs.PluginFunctionCallParam>) codes;

        List<PluginToolUniqueKey> pluginToolUniqueKeys = pluginFunctionCallParams.stream()
                .map(e -> new PluginToolUniqueKey(e.getPluginCode(), e.getPluginToolCode()))
                .toList();
        List<PluginToolDetailInfo> pluginTools = pluginService.getLatestVersionTool(pluginToolUniqueKeys);

        List<OpenAiTool> tools = new ArrayList<>();
        for (PluginToolDetailInfo pluginTool : pluginTools) {
            Optional<OpenAiTool> openAiTool = pluginService.convertToOpenAiTool(pluginTool);
            if (openAiTool.isEmpty()) {
                continue;
            }
            String uniqueKey = PluginToolUniqueKey.fromToolInfo(pluginTool).toUniqueKey();
            context.getOriginCodeMap().put(
                    pluginTool.getToolName(),
                    uniqueKey
            );
            tools.add(openAiTool.get());

        }

        return tools;
    }

    /**
     * 处理插件类型工具（异步版本）
     *
     * @param codes   插件代码列表
     * @param context 工具链上下文
     * @return 插件工具列表的Mono
     */
    private Mono<List<OpenAiTool>> processPluginAsync(Object codes, ToolChainContext context) {
        return Mono.fromCallable(() -> processPlugin(codes, context))
                .onErrorReturn(Collections.emptyList());
    }

    /**
     * 处理MCP类型工具（同步版本，为避免响应式线程阻塞，返回空列表）
     *
     * @param params  MCP参数列表
     * @param context 工具链上下文
     * @return 空的工具列表（需要使用异步版本获取MCP工具）
     */
    private List<OpenAiTool> processMcpSync(Object params, ToolChainContext context) {
        // 在同步版本中为避免响应式线程阻塞，直接返回空列表
        // 如需获取MCP工具，请使用 getToolsAsync() 方法
        System.out.println("警告：同步版本跳过MCP工具加载，如需MCP工具请使用 getToolsAsync() 方法");
        return Collections.emptyList();
    }

    /**
     * 处理MCP类型工具（异步版本，无需阻塞）
     *
     * @param params  MCP参数列表
     * @param context 工具链上下文
     * @return MCP工具定义列表的Mono
     */
    private Mono<List<OpenAiTool>> processMcpAsync(Object params, ToolChainContext context) {
        List<LlmInputs.McpFunctionCallParam> mcpParams = (List<LlmInputs.McpFunctionCallParam>) params;

        // 直接返回 Mono，无需使用 block()
        return mcpToolProvider.discoverMcpTools(mcpParams, context)
                .onErrorReturn(Collections.emptyList());
    }

    /**
     * 处理工具描述模板
     *
     * @param tool 原始工具定义
     * @param vars 模板变量表
     * @return 经过模板处理后的工具定义
     */
    private OpenAiTool processTemplate(OpenAiTool tool, Map<String, Object> vars) {
        // 使用FreeMarker处理工具描述中的变量
        tool.getFunction().setDescription(
                FreeMarkerTemplateCacheUtils.processTemplateIntoString(
                        tool.getFunction().getDescription(), vars));
        return tool;
    }
}