package com.wormhole.agent.tool.mcp.redis.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.wormhole.agent.tool.mcp.config.StandardMcpConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;

/**
 * MCP客户端消息数据结构
 * 用于Redis pub/sub频道 mcp:client:msg 的消息格式
 * 支持ADD/UPDATE/DELETE/RELOAD操作
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class McpClientMessage {
    
    /**
     * 消息唯一标识符，用于去重
     */
    @Builder.Default
    private String messageId = UUID.randomUUID().toString();
    
    /**
     * 消息时间戳
     */
    @Builder.Default
    private long timestamp = Instant.now().toEpochMilli();
    
    /**
     * MCP服务器配置映射
     * 键为服务器名称，值为服务器配置
     * 与主人提供的消息格式兼容：{mcpServers: {...}, action: "ADD"}
     */
    private Map<String, StandardMcpConfig.StandardMcpServer> mcpServers;
    
    /**
     * 操作类型：ADD, UPDATE, DELETE, RELOAD
     */
    private String action;

    /**
     * 操作类型枚举
     */
    public enum ActionType {
        ADD, UPDATE, DELETE, RELOAD
    }
    
    /**
     * 验证消息的有效性
     */
    @JsonIgnore
    public boolean isValid() {
        // 检查基本字段
        if (messageId == null || messageId.trim().isEmpty()) {
            return false;
        }
        
        if (action == null || action.trim().isEmpty()) {
            return false;
        }
        
        // 验证操作类型
        try {
            ActionType.valueOf(action.toUpperCase());
        } catch (IllegalArgumentException e) {
            return false;
        }
        
        // RELOAD操作不需要mcpServers
        if ("RELOAD".equalsIgnoreCase(action)) {
            return true;
        }
        
        // 其他操作需要有效的mcpServers
        if (mcpServers == null || mcpServers.isEmpty()) {
            return false;
        }
        
        // 验证服务器配置的有效性
        return mcpServers.values().stream().allMatch(StandardMcpConfig.StandardMcpServer::isValid);
    }
    
    /**
     * 获取操作类型枚举
     */
    @JsonIgnore
    public ActionType getActionType() {
        if (action == null) {
            return null;
        }
        
        try {
            return ActionType.valueOf(action.toUpperCase());
        } catch (IllegalArgumentException e) {
            return null;
        }
    }
    
    /**
     * 获取所有服务器名称
     */
    @JsonIgnore
    public java.util.Set<String> getServerNames() {
        return mcpServers != null ? mcpServers.keySet() : Collections.emptySet();
    }
    
    /**
     * 获取启用的服务器配置
     */
    @JsonIgnore
    public Map<String, StandardMcpConfig.StandardMcpServer> getEnabledServers() {
        if (mcpServers == null) {
            return Collections.emptyMap();
        }
        
        return mcpServers.entrySet().stream()
            .filter(entry -> entry.getValue().getEnabled() != null && entry.getValue().getEnabled())
            .collect(java.util.stream.Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue
            ));
    }
    
    /**
     * 转换为GlobalMcpConfig格式（用于RELOAD操作）
     */
    @JsonIgnore
    public GlobalMcpConfig toGlobalMcpConfig() {
        return GlobalMcpConfig.builder()
            .lastModified(this.timestamp)
            .mcpServers(this.mcpServers)
            .build();
    }
    
    /**
     * 从GlobalMcpConfig创建消息（用于配置同步）
     */
    public static McpClientMessage fromGlobalMcpConfig(GlobalMcpConfig globalConfig, ActionType actionType) {
        if (globalConfig == null || actionType == null) {
            return null;
        }
        
        return McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .timestamp(Instant.now().toEpochMilli())
            .mcpServers(globalConfig.getMcpServers())
            .action(actionType.name())
            .build();
    }
    
    /**
     * 检查是否为空消息
     */
    @JsonIgnore
    public boolean isEmpty() {
        return mcpServers == null || mcpServers.isEmpty();
    }
    
    /**
     * 获取服务器数量
     */
    @JsonIgnore
    public int getServerCount() {
        return mcpServers != null ? mcpServers.size() : 0;
    }
    
    /**
     * 生成分布式锁的键名
     */
    @JsonIgnore
    public String generateLockKey() {
        if (action == null) {
            return null;
        }
        
        // 对于单个服务器操作，使用服务器名称
        if (getServerCount() == 1) {
            String serverName = getServerNames().iterator().next();
            return String.format("mcp:%s:%s", action.toLowerCase(), serverName);
        }
        
        // 对于批量操作或RELOAD，使用操作类型
        return String.format("mcp:%s:batch", action.toLowerCase());
    }
}
