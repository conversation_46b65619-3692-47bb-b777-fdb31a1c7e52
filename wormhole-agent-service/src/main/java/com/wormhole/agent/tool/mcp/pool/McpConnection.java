package com.wormhole.agent.tool.mcp.pool;

import dev.langchain4j.mcp.client.McpClient;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.Instant;

/**
 * MCP连接接口
 * 定义MCP连接的基本操作和生命周期管理
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
public interface McpConnection extends AutoCloseable {
    
    /**
     * 获取MCP客户端实例
     * 
     * @return MCP客户端
     */
    McpClient getClient();
    
    /**
     * 获取客户端名称
     * 
     * @return 客户端名称
     */
    String getClientName();
    
    /**
     * 获取连接创建时间
     * 
     * @return 创建时间
     */
    Instant getCreatedAt();
    
    /**
     * 获取最后使用时间
     * 
     * @return 最后使用时间
     */
    Instant getLastUsedAt();
    
    /**
     * 更新最后使用时间
     */
    void updateLastUsed();
    
    /**
     * 检查连接是否已过期
     * 
     * @return true如果连接已过期
     */
    boolean isExpired();
    
    /**
     * 检查连接是否健康
     * 
     * @return 健康检查结果的Mono
     */
    Mono<Boolean> isHealthy();
    
    /**
     * 获取连接年龄
     * 
     * @return 连接年龄
     */
    Duration getAge();
    
    /**
     * 获取空闲时间
     * 
     * @return 空闲时间
     */
    Duration getIdleTime();
    
    /**
     * 检查连接是否已关闭
     * 
     * @return true如果连接已关闭
     */
    boolean isClosed();
    
    /**
     * 关闭连接
     */
    @Override
    void close();
    
    /**
     * 异步关闭连接
     * 
     * @return 关闭操作的Mono
     */
    Mono<Void> closeAsync();
    
    /**
     * 重置连接状态
     * 用于连接复用时清理状态
     */
    void reset();
    
    /**
     * 获取连接的详细信息
     * 
     * @return 连接信息
     */
    ConnectionInfo getConnectionInfo();
    
    /**
     * 连接信息数据类
     */
    interface ConnectionInfo {
        String getClientName();
        Instant getCreatedAt();
        Instant getLastUsedAt();
        Duration getAge();
        Duration getIdleTime();
        boolean isExpired();
        boolean isClosed();
        String getStatus();
    }
}
