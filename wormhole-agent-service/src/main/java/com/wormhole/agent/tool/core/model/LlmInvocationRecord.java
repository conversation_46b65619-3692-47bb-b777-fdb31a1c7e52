package com.wormhole.agent.tool.core.model;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.model.openai.ChatUsage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class LlmInvocationRecord {
    private int sort;

    private String modelName;
    private String modelProvider;
    /**
     * 请求时间戳
     */
    private long timestamp;

    /**
     * 请求参数（JSON格式）
     */
    private Object input;

    /**
     * 响应内容（JSON格式）
     */
    private Object output;

    /**
     * Token使用情况
     */
    private ChatUsage tokenUsage;
    /**
     * LLM调用耗时（毫秒）
     */
    private long llmElapsedMs;
    private long llmStartTime;
    private long llmEndTime;
    /**
     * 总耗时（包含所有工具调用和LLM调用的累计时间）
     */
    private long totalElapsedMs;
    /**
     * 总token消耗量（input + output）
     */
    private long totalTokensUsed;
    /**
     * 调用深度
     */
    private int recursionDepth;

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 错误信息（如有）
     */
    private String errorMessage;

    /**
     * 工具调用记录列表
     */
    private List<ToolInvocationDetail> toolInvocations;
    /**
     * 工具调用详情
     */
    @Data
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JSONType(naming = PropertyNamingStrategy.SnakeCase)
    public static class ToolInvocationDetail {
        /**
         * 工具名称（function name）
         */
        private String toolName;

        /**
         * 工具调用参数（原始toolCall对象）
         */
        private ChatToolCall toolCall;

        /**
         * 输入
         */
        private Object input;

        /**
         * 输出
         */
        private Object output;

        /**
         * 调用耗时（毫秒）
         */
        private long elapsedMs;
        private long startTime;
        private long endTime;

        /**
         * 调用时间戳
         */
        private long timestamp;

        /**
         * 是否成功
         */
        private boolean success;

        /**
         * 错误信息（如有）
         */
        private String errorMessage;
    }
}
