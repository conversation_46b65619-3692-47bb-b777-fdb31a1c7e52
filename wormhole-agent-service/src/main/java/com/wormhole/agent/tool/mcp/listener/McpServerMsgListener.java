package com.wormhole.agent.tool.mcp.listener;

import com.wormhole.agent.tool.mcp.config.McpConfigAdapter;
import com.wormhole.agent.tool.mcp.config.McpProperties;
import com.wormhole.agent.tool.mcp.redis.config.McpClientMessage;
import com.wormhole.agent.tool.mcp.redis.config.McpRedisProperties;
import com.wormhole.agent.tool.mcp.redis.manager.McpGlobalConfigManager;
import com.wormhole.agent.tool.mcp.service.McpServiceManager;
import com.wormhole.agent.vo.McpActionVO;
import com.wormhole.agent.workflow.util.LockUtil;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.redisson.api.RedissonReactiveClient;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.List;

/**
 * MCP服务器消息监听器
 * 处理MCP服务器的增删改查和重载操作
 *
 * @author: xiekangchen
 * @date: 2025/7/23
 * @Description: MCP服务器消息处理监听器，支持分布式锁和消息去重
 */
@Slf4j
@Component
@RocketMQMessageListener(
        topic = "mcp-server-msg-topic",
        consumerGroup = "agent-mcp-server-msg-consumer-group",
        enableMsgTrace = true,
        messageModel = MessageModel.BROADCASTING
)
public class McpServerMsgListener extends AbstractReactiveMessageListener<McpActionVO> {

    private static final int MAX_CONCURRENT_OPERATIONS = 3;
    private static final Duration OPERATION_TIMEOUT = Duration.ofSeconds(60);

    @Resource
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;
    @Resource
    private McpRedisProperties mcpRedisProperties;
    @Resource
    private McpGlobalConfigManager mcpGlobalConfigManager;
    @Resource
    private McpConfigAdapter mcpConfigAdapter;
    @Resource
    private McpServiceManager mcpServiceManager;
    @Resource
    private RedissonReactiveClient redissonReactiveClient;

    @Override
    protected Mono<Void> processMessage(McpActionVO mcpActionVO) {
        // 参数验证
        if (mcpActionVO == null) {
            log.error("接收到空的MCP消息");
            return Mono.error(new IllegalArgumentException("MCP消息不能为空"));
        }

        if (!StringUtils.hasText(mcpActionVO.getMessageId())) {
            log.error("MCP消息缺少messageId");
            return Mono.error(new IllegalArgumentException("消息ID不能为空"));
        }

        if (!StringUtils.hasText(mcpActionVO.getMcpServerSchema())) {
            log.error("MCP消息缺少服务器配置: messageId={}", mcpActionVO.getMessageId());
            return Mono.error(new IllegalArgumentException("服务器配置不能为空"));
        }

        return parseMessage(mcpActionVO)
                .flatMap(this::handleMessageWithDeduplication)
                .doOnSuccess(result -> {
                    if (result) {
                        log.info("MCP消息处理完成: messageId={}", mcpActionVO.getMessageId());
                    } else {
                        log.warn("MCP消息处理失败: messageId={}", mcpActionVO.getMessageId());
                    }
                })
                .onErrorResume(error -> {
                    log.error("处理MCP消息时发生异常: messageId={}", mcpActionVO.getMessageId(), error);
                    return Mono.just(false);
                })
                .then();
    }

    /**
     * 解析消息
     */
    private Mono<McpClientMessage> parseMessage(McpActionVO mcpActionVO) {
        return Mono.fromCallable(() -> {
            try {
                McpClientMessage mcpClientMessage = JacksonUtils.readValue(
                        mcpActionVO.getMcpServerSchema(),
                        McpClientMessage.class
                );

                // 补充缺失的字段
                mcpClientMessage.setMessageId(mcpActionVO.getMessageId());
                mcpClientMessage.setAction(mcpActionVO.getAction());
                mcpClientMessage.setTimestamp(mcpActionVO.getTimestamp());

                return mcpClientMessage;
            } catch (Exception e) {
                log.error("解析MCP消息失败: messageId={}, schema={}",
                        mcpActionVO.getMessageId(), mcpActionVO.getMcpServerSchema(), e);
                throw new RuntimeException("消息解析失败", e);
            }
        });
    }

    /**
     * 带去重的消息处理
     */
    private Mono<Boolean> handleMessageWithDeduplication(McpClientMessage message) {
        return checkMessageProcessed(message.getMessageId())
                .flatMap(processed -> {
                    if (processed) {
                        log.debug("消息已处理过，跳过: messageId={}", message.getMessageId());
                        return Mono.just(true); // 已处理的消息视为成功
                    }

                    return handleMessageWithLock(message)
                            .flatMap(success -> {
                                if (success) {
                                    return markMessageProcessed(message.getMessageId())
                                            .map(marked -> true);
                                }
                                return Mono.just(false);
                            });
                })
                .onErrorResume(error -> {
                    log.error("处理消息时发生异常: messageId={}", message.getMessageId(), error);
                    return Mono.just(false);
                });
    }

    /**
     * 带分布式锁的消息处理
     */
    private Mono<Boolean> handleMessageWithLock(McpClientMessage message) {
        if (message == null || !StringUtils.hasText(message.getMessageId())) {
            log.error("消息或消息ID为空");
            return Mono.just(false);
        }

        String lockKey = mcpRedisProperties.buildLockKey(message.generateLockKey());
        long lockTimeoutMs = mcpRedisProperties.getLockTimeoutMs();
        long waitTimeMs = mcpRedisProperties.getLockWaitTimeMs();

        return LockUtil.withMonoLock(lockKey, lockTimeoutMs, waitTimeMs, redissonReactiveClient,
                        () -> executeMessageOperation(message))
                .doOnSuccess(success -> {
                    if (success) {
                        log.info("成功处理MCP消息: action={}, messageId={}",
                                message.getAction(), message.getMessageId());
                    } else {
                        log.warn("MCP消息处理失败: action={}, messageId={}",
                                message.getAction(), message.getMessageId());
                    }
                })
                .onErrorResume(error -> {
                    log.error("获取分布式锁或处理消息失败: lockKey={}, messageId={}",
                            lockKey, message.getMessageId(), error);
                    return Mono.just(false);
                });
    }

    /**
     * 执行消息操作
     */
    private Mono<Boolean> executeMessageOperation(McpClientMessage message) {
        McpClientMessage.ActionType actionType = message.getActionType();
        if (actionType == null) {
            log.error("无效的操作类型: action={}, messageId={}",
                    message.getAction(), message.getMessageId());
            return Mono.just(false);
        }

        switch (actionType) {
            case ADD:
                return handleAddOperation(message);
            case UPDATE:
                return handleUpdateOperation(message);
            case DELETE:
                return handleDeleteOperation(message);
            case RELOAD:
                return handleReloadOperation(message);
            default:
                log.error("不支持的操作类型: actionType={}, messageId={}",
                        actionType, message.getMessageId());
                return Mono.just(false);
        }
    }

    /**
     * 处理ADD操作
     */
    private Mono<Boolean> handleAddOperation(McpClientMessage message) {
        if (message.getMcpServers() == null || message.getMcpServers().isEmpty()) {
            log.error("ADD操作缺少服务器配置: messageId={}", message.getMessageId());
            return Mono.just(false);
        }

        return convertAndValidateConfigs(message)
                .flatMap(configs -> processServerConfigs(message, configs, this::addClient))
                .onErrorResume(error -> {
                    log.error("ADD操作处理失败: messageId={}", message.getMessageId(), error);
                    return Mono.just(false);
                });
    }

    /**
     * 处理UPDATE操作
     */
    private Mono<Boolean> handleUpdateOperation(McpClientMessage message) {
        if (message.getMcpServers() == null || message.getMcpServers().isEmpty()) {
            log.error("UPDATE操作缺少服务器配置: messageId={}", message.getMessageId());
            return Mono.just(false);
        }

        return convertAndValidateConfigs(message)
                .flatMap(configs -> processServerConfigs(message, configs, this::updateClient))
                .onErrorResume(error -> {
                    log.error("UPDATE操作处理失败: messageId={}", message.getMessageId(), error);
                    return Mono.just(false);
                });
    }

    /**
     * 处理DELETE操作
     */
    private Mono<Boolean> handleDeleteOperation(McpClientMessage message) {
        if (message.getMcpServers() == null || message.getMcpServers().isEmpty()) {
            log.error("DELETE操作缺少服务器配置: messageId={}", message.getMessageId());
            return Mono.just(false);
        }

        return Flux.fromIterable(message.getServerNames())
                .flatMap(serverName -> {
                    if (!StringUtils.hasText(serverName)) {
                        log.warn("跳过空的服务器名称: messageId={}", message.getMessageId());
                        return Mono.just(true);
                    }

                    return mcpServiceManager.removeClient(serverName)
                            .timeout(OPERATION_TIMEOUT)
                            .doOnSuccess(result -> log.info("删除MCP客户端成功: serverName={}, messageId={}",
                                    serverName, message.getMessageId()))
                            .onErrorResume(error -> {
                                log.error("删除MCP客户端失败: serverName={}, messageId={}",
                                        serverName, message.getMessageId(), error);
                                return Mono.just(false);
                            });
                }, MAX_CONCURRENT_OPERATIONS)
                .reduce(true, Boolean::logicalAnd);
    }

    /**
     * 处理RELOAD操作
     */
    private Mono<Boolean> handleReloadOperation(McpClientMessage message) {
        log.info("开始执行MCP配置重新加载: messageId={}", message.getMessageId());

        return mcpGlobalConfigManager.loadGlobalConfig()
                .flatMap(globalConfig -> {
                    if (globalConfig.isEmpty()) {
                        log.warn("全局配置为空，跳过重新加载: messageId={}", message.getMessageId());
                        return Mono.just(true);
                    }

                    try {
                        List<McpProperties.McpClientConfig> configs =
                                mcpConfigAdapter.convertStandardToLangChain4j(globalConfig.toStandardMcpConfig());

                        return reloadAllClients(configs, message.getMessageId());
                    } catch (Exception e) {
                        log.error("配置转换失败: messageId={}", message.getMessageId(), e);
                        return Mono.just(false);
                    }
                })
                .onErrorResume(error -> {
                    log.error("执行MCP配置重新加载失败: messageId={}", message.getMessageId(), error);
                    return Mono.just(false);
                });
    }

    /**
     * 重新加载所有客户端
     */
    private Mono<Boolean> reloadAllClients(List<McpProperties.McpClientConfig> configs, String messageId) {
        return Flux.fromIterable(configs)
                .flatMap(config -> {
                    if (config == null || !StringUtils.hasText(config.getName())) {
                        log.warn("跳过无效的配置: messageId={}", messageId);
                        return Mono.just(true);
                    }

                    return mcpServiceManager.removeClient(config.getName())
                            .then(mcpServiceManager.addClient(config))
                            .map(status -> status != null)
                            .timeout(OPERATION_TIMEOUT)
                            .doOnSuccess(result -> {
                                if (result) {
                                    log.info("重新加载MCP客户端成功: clientName={}, messageId={}",
                                            config.getName(), messageId);
                                }
                            })
                            .onErrorResume(error -> {
                                log.error("重新加载MCP客户端失败: clientName={}, messageId={}",
                                        config.getName(), messageId, error);
                                return Mono.just(false);
                            });
                }, MAX_CONCURRENT_OPERATIONS)
                .reduce(true, Boolean::logicalAnd);
    }

    /**
     * 转换并验证配置
     */
    private Mono<List<McpProperties.McpClientConfig>> convertAndValidateConfigs(McpClientMessage message) {
        return Mono.fromCallable(() -> {
            try {
                if (mcpConfigAdapter == null) {
                    throw new IllegalStateException("McpConfigAdapter未初始化");
                }

                List<McpProperties.McpClientConfig> configs =
                        mcpConfigAdapter.convertStandardToLangChain4j(
                                message.toGlobalMcpConfig().toStandardMcpConfig()
                        );

                if (configs == null || configs.isEmpty()) {
                    log.warn("配置转换结果为空: messageId={}", message.getMessageId());
                }

                return configs;
            } catch (Exception e) {
                log.error("配置转换失败: messageId={}", message.getMessageId(), e);
                throw new RuntimeException("配置转换失败", e);
            }
        });
    }

    /**
     * 处理服务器配置
     */
    private Mono<Boolean> processServerConfigs(McpClientMessage message,
                                               List<McpProperties.McpClientConfig> configs,
                                               ClientOperation operation) {
        return Flux.fromIterable(message.getMcpServers().entrySet())
                .flatMap(entry -> {
                    String serverName = entry.getKey();

                    if (!StringUtils.hasText(serverName)) {
                        log.warn("跳过空的服务器名称: messageId={}", message.getMessageId());
                        return Mono.just(true);
                    }

                    McpProperties.McpClientConfig config = configs.stream()
                            .filter(c -> serverName.equals(c.getName()))
                            .findFirst()
                            .orElse(null);

                    if (config == null) {
                        log.error("无法找到对应的服务器配置: serverName={}, messageId={}",
                                serverName, message.getMessageId());
                        return Mono.just(false);
                    }

                    return operation.execute(config, serverName, message.getMessageId());
                }, MAX_CONCURRENT_OPERATIONS)
                .reduce(true, Boolean::logicalAnd);
    }

    /**
     * 添加客户端
     */
    private Mono<Boolean> addClient(McpProperties.McpClientConfig config, String serverName, String messageId) {
        return mcpServiceManager.addClient(config)
                .map(status -> status != null)
                .timeout(OPERATION_TIMEOUT)
                .doOnSuccess(result -> {
                    if (result) {
                        log.info("添加MCP客户端成功: serverName={}, messageId={}", serverName, messageId);
                    }
                })
                .onErrorResume(error -> {
                    log.error("添加MCP客户端失败: serverName={}, messageId={}", serverName, messageId, error);
                    return Mono.just(false);
                });
    }

    /**
     * 更新客户端
     */
    private Mono<Boolean> updateClient(McpProperties.McpClientConfig config, String serverName, String messageId) {
        return mcpServiceManager.updateClient(config)
                .map(status -> status != null)
                .timeout(OPERATION_TIMEOUT)
                .doOnSuccess(result -> {
                    if (result) {
                        log.info("更新MCP客户端成功: serverName={}, messageId={}", serverName, messageId);
                    }
                })
                .onErrorResume(error -> {
                    log.error("更新MCP客户端失败: serverName={}, messageId={}", serverName, messageId, error);
                    return Mono.just(false);
                });
    }

    /**
     * 检查消息是否已处理
     */
    private Mono<Boolean> checkMessageProcessed(String messageId) {
        String key = mcpRedisProperties.getProcessedMessagesKey();

        return reactiveStringRedisTemplate.opsForSet()
                .isMember(key, messageId)
                .onErrorResume(error -> {
                    log.warn("检查消息处理状态失败: messageId={}", messageId, error);
                    return Mono.just(false);
                });
    }

    /**
     * 标记消息已处理
     */
    private Mono<Boolean> markMessageProcessed(String messageId) {
        String key = mcpRedisProperties.getProcessedMessagesKey();
        long expireSeconds = mcpRedisProperties.getMessageDeduplicationExpireSeconds();

        return reactiveStringRedisTemplate.opsForSet()
                .add(key, messageId)
                .flatMap(added -> {
                    if (added > 0) {
                        return reactiveStringRedisTemplate.expire(key, Duration.ofSeconds(expireSeconds))
                                .map(expired -> true);
                    }
                    return Mono.just(true);
                })
                .onErrorResume(error -> {
                    log.warn("标记消息已处理失败: messageId={}", messageId, error);
                    return Mono.just(false);
                });
    }

    /**
     * 客户端操作接口
     */
    @FunctionalInterface
    private interface ClientOperation {
        Mono<Boolean> execute(McpProperties.McpClientConfig config, String serverName, String messageId);
    }
}