package com.wormhole.agent.tool.mcp.redis.util;

import com.wormhole.agent.tool.mcp.config.StandardMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.GlobalMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.McpClientMessage;
import com.wormhole.agent.tool.mcp.redis.constant.McpRedisConstants;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * MCP Redis工具类
 * 提供配置转换、验证、迁移等辅助功能，以及配置管理的便捷方法
 * 所有方法都是静态方法，工具类无状态
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@Slf4j
public final class McpRedisUtils {
    
    // 防止实例化
    private McpRedisUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
    
    // ==================== 配置转换方法 ====================
    
    /**
     * StandardMcpConfig转GlobalMcpConfig
     */
    public static GlobalMcpConfig standardToGlobal(StandardMcpConfig standardConfig) {
        if (standardConfig == null) {
            log.warn("StandardMcpConfig为空，返回默认GlobalMcpConfig");
            return createDefaultGlobalConfig();
        }
        
        try {
            return GlobalMcpConfig.builder()
                .lastModified(Instant.now().toEpochMilli())
                .mcpServers(standardConfig.getMcpServers())
                .build();
        } catch (Exception e) {
            log.error("StandardMcpConfig转GlobalMcpConfig失败", e);
            return createDefaultGlobalConfig();
        }
    }
    
    /**
     * GlobalMcpConfig转StandardMcpConfig
     */
    public static StandardMcpConfig globalToStandard(GlobalMcpConfig globalConfig) {
        if (globalConfig == null) {
            log.warn("GlobalMcpConfig为空，返回默认StandardMcpConfig");
            return createDefaultStandardConfig();
        }
        
        try {
            return StandardMcpConfig.builder()
                .mcpServers(globalConfig.getMcpServers())
                .build();
        } catch (Exception e) {
            log.error("GlobalMcpConfig转StandardMcpConfig失败", e);
            return createDefaultStandardConfig();
        }
    }
    
    /**
     * McpClientMessage转GlobalMcpConfig
     */
    public static GlobalMcpConfig messageToConfig(McpClientMessage message) {
        if (message == null) {
            log.warn("McpClientMessage为空，返回默认GlobalMcpConfig");
            return createDefaultGlobalConfig();
        }
        
        try {
            return GlobalMcpConfig.builder()
                .lastModified(message.getTimestamp())
                .mcpServers(message.getMcpServers())
                .build();
        } catch (Exception e) {
            log.error("McpClientMessage转GlobalMcpConfig失败", e);
            return createDefaultGlobalConfig();
        }
    }
    
    // ==================== 配置验证方法 ====================
    
    /**
     * 验证全局配置有效性
     */
    public static ValidationResult validateGlobalConfig(GlobalMcpConfig config) {
        if (config == null) {
            return ValidationResult.failure(McpRedisConstants.ERROR_CONFIG_NULL);
        }
        
        List<String> errors = new ArrayList<>();
        

        
        // 验证服务器配置
        if (config.getMcpServers() != null) {
            if (config.getMcpServers().size() > McpRedisConstants.MAX_SERVER_COUNT) {
                errors.add(String.format("服务器数量超过最大限制: %d", McpRedisConstants.MAX_SERVER_COUNT));
            }
            
            for (Map.Entry<String, StandardMcpConfig.StandardMcpServer> entry : config.getMcpServers().entrySet()) {
                String serverName = entry.getKey();
                StandardMcpConfig.StandardMcpServer server = entry.getValue();
                
                if (!McpRedisConstants.isValidClientName(serverName)) {
                    errors.add(String.format("无效的服务器名称: %s", serverName));
                }
                
                if (server == null || !server.isValid()) {
                    errors.add(String.format("服务器配置无效: %s", serverName));
                }
            }
        }
        
        return errors.isEmpty() ? ValidationResult.success() : ValidationResult.failure(errors);
    }
    
    /**
     * 验证客户端消息格式
     */
    public static ValidationResult validateClientMessage(McpClientMessage message) {
        if (message == null) {
            return ValidationResult.failure("消息不能为空");
        }
        
        List<String> errors = new ArrayList<>();
        
        // 验证消息ID
        if (message.getMessageId() == null || message.getMessageId().trim().isEmpty()) {
            errors.add(McpRedisConstants.ERROR_MESSAGE_ID_EMPTY);
        }
        
        // 验证操作类型
        if (!McpRedisConstants.isValidActionType(message.getAction())) {
            errors.add(McpRedisConstants.formatError(McpRedisConstants.ERROR_INVALID_ACTION_TYPE, message.getAction()));
        }
        
        // 验证时间戳
        if (message.getTimestamp() <= 0) {
            errors.add("消息时间戳无效");
        }
        
        // 对于非RELOAD操作，验证服务器配置
        if (!McpClientMessage.ActionType.RELOAD.name().equals(message.getAction())) {
            if (message.getMcpServers() == null || message.getMcpServers().isEmpty()) {
                errors.add("非RELOAD操作必须包含服务器配置");
            } else {
                ValidationResult configResult = validateGlobalConfig(messageToConfig(message));
                if (!configResult.isValid()) {
                    errors.addAll(configResult.getErrors());
                }
            }
        }
        
        return errors.isEmpty() ? ValidationResult.success() : ValidationResult.failure(errors);
    }

    // ==================== 配置管理辅助方法 ====================
    
    /**
     * 提取配置中的客户端名称
     */
    public static Set<String> extractClientNames(GlobalMcpConfig config) {
        if (config == null || config.getMcpServers() == null) {
            return Collections.emptySet();
        }
        
        return new HashSet<>(config.getMcpServers().keySet());
    }
    
    /**
     * 合并多个配置
     */
    public static GlobalMcpConfig mergeConfigs(GlobalMcpConfig... configs) {
        if (configs == null || configs.length == 0) {
            return createDefaultGlobalConfig();
        }
        
        Map<String, StandardMcpConfig.StandardMcpServer> mergedServers = new HashMap<>();
        long latestTimestamp = Instant.now().toEpochMilli();
        
        for (GlobalMcpConfig config : configs) {
            if (config == null) continue;
            
            if (config.getMcpServers() != null) {
                mergedServers.putAll(config.getMcpServers());
            }
            
            if (config.getLastModified() > latestTimestamp) {
                latestTimestamp = config.getLastModified();

            }
        }
        
        return GlobalMcpConfig.builder()
            .lastModified(latestTimestamp)
            .mcpServers(mergedServers)
            .build();
    }
    
    /**
     * 生成唯一消息ID
     */
    public static String generateMessageId() {
        return UUID.randomUUID().toString();
    }
    
    /**
     * 格式化配置用于日志
     */
    public static String formatConfigForLogging(GlobalMcpConfig config) {
        if (config == null) {
            return "null";
        }
        
        try {
            return String.format("GlobalMcpConfig{version='%s', lastModified=%d, serverCount=%d, servers=[%s]}", 
                config.getLastModified(),
                config.getServerCount(),
                config.getServerNames().stream().collect(Collectors.joining(", ")));
        } catch (Exception e) {
            return "GlobalMcpConfig{error formatting}";
        }
    }
    
    /**
     * 格式化消息用于日志
     */
    public static String formatMessageForLogging(McpClientMessage message) {
        if (message == null) {
            return "null";
        }
        
        try {
            return String.format("McpClientMessage{messageId='%s', action='%s', timestamp=%d, serverCount=%d}", 
                message.getMessageId(), 
                message.getAction(), 
                message.getTimestamp(),
                message.getServerCount());
        } catch (Exception e) {
            return "McpClientMessage{error formatting}";
        }
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 创建默认全局配置
     */
    private static GlobalMcpConfig createDefaultGlobalConfig() {
        return GlobalMcpConfig.builder()
            .lastModified(Instant.now().toEpochMilli())
            .mcpServers(Collections.emptyMap())
            .build();
    }
    
    /**
     * 创建默认标准配置
     */
    private static StandardMcpConfig createDefaultStandardConfig() {
        return StandardMcpConfig.builder()
            .mcpServers(Collections.emptyMap())
            .build();
    }
    
    // ==================== 内部类 ====================
    
    /**
     * 验证结果
     */
    public static class ValidationResult {
        private final boolean valid;
        private final List<String> errors;
        
        private ValidationResult(boolean valid, List<String> errors) {
            this.valid = valid;
            this.errors = errors != null ? new ArrayList<>(errors) : new ArrayList<>();
        }
        
        public static ValidationResult success() {
            return new ValidationResult(true, Collections.emptyList());
        }
        
        public static ValidationResult failure(String error) {
            return new ValidationResult(false, Collections.singletonList(error));
        }
        
        public static ValidationResult failure(List<String> errors) {
            return new ValidationResult(false, errors);
        }
        
        public boolean isValid() { return valid; }
        public List<String> getErrors() { return new ArrayList<>(errors); }
        public String getErrorMessage() { return String.join("; ", errors); }
    }
    
    /**
     * 兼容性检查结果
     */
    public static class CompatibilityResult {
        private final boolean compatible;
        private final List<String> conflicts;
        private final List<String> warnings;
        
        private CompatibilityResult(boolean compatible, List<String> conflicts, List<String> warnings) {
            this.compatible = compatible;
            this.conflicts = conflicts != null ? new ArrayList<>(conflicts) : new ArrayList<>();
            this.warnings = warnings != null ? new ArrayList<>(warnings) : new ArrayList<>();
        }
        
        public static CompatibilityResult compatible(String message) {
            return new CompatibilityResult(true, Collections.emptyList(), 
                message != null ? Collections.singletonList(message) : Collections.emptyList());
        }
        
        public static CompatibilityResult compatible(List<String> warnings) {
            return new CompatibilityResult(true, Collections.emptyList(), warnings);
        }
        
        public static CompatibilityResult incompatible(List<String> conflicts, List<String> warnings) {
            return new CompatibilityResult(false, conflicts, warnings);
        }
        
        public boolean isCompatible() { return compatible; }
        public List<String> getConflicts() { return new ArrayList<>(conflicts); }
        public List<String> getWarnings() { return new ArrayList<>(warnings); }
    }
}
