package com.wormhole.agent.tool.mcp.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * MCP异步配置
 * 提供专用的线程池用于MCP客户端的异步初始化
 * 
 * <AUTHOR>
 * @version 2025-08-19
 */
@Slf4j
@Configuration
@EnableAsync
public class McpAsyncConfig {

    /**
     * MCP任务执行器
     * 专门用于MCP客户端的异步初始化和工具发现
     */
    @Bean("mcpTaskExecutor")
    public Executor mcpTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数：系统核心数或5，取较小值
        int corePoolSize = Math.min(Runtime.getRuntime().availableProcessors(), 5);
        executor.setCorePoolSize(corePoolSize);
        
        // 最大线程数：核心线程数的2倍
        executor.setMaxPoolSize(corePoolSize * 2);
        
        // 队列容量：50个任务
        executor.setQueueCapacity(50);
        
        // 线程名称前缀
        executor.setThreadNamePrefix("mcp-async-");
        
        // 拒绝策略：由调用者直接执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 线程空闲时间：60秒
        executor.setKeepAliveSeconds(60);
        
        // 允许核心线程超时
        executor.setAllowCoreThreadTimeOut(true);
        
        // 等待任务完成后再关闭
        executor.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间：10秒
        executor.setAwaitTerminationSeconds(10);
        
        executor.initialize();
        
        log.info("MCP异步任务执行器已配置 - 核心线程数: {}, 最大线程数: {}, 队列容量: {}", 
            corePoolSize, corePoolSize * 2, 50);
        
        return executor;
    }
}