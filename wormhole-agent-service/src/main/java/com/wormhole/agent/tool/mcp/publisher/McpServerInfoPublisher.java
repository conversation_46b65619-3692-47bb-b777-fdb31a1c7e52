package com.wormhole.agent.tool.mcp.publisher;

import com.alibaba.fastjson.JSON;
import com.wormhole.agent.client.chat.mcp.dto.McpServerInfoMessage;
import com.wormhole.agent.client.chat.mcp.dto.McpServiceStatus;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.tool.mcp.redis.config.McpRedisProperties;
import com.wormhole.mq.producer.ReactiveMessageSender;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * MCP服务器信息发布服务
 * 负责构建和发布MCP服务器信息消息到Redis频道
 * 向wormhole-platform推送MCP服务器状态变更信息
 *
 * <AUTHOR>
 * @version 2025-07-23
 */
@Slf4j
@Service
public class McpServerInfoPublisher {

    
    @Resource
    private McpRedisProperties mcpRedisProperties;
    @Resource
    private ReactiveMessageSender reactiveMessageSender;
    
    /**
     * 发布初始化消息
     * 系统启动时发布所有MCP服务器的初始化信息
     *
     * @param serverName MCP服务器名称
     * @param status 服务状态
     * @param tools 工具列表
     * @return 发布结果
     */
    public Mono<Void> publishInitMessage(String serverName, McpServiceStatus status, List<OpenAiTool> tools) {
        if (!mcpRedisProperties.isServerInfoEnabled()) {
            log.debug("服务器信息发布功能已禁用，跳过初始化消息发布: {}", serverName);
            return Mono.empty();
        }
        
        McpServerInfoMessage message = McpServerInfoMessage.createInitMessage(serverName, status, tools);
        return publishMessage(message)
                .doOnSuccess(unused -> log.info("成功发布MCP服务器初始化消息: {}", serverName))
                .doOnError(error -> log.error("发布MCP服务器初始化消息失败: {}", serverName, error));
    }
    
    /**
     * 发布添加消息
     * 新增MCP服务器时发布添加信息
     *
     * @param serverName MCP服务器名称
     * @param status 服务状态
     * @param tools 工具列表
     * @return 发布结果
     */
    public Mono<Void> publishAddMessage(String serverName, McpServiceStatus status, List<OpenAiTool> tools) {
        if (!mcpRedisProperties.isServerInfoEnabled()) {
            log.debug("服务器信息发布功能已禁用，跳过添加消息发布: {}", serverName);
            return Mono.empty();
        }
        
        McpServerInfoMessage message = McpServerInfoMessage.createAddMessage(serverName, status, tools);
        return publishMessage(message)
                .doOnSuccess(unused -> log.info("成功发布MCP服务器添加消息: {}", serverName))
                .doOnError(error -> log.error("发布MCP服务器添加消息失败: {}", serverName, error));
    }
    
    /**
     * 发布更新消息
     * 更新MCP服务器时发布更新信息
     *
     * @param serverName MCP服务器名称
     * @param status 服务状态
     * @param tools 工具列表
     * @return 发布结果
     */
    public Mono<Void> publishUpdateMessage(String serverName, McpServiceStatus status, List<OpenAiTool> tools) {
        if (!mcpRedisProperties.isServerInfoEnabled()) {
            log.debug("服务器信息发布功能已禁用，跳过更新消息发布: {}", serverName);
            return Mono.empty();
        }
        
        McpServerInfoMessage message = McpServerInfoMessage.createUpdateMessage(serverName, status, tools);
        return publishMessage(message)
                .doOnSuccess(unused -> log.info("成功发布MCP服务器更新消息: {}", serverName))
                .doOnError(error -> log.error("发布MCP服务器更新消息失败: {}", serverName, error));
    }
    
    /**
     * 发布删除消息
     * 删除MCP服务器时发布删除信息
     *
     * @param serverName MCP服务器名称
     * @param status 服务状态
     * @return 发布结果
     */
    public Mono<Void> publishDeleteMessage(String serverName, McpServiceStatus status) {
        if (!mcpRedisProperties.isServerInfoEnabled()) {
            log.debug("服务器信息发布功能已禁用，跳过删除消息发布: {}", serverName);
            return Mono.empty();
        }
        
        McpServerInfoMessage message = McpServerInfoMessage.createDeleteMessage(serverName, status);
        return publishMessage(message)
                .doOnSuccess(unused -> log.info("成功发布MCP服务器删除消息: {}", serverName))
                .doOnError(error -> log.error("发布MCP服务器删除消息失败: {}", serverName, error));
    }
    
    /**
     * 发布错误消息
     * MCP服务器连接失败、初始化失败或重试失败时发布错误信息
     *
     * @param serverName MCP服务器名称
     * @param errorMessage 错误信息
     * @param retryInfo 重试信息（可选）
     * @return 发布结果
     */
    public Mono<Void> publishErrorMessage(String serverName, String errorMessage, String retryInfo) {
        if (!mcpRedisProperties.isServerInfoEnabled()) {
            log.debug("服务器信息发布功能已禁用，跳过错误消息发布: {}", serverName);
            return Mono.empty();
        }
        
        // 构建详细的错误状态
        McpServiceStatus errorStatus = McpServiceStatus.error(serverName, errorMessage);
        if (retryInfo != null && !retryInfo.trim().isEmpty()) {
            errorStatus.setError(errorMessage + " [" + retryInfo + "]");
        }
        
        McpServerInfoMessage message = McpServerInfoMessage.createErrorMessage(serverName, errorStatus, errorMessage);
        return publishMessage(message)
                .doOnSuccess(unused -> log.info("成功发布MCP服务器错误消息: {} - {}", serverName, errorMessage))
                .doOnError(error -> log.error("发布MCP服务器错误消息失败: {}", serverName, error));
    }
    
    /**
     * 发布连接失败消息
     * MCP服务器连接失败时的专用方法
     *
     * @param serverName MCP服务器名称
     * @param connectionError 连接错误信息
     * @param transport 传输方式
     * @param endpoint 端点信息
     * @param retryCount 已重试次数
     * @param maxRetries 最大重试次数
     * @return 发布结果
     */
    public Mono<Void> publishConnectionFailureMessage(String serverName, String connectionError, 
                                                     String transport, String endpoint, 
                                                     int retryCount, int maxRetries) {
        if (!mcpRedisProperties.isServerInfoEnabled()) {
            log.debug("服务器信息发布功能已禁用，跳过连接失败消息发布: {}", serverName);
            return Mono.empty();
        }
        
        String errorMessage = String.format("连接失败: %s (传输: %s)", connectionError, transport);
        String retryInfo = String.format("重试 %d/%d 次", retryCount, maxRetries);
        
        // 创建连接失败状态
        McpServiceStatus failureStatus = McpServiceStatus.error(serverName, errorMessage);
        failureStatus.setTransport(transport);
        failureStatus.setEndpoint(endpoint);
        
        String fullErrorMessage = errorMessage + " [" + retryInfo + "]";
        failureStatus.setError(fullErrorMessage);
        
        McpServerInfoMessage message = McpServerInfoMessage.createErrorMessage(serverName, failureStatus, fullErrorMessage);
        return publishMessage(message)
                .doOnSuccess(unused -> log.info("成功发布MCP服务器连接失败消息: {} - {}", serverName, errorMessage))
                .doOnError(error -> log.error("发布MCP服务器连接失败消息失败: {}", serverName, error));
    }
    
    /**
     * 发布重试失败消息
     * 达到最大重试次数后的最终失败通知
     *
     * @param serverName MCP服务器名称
     * @param originalError 原始错误信息
     * @param retryCount 总重试次数
     * @return 发布结果
     */
    public Mono<Void> publishRetryExhaustedMessage(String serverName, String originalError, int retryCount) {
        if (!mcpRedisProperties.isServerInfoEnabled()) {
            log.debug("服务器信息发布功能已禁用，跳过重试耗尽消息发布: {}", serverName);
            return Mono.empty();
        }
        
        String errorMessage = String.format("重试失败，已达到最大重试次数: %s", originalError);
        String retryInfo = String.format("已重试 %d 次，不再重试", retryCount);
        
        McpServiceStatus finalFailureStatus = McpServiceStatus.error(serverName, errorMessage);
        finalFailureStatus.setError(errorMessage + " [" + retryInfo + "]");
        
        McpServerInfoMessage message = McpServerInfoMessage.createErrorMessage(serverName, finalFailureStatus, errorMessage);
        return publishMessage(message)
                .doOnSuccess(unused -> log.info("成功发布MCP服务器重试耗尽消息: {} - 已停止重试", serverName))
                .doOnError(error -> log.error("发布MCP服务器重试耗尽消息失败: {}", serverName, error));
    }
    
    /**
     * 统一的消息发布逻辑
     * 异步发布消息到Redis频道，不阻塞主流程
     *
     * @param message 要发布的消息
     * @return 发布结果
     */
    private Mono<Void> publishMessage(McpServerInfoMessage message) {
        if (message == null) {
            log.warn("尝试发布空的MCP服务器信息消息");
            return Mono.empty();
        }
        if (!message.isValid()) {
            log.error("MCP服务器信息消息验证失败，拒绝发布: {}", message.getSummary());
            return Mono.empty();
        }
        log.info("准备发布MCP服务器信息消息到MQ: {}", message.getSummary());
        return reactiveMessageSender.sendMessage("mcp-server-info-topic", message)
                .doOnSuccess(result ->
                        log.info("MCP服务器信息消息发布成功: {}", JSON.toJSONString(message))
                )
                .then()
                .onErrorResume(error -> {
                    log.error("发布MCP服务器信息消息到MQ失败: {}", message.getSummary(), error);
                    return Mono.empty();
                });
    }


}
