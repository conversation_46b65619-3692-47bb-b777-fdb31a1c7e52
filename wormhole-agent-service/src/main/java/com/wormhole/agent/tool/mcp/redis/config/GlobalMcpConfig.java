package com.wormhole.agent.tool.mcp.redis.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.wormhole.agent.tool.mcp.config.StandardMcpConfig;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Collections;
import java.util.Map;

/**
 * 全局MCP配置数据结构
 * 用于存储在Redis key: mcp:config:global 中的配置格式
 * 与StandardMcpConfig完全兼容，支持相互转换
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GlobalMcpConfig {
    

    /**
     * 最后修改时间戳
     */
    @Builder.Default
    private long lastModified = Instant.now().toEpochMilli();
    
    /**
     * MCP服务器配置映射
     * 键为服务器名称，值为服务器配置
     * 与StandardMcpConfig.mcpServers完全兼容
     */
    private Map<String, StandardMcpConfig.StandardMcpServer> mcpServers;
    
    /**
     * 验证全局配置的有效性
     */
    @JsonIgnore
    public boolean isValid() {
        if (mcpServers == null || mcpServers.isEmpty()) {
            return false;
        }
        
        return mcpServers.values().stream().allMatch(StandardMcpConfig.StandardMcpServer::isValid);
    }
    
    /**
     * 获取所有服务器名称
     */
    @JsonIgnore
    public java.util.Set<String> getServerNames() {
        return mcpServers != null ? mcpServers.keySet() : Collections.emptySet();
    }
    
    /**
     * 获取启用的服务器配置
     */
    @JsonIgnore
    public Map<String, StandardMcpConfig.StandardMcpServer> getEnabledServers() {
        if (mcpServers == null) {
            return Collections.emptyMap();
        }
        
        return mcpServers.entrySet().stream()
            .filter(entry -> entry.getValue().getEnabled() != null && entry.getValue().getEnabled())
            .collect(java.util.stream.Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue
            ));
    }
    
    /**
     * 转换为StandardMcpConfig格式
     */
    @JsonIgnore
    public StandardMcpConfig toStandardMcpConfig() {
        return StandardMcpConfig.builder()
            .mcpServers(this.mcpServers)
            .build();
    }
    
    /**
     * 从StandardMcpConfig创建GlobalMcpConfig
     */
    public static GlobalMcpConfig fromStandardMcpConfig(StandardMcpConfig standardConfig) {
        if (standardConfig == null) {
            return null;
        }
        
        return GlobalMcpConfig.builder()
            .lastModified(Instant.now().toEpochMilli())
            .mcpServers(standardConfig.getMcpServers())
            .build();
    }
    
    /**
     * 更新最后修改时间
     */
    public void updateLastModified() {
        this.lastModified = Instant.now().toEpochMilli();
    }
    
    /**
     * 检查配置是否为空
     */
    @JsonIgnore
    public boolean isEmpty() {
        return mcpServers == null || mcpServers.isEmpty();
    }
    
    /**
     * 获取服务器数量
     */
    @JsonIgnore
    public int getServerCount() {
        return mcpServers != null ? mcpServers.size() : 0;
    }
    
    /**
     * 获取启用的服务器数量
     */
    @JsonIgnore
    public int getEnabledServerCount() {
        return getEnabledServers().size();
    }
}
