package com.wormhole.agent.tool.mcp.service;

import com.wormhole.agent.client.chat.mcp.dto.McpServiceStatus;
import com.wormhole.agent.tool.mcp.model.McpClientInfo;
import dev.langchain4j.mcp.client.McpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * MCP健康检查器
 * 负责检查MCP客户端的健康状态
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@Component
public class McpHealthChecker {
    
    /**
     * 健康检查超时时间（秒）
     */
    private static final int HEALTH_CHECK_TIMEOUT = 10;
    
    /**
     * 健康检查间隔时间（分钟）
     */
    private static final int HEALTH_CHECK_INTERVAL_MINUTES = 5;
    
    /**
     * 检查客户端健康状态
     * 
     * @param clientInfo 客户端信息
     * @return 健康检查结果
     */
    public CompletableFuture<HealthCheckResult> checkHealth(McpClientInfo clientInfo) {
        if (clientInfo == null) {
            return CompletableFuture.completedFuture(
                HealthCheckResult.unhealthy("客户端信息为空"));
        }
        
        String clientName = clientInfo.getClientName();
        McpClient client = clientInfo.getClient();
        
        if (client == null) {
            return CompletableFuture.completedFuture(
                HealthCheckResult.unhealthy("客户端实例为空"));
        }
        
        log.debug("开始健康检查: {}", clientName);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 执行简单的工具列表查询作为健康检查
                long startTime = System.currentTimeMillis();
                client.listTools();
                long duration = System.currentTimeMillis() - startTime;
                
                log.debug("客户端 {} 健康检查成功，耗时: {}ms", clientName, duration);

                // 更新客户端状态为活跃
                clientInfo.getStatus().updateStatus(McpServiceStatus.ServiceStatus.ACTIVE, "健康检查通过");
                clientInfo.getStatus().setLastHealthCheck(Instant.now());
                clientInfo.updateActiveTime();
                
                return HealthCheckResult.healthy(duration);
                
            } catch (Exception e) {
                log.warn("客户端 {} 健康检查失败", clientName, e);

                // 增强的错误分析和分类处理
                HealthCheckResult result = analyzeHealthCheckError(e, clientName);
                
                // 更新客户端状态
                clientInfo.getStatus().updateStatus(McpServiceStatus.ServiceStatus.ERROR, result.getMessage());
                clientInfo.getStatus().setLastHealthCheck(Instant.now());

                return result;
            }
        }).orTimeout(HEALTH_CHECK_TIMEOUT, TimeUnit.SECONDS)
        .exceptionally(throwable -> {
            log.error("客户端 {} 健康检查超时或异常", clientName, throwable);

            // 更新客户端状态为错误
            if (clientInfo.getStatus() != null) {
                clientInfo.getStatus().updateStatus(McpServiceStatus.ServiceStatus.ERROR, "健康检查超时或异常: " + throwable.getMessage());
                clientInfo.getStatus().setLastHealthCheck(Instant.now());
            }

            return HealthCheckResult.unhealthy("健康检查超时或异常: " + throwable.getMessage());
        });
    }
    
    /**
     * 检查是否需要进行健康检查
     * 
     * @param clientInfo 客户端信息
     * @return 是否需要检查
     */
    public boolean needsHealthCheck(McpClientInfo clientInfo) {
        if (clientInfo == null || clientInfo.getStatus() == null) {
            return false;
        }
        
        Instant lastCheck = clientInfo.getStatus().getLastHealthCheck();
        if (lastCheck == null) {
            return true; // 从未检查过
        }
        
        Duration timeSinceLastCheck = Duration.between(lastCheck, Instant.now());
        return timeSinceLastCheck.toMinutes() >= HEALTH_CHECK_INTERVAL_MINUTES;
    }
    
    /**
     * 批量健康检查
     * 
     * @param clientInfos 客户端信息列表
     * @return 健康检查结果的CompletableFuture
     */
    public CompletableFuture<Void> batchHealthCheck(Iterable<McpClientInfo> clientInfos) {
        CompletableFuture<?>[] futures = new CompletableFuture[0];
        
        for (McpClientInfo clientInfo : clientInfos) {
            if (needsHealthCheck(clientInfo)) {
                CompletableFuture<HealthCheckResult> future = checkHealth(clientInfo);
                futures = addToArray(futures, future);
            }
        }
        
        return CompletableFuture.allOf(futures);
    }
    
    /**
     * 分析健康检查错误，提供详细的错误分类和建议
     * 
     * @param e 异常信息
     * @param clientName 客户端名称
     * @return 健康检查结果
     */
    private HealthCheckResult analyzeHealthCheckError(Exception e, String clientName) {
        String errorMessage = e.getMessage();
        String causeMessage = e.getCause() != null ? e.getCause().getMessage() : "";
        
        // 404 错误特殊处理
        if (errorMessage != null && errorMessage.contains("404") || 
            causeMessage.contains("404") || 
            causeMessage.contains("Unexpected status code: 404")) {
            
            log.error("检测到404错误，MCP服务端可能不可达: {}", clientName);
            return HealthCheckResult.connectionNotFound(
                String.format("MCP服务端不可达(404): %s - 请检查服务端URL或服务是否运行", clientName)
            );
        }
        
        // 连接相关错误
        if (errorMessage != null && (
            errorMessage.contains("Connection refused") ||
            errorMessage.contains("ConnectException") ||
            causeMessage.contains("Connection refused"))) {
            
            log.error("检测到连接被拒绝错误: {}", clientName);
            return HealthCheckResult.connectionRefused(
                String.format("连接被拒绝: %s - 请检查服务端是否启动", clientName)
            );
        }
        
        // 超时错误
        if (errorMessage != null && (
            errorMessage.contains("timeout") ||
            errorMessage.contains("Timeout") ||
            causeMessage.contains("timeout"))) {
            
            log.error("检测到超时错误: {}", clientName);
            return HealthCheckResult.timeout(
                String.format("连接超时: %s - 请检查网络连接或增加超时时间", clientName)
            );
        }
        
        // 认证相关错误
        if (errorMessage != null && (
            errorMessage.contains("401") ||
            errorMessage.contains("403") ||
            errorMessage.contains("Unauthorized") ||
            causeMessage.contains("401") ||
            causeMessage.contains("403"))) {
            
            log.error("检测到认证错误: {}", clientName);
            return HealthCheckResult.authenticationFailed(
                String.format("认证失败: %s - 请检查访问凭据", clientName)
            );
        }
        
        // 服务器错误
        if (errorMessage != null && (
            errorMessage.contains("500") ||
            errorMessage.contains("502") ||
            errorMessage.contains("503") ||
            causeMessage.contains("500") ||
            causeMessage.contains("502") ||
            causeMessage.contains("503"))) {
            
            log.error("检测到服务器错误: {}", clientName);
            return HealthCheckResult.serverError(
                String.format("服务器错误: %s - 请稍后重试或联系服务提供方", clientName)
            );
        }
        
        // 通用错误
        log.error("未分类的健康检查错误: {} - {}", clientName, errorMessage);
        return HealthCheckResult.unknownError(
            String.format("健康检查失败: %s - %s", clientName, errorMessage)
        );
    }
    
    /**
     * 辅助方法：向数组添加元素
     */
    private CompletableFuture<?>[] addToArray(CompletableFuture<?>[] array, CompletableFuture<?> element) {
        CompletableFuture<?>[] newArray = new CompletableFuture[array.length + 1];
        System.arraycopy(array, 0, newArray, 0, array.length);
        newArray[array.length] = element;
        return newArray;
    }
    
    /**
     * 健康检查结果
     */
    public static class HealthCheckResult {
        private final boolean healthy;
        private final String message;
        private final long responseTime;
        private final Instant checkTime;
        private final ErrorType errorType;
        
        private HealthCheckResult(boolean healthy, String message, long responseTime, ErrorType errorType) {
            this.healthy = healthy;
            this.message = message;
            this.responseTime = responseTime;
            this.checkTime = Instant.now();
            this.errorType = errorType;
        }
        
        public static HealthCheckResult healthy(long responseTime) {
            return new HealthCheckResult(true, "健康", responseTime, ErrorType.NONE);
        }
        
        public static HealthCheckResult unhealthy(String message) {
            return new HealthCheckResult(false, message, -1, ErrorType.UNKNOWN);
        }
        
        public static HealthCheckResult connectionNotFound(String message) {
            return new HealthCheckResult(false, message, -1, ErrorType.NOT_FOUND);
        }
        
        public static HealthCheckResult connectionRefused(String message) {
            return new HealthCheckResult(false, message, -1, ErrorType.CONNECTION_REFUSED);
        }
        
        public static HealthCheckResult timeout(String message) {
            return new HealthCheckResult(false, message, -1, ErrorType.TIMEOUT);
        }
        
        public static HealthCheckResult authenticationFailed(String message) {
            return new HealthCheckResult(false, message, -1, ErrorType.AUTHENTICATION_FAILED);
        }
        
        public static HealthCheckResult serverError(String message) {
            return new HealthCheckResult(false, message, -1, ErrorType.SERVER_ERROR);
        }
        
        public static HealthCheckResult unknownError(String message) {
            return new HealthCheckResult(false, message, -1, ErrorType.UNKNOWN);
        }
        
        public boolean isHealthy() {
            return healthy;
        }
        
        public String getMessage() {
            return message;
        }
        
        public long getResponseTime() {
            return responseTime;
        }
        
        public Instant getCheckTime() {
            return checkTime;
        }
        
        public ErrorType getErrorType() {
            return errorType;
        }
        
        public boolean isRetriable() {
            return errorType == ErrorType.TIMEOUT || 
                   errorType == ErrorType.SERVER_ERROR || 
                   errorType == ErrorType.CONNECTION_REFUSED;
        }
        
        public boolean requiresReconfiguration() {
            return errorType == ErrorType.NOT_FOUND || 
                   errorType == ErrorType.AUTHENTICATION_FAILED;
        }
        
        @Override
        public String toString() {
            return String.format("HealthCheckResult{healthy=%s, message='%s', responseTime=%dms, errorType=%s, checkTime=%s}",
                healthy, message, responseTime, errorType, checkTime);
        }
    }
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        NONE("无错误"),
        NOT_FOUND("服务不存在(404)"),
        CONNECTION_REFUSED("连接被拒绝"),
        TIMEOUT("超时"),
        AUTHENTICATION_FAILED("认证失败"),
        SERVER_ERROR("服务器错误"),
        UNKNOWN("未知错误");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
}
