package com.wormhole.agent.tool.mcp.config;

import com.wormhole.common.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * MCP配置格式适配器
 * 负责在标准MCP格式和LangChain4j格式之间进行转换
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@Component
public class McpConfigAdapter {
    
    /**
     * 检测配置格式类型
     * 
     * @param jsonContent JSON配置内容
     * @return true表示标准MCP格式，false表示LangChain4j格式
     */
    public boolean isStandardMcpFormat(String jsonContent) {
        if (jsonContent == null || jsonContent.trim().isEmpty()) {
            return false;
        }
        
        try {
            // 简单检测：标准MCP格式包含"servers"字段，LangChain4j格式包含"clients"字段
            return jsonContent.contains("\"servers\"") && !jsonContent.contains("\"clients\"");
        } catch (Exception e) {
            log.warn("检测配置格式时发生异常", e);
            return false;
        }
    }
    
    /**
     * 适配单个配置对象
     * 
     * @param input 输入配置对象
     * @return 转换后的LangChain4j格式配置
     */
    public McpProperties.McpClientConfig adaptSingleConfig(Object input) {
        String json = JacksonUtils.writeValueAsString(input);
        
        if (isStandardMcpFormat(json)) {
            StandardMcpConfig standardConfig = JacksonUtils.readValue(json, StandardMcpConfig.class);
            List<McpProperties.McpClientConfig> configs = convertStandardToLangChain4j(standardConfig);
            if (!configs.isEmpty()) {
                return configs.get(0);
            }
        }
        
        // 回退到LangChain4j格式
        return JacksonUtils.readValue(json, McpProperties.McpClientConfig.class);
    }
    
    /**
     * 适配多个配置对象
     * 
     * @param input 输入配置对象
     * @return 转换后的LangChain4j格式配置列表
     */
    public List<McpProperties.McpClientConfig> adaptMultipleConfigs(Object input) {
        String json = JacksonUtils.writeValueAsString(input);
        
        if (isStandardMcpFormat(json)) {
            StandardMcpConfig standardConfig = JacksonUtils.readValue(json, StandardMcpConfig.class);
            return convertStandardToLangChain4j(standardConfig);
        } else {
            // 尝试解析为McpProperties格式
            try {
                McpProperties props = JacksonUtils.readValue(json, McpProperties.class);
                return props.getClients();
            } catch (Exception e) {
                // 尝试解析为单个配置
                try {
                    McpProperties.McpClientConfig singleConfig = 
                        JacksonUtils.readValue(json, McpProperties.McpClientConfig.class);
                    List<McpProperties.McpClientConfig> result = new ArrayList<>();
                    result.add(singleConfig);
                    return result;
                } catch (Exception ex) {
                    log.error("无法解析配置格式", ex);
                    return new ArrayList<>();
                }
            }
        }
    }
    
    /**
     * 将标准MCP格式转换为LangChain4j格式
     * 
     * @param standardConfig 标准MCP配置
     * @return LangChain4j格式配置列表
     */
    public List<McpProperties.McpClientConfig> convertStandardToLangChain4j(StandardMcpConfig standardConfig) {
        List<McpProperties.McpClientConfig> result = new ArrayList<>();
        
        if (standardConfig == null || standardConfig.getMcpServers() == null) {
            return result;
        }
        
        for (Map.Entry<String, StandardMcpConfig.StandardMcpServer> entry : 
             standardConfig.getMcpServers().entrySet()) {
            
            String serverName = entry.getKey();
            StandardMcpConfig.StandardMcpServer server = entry.getValue();
            
            McpProperties.McpClientConfig config = McpProperties.McpClientConfig.builder()
                .name(serverName)
                .transport(mapTransportType(server.getType()))
                .url(server.getUrl())
                .command(server.buildFullCommand())
                .timeout(server.getTimeout())
                .enabled(server.getEnabled() != null ? server.getEnabled() : true)
                .createdAt(Instant.now())
                .source("api")
                .status("active")
                .build();
            
            // 设置元数据
            if (server.getEnv() != null || server.getHeaders() != null) {
                Map<String, Object> metadata = new java.util.HashMap<>();
                if (server.getEnv() != null) {
                    metadata.put("env", server.getEnv());
                }
                if (server.getHeaders() != null) {
                    metadata.put("headers", server.getHeaders());
                }
                if (server.getEnvFile() != null) {
                    metadata.put("envFile", server.getEnvFile());
                }
                config.setMetadata(metadata);
            }
            
            result.add(config);
        }
        
        return result;
    }
    
    /**
     * 将LangChain4j格式转换为标准MCP格式
     * 
     * @param langChain4jConfigs LangChain4j格式配置列表
     * @return 标准MCP配置
     */
    public StandardMcpConfig convertLangChain4jToStandard(List<McpProperties.McpClientConfig> langChain4jConfigs) {
        Map<String, StandardMcpConfig.StandardMcpServer> servers = new java.util.HashMap<>();
        
        for (McpProperties.McpClientConfig config : langChain4jConfigs) {
            StandardMcpConfig.StandardMcpServer.StandardMcpServerBuilder serverBuilder = 
                StandardMcpConfig.StandardMcpServer.builder()
                    .type(mapTransportType(config.getTransport()))
                    .url(config.getUrl())
                    .timeout(config.getTimeout())
                    .enabled(config.isEnabled());
            
            // 处理stdio命令
            if ("stdio".equals(config.getTransport()) && config.getCommand() != null && !config.getCommand().isEmpty()) {
                serverBuilder.command(config.getCommand().get(0));
                if (config.getCommand().size() > 1) {
                    serverBuilder.args(config.getCommand().subList(1, config.getCommand().size()));
                }
            }
            
            // 处理元数据
            if (config.getMetadata() != null) {
                Map<String, String> env = extractStringMap(config.getMetadata(), "env");
                Map<String, String> headers = extractStringMap(config.getMetadata(), "headers");
                String envFile = (String) config.getMetadata().get("envFile");
                
                serverBuilder.env(env).headers(headers).envFile(envFile);
            }
            
            servers.put(config.getName(), serverBuilder.build());
        }
        
        return StandardMcpConfig.builder()
            .mcpServers(servers)
            .build();
    }
    
    /**
     * 映射传输类型
     */
    private String mapTransportType(String transport) {
        if (transport == null) {
            return "sse";
        }
        
        switch (transport.toLowerCase()) {
            case "http":
                return "sse"; // HTTP映射为SSE
            default:
                return transport.toLowerCase();
        }
    }
    
    /**
     * 从元数据中提取字符串映射
     */
    @SuppressWarnings("unchecked")
    private Map<String, String> extractStringMap(Map<String, Object> metadata, String key) {
        Object value = metadata.get(key);
        if (value instanceof Map) {
            try {
                return (Map<String, String>) value;
            } catch (ClassCastException e) {
                log.warn("无法转换元数据中的{}为字符串映射", key);
            }
        }
        return null;
    }
    
    /**
     * 格式转换工具方法
     * 
     * @param input 输入配置
     * @param targetFormat 目标格式：standard 或 langchain4j
     * @return 转换后的配置
     */
    public Object convertFormat(Object input, String targetFormat) {
        String json = JacksonUtils.writeValueAsString(input);
        
        if ("standard".equalsIgnoreCase(targetFormat)) {
            if (isStandardMcpFormat(json)) {
                return input; // 已经是标准格式
            } else {
                // 转换为标准格式
                List<McpProperties.McpClientConfig> configs = adaptMultipleConfigs(input);
                return convertLangChain4jToStandard(configs);
            }
        } else if ("langchain4j".equalsIgnoreCase(targetFormat)) {
            if (!isStandardMcpFormat(json)) {
                return input; // 已经是LangChain4j格式
            } else {
                // 转换为LangChain4j格式
                return adaptMultipleConfigs(input);
            }
        }
        
        throw new IllegalArgumentException("不支持的目标格式: " + targetFormat);
    }
}
