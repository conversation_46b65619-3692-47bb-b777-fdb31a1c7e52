package com.wormhole.agent.tool.mcp.redis.constant;

/**
 * MCP Redis常量定义
 * 包含Redis key名称、默认配置值、超时参数、错误消息模板等
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
public final class McpRedisConstants {
    
    // 防止实例化
    private McpRedisConstants() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    // ==================== Redis Key 常量 ====================
    /**
     * 分布式锁键名前缀
     */
    public static final String LOCK_KEY_PREFIX = "wormhole-agent:mcp:";

    // ==================== 配置键名常量 ====================

    
    // ==================== 操作类型常量 ====================
    
    /**
     * 添加操作
     */
    public static final String ACTION_ADD = "ADD";
    
    /**
     * 更新操作
     */
    public static final String ACTION_UPDATE = "UPDATE";
    
    /**
     * 删除操作
     */
    public static final String ACTION_DELETE = "DELETE";
    
    /**
     * 重新加载操作
     */
    public static final String ACTION_RELOAD = "RELOAD";
    
    // ==================== 错误消息模板 ====================
    
    /**
     * 配置为空错误消息
     */
    public static final String ERROR_CONFIG_NULL = "配置不能为空";
    
    /**
     * 配置验证失败错误消息
     */
    public static final String ERROR_CONFIG_INVALID = "配置验证失败: %s";
    
    /**
     * 客户端名称为空错误消息
     */
    public static final String ERROR_CLIENT_NAME_EMPTY = "客户端名称不能为空";
    
    /**
     * 消息ID为空错误消息
     */
    public static final String ERROR_MESSAGE_ID_EMPTY = "消息ID不能为空";
    
    /**
     * 操作类型无效错误消息
     */
    public static final String ERROR_INVALID_ACTION_TYPE = "无效的操作类型: %s";
    
    /**
     * 配置转换失败错误消息
     */
    public static final String ERROR_CONFIG_CONVERSION_FAILED = "配置转换失败: %s";
    
    /**
     * Redis连接失败错误消息
     */
    public static final String ERROR_REDIS_CONNECTION_FAILED = "Redis连接失败";
    
    /**
     * 分布式锁获取失败错误消息
     */
    public static final String ERROR_LOCK_ACQUISITION_FAILED = "分布式锁获取失败: %s";
    
    // ==================== 日志消息模板 ====================
    
    /**
     * 配置加载成功日志消息
     */
    public static final String LOG_CONFIG_LOADED = "成功加载MCP配置: 版本={}, 服务器数量={}";
    
    /**
     * 配置保存成功日志消息
     */
    public static final String LOG_CONFIG_SAVED = "成功保存MCP配置: 版本={}, 服务器数量={}";
    
    /**
     * 消息处理成功日志消息
     */
    public static final String LOG_MESSAGE_PROCESSED = "成功处理MCP消息: action={}, messageId={}";
    
    /**
     * 客户端操作成功日志消息
     */
    public static final String LOG_CLIENT_OPERATION_SUCCESS = "MCP客户端操作成功: action={}, clientName={}";
    
    // ==================== 配置验证规则 ====================
    
    /**
     * 最小客户端名称长度
     */
    public static final int MIN_CLIENT_NAME_LENGTH = 1;
    
    /**
     * 最大客户端名称长度
     */
    public static final int MAX_CLIENT_NAME_LENGTH = 100;
    
    /**
     * 最小超时时间（秒）
     */
    public static final int MIN_TIMEOUT_SECONDS = 1;
    
    /**
     * 最大超时时间（秒）
     */
    public static final int MAX_TIMEOUT_SECONDS = 3600;
    
    /**
     * 最大服务器数量
     */
    public static final int MAX_SERVER_COUNT = 100;
    
    // ==================== 工具方法 ====================
    
    /**
     * 构建分布式锁键名
     */
    public static String buildLockKey(String suffix) {
        if (suffix == null || suffix.trim().isEmpty()) {
            throw new IllegalArgumentException("Lock key suffix cannot be null or empty");
        }
        return LOCK_KEY_PREFIX + suffix;
    }
    
    /**
     * 构建客户端锁键名
     */
    public static String buildClientLockKey(String action, String clientName) {
        if (action == null || action.trim().isEmpty()) {
            throw new IllegalArgumentException("Action cannot be null or empty");
        }
        if (clientName == null || clientName.trim().isEmpty()) {
            throw new IllegalArgumentException("Client name cannot be null or empty");
        }
        return buildLockKey(String.format("%s:%s", action.toLowerCase(), clientName));
    }

    
    /**
     * 验证客户端名称
     */
    public static boolean isValidClientName(String clientName) {
        return clientName != null 
            && !clientName.trim().isEmpty() 
            && clientName.length() >= MIN_CLIENT_NAME_LENGTH 
            && clientName.length() <= MAX_CLIENT_NAME_LENGTH;
    }
    
    /**
     * 验证操作类型
     */
    public static boolean isValidActionType(String action) {
        return ACTION_ADD.equals(action) 
            || ACTION_UPDATE.equals(action) 
            || ACTION_DELETE.equals(action) 
            || ACTION_RELOAD.equals(action);
    }
    
    /**
     * 格式化错误消息
     */
    public static String formatError(String template, Object... args) {
        if (template == null) {
            return "Unknown error";
        }
        try {
            return String.format(template, args);
        } catch (Exception e) {
            return template;
        }
    }
}
