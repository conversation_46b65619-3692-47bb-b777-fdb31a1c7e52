package com.wormhole.agent.tool.mcp.event;

import com.wormhole.agent.tool.mcp.config.McpProperties;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * MCP客户端事件
 * 用于在客户端连接失败时通知服务管理层进行清理
 *
 * <AUTHOR>
 * @version 2025-08-01
 */
@Getter
public class McpClientEvent extends ApplicationEvent {

    /**
     * 事件类型
     */
    public enum EventType {
        /**
         * 连接失败
         */
        CONNECTION_FAILED,
        /**
         * 重试
         */
        RETRY,

        /**
         * 客户端异常
         */
        CLIENT_ERROR
    }

    /**
     * 客户端名称
     */
    private final String clientName;

    /**
     * 事件类型
     */
    private final EventType eventType;

    /**
     * 错误信息
     */
    private final String errorMessage;

    /**
     * 错误异常
     */
    private final Throwable cause;

    private McpProperties.McpClientConfig config;

    /**
     * 构造函数
     *
     * @param source       事件源
     * @param clientName   客户端名称
     * @param eventType    事件类型
     * @param errorMessage 错误信息
     */
    public McpClientEvent(Object source, String clientName, EventType eventType, String errorMessage) {
        this(source, clientName, eventType, null, errorMessage, null);
    }

    public McpClientEvent(Object source, String clientName, EventType eventType, McpProperties.McpClientConfig config, String errorMessage) {
        this(source, clientName, eventType, config, errorMessage, null);
    }

    /**
     * 构造函数（带异常）
     *
     * @param source       事件源
     * @param clientName   客户端名称
     * @param eventType    事件类型
     * @param errorMessage 错误信息
     * @param cause        异常原因
     */
    public McpClientEvent(Object source, String clientName, EventType eventType, McpProperties.McpClientConfig config, String errorMessage, Throwable cause) {
        super(source);
        this.clientName = clientName;
        this.eventType = eventType;
        this.config = config;
        this.errorMessage = errorMessage;
        this.cause = cause;
    }

    /**
     * 创建连接失败事件
     *
     * @param source       事件源
     * @param clientName   客户端名称
     * @param errorMessage 错误信息
     * @return 连接失败事件
     */
    public static McpClientEvent connectionFailed(Object source, String clientName, String errorMessage) {
        return new McpClientEvent(source, clientName, EventType.CONNECTION_FAILED, errorMessage);
    }

    /**
     * 创建重试事件
     *
     * @param source       事件源
     * @param clientName   客户端名称
     * @param errorMessage 错误信息
     * @return 连接重试事件
     */
    public static McpClientEvent retry(Object source, String clientName, McpProperties.McpClientConfig config, String errorMessage) {
        return new McpClientEvent(source, clientName, EventType.RETRY, config, errorMessage);
    }

    /**
     * 创建连接失败事件（带异常）
     *
     * @param source       事件源
     * @param clientName   客户端名称
     * @param errorMessage 错误信息
     * @param cause        异常原因
     * @return 连接失败事件
     */
    public static McpClientEvent connectionFailed(Object source, String clientName, String errorMessage, Throwable cause) {
        return new McpClientEvent(source, clientName, EventType.CONNECTION_FAILED, null, errorMessage, cause);
    }

    /**
     * 创建客户端异常事件
     *
     * @param source       事件源
     * @param clientName   客户端名称
     * @param errorMessage 错误信息
     * @return 客户端异常事件
     */
    public static McpClientEvent clientError(Object source, String clientName, String errorMessage) {
        return new McpClientEvent(source, clientName, EventType.CLIENT_ERROR, errorMessage);
    }

    /**
     * 创建客户端异常事件（带异常）
     *
     * @param source       事件源
     * @param clientName   客户端名称
     * @param errorMessage 错误信息
     * @param cause        异常原因
     * @return 客户端异常事件
     */
    public static McpClientEvent clientError(Object source, String clientName, String errorMessage, Throwable cause) {
        return new McpClientEvent(source, clientName, EventType.CLIENT_ERROR, null, errorMessage, cause);
    }

    @Override
    public String toString() {
        return String.format("McpClientEvent{clientName='%s', eventType=%s, errorMessage='%s'}",
                clientName, eventType, errorMessage);
    }
}