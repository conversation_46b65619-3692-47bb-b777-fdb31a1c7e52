package com.wormhole.agent.tool;

import com.wormhole.agent.ai.core.service.ChatClientService;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.util.ChatMessageUtils;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.log.metrics.PerformanceMonitorUtils;
import com.wormhole.agent.model.openai.*;
import com.wormhole.agent.tool.core.dispatcher.ToolDispatcher;
import com.wormhole.agent.tool.core.model.LlmInvocationRecord;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 工具链驱动器，负责执行工具链并处理递归调用。
 */
@Slf4j
@Service
public class ToolChainDriver {

    private static final String LOG_PREFIX = "[TOOL_CHAIN]";

    @Resource
    private ChatClientService chatClientService;
    @Resource
    private ToolDispatcher toolDispatcher;

    /**
     * 主执行入口，执行工具链。
     */
    public Mono<Map<String, Object>> executeToolChain(
            LlmInputs.IntentLlmParams intentLlmParams,
            List<OpenAiTool> tools,
            Map<String, Object> inputMap,
            ToolChainContext context) {
        long chainStart = System.currentTimeMillis();
        int currentDepth = context.getRecursionDepth();
        log.info("{} [ROUND: {}] [START] Tools: {}",
                LOG_PREFIX, currentDepth, tools);
        Integer maxRecursionDepth = intentLlmParams.getMaxRecursionDepth() + 1;
        if (currentDepth >= maxRecursionDepth) {
            log.info("{} [ROUND: {}] [MAX_DEPTH_REACHED]",
                    LOG_PREFIX, currentDepth);
            return Mono.just(context.getAccumulatedResults());
        }

        ModelContext modelContext = buildToolCallModelContext(intentLlmParams, tools, inputMap, context);
        log.info("{} [ROUND: {}] [MODEL_CONTEXT] Built context for {} inputMap: {}",
                LOG_PREFIX, currentDepth, modelContext.getOpenAiChatParams().getModel(), inputMap);

        return chatClientService.chatCompletions(modelContext)
                .timeout(Duration.ofMinutes(2))
                .collectList()
                .flatMap(completions -> {
                    long llmEndTime = System.currentTimeMillis();

                    // 记录响应
                    ChatCompletions lastCompletion = completions.get(completions.size() - 1);

                    LlmInvocationRecord record = LlmInvocationRecord.builder()
                            .timestamp(System.currentTimeMillis())
                            .modelName(modelContext.getOpenAiChatParams().getModel())
                            .modelProvider(modelContext.getOpenAiChatParams().getModelProvider())
                            .llmElapsedMs(llmEndTime - chainStart)
                            .llmEndTime(llmEndTime )
                            .llmStartTime(chainStart)
//                            .totalTokensUsed(lastCompletion.getUsage().getTotalTokens())
                            .input(modelContext.getOpenAiChatParams())
                            .output(lastCompletion)
                            .tokenUsage(lastCompletion.getUsage())
                            .recursionDepth(context.getRecursionDepth())
                            .success(lastCompletion.getError() == null)
                            .errorMessage(lastCompletion.getError() != null ?
                                    lastCompletion.getError().getMessage() : null)
                            .build();
                    PerformanceMonitorUtils.log(record);
                    context.addLlmInvocation(record);
                    context.setTotalElapsedMs(context.getTotalElapsedMs() + record.getLlmElapsedMs());
                    context.setTotalTokensUsed(context.getTotalTokensUsed() + record.getTotalTokensUsed());
                    log.info("{} [ROUND: {}] [LLM_RESPONSE] Received {} completions, lastInfo:{}",
                            LOG_PREFIX, currentDepth, completions.size(),JacksonUtils.writeValueAsString(lastCompletion));
                    List<ChatToolCall> toolCalls = extractToolCalls(completions, context);
                    if (CollectionUtils.isEmpty(toolCalls)) {
                        context.getAccumulatedResults().put("end",lastCompletion);
                        log.info("{} [ROUND: {}] [NO_TOOL_CALLS] Direct response", LOG_PREFIX, currentDepth);
                        return Mono.just(context.getAccumulatedResults());
                    }

                    log.info("{} [ROUND: {}] [TOOL_CALLS] Found {} tool calls: {}",
                            LOG_PREFIX, currentDepth, toolCalls.size(),
                            toolCalls.stream()
                                    .map(t -> t.getFunction().getName())
                                    .collect(Collectors.joining(", ")));

                    return executeToolCalls(toolCalls, inputMap, context, currentDepth)
                            .doOnSuccess(results -> {
                                long totalEndTime = System.currentTimeMillis();
                                // 更新总耗时
                                record.setTotalElapsedMs(totalEndTime - chainStart);
                                context.setTotalElapsedMs(context.getTotalElapsedMs() +
                                        (totalEndTime - llmEndTime));
                            })
                            .flatMap(results -> {
                                log.info("{} [ROUND: {}] [TOOL_RESULTS] Merged results: {}",
                                        LOG_PREFIX, currentDepth, results.keySet());

                                if (shouldContinueRecursion(context, results,maxRecursionDepth)) {
                                    context.incrementDepth();
                                    Map<String, Object> newInputs = inputMap != null ? new HashMap<>(inputMap) : new HashMap<>();
                                    newInputs.put(WorkflowConstant.INNER_OUTPUT,
                                            JacksonUtils.writeValueAsString(results));

                                    log.info("{} [ROUND: {}] [RECURSING] Depth increased to {}",
                                            LOG_PREFIX, currentDepth, context.getRecursionDepth());

                                    return executeToolChain(intentLlmParams, tools, newInputs, context);
                                }
                                log.info("{} [ROUND: {}] [TERMINATING] Final results", LOG_PREFIX, currentDepth);
                                return Mono.just(context.getAccumulatedResults());
                            });
                })
                .onErrorResume(e -> {
                    if (e instanceof TimeoutException) {
                        log.warn("{} [ROUND: {}] [TIMEOUT] Chat completion timeout after 2 minutes: {}", 
                                LOG_PREFIX, currentDepth, e.getMessage());
                    } else {
                        log.error("{} [ROUND: {}] [ERROR] {}", LOG_PREFIX, currentDepth, e.getMessage(), e);
                    }
                    return Mono.just(context.getAccumulatedResults());
                })
                .doFinally(signal ->
                        log.info("{} [ROUND: {}] [END] Signal: {}",
                                LOG_PREFIX, currentDepth, signal));
    }

    private List<ChatToolCall> extractToolCalls(List<ChatCompletions> completions, ToolChainContext context) {
        if (CollectionUtils.isEmpty(completions)) {
            log.info("{} [ROUND: {}] [NO_COMPLETIONS] Empty response", LOG_PREFIX, context.getRecursionDepth());
            return Collections.emptyList();
        }

        ChatCompletions lastCompletion = completions.get(completions.size() - 1);
        if (CollectionUtils.isEmpty(lastCompletion.getChoices())) {
            log.info("{} [ROUND: {}] [NO_CHOICES] In completion", LOG_PREFIX, context.getRecursionDepth());
            return Collections.emptyList();
        }

        ChatMessage assistantMessage = lastCompletion.getChoices().get(0).getMessage();
        if (assistantMessage == null || CollectionUtils.isEmpty(assistantMessage.getToolCalls())) {
            log.info("{} [ROUND: {}] [DIRECT_RESPONSE] No tool calls", LOG_PREFIX, context.getRecursionDepth());
            context.setLastDirectResponse(lastCompletion);
            return Collections.emptyList();
        }

        List<ChatToolCall> toolCalls = assistantMessage.getToolCalls().stream()
                .filter(toolCall -> toolCall.getFunction() != null)
                .map(toolCall -> {
                    if (toolCall.getIndex() == null) {
                        toolCall.setIndex(context.getNextToolIndex());
                    }
                    return toolCall;
                })
                .collect(Collectors.toList());

        updateConversationHistory(context, assistantMessage, toolCalls);
        return toolCalls;
    }

    private void updateConversationHistory(
            ToolChainContext context,
            ChatMessage assistantMessage,
            List<ChatToolCall> toolCalls) {

        OpenAiChatMessage newAssistantMessage = OpenAiChatMessage.builder()
                .role(ChatRole.ASSISTANT.getValue())
                .content(assistantMessage.getContent())
                .toolCalls(toolCalls)
                .build();

        context.getConversationHistory().add(newAssistantMessage);
        log.info("{} [ROUND: {}] [HISTORY_UPDATED] Added assistant message with {} tool calls",
                LOG_PREFIX, context.getRecursionDepth(), toolCalls.size());
    }

    private Mono<Map<String, Object>> executeToolCalls(
            List<ChatToolCall> toolCalls,
            Map<String, Object> inputMap,
            ToolChainContext context,
            int currentDepth) {

        return Flux.fromIterable(toolCalls)
                .flatMapSequential(toolCall -> {
                    long toolStart = System.currentTimeMillis();
                    String toolName = toolCall.getFunction().getName();

                    return toolDispatcher.dispatch(toolCall, inputMap, context)
                            .doOnSuccess(result -> {
                                long toolEnd = System.currentTimeMillis();

                                recordToolResponse(context, toolCall, result);
                                // 记录成功调用
                                LlmInvocationRecord.ToolInvocationDetail detail =
                                        LlmInvocationRecord.ToolInvocationDetail.builder()
                                                .toolName(toolName)
                                                .toolCall(toolCall)
                                                .input(inputMap != null ? new HashMap<>(inputMap) : new HashMap<>()) // 防止后续修改影响
                                                .output(result != null ? new HashMap<>(result) : new HashMap<>()) // 防止后续修改影响
                                                .elapsedMs(toolEnd - toolStart)
                                                .startTime(toolStart)
                                                .endTime(toolEnd)
                                                .timestamp(System.currentTimeMillis())
                                                .success(true)
                                                .build();
                                PerformanceMonitorUtils.log(detail);
                                context.addToolInvocation(detail);
                            })
                            .onErrorResume(e -> {
                                // 记录失败调用
                                long end = System.currentTimeMillis();
                                LlmInvocationRecord.ToolInvocationDetail detail =
                                        LlmInvocationRecord.ToolInvocationDetail.builder()
                                                .toolName(toolName)
                                                .toolCall(toolCall)
                                                .input(inputMap != null ? new HashMap<>(inputMap) : new HashMap<>())
                                                .output(null)
                                                .elapsedMs(end - toolStart)
                                                .startTime(toolStart)
                                                .endTime(end)
                                                .timestamp(System.currentTimeMillis())
                                                .success(false)
                                                .errorMessage(e.getMessage())
                                                .build();
                                PerformanceMonitorUtils.log(detail);
                                context.addToolInvocation(detail);
                                return handleToolError(toolCall, e, currentDepth);
                            });
                })
                .collectList()
                .map(this::mergeResults);
    }


    private void recordToolResponse(ToolChainContext context, ChatToolCall toolCall, Map<String, Object> result) {
        OpenAiChatMessage toolMessage = OpenAiChatMessage.builder()
                .role(ChatRole.TOOL.getValue())
                .toolCallId(toolCall.getId())
                .name(toolCall.getFunction().getName())
                .content(JacksonUtils.writeValueAsString(result))
                .build();

        context.getConversationHistory().add(toolMessage);
        log.info("{} [ROUND: {}] [TOOL_RESPONSE_RECORDED] Tool: {}",
                LOG_PREFIX, context.getRecursionDepth(), toolCall.getFunction().getName());
    }

    private boolean shouldContinueRecursion(ToolChainContext context, Map<String, Object> results,Integer maxRecursionDepth) {
        boolean shouldContinue = context.getRecursionDepth() < maxRecursionDepth &&
                !results.isEmpty() &&
                !results.containsKey("tool_error");

        log.info("{} [ROUND: {}] [RECURSION_CHECK] Continue: {} (Depth: {}, HasError: {})",
                LOG_PREFIX, context.getRecursionDepth(), shouldContinue, context.getRecursionDepth(),
                results.containsKey("tool_error"));

        return shouldContinue;
    }

    /**
     * 构建工具调用模型上下文。
     *
     * @param intentLlmParams LLM参数
     * @param tools 工具列表
     * @param inputMap 输入参数
     * @param context 工具链上下文
     * @return 模型上下文
     */
    private ModelContext buildToolCallModelContext(
            LlmInputs.IntentLlmParams intentLlmParams,
            List<OpenAiTool> tools,
            Map<String, Object> inputMap,
            ToolChainContext context) {

        List<OpenAiChatMessage> chatMessageList = new ArrayList<>();
        String systemPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(
                intentLlmParams.getSystemPrompt(), inputMap);
        if (StringUtils.isNotBlank(systemPrompt)) {
            OpenAiChatMessage systemMessage = OpenAiChatMessage.builder()
                    .role(ChatRole.SYSTEM.getValue())
                    .content(systemPrompt)
                    .build();

            chatMessageList.add(systemMessage);
        }



        if (intentLlmParams.isEnableChatHistory()) {
            int recentRound = intentLlmParams.getChatHistoryRound() == null ?
                    context.getRecentRound() : intentLlmParams.getChatHistoryRound();
            chatMessageList.addAll(ChatMessageUtils.getRecentMessageList(
                    context.getRecentMessageList(), recentRound));
        }
        String userPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(
                intentLlmParams.getUserPrompt(), inputMap);
        OpenAiChatMessage userMessage = OpenAiChatMessage.builder()
                .role(ChatRole.USER.getValue())
                .content(userPrompt)
                .build();
        chatMessageList.add(userMessage);

        if (CollectionUtils.isNotEmpty(context.getConversationHistory())) {
            chatMessageList.addAll(context.getConversationHistory());
        }

        OpenAiChatParams openAiChatParams = OpenAiChatParams.builder()
                .stream(false)
                .model(intentLlmParams.getModel())
                .modelProvider(intentLlmParams.getModelProvider())
                .temperature(0.1d)
                .messages(chatMessageList)
                .tools(tools)
                .build();

        return ModelContext.builder()
                .openAiChatParams(openAiChatParams)
                .modelLogContext(ModelContext.ModelLogContext.builder().build())
                .build();
    }

    private Mono<Map<String, Object>> handleToolError(ChatToolCall toolCall, Throwable e, int currentDepth) {
        String toolName = toolCall.getFunction().getName();
        log.warn("{} [ROUND: {}] [TOOL_ERROR_HANDLED] Tool: {}, Error: {}",
                LOG_PREFIX, currentDepth, toolName, e.getMessage());
        return Mono.just(Map.of(
                "tool_error", toolName,
                "message", e.getMessage(),
                "stack_trace", Arrays.toString(e.getStackTrace())
        ));
    }

    private Map<String, Object> mergeResults(List<Map<String, Object>> results) {
        return results.stream()
                .flatMap(map -> map.entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> replacement
                ));
    }
}