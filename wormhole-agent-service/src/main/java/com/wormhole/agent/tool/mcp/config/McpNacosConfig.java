package com.wormhole.agent.tool.mcp.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * MCP Nacos配置结构
 * 定义存储在Nacos配置中心的MCP配置格式
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class McpNacosConfig {
    
    /**
     * 配置版本
     */
    private String version = "1.0";
    
    /**
     * 配置创建时间
     */
    @JsonProperty("created_at")
    private Instant createdAt;
    
    /**
     * 配置更新时间
     */
    @JsonProperty("updated_at")
    private Instant updatedAt;
    
    /**
     * 配置描述
     */
    private String description;
    
    /**
     * 是否启用MCP功能
     */
    private boolean enabled = true;
    
    /**
     * 默认超时时间（秒）
     */
    @JsonProperty("default_timeout")
    private Integer defaultTimeout = 30;
    
    /**
     * MCP客户端配置列表
     */
    private List<McpClientConfigItem> clients;
    
    /**
     * 全局配置元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * MCP客户端配置项
     * 扩展了基础配置，增加了Nacos特有的字段
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class McpClientConfigItem {
        
        /**
         * 配置项ID（用于Nacos配置管理）
         */
        private String id;
        
        /**
         * 客户端名称
         */
        private String name;
        
        /**
         * 传输方式
         */
        private String transport;
        
        /**
         * 命令列表（stdio）
         */
        private List<String> command;
        
        /**
         * 服务器URL（sse）
         */
        private String url;
        
        /**
         * 超时时间
         */
        private Integer timeout;
        
        /**
         * 日志配置
         */
        @JsonProperty("log_events")
        private Boolean logEvents = false;
        
        @JsonProperty("log_requests")
        private Boolean logRequests = false;
        
        @JsonProperty("log_responses")
        private Boolean logResponses = false;
        
        /**
         * 是否启用
         */
        private Boolean enabled = true;
        
        /**
         * 配置版本
         */
        private String version;
        
        /**
         * 创建时间
         */
        @JsonProperty("created_at")
        private Instant createdAt;
        
        /**
         * 更新时间
         */
        @JsonProperty("updated_at")
        private Instant updatedAt;

        
        /**
         * 配置状态
         */
        private String status = "active";
        
        /**
         * 元数据
         */
        private Map<String, Object> metadata;
        
        /**
         * 配置标签（用于分类和过滤）
         */
        private List<String> tags;
        
        /**
         * 配置优先级（数字越小优先级越高）
         */
        private Integer priority = 100;
        
        /**
         * 转换为标准McpClientConfig
         */
        public McpProperties.McpClientConfig toMcpClientConfig() {
            return McpProperties.McpClientConfig.builder()
                .name(this.name)
                .transport(this.transport)
                .command(this.command)
                .url(this.url)
                .timeout(this.timeout)
                .logEvents(this.logEvents != null ? this.logEvents : false)
                .logRequests(this.logRequests != null ? this.logRequests : false)
                .logResponses(this.logResponses != null ? this.logResponses : false)
                .enabled(this.enabled != null ? this.enabled : true)
                .version(this.version)
                .createdAt(this.createdAt)
                .updatedAt(this.updatedAt)
                .status(this.status)
                .metadata(this.metadata)
                .build();
        }
        
        /**
         * 从标准McpClientConfig创建
         */
        public static McpClientConfigItem fromMcpClientConfig(McpProperties.McpClientConfig config) {
            return McpClientConfigItem.builder()
                .id(generateId(config.getName()))
                .name(config.getName())
                .transport(config.getTransport())
                .command(config.getCommand())
                .url(config.getUrl())
                .timeout(config.getTimeout())
                .logEvents(config.isLogEvents())
                .logRequests(config.isLogRequests())
                .logResponses(config.isLogResponses())
                .enabled(config.isEnabled())
                .version(config.getVersion())
                .createdAt(config.getCreatedAt())
                .updatedAt(config.getUpdatedAt())
                .status(config.getStatus())
                .metadata(config.getMetadata())
                .build();
        }
        
        /**
         * 生成配置项ID
         */
        private static String generateId(String name) {
            return "mcp_client_" + name + "_" + System.currentTimeMillis();
        }
        
        /**
         * 验证配置有效性
         */
        public boolean isValid() {
            if (name == null || name.trim().isEmpty()) {
                return false;
            }
            if (transport == null || (!transport.equals("stdio") && !transport.equals("sse"))) {
                return false;
            }
            if ("stdio".equals(transport) && (command == null || command.isEmpty())) {
                return false;
            }
            if ("sse".equals(transport) && (url == null || url.trim().isEmpty())) {
                return false;
            }
            return true;
        }
    }
    
    /**
     * 转换为McpProperties格式
     */
    public McpProperties toMcpProperties() {
        McpProperties properties = new McpProperties();
        properties.setEnabled(this.enabled);
        properties.setDefaultTimeout(this.defaultTimeout != null ? this.defaultTimeout : 30);
        
        if (this.clients != null) {
            List<McpProperties.McpClientConfig> clientConfigs = this.clients.stream()
                .map(McpClientConfigItem::toMcpClientConfig)
                .collect(java.util.stream.Collectors.toList());
            properties.setClients(clientConfigs);
        }
        
        return properties;
    }
    
    /**
     * 从McpProperties创建
     */
    public static McpNacosConfig fromMcpProperties(McpProperties properties) {
        McpNacosConfigBuilder builder = McpNacosConfig.builder()
            .version("1.0")
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .enabled(properties.isEnabled())
            .defaultTimeout(properties.getDefaultTimeout());
        
        if (properties.getClients() != null) {
            List<McpClientConfigItem> clientItems = properties.getClients().stream()
                .map(McpClientConfigItem::fromMcpClientConfig)
                .collect(java.util.stream.Collectors.toList());
            builder.clients(clientItems);
        }
        
        return builder.build();
    }
    
    /**
     * 获取启用的客户端配置
     */
    public List<McpClientConfigItem> getEnabledClients() {
        if (clients == null) {
            return java.util.Collections.emptyList();
        }
        
        return clients.stream()
            .filter(client -> client.getEnabled() != null && client.getEnabled())
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 按优先级排序的客户端配置
     */
    public List<McpClientConfigItem> getClientsByPriority() {
        if (clients == null) {
            return java.util.Collections.emptyList();
        }
        
        return clients.stream()
            .sorted((a, b) -> {
                int priorityA = a.getPriority() != null ? a.getPriority() : 100;
                int priorityB = b.getPriority() != null ? b.getPriority() : 100;
                return Integer.compare(priorityA, priorityB);
            })
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 验证整个配置的有效性
     */
    public boolean isValid() {
        if (clients == null || clients.isEmpty()) {
            return false;
        }
        
        return clients.stream().allMatch(McpClientConfigItem::isValid);
    }
}
