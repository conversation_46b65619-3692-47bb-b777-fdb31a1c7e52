package com.wormhole.agent.tool.mcp.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 标准MCP配置格式（VS Code/Claude Desktop风格）
 * 用于支持标准MCP JSON配置格式的解析和转换
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class StandardMcpConfig {
    
    /**
     * MCP服务器配置映射
     * 键为服务器名称，值为服务器配置
     */
    private Map<String, StandardMcpServer> mcpServers;
    
    /**
     * 输入变量定义（可选）
     */
    private List<InputVariable> inputs;
    
    /**
     * 标准MCP服务器配置
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StandardMcpServer {
        
        /**
         * 连接类型：stdio, sse, http
         */
        private String type;
        
        /**
         * 服务器URL（用于sse/http类型）
         */
        private String url;
        
        /**
         * 执行命令（用于stdio类型）
         */
        private String command;
        
        /**
         * 命令参数（用于stdio类型）
         * 支持多种格式的自动转换：数组格式、字符串格式
         */
        @JsonDeserialize(using = ArgsDeserializer.class)
        private List<String> args;
        
        /**
         * 环境变量
         */
        private Map<String, String> env;
        
        /**
         * 环境变量文件路径
         */
        @JsonProperty("envFile")
        private String envFile;
        
        /**
         * HTTP请求头（用于sse/http类型）
         */
        private Map<String, String> headers;
        
        /**
         * 超时时间（秒）
         */
        private Integer timeout;
        
        /**
         * 是否启用
         */
        private Boolean enabled = true;
        
        /**
         * 验证配置的完整性
         */
        @JsonIgnore
        public boolean isValid() {
            if (type == null) {
                return false;
            }
            
            switch (type.toLowerCase()) {
                case "stdio":
                    return command != null && !command.trim().isEmpty();
                case "sse":
                case "http":
                    return url != null && !url.trim().isEmpty();
                default:
                    return false;
            }
        }
        
        /**
         * 构建完整的命令列表（包含命令和参数）
         */
        public List<String> buildFullCommand() {
            if (!"stdio".equals(type) || command == null) {
                return null;
            }
            
            List<String> fullCommand = new java.util.ArrayList<>();
            fullCommand.add(command);
            if (args != null && !args.isEmpty()) {
                fullCommand.addAll(args);
            }
            return fullCommand;
        }
    }
    
    /**
     * 输入变量定义
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class InputVariable {
        
        /**
         * 变量类型
         */
        private String type;

        /**
         * 变量ID
         */
        private String id;
        
        /**
         * 变量描述
         */
        private String description;
        
        /**
         * 是否为密码类型
         */
        private Boolean password = false;
        
        /**
         * 默认值
         */
        @JsonProperty("default")
        private String defaultValue;
    }
    
    /**
     * 验证整个配置的有效性
     */
    @JsonIgnore
    public boolean isValid() {
        if (mcpServers == null || mcpServers.isEmpty()) {
            return false;
        }
        
        return mcpServers.values().stream().allMatch(StandardMcpServer::isValid);
    }
    
    /**
     * 获取所有服务器名称
     */
    @JsonIgnore
    public java.util.Set<String> getServerNames() {
        return mcpServers != null ? mcpServers.keySet() : java.util.Collections.emptySet();
    }
    
    /**
     * 获取启用的服务器配置
     */
    @JsonIgnore
    public Map<String, StandardMcpServer> getEnabledServers() {
        if (mcpServers == null) {
            return java.util.Collections.emptyMap();
        }
        
        return mcpServers.entrySet().stream()
            .filter(entry -> entry.getValue().getEnabled() != null && entry.getValue().getEnabled())
            .collect(java.util.stream.Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue
            ));
    }
    
    /**
     * args字段的自定义反序列化器
     * 支持多种格式的自动转换：
     * 1. 标准数组格式：["arg1", "arg2"]
     * 2. 字符串数组格式："['arg1', 'arg2']"
     * 3. 单个字符串格式："arg1"
     */
    @Slf4j
    public static class ArgsDeserializer extends JsonDeserializer<List<String>> {
        
        // 匹配类似 ['arg1', 'arg2'] 的格式
        private static final Pattern STRING_ARRAY_PATTERN = Pattern.compile("\\['([^']*)'(?:\\s*,\\s*'([^']*)')*\\]");
        // 匹配类似 ["arg1", "arg2"] 的格式  
        private static final Pattern QUOTED_ARRAY_PATTERN = Pattern.compile("\\[\"([^\"]*)\"(?:\\s*,\\s*\"([^\"]*)\")*\\]");
        
        @Override
        public List<String> deserialize(JsonParser parser, DeserializationContext context) throws IOException {
            JsonNode node = parser.getCodec().readTree(parser);
            
            if (node == null || node.isNull()) {
                return null;
            }
            
            // 情况1：标准数组格式
            if (node.isArray()) {
                List<String> result = new ArrayList<>();
                for (JsonNode element : node) {
                    if (element.isTextual()) {
                        result.add(element.asText());
                    } else {
                        result.add(element.toString());
                    }
                }
                log.debug("成功解析标准数组格式args: {}", result);
                return result;
            }
            
            // 情况2：字符串格式，需要解析
            if (node.isTextual()) {
                String text = node.asText().trim();
                
                // 尝试解析类似 "['--ignore-robots-txt']" 的格式
                if (text.startsWith("[") && text.endsWith("]")) {
                    List<String> result = parseStringArray(text);
                    if (result != null) {
                        log.info("成功转换字符串数组格式args: {} -> {}", text, result);
                        return result;
                    }
                }
                
                // 单个字符串参数
                if (!text.isEmpty()) {
                    List<String> result = List.of(text);
                    log.debug("解析单个字符串参数: {}", result);
                    return result;
                }
            }
            
            log.warn("无法解析args字段，使用空列表: {}", node);
            return new ArrayList<>();
        }
        
        /**
         * 解析字符串格式的数组
         * 支持：['arg1', 'arg2'] 和 ["arg1", "arg2"] 格式
         */
        private List<String> parseStringArray(String text) {
            try {
                // 尝试使用标准JSON解析
                if (text.startsWith("[\"") || text.equals("[]")) {
                    TypeReference<List<String>> typeRef = new TypeReference<List<String>>() {};
                    return com.wormhole.common.util.JacksonUtils.readValue(text, typeRef);
                }
                
                // 解析单引号格式：['arg1', 'arg2']
                if (text.startsWith("['")) {
                    Matcher matcher = STRING_ARRAY_PATTERN.matcher(text);
                    if (matcher.matches()) {
                        List<String> result = new ArrayList<>();
                        // 提取所有匹配的参数
                        String content = text.substring(2, text.length() - 2); // 去掉 [' 和 ']
                        String[] parts = content.split("'\\s*,\\s*'");
                        for (String part : parts) {
                            if (!part.trim().isEmpty()) {
                                result.add(part.trim());
                            }
                        }
                        return result;
                    }
                }
                
                // 简单的split处理，以逗号分隔
                String content = text.substring(1, text.length() - 1).trim(); // 去掉 [ ]
                if (content.isEmpty()) {
                    return new ArrayList<>();
                }
                
                List<String> result = new ArrayList<>();
                String[] parts = content.split(",");
                for (String part : parts) {
                    String cleaned = part.trim().replaceAll("^['\"]|['\"]$", ""); // 去掉首尾引号
                    if (!cleaned.isEmpty()) {
                        result.add(cleaned);
                    }
                }
                return result;
                
            } catch (Exception e) {
                log.warn("解析字符串数组格式失败: {}, 错误: {}", text, e.getMessage());
                return null;
            }
        }
    }
}
