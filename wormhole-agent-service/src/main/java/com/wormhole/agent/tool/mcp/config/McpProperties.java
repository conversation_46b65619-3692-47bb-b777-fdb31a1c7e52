package com.wormhole.agent.tool.mcp.config;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.util.ObjectMapperSupport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * MCP配置属性类
 * 注意：JSON文件读取功能已移除，现在从Redis读取配置
 * 保留此类作为数据模型使用
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@Slf4j
@Data
@Component
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class McpProperties {

    @JsonIgnore
    private final ObjectMapper objectMapper = ObjectMapperSupport.mapper();
    
    /**
     * 是否启用MCP功能
     */
    private boolean enabled = false;

    /**
     * 默认工具执行超时时间（秒）
     */
    @JsonProperty("default_timeout")
    private int defaultTimeout = 30;

    /**
     * MCP客户端配置列表
     */
    private List<McpClientConfig> clients = new ArrayList<>();

    /**
     * JSON配置文件路径
     */
    private static final String CONFIG_FILE_PATH = "mcp-config.json";

    /**
     * 初始化方法，从JSON文件加载配置
     * 注意：JSON读取功能已移除，现在从Redis读取配置
     */
    /*
    @PostConstruct
    public void init() {
        loadConfigFromJson();
    }
    */

    /**
     * 从JSON文件加载MCP配置
     * 注意：此方法已被注释，不再从JSON文件读取配置
     */
    /*
    private void loadConfigFromJson() {
        try {
            ClassPathResource resource = new ClassPathResource(CONFIG_FILE_PATH);
            if (!resource.exists()) {
                log.warn("MCP配置文件不存在：{}，使用默认配置", CONFIG_FILE_PATH);
                return;
            }

            try (InputStream inputStream = resource.getInputStream()) {
                McpProperties config = objectMapper.readValue(inputStream, McpProperties.class);

                // 复制配置到当前实例
                this.enabled = config.enabled;
                this.defaultTimeout = config.defaultTimeout;
                this.clients = config.clients != null ? config.clients : new ArrayList<>();

                log.info("成功从JSON文件加载MCP配置，启用状态：{}，客户端数量：{}",
                        this.enabled, this.clients.size());
            }

        } catch (IOException e) {
            log.error("加载MCP配置文件失败：{}", CONFIG_FILE_PATH, e);
            // 使用默认配置
            this.enabled = false;
            this.clients = new ArrayList<>();
        }
    }
    */
    
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class McpClientConfig {

        /**
         * 客户端名称（唯一标识）
         */
        private String name;
        
        /**
         * 传输方式：stdio 或 sse
         */
        private String transport;
        
        /**
         * STDIO传输：执行命令
         */
        private List<String> command;
        
        /**
         * SSE传输：服务器URL
         */
        private String url;
        
        /**
         * 工具执行超时时间（秒）
         */
        private Integer timeout;
        
        /**
         * 是否启用事件日志
         */
        private boolean logEvents = true;
        
        /**
         * 是否启用请求日志
         */
        private boolean logRequests = true;
        
        /**
         * 是否启用响应日志
         */
        private boolean logResponses = true;
        
        /**
         * 是否启用此客户端
         */
        private boolean enabled = true;

        private String description;

        /**
         * 元数据信息（用于扩展配置）
         */
        private Map<String, Object> metadata;

        /**
         * 配置版本（用于版本控制）
         */
        private String version;

        /**
         * 创建时间
         */
        @JsonProperty("created_at")
        private Instant createdAt;

        /**
         * 更新时间
         */
        @JsonProperty("updated_at")
        private Instant updatedAt;

        /**
         * 配置来源（nacos/local/api）
         */
        private String source = "local";

        /**
         * 配置状态（active/inactive/error）
         */
        private String status = "active";

        /**
         * 获取超时时间Duration对象
         */
        public Duration getTimeoutDuration() {
            return Duration.ofSeconds(timeout != null ? timeout : 30);
        }

        /**
         * 设置创建时间（如果未设置）
         */
        public void ensureCreatedAt() {
            if (this.createdAt == null) {
                this.createdAt = Instant.now();
            }
        }

        /**
         * 更新修改时间
         */
        public void updateModifiedAt() {
            this.updatedAt = Instant.now();
        }

        /**
         * 验证配置的完整性
         */
        public boolean isValid() {
            if (name == null || name.trim().isEmpty()) {
                return false;
            }
            if (transport == null || (!transport.equals("stdio") && !transport.equals("sse"))) {
                return false;
            }
            if ("stdio".equals(transport) && (command == null || command.isEmpty())) {
                return false;
            }
            if ("sse".equals(transport) && (url == null || url.trim().isEmpty())) {
                return false;
            }
            return true;
        }
    }
}
