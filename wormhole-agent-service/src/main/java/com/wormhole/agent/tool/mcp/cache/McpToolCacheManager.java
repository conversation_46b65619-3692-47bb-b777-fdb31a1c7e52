package com.wormhole.agent.tool.mcp.cache;

import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.service.GenericRedisService;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import javax.annotation.PreDestroy;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * MCP工具多级缓存管理器
 * 实现L1本地内存缓存和L2 Redis分布式缓存
 * 支持缓存预热、失效策略、缓存一致性保证
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class McpToolCacheManager {
    
    private final GenericRedisService redisService;
    
    /**
     * 缓存键前缀，遵循项目命名规范
     */
    private static final String CACHE_KEY_PREFIX = "MCP_TOOLS:";
    
    /**
     * 默认缓存过期时间（秒）- 1小时
     */
    private static final long DEFAULT_CACHE_EXPIRE_SECONDS = 3600L;
    
    /**
     * L1本地内存缓存：客户端名称 -> 工具列表
     */
    private final ConcurrentHashMap<String, List<OpenAiTool>> localCache = new ConcurrentHashMap<>();
    
    /**
     * 获取工具列表（三级查询策略）
     * L1本地缓存 -> L2 Redis缓存 -> 返回空列表
     * 
     * @param clientName 客户端名称
     * @return 工具列表的Mono
     */
    public Mono<List<OpenAiTool>> getTools(String clientName) {
        if (clientName == null || clientName.trim().isEmpty()) {
            return Mono.just(Collections.emptyList());
        }
        
        return Mono.fromCallable(() -> {
            // L1缓存查询
            List<OpenAiTool> tools = localCache.get(clientName);
            if (tools != null) {
                log.debug("L1缓存命中: {}, 工具数: {}", clientName, tools.size());
                return tools;
            }
            
            log.debug("L1缓存未命中: {}", clientName);
            return null;
        })
        .flatMap(tools -> {
            if (tools != null) {
                return Mono.just(tools);
            }
            
            // L2缓存查询
            return getToolsFromRedis(clientName)
                .doOnNext(redisTools -> {
                    if (!redisTools.isEmpty()) {
                        // 回写L1缓存
                        localCache.put(clientName, redisTools);
                        log.debug("L2缓存命中并回写L1: {}, 工具数: {}", clientName, redisTools.size());
                    }
                });
        })
        .switchIfEmpty(Mono.just(Collections.emptyList()))
        .subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 存储工具列表（双写策略）
     * 同时写入L1本地缓存和L2 Redis缓存
     * 
     * @param clientName 客户端名称
     * @param tools 工具列表
     * @return 存储结果的Mono
     */
    public Mono<Boolean> putTools(String clientName, List<OpenAiTool> tools) {
        if (clientName == null || clientName.trim().isEmpty()) {
            return Mono.just(false);
        }
        
        if (tools == null) {
            tools = Collections.emptyList();
        }
        
        final List<OpenAiTool> finalTools = tools;
        
        return Mono.fromCallable(() -> {
            // 写入L1缓存
            localCache.put(clientName, finalTools);
            log.debug("L1缓存写入: {}, 工具数: {}", clientName, finalTools.size());
            return true;
        })
        .flatMap(success -> {
            // 写入L2缓存
            return putToolsToRedis(clientName, finalTools)
                .doOnNext(redisSuccess -> {
                    if (redisSuccess) {
                        log.debug("L2缓存写入成功: {}, 工具数: {}", clientName, finalTools.size());
                    } else {
                        log.warn("L2缓存写入失败: {}", clientName);
                    }
                });
        })
        .onErrorResume(error -> {
            log.error("缓存写入异常: {}", clientName, error);
            return Mono.just(false);
        })
        .subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 失效指定客户端的缓存
     * 
     * @param clientName 客户端名称
     * @return 失效结果的Mono
     */
    public Mono<Boolean> evictTools(String clientName) {
        if (clientName == null || clientName.trim().isEmpty()) {
            return Mono.just(false);
        }
        
        return Mono.fromCallable(() -> {
            // 清除L1缓存
            List<OpenAiTool> removed = localCache.remove(clientName);
            log.debug("L1缓存清除: {}, 原工具数: {}", clientName, removed != null ? removed.size() : 0);
            return true;
        })
        .flatMap(success -> {
            // 清除L2缓存
            return deleteToolsFromRedis(clientName)
                .doOnNext(redisSuccess -> {
                    if (redisSuccess) {
                        log.debug("L2缓存清除成功: {}", clientName);
                    } else {
                        log.warn("L2缓存清除失败: {}", clientName);
                    }
                });
        })
        .onErrorResume(error -> {
            log.error("缓存清除异常: {}", clientName, error);
            return Mono.just(false);
        })
        .subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 清空所有缓存
     * 
     * @return 清空结果的Mono
     */
    public Mono<Boolean> clearAllCache() {
        return Mono.fromCallable(() -> {
            // 清空L1缓存
            int localSize = localCache.size();
            localCache.clear();
            log.info("L1缓存全部清空，原缓存客户端数: {}", localSize);
            return true;
        })
        .flatMap(success -> {
            // 清空L2缓存（通过模式匹配删除）
            return clearAllRedisCache()
                .doOnNext(redisSuccess -> {
                    if (redisSuccess) {
                        log.info("L2缓存全部清空成功");
                    } else {
                        log.warn("L2缓存全部清空失败");
                    }
                });
        })
        .onErrorResume(error -> {
            log.error("清空所有缓存异常", error);
            return Mono.just(false);
        })
        .subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 预热指定客户端的缓存
     * 
     * @param clientName 客户端名称
     * @param tools 工具列表
     * @return 预热结果的Mono
     */
    public Mono<Boolean> preloadCache(String clientName, List<OpenAiTool> tools) {
        log.info("开始预热缓存: {}, 工具数: {}", clientName, tools != null ? tools.size() : 0);
        
        return putTools(clientName, tools)
            .doOnSuccess(success -> {
                if (success) {
                    log.info("缓存预热成功: {}", clientName);
                } else {
                    log.warn("缓存预热失败: {}", clientName);
                }
            });
    }
    
    /**
     * 从Redis获取工具列表
     */
    private Mono<List<OpenAiTool>> getToolsFromRedis(String clientName) {
        String key = buildCacheKey(clientName);

        return redisService.getValue(key)
            .flatMap(json -> {
                if (json == null || json.trim().isEmpty()) {
                    log.debug("L2缓存未命中: {}", clientName);
                    return Mono.just(Collections.<OpenAiTool>emptyList());
                }

                try {
                    List<OpenAiTool> tools = JacksonUtils.readValues(json, OpenAiTool.class);
                    return Mono.just(tools != null ? tools : Collections.<OpenAiTool>emptyList());
                } catch (Exception e) {
                    log.warn("L2缓存反序列化失败: {}", clientName, e);
                    return Mono.just(Collections.<OpenAiTool>emptyList());
                }
            })
            .onErrorResume(error -> {
                log.warn("L2缓存查询异常: {}", clientName, error);
                return Mono.just(Collections.emptyList());
            });
    }

    /**
     * 向Redis存储工具列表
     */
    private Mono<Boolean> putToolsToRedis(String clientName, List<OpenAiTool> tools) {
        String key = buildCacheKey(clientName);
        
        try {
            String json = JacksonUtils.writeValueAsString(tools);
            return redisService.setValue(key, json, DEFAULT_CACHE_EXPIRE_SECONDS);
        } catch (Exception e) {
            log.warn("L2缓存序列化失败: {}", clientName, e);
            return Mono.just(false);
        }
    }
    
    /**
     * 从Redis删除工具列表
     */
    private Mono<Boolean> deleteToolsFromRedis(String clientName) {
        String key = buildCacheKey(clientName);
        return redisService.deleteKey(key);
    }
    
    /**
     * 清空所有Redis缓存
     */
    private Mono<Boolean> clearAllRedisCache() {
        String pattern = CACHE_KEY_PREFIX + "*";
        
        return redisService.findKeys(pattern)
            .collectList()
            .flatMap(keys -> {
                if (keys.isEmpty()) {
                    return Mono.just(true);
                }
                
                log.info("找到{}个缓存键需要清除", keys.size());
                
                // 批量删除
                return Mono.fromCallable(() -> {
                    for (String key : keys) {
                        redisService.deleteKey(key).subscribe();
                    }
                    return true;
                }).subscribeOn(Schedulers.boundedElastic());
            });
    }
    
    /**
     * 构建缓存键
     */
    private String buildCacheKey(String clientName) {
        return CACHE_KEY_PREFIX + clientName;
    }
    
    /**
     * 获取缓存统计信息
     */
    public Mono<CacheStats> getCacheStats() {
        return Mono.fromCallable(() -> {
            int localCacheSize = localCache.size();
            int totalLocalTools = localCache.values().stream()
                .mapToInt(List::size)
                .sum();
            
            return CacheStats.builder()
                .localCacheSize(localCacheSize)
                .totalLocalTools(totalLocalTools)
                .build();
        }).subscribeOn(Schedulers.boundedElastic());
    }
    
    /**
     * 销毁时清理资源
     */
    @PreDestroy
    public void destroy() {
        log.info("开始清理MCP工具缓存管理器");
        localCache.clear();
        log.info("MCP工具缓存管理器清理完成");
    }
}
