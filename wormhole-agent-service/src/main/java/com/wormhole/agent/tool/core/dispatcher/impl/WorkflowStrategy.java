package com.wormhole.agent.tool.core.dispatcher.impl;

import com.wormhole.agent.model.openai.ChatFunctionCall;
import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.tool.core.dispatcher.ToolExecutionStrategy;
import com.wormhole.agent.tool.core.dispatcher.ToolStrategy;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.core.model.ToolType;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.TerminatePlanEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.WorkflowRequestSourceEnum;
import com.wormhole.agent.workflow.service.WorkflowService;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 工作流策略实现。
 */
@ToolStrategy(ToolType.WORKFLOW)
@Component
@RequiredArgsConstructor
public class WorkflowStrategy implements ToolExecutionStrategy {

    private final WorkflowService workflowService;


    /**
     * 执行工具调用。
     *
     * @param toolCall 工具调用
     * @param inputMap 输入参数
     * @param context 工具链上下文
     * @return 执行结果
     */
    @Override
    public Mono<Map<String, Object>> execute(ChatToolCall toolCall, Map<String, Object> inputMap, ToolChainContext context) {
        WorkflowContext wfContext = buildWorkflowContext(toolCall, inputMap,context);
        return workflowService.executeWorkflow(wfContext)
                .map(this::extractWorkflowOutput)
                .onErrorResume(e -> Mono.just(Collections.singletonMap("error", e.getMessage())));
    }

    /**
     * 构建工作流上下文。
     *
     * @param toolCall 工具调用
     * @param inputMap 输入参数
     * @return 工作流上下文
     */
    private WorkflowContext buildWorkflowContext(ChatToolCall toolCall, Map<String, Object> inputMap,ToolChainContext context) {
        Map<String, String> originCodeMap = context.getOriginCodeMap();
        ChatFunctionCall function = toolCall.getFunction();
        Map<String, Object> argsMap = Optional.ofNullable(function.getArguments())
                .map(JacksonUtils::readValue)
                .orElse(new HashMap<>());

        if (inputMap != null) {
            argsMap.putAll(inputMap);
        }

        return WorkflowContext.builder()
                .workflowCode(originCodeMap.get(function.getName()))
                .initialInput(argsMap)
                .workflowRequestSource(WorkflowRequestSourceEnum.FUNCTION)
                .chatToolCall(toolCall)
                .sinks(context.getSinks())
                .build();
    }

    /**
     * 提取工作流输出。
     *
     * @param context 工作流上下文
     * @return 输出结果
     */
    private Map<String, Object> extractWorkflowOutput(WorkflowContext context) {
        Map<String, Object> output = WorkflowUtils.getWorkflowOutput(context);
        if (TerminatePlanEnum.RETURN_VARIABLES.equals(context.getTerminatePlan())) {
            return output;
        }
        return (Map<String, Object>) MapUtils.getMap(output, WorkflowConstant.INNER_OUTPUT);
    }
}
