package com.wormhole.agent.tool.core.dispatcher;

import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.core.model.ToolType;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.EnumMap;
import java.util.Map;
import java.util.Optional;

/**
 * 工具调度器，负责分发工具调用。
 */
@Component
public class ToolDispatcher {

    private final EnumMap<ToolType, ToolExecutionStrategy> strategyMap;

    /**
     * 构造函数，初始化策略映射。
     *
     * @param strategies 策略实现列表
     */
    public ToolDispatcher(Map<ToolType, ToolExecutionStrategy> strategies) {
        this.strategyMap = new EnumMap<>(strategies);
    }

    /**
     * 分发工具调用。
     *
     * @param toolCall 工具调用
     * @param inputMap 输入参数
     * @param context 工具链上下文
     * @return 执行结果
     */
    public Mono<Map<String, Object>> dispatch(ChatToolCall toolCall, Map<String, Object> inputMap, ToolChainContext context) {
        ToolType type = context.getToolTypeMap().get(toolCall.getFunction().getName());
        return Optional.ofNullable(strategyMap.get(type))
                .map(strategy -> strategy.execute(toolCall, inputMap, context))
                .orElseGet(() -> fallbackExecute(toolCall));
    }

    /**
     * 处理不支持的工具类型。
     *
     * @param toolCall 工具调用
     * @return 错误信息
     */
    private Mono<Map<String, Object>> fallbackExecute(ChatToolCall toolCall) {
        return Mono.just(Map.of(
                "error", "Unsupported tool type: " + toolCall.getFunction().getName()
        ));
    }
}
