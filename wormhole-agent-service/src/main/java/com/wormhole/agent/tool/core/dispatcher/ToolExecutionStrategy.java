package com.wormhole.agent.tool.core.dispatcher;

import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 工具执行策略接口。
 */
public interface ToolExecutionStrategy {




    /**
     * 执行工具调用。
     *
     * @param toolCall 工具调用
     * @param inputMap 输入参数
     * @param context 工具链上下文
     * @return 执行结果
     */
    Mono<Map<String, Object>> execute(
            ChatToolCall toolCall,
            Map<String, Object> inputMap,
            ToolChainContext context);


}
