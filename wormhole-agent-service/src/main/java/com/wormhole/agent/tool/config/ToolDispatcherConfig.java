package com.wormhole.agent.tool.config;

import com.wormhole.agent.tool.core.dispatcher.ToolExecutionStrategy;
import com.wormhole.agent.tool.core.dispatcher.ToolStrategy;
import com.wormhole.agent.tool.core.model.ToolType;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 配置类，用于自动注册所有策略实现到EnumMap。
 */
@Configuration
public class ToolDispatcherConfig {

    /**
     * 自动注册所有策略实现到EnumMap。
     *
     * @param strategies 策略实现列表
     * @return 以工具类型为键的策略映射
     */
    @Bean
    public Map<ToolType, ToolExecutionStrategy> strategyMap(List<ToolExecutionStrategy> strategies) {
        return strategies.stream()
                .collect(Collectors.toMap(
                        this::resolveToolType,
                        Function.identity(),
                        (existing, replacement) -> existing,
                        () -> new EnumMap<>(ToolType.class)
                ));
    }

    /**
     * 通过注解标记解析策略对应的工具类型。
     *
     * @param strategy 策略实现
     * @return 工具类型
     */
    private ToolType resolveToolType(ToolExecutionStrategy strategy) {
        return strategy.getClass().getAnnotation(ToolStrategy.class).value();
    }
}
