package com.wormhole.agent.tool.mcp.cache;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * 缓存统计信息
 * 用于监控和调试缓存性能
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CacheStats {
    
    /**
     * L1本地缓存大小（客户端数量）
     */
    @JsonProperty("local_cache_size")
    private int localCacheSize;
    
    /**
     * L1本地缓存中的总工具数
     */
    @JsonProperty("total_local_tools")
    private int totalLocalTools;
    
    /**
     * 统计时间
     */
    @JsonProperty("stats_time")
    @Builder.Default
    private Instant statsTime = Instant.now();
    
    /**
     * 获取统计摘要
     */
    public String getSummary() {
        return String.format("L1缓存: %d个客户端, %d个工具", 
            localCacheSize, totalLocalTools);
    }
}
