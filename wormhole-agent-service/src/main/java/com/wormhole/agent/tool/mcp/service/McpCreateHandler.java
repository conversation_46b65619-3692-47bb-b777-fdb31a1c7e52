package com.wormhole.agent.tool.mcp.service;

import com.wormhole.agent.tool.mcp.config.McpProperties;
import com.wormhole.agent.tool.mcp.event.McpClientEvent;
import dev.langchain4j.mcp.client.DefaultMcpClient;
import dev.langchain4j.mcp.client.McpClient;
import dev.langchain4j.mcp.client.transport.McpTransport;
import dev.langchain4j.mcp.client.transport.http.HttpMcpTransport;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.time.Duration;

@Slf4j
@Component
public class McpCreateHandler {
    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 创建MCP客户端（统一的客户端创建入口）
     * 负责所有传输方式的MCP客户端创建，供内部和外部组件使用
     *
     * @param config 客户端配置
     * @return MCP客户端实例
     * @throws IllegalArgumentException 当传输方式不支持时
     */
    public McpClient createMcpClient(McpProperties.McpClientConfig config) {
        String clientName = config.getName();

        // 只支持SSE传输方式，其他传输方式应该通过McpServiceManager处理
        if (!"sse".equalsIgnoreCase(config.getTransport())) {
            throw new IllegalArgumentException("McpToolProvider只支持SSE传输方式，其他传输方式请通过McpServiceManager处理: " + config.getTransport());
        }

        // 使用优化的超时配置
        Duration connectionTimeout = config.getTimeoutDuration() != null ?
                config.getTimeoutDuration() : Duration.ofSeconds(30);
        Duration toolTimeout = connectionTimeout.multipliedBy(2);

        DefaultMcpClient.Builder builder = new DefaultMcpClient.Builder()
                .clientName(clientName)
                .autoHealthCheck(true)                                    // 启用健康检查
                .autoHealthCheckInterval(Duration.ofSeconds(15))          // 15秒检查一次
                .pingTimeout(Duration.ofSeconds(8))                       // ping超时8秒
                .reconnectInterval(Duration.ofSeconds(3))                 // 重连间隔3
                .toolExecutionTimeout(toolTimeout);

        // 创建HTTP传输配置
        McpTransport httpMcpTransport = new HttpMcpTransport.Builder()
                .sseUrl(config.getUrl())
                .timeout(connectionTimeout)
                .logRequests(config.isLogRequests())
                .logResponses(config.isLogResponses())
                .build();

        builder.transport(httpMcpTransport);
        McpClient client = builder.build();

        // 设置连接失败回调
        httpMcpTransport.onFailure(() -> {
            log.info("MCP客户端连接失败: {} 开始重试", clientName);
            eventPublisher.publishEvent(McpClientEvent.retry(
                    this, clientName, config,"HTTP传输连接失败，开始重试"));
        });

        log.debug("创建MCP客户端成功: {}, 传输: SSE", clientName);
        return client;
    }
}
