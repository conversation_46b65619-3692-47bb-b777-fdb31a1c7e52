package com.wormhole.agent.tool.mcp;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.agent.core.constant.AiConstant;
import com.wormhole.agent.model.openai.OpenAiFunction;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.workflow.model.SchemaTypeEnum;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.ObjectMapperSupport;
import dev.langchain4j.agent.tool.ToolExecutionRequest;
import dev.langchain4j.agent.tool.ToolSpecification;
import dev.langchain4j.mcp.client.McpClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * MCP工具适配器
 * 负责LangChain4j MCP工具与OpenAI工具格式之间的转换
 * 注意：此类专注于工具发现和格式转换，缓存管理由McpToolCacheManager负责
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class McpToolAdapter {


    /**
     * 从MCP客户端发现工具并转换为OpenAI工具格式
     * 注意：此方法只负责工具发现和格式转换，不处理缓存逻辑
     *
     * @param mcpClient MCP客户端
     * @return OpenAI工具列表
     */
    public List<OpenAiTool> discoverTools(McpClient mcpClient) {
        try {

            // 直接使用MCP客户端的listTools方法
            List<ToolSpecification> toolsResponse = mcpClient.listTools();
            List<OpenAiTool> openAiTools = new ArrayList<>();

            if (toolsResponse != null) {
                for (ToolSpecification mcpTool : toolsResponse) {
                    OpenAiTool openAiTool = convertMcpToolToOpenAiTool(mcpTool);
                    if (openAiTool != null) {
                        openAiTools.add(openAiTool);
                    }
                }
            }

            log.info("成功从MCP客户端 {} 发现{}个工具", mcpClient.key(), openAiTools.size());
            return openAiTools;

        } catch (Exception e) {
            log.error("从MCP客户端 {} 发现工具时发生异常", mcpClient.key(), e);
            throw new RuntimeException("MCP工具发现失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 执行MCP工具
     *
     * @param mcpClient MCP客户端
     * @param toolName 工具名称
     * @param arguments 工具参数
     * @return 执行结果
     */
    public String executeTool(McpClient mcpClient, String toolName, Map<String, Object> arguments) {
        try {

            // 执行前连接验证 - 快速健康检查
            validateConnectionBeforeExecution(mcpClient, toolName);

            // 构建工具执行请求
            ToolExecutionRequest request = ToolExecutionRequest.builder()
                .name(toolName)
                .arguments(JacksonUtils.writeValueAsString(arguments))
                .id(generateToolExecutionId())
                .build();

            // 执行MCP工具
            String result = mcpClient.executeTool(request);


            return result;

        } catch (Exception e) {
            log.error("执行MCP工具时发生异常：{}", toolName, e);
            
            // 增强的错误处理，提供更详细的错误信息
            String enhancedMessage = analyzeExecutionError(e, toolName, mcpClient.key());
            throw new RuntimeException(enhancedMessage, e);
        }
    }
    
    /**
     * 将MCP工具转换为OpenAI工具格式
     *
     * @param toolSpec MCP工具规范
     * @return OpenAI工具
     */
    OpenAiTool convertMcpToolToOpenAiTool(ToolSpecification toolSpec) {
        try {
            // 直接使用ToolSpecification的方法获取信息
            String name = toolSpec.name();
            String description = toolSpec.description();

            // 转换参数
            OpenAiFunction.OpenAiParameters openAiParameters = convertToolSpecParametersToOpenAiParameters(toolSpec);

            // 构建OpenAI工具函数
            OpenAiFunction function = OpenAiFunction.builder()
                .name(name)
                .description(description)
                .parameters(openAiParameters)
                .build();

            // 构建OpenAI工具
            return OpenAiTool.builder()
                .type(AiConstant.FUNCTION)
                .function(function)
                .build();

        } catch (Exception e) {
            log.error("转换MCP工具时发生异常：{}", toolSpec.name(), e);
            return null;
        }
    }

    /**
     * 将ToolSpecification的参数转换为OpenAI参数格式
     *
     * @param toolSpec 工具规范
     * @return OpenAI参数格式
     */
    private OpenAiFunction.OpenAiParameters convertToolSpecParametersToOpenAiParameters(ToolSpecification toolSpec) {
        try {

            // 尝试获取参数信息的几种可能方式
            Object parametersObj = tryGetParametersFromToolSpec(toolSpec);

            // 如果获取到参数信息，进行转换
            if (parametersObj != null) {
                return convertParametersObject(parametersObj, toolSpec.name());
            }

            // 如果都没有获取到，返回空参数
            return createEmptyOpenAiParameters();

        } catch (Exception e) {
            return createEmptyOpenAiParameters();
        }
    }

    /**
     * 尝试从ToolSpecification获取参数信息
     * 使用反射尝试多种可能的方法名
     *
     * @param toolSpec 工具规范
     * @return 参数对象，如果没有找到则返回null
     */
    private Object tryGetParametersFromToolSpec(ToolSpecification toolSpec) {
        // 定义可能的方法名列表，按优先级排序
        String[] possibleMethodNames = {
            "parameters",      // 最常见的方法名
            "inputSchema",     // 输入模式
            "getParameters",   // getter风格
            "schema",          // 模式信息
            "getInputSchema",  // getter风格的输入模式
            "getSchema"        // getter风格的模式
        };

        Class<?> toolSpecClass = toolSpec.getClass();

        for (String methodName : possibleMethodNames) {
            try {
                Method method = toolSpecClass.getMethod(methodName);
                Object result = method.invoke(toolSpec);

                if (result != null) {
                    return result;
                }

            } catch (NoSuchMethodException e) {
                // 方法不存在，继续尝试下一个
                log.trace("方法 {} 不存在于 {}", methodName, toolSpecClass.getSimpleName());
            } catch (Exception e) {
                // 方法调用失败，继续尝试
            }
        }

        return null;
    }

    /**
     * 转换参数对象为OpenAI参数格式
     * 支持多种参数对象类型：JsonNode、String、Map等
     *
     * @param parametersObj 参数对象
     * @param toolName 工具名称（用于日志）
     * @return OpenAI参数格式
     */
    private OpenAiFunction.OpenAiParameters convertParametersObject(Object parametersObj, String toolName) {
        try {
            if (parametersObj == null) {
                return createEmptyOpenAiParameters();
            }

            // 处理JsonNode类型
            if (parametersObj instanceof JsonNode) {
                JsonNode jsonNode = (JsonNode) parametersObj;
                return convertJsonNodeToOpenAiParameters(jsonNode, toolName);
            }

            // 处理String类型（JSON字符串）
            if (parametersObj instanceof String) {
                String jsonString = (String) parametersObj;
                if (jsonString.trim().isEmpty()) {
                    return createEmptyOpenAiParameters();
                }

                try {
                    ObjectMapper mapper = ObjectMapperSupport.mapper();
                    JsonNode jsonNode = mapper.readTree(jsonString);
                    return convertJsonNodeToOpenAiParameters(jsonNode, toolName);
                } catch (Exception e) {
                    return createEmptyOpenAiParameters();
                }
            }

            // 处理Map类型
            if (parametersObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> paramMap = (Map<String, Object>) parametersObj;
                return convertMapToOpenAiParameters(paramMap, toolName);
            }

            // 处理JsonObjectSchema类型（LangChain4j特有）
            if (parametersObj.getClass().getSimpleName().equals("JsonObjectSchema")) {
                return convertJsonObjectSchemaToOpenAiParameters(parametersObj, toolName);
            }

            // 其他类型，尝试转换为JSON再处理
            try {
                ObjectMapper mapper = ObjectMapperSupport.mapper();
                JsonNode jsonNode = mapper.valueToTree(parametersObj);
                return convertJsonNodeToOpenAiParameters(jsonNode, toolName);
            } catch (Exception e) {
                return convertUnknownObjectToOpenAiParameters(parametersObj, toolName);
            }

        } catch (Exception e) {
            return createEmptyOpenAiParameters();
        }
    }

    /**
     * 将JsonNode转换为OpenAI参数格式
     *
     * @param jsonNode JSON节点
     * @param toolName 工具名称（用于日志）
     * @return OpenAI参数格式
     */
    private OpenAiFunction.OpenAiParameters convertJsonNodeToOpenAiParameters(JsonNode jsonNode, String toolName) {
        try {
            Map<String, OpenAiFunction.PropertiesInfo> properties = new HashMap<>();
            List<String> required = new ArrayList<>();

            // 如果是对象类型，直接处理properties
            if (jsonNode.isObject()) {
                // 检查是否有properties字段
                if (jsonNode.has("properties")) {
                    JsonNode propertiesNode = jsonNode.get("properties");
                    processPropertiesNode(propertiesNode, properties);
                } else {
                    // 如果没有properties字段，将整个对象作为properties处理
                    processPropertiesNode(jsonNode, properties);
                }

                // 处理required字段
                if (jsonNode.has("required")) {
                    JsonNode requiredNode = jsonNode.get("required");
                    processRequiredNode(requiredNode, required);
                }
            }

            return OpenAiFunction.OpenAiParameters.builder()
                .type(SchemaTypeEnum.OBJECT.getType())
                .properties(properties)
                .required(required)
                .build();

        } catch (Exception e) {
            return createEmptyOpenAiParameters();
        }
    }

    /**
     * 将Map转换为OpenAI参数格式
     *
     * @param paramMap 参数Map
     * @param toolName 工具名称（用于日志）
     * @return OpenAI参数格式
     */
    private OpenAiFunction.OpenAiParameters convertMapToOpenAiParameters(Map<String, Object> paramMap, String toolName) {
        try {
            Map<String, OpenAiFunction.PropertiesInfo> properties = new HashMap<>();
            List<String> required = new ArrayList<>();

            // 检查是否有properties字段
            Object propertiesObj = paramMap.get("properties");
            if (propertiesObj instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> propertiesMap = (Map<String, Object>) propertiesObj;
                processPropertiesMap(propertiesMap, properties);
            } else {
                // 如果没有properties字段，将整个Map作为properties处理
                processPropertiesMap(paramMap, properties);
            }

            // 处理required字段
            Object requiredObj = paramMap.get("required");
            if (requiredObj instanceof List) {
                @SuppressWarnings("unchecked")
                List<Object> requiredList = (List<Object>) requiredObj;
                for (Object item : requiredList) {
                    if (item != null) {
                        required.add(item.toString());
                    }
                }
            }

            return OpenAiFunction.OpenAiParameters.builder()
                .type(SchemaTypeEnum.OBJECT.getType())
                .properties(properties)
                .required(required)
                .build();

        } catch (Exception e) {
            return createEmptyOpenAiParameters();
        }
    }

    /**
     * 处理JsonNode的properties字段
     *
     * @param propertiesNode properties节点
     * @param properties 输出的properties映射
     */
    private void processPropertiesNode(JsonNode propertiesNode, Map<String, OpenAiFunction.PropertiesInfo> properties) {
        if (propertiesNode != null && propertiesNode.isObject()) {
            propertiesNode.fields().forEachRemaining(entry -> {
                String propertyName = entry.getKey();
                JsonNode propertyValue = entry.getValue();

                String type = propertyValue.has("type") ? propertyValue.get("type").asText() : "string";
                String description = propertyValue.has("description") ? propertyValue.get("description").asText() : "";

                OpenAiFunction.PropertiesInfo propertiesInfo = OpenAiFunction.PropertiesInfo.builder()
                    .type(type)
                    .description(description)
                    .build();

                properties.put(propertyName, propertiesInfo);
            });
        }
    }

    /**
     * 处理JsonNode的required字段
     *
     * @param requiredNode required节点
     * @param required 输出的required列表
     */
    private void processRequiredNode(JsonNode requiredNode, List<String> required) {
        if (requiredNode != null && requiredNode.isArray()) {
            requiredNode.forEach(node -> {
                if (node != null && !node.isNull()) {
                    required.add(node.asText());
                }
            });
        }
    }

    /**
     * 处理Map的properties字段
     *
     * @param propertiesMap properties映射
     * @param properties 输出的properties映射
     */
    private void processPropertiesMap(Map<String, Object> propertiesMap, Map<String, OpenAiFunction.PropertiesInfo> properties) {
        if (propertiesMap != null) {
            for (Map.Entry<String, Object> entry : propertiesMap.entrySet()) {
                String propertyName = entry.getKey();
                Object propertyValue = entry.getValue();

                // 跳过特殊字段
                if ("type".equals(propertyName) || "required".equals(propertyName)) {
                    continue;
                }

                String type = "string";
                String description = "";

                if (propertyValue instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> propMap = (Map<String, Object>) propertyValue;

                    Object typeObj = propMap.get("type");
                    if (typeObj != null) {
                        type = typeObj.toString();
                    }

                    Object descObj = propMap.get("description");
                    if (descObj != null) {
                        description = descObj.toString();
                    }
                }

                OpenAiFunction.PropertiesInfo propertiesInfo = OpenAiFunction.PropertiesInfo.builder()
                    .type(type)
                    .description(description)
                    .build();

                properties.put(propertyName, propertiesInfo);
            }
        }
    }

    /**
     * 转换JsonObjectSchema为OpenAI参数格式
     * 专门处理LangChain4j的JsonObjectSchema类型
     *
     * @param jsonObjectSchema JsonObjectSchema对象
     * @param toolName 工具名称（用于日志）
     * @return OpenAI参数格式
     */
    private OpenAiFunction.OpenAiParameters convertJsonObjectSchemaToOpenAiParameters(Object jsonObjectSchema, String toolName) {
        try {

            Map<String, OpenAiFunction.PropertiesInfo> properties = new HashMap<>();
            List<String> required = new ArrayList<>();

            // 使用反射获取JsonObjectSchema的属性
            Class<?> schemaClass = jsonObjectSchema.getClass();

            // 尝试获取properties方法
            try {
                Method propertiesMethod = schemaClass.getMethod("properties");
                Object propertiesObj = propertiesMethod.invoke(jsonObjectSchema);

                if (propertiesObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> propertiesMap = (Map<String, Object>) propertiesObj;
                    processJsonObjectSchemaProperties(propertiesMap, properties);
                }

            } catch (Exception e) {
            }

            // 尝试获取required方法
            try {
                Method requiredMethod = schemaClass.getMethod("required");
                Object requiredObj = requiredMethod.invoke(jsonObjectSchema);

                if (requiredObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> requiredList = (List<String>) requiredObj;
                    required.addAll(requiredList);
                }

            } catch (Exception e) {
            }


            return OpenAiFunction.OpenAiParameters.builder()
                .type(SchemaTypeEnum.OBJECT.getType())
                .properties(properties)
                .required(required)
                .build();

        } catch (Exception e) {
            return createEmptyOpenAiParameters();
        }
    }

    /**
     * 处理JsonObjectSchema的properties
     *
     * @param propertiesMap properties映射
     * @param properties 输出的properties映射
     */
    private void processJsonObjectSchemaProperties(Map<String, Object> propertiesMap, Map<String, OpenAiFunction.PropertiesInfo> properties) {
        for (Map.Entry<String, Object> entry : propertiesMap.entrySet()) {
            String propertyName = entry.getKey();
            Object propertySchema = entry.getValue();

            String type = "string";
            String description = "";

            if (propertySchema != null) {
                try {
                    // 尝试获取type方法
                    Method typeMethod = propertySchema.getClass().getMethod("type");
                    Object typeObj = typeMethod.invoke(propertySchema);
                    if (typeObj != null) {
                        type = typeObj.toString();
                    }
                } catch (Exception e) {
                    log.trace("获取property type失败: {}", e.getMessage());
                }

                try {
                    // 尝试获取description方法
                    Method descMethod = propertySchema.getClass().getMethod("description");
                    Object descObj = descMethod.invoke(propertySchema);
                    if (descObj != null) {
                        description = descObj.toString();
                    }
                } catch (Exception e) {
                    log.trace("获取property description失败: {}", e.getMessage());
                }
            }

            OpenAiFunction.PropertiesInfo propertiesInfo = OpenAiFunction.PropertiesInfo.builder()
                .type(type)
                .description(description)
                .build();

            properties.put(propertyName, propertiesInfo);
        }
    }

    /**
     * 转换未知对象类型为OpenAI参数格式
     * 作为最后的降级处理方案
     *
     * @param unknownObj 未知对象
     * @param toolName 工具名称（用于日志）
     * @return OpenAI参数格式
     */
    private OpenAiFunction.OpenAiParameters convertUnknownObjectToOpenAiParameters(Object unknownObj, String toolName) {
        try {

            // 尝试常见的方法名来获取参数信息
            String[] methodNames = {"properties", "getProperties", "schema", "getSchema"};

            for (String methodName : methodNames) {
                try {
                    Method method = unknownObj.getClass().getMethod(methodName);
                    Object result = method.invoke(unknownObj);

                    if (result != null) {
                        return convertParametersObject(result, toolName);
                    }

                } catch (Exception e) {
                    log.trace("方法 {} 调用失败: {}", methodName, e.getMessage());
                }
            }

            return createEmptyOpenAiParameters();

        } catch (Exception e) {
            return createEmptyOpenAiParameters();
        }
    }

    /**
     * 将MCP工具的输入模式转换为OpenAI参数格式
     *
     * @param mcpToolNode MCP工具的JSON节点
     * @return OpenAI参数格式
     */
    private OpenAiFunction.OpenAiParameters convertMcpInputSchemaToOpenAiParameters(JsonNode mcpToolNode) {
        try {
            // 构建properties映射
            Map<String, OpenAiFunction.PropertiesInfo> properties = new HashMap<>();
            List<String> required = new ArrayList<>();

            // 检查是否有inputSchema
            if (mcpToolNode.has("inputSchema")) {
                JsonNode inputSchema = mcpToolNode.get("inputSchema");

                if (inputSchema.has("properties")) {
                    JsonNode propertiesNode = inputSchema.get("properties");
                    propertiesNode.fields().forEachRemaining(entry -> {
                        String propertyName = entry.getKey();
                        JsonNode propertyValue = entry.getValue();

                        String type = propertyValue.has("type") ? propertyValue.get("type").asText() : "string";
                        String description = propertyValue.has("description") ? propertyValue.get("description").asText() : "";

                        OpenAiFunction.PropertiesInfo propertiesInfo = OpenAiFunction.PropertiesInfo.builder()
                            .type(type)
                            .description(description)
                            .build();

                        properties.put(propertyName, propertiesInfo);
                    });
                }

                // 处理required字段
                if (inputSchema.has("required")) {
                    JsonNode requiredNode = inputSchema.get("required");
                    if (requiredNode.isArray()) {
                        requiredNode.forEach(node -> required.add(node.asText()));
                    }
                }
            }

            return OpenAiFunction.OpenAiParameters.builder()
                .type(SchemaTypeEnum.OBJECT.getType())
                .properties(properties)
                .required(required)
                .build();

        } catch (Exception e) {
            return createEmptyOpenAiParameters();
        }
    }

    /**
     * 创建空的OpenAI参数对象
     */
    private OpenAiFunction.OpenAiParameters createEmptyOpenAiParameters() {
        return OpenAiFunction.OpenAiParameters.builder()
            .type(SchemaTypeEnum.OBJECT.getType())
            .properties(new HashMap<>())
            .required(new ArrayList<>())
            .build();
    }

    /**
     * 执行前连接验证 - 快速健康检查
     * 
     * @param mcpClient MCP客户端
     * @param toolName 工具名称
     */
    private void validateConnectionBeforeExecution(McpClient mcpClient, String toolName) {
        try {
            
            // 快速连接检查 - 尝试获取工具列表
            long startTime = System.currentTimeMillis();
            mcpClient.listTools();
            long duration = System.currentTimeMillis() - startTime;
            
            
            
        } catch (Exception e) {
            log.error("连接验证失败：{} - {}", mcpClient.key(), toolName, e);
            
            String errorMessage = analyzeConnectionError(e, mcpClient.key(), toolName);
            throw new RuntimeException("MCP连接验证失败: " + errorMessage, e);
        }
    }
    
    /**
     * 分析连接错误，提供具体的错误信息和建议
     * 
     * @param e 异常信息
     * @param clientKey 客户端标识
     * @param toolName 工具名称
     * @return 增强的错误消息
     */
    private String analyzeConnectionError(Exception e, String clientKey, String toolName) {
        String errorMessage = e.getMessage();
        String causeMessage = e.getCause() != null ? e.getCause().getMessage() : "";
        
        // 404 错误特殊处理
        if (errorMessage != null && errorMessage.contains("404") || 
            causeMessage.contains("404") || 
            causeMessage.contains("Unexpected status code: 404")) {
            
            return String.format("MCP服务端不可达(404): %s/%s - 服务端URL无效或服务已停止，请检查配置或重启服务", 
                clientKey, toolName);
        }
        
        // 连接被拒绝
        if (errorMessage != null && (errorMessage.contains("Connection refused") || 
            errorMessage.contains("ConnectException"))) {
            return String.format("连接被拒绝: %s/%s - 请检查MCP服务是否启动", clientKey, toolName);
        }
        
        // 超时错误
        if (errorMessage != null && errorMessage.contains("timeout")) {
            return String.format("连接超时: %s/%s - 请检查网络连接或增加超时时间", clientKey, toolName);
        }
        
        // 认证错误
        if (errorMessage != null && (errorMessage.contains("401") || errorMessage.contains("403"))) {
            return String.format("认证失败: %s/%s - 请检查访问凭据", clientKey, toolName);
        }
        
        // 服务器错误
        if (errorMessage != null && (errorMessage.contains("500") || errorMessage.contains("502") || 
            errorMessage.contains("503"))) {
            return String.format("服务器错误: %s/%s - 请稍后重试或联系服务提供方", clientKey, toolName);
        }
        
        return String.format("连接失败: %s/%s - %s", clientKey, toolName, errorMessage);
    }
    
    /**
     * 分析工具执行错误，提供详细的错误信息
     * 
     * @param e 异常信息
     * @param toolName 工具名称
     * @param clientKey 客户端标识
     * @return 增强的错误消息
     */
    private String analyzeExecutionError(Exception e, String toolName, String clientKey) {
        String errorMessage = e.getMessage();
        String causeMessage = e.getCause() != null ? e.getCause().getMessage() : "";
        
        // 404 错误特殊处理
        if (errorMessage != null && errorMessage.contains("404") || 
            causeMessage.contains("404") || 
            causeMessage.contains("Unexpected status code: 404")) {
            
            return String.format("MCP工具执行失败 - 服务不可达(404): %s@%s。" +
                "可能原因：1) MCP服务已停止 2) 服务端点URL变更 3) 工具不存在。" +
                "建议：重新配置MCP连接或联系服务提供方", toolName, clientKey);
        }
        
        // 工具不存在
        if (errorMessage != null && (errorMessage.contains("tool not found") || 
            errorMessage.contains("unknown tool"))) {
            return String.format("工具不存在: %s@%s - 请检查工具名称或更新MCP服务", toolName, clientKey);
        }
        
        // 参数错误
        if (errorMessage != null && (errorMessage.contains("invalid argument") || 
            errorMessage.contains("parameter"))) {
            return String.format("工具参数错误: %s@%s - 请检查传入参数格式", toolName, clientKey);
        }
        
        // 超时错误
        if (errorMessage != null && errorMessage.contains("timeout")) {
            return String.format("工具执行超时: %s@%s - 请增加超时时间或稍后重试", toolName, clientKey);
        }
        
        // 连接中断
        if (errorMessage != null && (errorMessage.contains("Connection reset") || 
            errorMessage.contains("Connection aborted"))) {
            return String.format("连接中断: %s@%s - 服务可能不稳定，建议重试", toolName, clientKey);
        }
        
        return String.format("MCP工具执行失败: %s@%s - %s", toolName, clientKey, errorMessage);
    }
    
    /**
     * 生成工具执行ID
     */
    private String generateToolExecutionId() {
        return "mcp-tool-" + System.currentTimeMillis() + "-" + (int)(Math.random() * 1000);
    }
}
