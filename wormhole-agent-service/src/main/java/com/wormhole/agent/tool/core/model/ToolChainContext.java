package com.wormhole.agent.tool.core.model;

import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.tool.ToolDiscoveryProvider;
import lombok.Data;
import reactor.core.publisher.Sinks;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 工具链执行上下文，维护工具链调用过程中的状态
 *
 * 核心职责：
 * - 维护工具调用顺序和索引
 * - 累积执行结果
 * - 控制递归调用深度
 * - 管理对话历史记录
 * - 维护工具元数据映射关系
 *
 * @see ToolDiscoveryProvider
 * @see com.wormhole.agent.tool.ToolChainDriver
 */
@Data
public class ToolChainContext {
    private String nodeId;
    /**
     * 工具调用索引计数器（线程安全）
     */
    private final AtomicInteger toolIndexCounter = new AtomicInteger(0);

    /**
     * 累积执行结果（线程安全Map）
     * Key格式：toolName.result 或 toolName.error
     */
    private final Map<String, Object> accumulatedResults = new ConcurrentHashMap<>();

    /**
     * 当前递归调用深度
     */
    private int recursionDepth = 0;

    /**
     * 已使用的工具名称集合
     */
    private Set<String> usedToolNames = new HashSet<>();

    /**
     * 完整的对话历史记录
     * 包含SYSTEM、USER、ASSISTANT和TOOL角色消息
     */
    private List<OpenAiChatMessage> conversationHistory = new ArrayList<>();

    /**
     * 最近N轮对话缓存（滑动窗口）
     */
    private List<OpenAiChatMessage> recentMessageList = new ArrayList<>();

    /**
     * 最近对话轮数限制
     */
    private int recentRound = 10;

    /**
     * 最后一次直接响应（非工具调用）
     */
    private ChatCompletions lastDirectResponse;

    /**
     * 扩展参数容器
     */
    private Map<String, Object> extParams = new HashMap<>();

    /**
     * 工具名称 -> 工具类型映射表
     */
    private Map<String, ToolType> toolTypeMap = new HashMap<>();

    /**
     * 工具名称 -> 原始代码映射表
     */
    private Map<String, String> originCodeMap = new HashMap<>();

    /**
     * MCP工具名称 -> 客户端名称映射表
     */
    private Map<String, String> mcpToolClientMap = new HashMap<>();

    /**
     * 最大递归深度限制
     */
    private static final int MAX_RECURSION_DEPTH = 5;
    /**
     * 意图识别LLM调用记录表（线程安全）
     * Key: 调用ID (timestamp + sequence)
     * Value: 调用详情
     */
    private final Map<String, LlmInvocationRecord> intentProcessingRecords =
            new ConcurrentHashMap<>();

    /**
     * 累计总耗时（毫秒）
     */
    private long totalElapsedMs;

    /**
     * 累计总token数
     */
    private long totalTokensUsed;

    private Sinks.Many<ChatCompletions> sinks;

    /**
     * 添加LLM调用记录
     */
    public void addLlmInvocation(LlmInvocationRecord record) {
        String key = String.valueOf(recursionDepth);
        intentProcessingRecords.put(key, record);
    }
    /**
     * 添加工具调用记录
     */
    public void addToolInvocation(LlmInvocationRecord.ToolInvocationDetail record) {
        // 获取当前最后一个LLM调用记录
        String lastKey = String.valueOf(recursionDepth);;
        LlmInvocationRecord llmRecord = intentProcessingRecords.get(lastKey);
        if (llmRecord != null) {
            if (llmRecord.getToolInvocations() == null) {
                llmRecord.setToolInvocations(new ArrayList<>());
            }
            llmRecord.getToolInvocations().add(record);

        }
    }
    /**
     * 增加递归深度计数器
     * @throws IllegalStateException 当超过MAX_RECURSION_DEPTH时抛出
     */
    public void incrementDepth() {
        if (this.recursionDepth >= MAX_RECURSION_DEPTH) {
            throw new IllegalStateException("Maximum recursion depth exceeded");
        }
        this.recursionDepth++;
    }

    /**
     * 获取下一个工具调用索引
     * @return 自增的索引值
     */
    public int getNextToolIndex() {
        return toolIndexCounter.getAndIncrement();
    }

    /**
     * 重置上下文状态（保留配置参数）
     */
    public void reset() {
        this.recursionDepth = 0;
        this.usedToolNames.clear();
        this.conversationHistory.clear();
        this.accumulatedResults.clear();
        this.lastDirectResponse = null;
        this.toolIndexCounter.set(0);
    }
}