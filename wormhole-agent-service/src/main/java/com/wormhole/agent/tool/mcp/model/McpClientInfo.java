package com.wormhole.agent.tool.mcp.model;

import com.wormhole.agent.client.chat.mcp.dto.McpServiceStatus;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.tool.mcp.config.McpProperties;
import dev.langchain4j.mcp.client.McpClient;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * MCP客户端信息
 * 包含客户端配置、实例和运行时状态信息
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpClientInfo {
    
    /**
     * 客户端配置
     */
    private McpProperties.McpClientConfig config;
    
    /**
     * MCP客户端实例
     */
    private McpClient client;
    
    /**
     * 服务状态
     */
    private McpServiceStatus status;
    
    /**
     * 可用工具列表
     */
    private List<OpenAiTool> tools;
    
    /**
     * 客户端初始化任务
     */
    private CompletableFuture<Void> initializationTask;
    
    /**
     * 最后活跃时间
     */
    private Instant lastActiveTime;
    
    /**
     * 工具执行统计
     */
    private ToolExecutionStats executionStats;
    
    /**
     * 重试次数
     */
    private int retryCount = 0;
    
    /**
     * 最大重试次数
     */
    private int maxRetries = 1;
    
    /**
     * 最后重试时间
     */
    private Instant lastRetryTime;
    
    /**
     * 最后错误信息
     */
    private String lastError;
    
    /**
     * 工具执行统计信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ToolExecutionStats {
        /**
         * 总执行次数
         */
        private long totalExecutions;
        
        /**
         * 成功执行次数
         */
        private long successfulExecutions;
        
        /**
         * 失败执行次数
         */
        private long failedExecutions;
        
        /**
         * 平均执行时间（毫秒）
         */
        private double averageExecutionTime;
        
        /**
         * 最后执行时间
         */
        private Instant lastExecutionTime;
        
        /**
         * 创建默认统计信息
         */
        public static ToolExecutionStats createDefault() {
            return ToolExecutionStats.builder()
                .totalExecutions(0)
                .successfulExecutions(0)
                .failedExecutions(0)
                .averageExecutionTime(0.0)
                .build();
        }
        
        /**
         * 记录成功执行
         */
        public void recordSuccess(long executionTimeMs) {
            totalExecutions++;
            successfulExecutions++;
            updateAverageTime(executionTimeMs);
            lastExecutionTime = Instant.now();
        }
        
        /**
         * 记录失败执行
         */
        public void recordFailure(long executionTimeMs) {
            totalExecutions++;
            failedExecutions++;
            updateAverageTime(executionTimeMs);
            lastExecutionTime = Instant.now();
        }
        
        /**
         * 更新平均执行时间
         */
        private void updateAverageTime(long executionTimeMs) {
            if (totalExecutions == 1) {
                averageExecutionTime = executionTimeMs;
            } else {
                averageExecutionTime = (averageExecutionTime * (totalExecutions - 1) + executionTimeMs) / totalExecutions;
            }
        }
        
        /**
         * 获取成功率
         */
        public double getSuccessRate() {
            return totalExecutions > 0 ? (double) successfulExecutions / totalExecutions : 0.0;
        }
    }
    
    /**
     * 创建客户端信息
     */
    public static McpClientInfo create(McpProperties.McpClientConfig config) {
        return McpClientInfo.builder()
            .config(config)
            .status(McpServiceStatus.initializing(config.getName()))
            .lastActiveTime(Instant.now())
            .executionStats(ToolExecutionStats.createDefault())
            .retryCount(0)
            .maxRetries(1)
            .build();
    }

    /**
     * 从现有客户端和工具创建客户端信息
     * 专门用于从已存在的客户端创建 McpClientInfo，区别于从配置创建的场景
     *
     * @param config 客户端配置
     * @param client MCP客户端实例
     * @param tools 工具列表
     * @return 包含完整状态信息的 McpClientInfo 实例
     */
    public static McpClientInfo fromExistingClient(McpProperties.McpClientConfig config,
                                                  McpClient client,
                                                  List<OpenAiTool> tools) {
        if (config == null) {
            throw new IllegalArgumentException("客户端配置不能为空");
        }
        if (client == null) {
            throw new IllegalArgumentException("客户端实例不能为空");
        }

        // 确定端点信息
        String endpoint = null;
        if ("sse".equalsIgnoreCase(config.getTransport())) {
            endpoint = config.getUrl();
        } else if ("stdio".equalsIgnoreCase(config.getTransport())) {
            endpoint = config.getCommand() != null && !config.getCommand().isEmpty()
                ? String.join(" ", config.getCommand())
                : null;
        }

        // 创建活跃状态
        McpServiceStatus activeStatus = McpServiceStatus.active(
            config.getName(),
            config.getTransport(),
            endpoint,
            tools != null ? tools.size() : 0
        );

        // 构建 McpClientInfo 实例
        return McpClientInfo.builder()
            .config(config)
            .client(client)
            .tools(tools != null ? tools : Collections.emptyList())
            .status(activeStatus)
            .lastActiveTime(Instant.now())
            .executionStats(ToolExecutionStats.createDefault())
            .retryCount(0)
            .maxRetries(1)
            .build();
    }
    
    /**
     * 获取客户端名称
     */
    public String getClientName() {
        return config != null ? config.getName() : null;
    }
    
    /**
     * 获取传输方式
     */
    public String getTransport() {
        return config != null ? config.getTransport() : null;
    }
    
    /**
     * 获取端点信息
     */
    public String getEndpoint() {
        if (config == null) {
            return null;
        }
        
        if ("sse".equalsIgnoreCase(config.getTransport())) {
            return config.getUrl();
        } else if ("stdio".equalsIgnoreCase(config.getTransport())) {
            return config.getCommand() != null && !config.getCommand().isEmpty() 
                ? String.join(" ", config.getCommand()) 
                : null;
        }
        
        return null;
    }
    
    /**
     * 获取工具数量
     */
    public int getToolCount() {
        return tools != null ? tools.size() : 0;
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return client != null && status != null && status.isActive();
    }
    
    /**
     * 检查是否健康
     */
    public boolean isHealthy() {
        return status != null && status.isHealthy();
    }
    
    /**
     * 更新活跃时间
     */
    public void updateActiveTime() {
        this.lastActiveTime = Instant.now();
    }
    
    /**
     * 设置客户端实例
     */
    public void setClientInstance(McpClient client, List<OpenAiTool> tools) {
        this.client = client;
        this.tools = tools;
        
        // 更新状态为活跃
        if (status != null) {
            this.status = McpServiceStatus.active(
                getClientName(), 
                getTransport(), 
                getEndpoint(), 
                getToolCount()
            );
        }
        
        updateActiveTime();
    }
    
    /**
     * 设置错误状态
     */
    public void setError(String errorMessage) {
        this.lastError = errorMessage;
        if (status != null) {
            status.setError(errorMessage);
        } else {
            this.status = McpServiceStatus.error(getClientName(), errorMessage);
        }
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (client != null) {
            try {
                client.close();
            } catch (Exception e) {
                // 忽略关闭异常
            }
            client = null;
        }
        
        if (initializationTask != null && !initializationTask.isDone()) {
            initializationTask.cancel(true);
        }
        
        if (status != null) {
            status.updateStatus(McpServiceStatus.ServiceStatus.INACTIVE, "客户端已关闭");
        }
    }
    
    /**
     * 获取状态摘要
     */
    public String getStatusSummary() {
        return status != null ? status.getStatusSummary() : "未知状态";
    }
    
    /**
     * 检查初始化任务是否完成
     */
    public boolean isInitializationComplete() {
        return initializationTask == null || initializationTask.isDone();
    }
    
    /**
     * 检查初始化任务是否成功
     */
    public boolean isInitializationSuccessful() {
        return isInitializationComplete() && 
               (initializationTask == null || !initializationTask.isCompletedExceptionally()) &&
               isInitialized();
    }
    
    /**
     * 检查是否可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetries;
    }
    
    /**
     * 增加重试次数
     */
    public void incrementRetry() {
        this.retryCount++;
        this.lastRetryTime = Instant.now();
    }
    
    /**
     * 重置重试状态
     */
    public void resetRetry() {
        this.retryCount = 0;
        this.lastRetryTime = null;
        this.lastError = null;
    }
    
    /**
     * 设置最大重试次数
     */
    public void setMaxRetries(int maxRetries) {
        this.maxRetries = Math.max(0, maxRetries);
    }
    
    /**
     * 获取重试状态信息
     */
    public String getRetryStatusInfo() {
        if (retryCount == 0) {
            return "未重试";
        }
        return String.format("已重试 %d/%d 次%s", 
            retryCount, maxRetries,
            lastRetryTime != null ? ", 最后重试: " + lastRetryTime : "");
    }
    
    /**
     * 记录重试失败
     */
    public void recordRetryFailure(String errorMessage) {
        incrementRetry();
        setError(String.format("重试失败 (%d/%d): %s", retryCount, maxRetries, errorMessage));
    }
}
