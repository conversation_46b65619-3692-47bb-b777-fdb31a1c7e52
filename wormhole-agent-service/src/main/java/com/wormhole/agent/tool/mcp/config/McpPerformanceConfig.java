package com.wormhole.agent.tool.mcp.config;

import com.wormhole.agent.tool.mcp.pool.McpConnectionPool;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

/**
 * MCP性能配置
 * 配置MCP专用线程池、连接池等性能优化组件
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties(McpPerformanceProperties.class)
public class McpPerformanceConfig {
    
    private final McpPerformanceProperties performanceProperties;
    
    /**
     * MCP专用线程池常量
     */
    public static final String MCP_SCHEDULER_BEAN_NAME = "mcpScheduler";
    public static final String MCP_CONNECTION_POOL_BEAN_NAME = "mcpConnectionPool";
    
    /**
     * 创建MCP专用调度器（线程池）
     * 参考AgentRtcConfiguration的实现模式
     * 
     * @return MCP专用调度器
     */
    @Bean(MCP_SCHEDULER_BEAN_NAME)
    public Scheduler mcpScheduler() {
        if (!performanceProperties.isThreadPoolEnabled()) {
            log.info("MCP线程池已禁用，使用默认调度器");
            return Schedulers.boundedElastic();
        }
        
        int coreSize = performanceProperties.getThreadPoolCoreSize();
        int queuedMax = performanceProperties.getThreadPoolQueuedMax();
        String threadNamePrefix = performanceProperties.getThreadPool().getThreadNamePrefix();
        
        log.info("创建MCP专用线程池 - 核心线程数: {}, 最大队列: {}, 线程名前缀: {}", 
            coreSize, queuedMax, threadNamePrefix);
        
        return Schedulers.newBoundedElastic(coreSize, queuedMax, threadNamePrefix);
    }
    
    /**
     * 创建MCP连接池
     * 
     * @return MCP连接池
     */
    @Bean(MCP_CONNECTION_POOL_BEAN_NAME)
    public McpConnectionPool mcpConnectionPool() {
        if (!performanceProperties.isConnectionPoolEnabled()) {
            log.info("MCP连接池已禁用");
            return null;
        }
        
        McpPerformanceProperties.ConnectionPoolConfig config = performanceProperties.getConnectionPool();
        
        log.info("创建MCP连接池 - 最大连接数: {}, 获取超时: {}s, 空闲超时: {}min", 
            config.getMaxConnections(), 
            config.getAcquireTimeout().getSeconds(),
            config.getIdleTimeout().toMinutes());
        
        return McpConnectionPool.builder()
            .poolName(config.getPoolName())
            .maxConnections(config.getMaxConnections())
            .acquireTimeout(config.getAcquireTimeout())
            .idleTimeout(config.getIdleTimeout())
            .maxLifetime(config.getMaxLifetime())
            .pendingAcquireMaxCount(config.getPendingAcquireMaxCount())
            .build();
    }
    
    /**
     * 获取工具执行超时时间
     * 
     * @return 超时时间
     */
    public java.time.Duration getToolExecutionTimeout() {
        return performanceProperties.getTimeout().getToolExecution();
    }
    
    /**
     * 获取工具发现超时时间
     * 
     * @return 超时时间
     */
    public java.time.Duration getToolDiscoveryTimeout() {
        return performanceProperties.getTimeout().getToolDiscovery();
    }
    
    /**
     * 获取客户端初始化超时时间
     * 
     * @return 超时时间
     */
    public java.time.Duration getClientInitializationTimeout() {
        return performanceProperties.getTimeout().getClientInitialization();
    }
    
    /**
     * 获取健康检查超时时间
     * 
     * @return 超时时间
     */
    public java.time.Duration getHealthCheckTimeout() {
        return performanceProperties.getTimeout().getHealthCheck();
    }
    
    /**
     * 获取批量操作最大大小
     * 
     * @return 批量大小
     */
    public int getBatchMaxSize() {
        return performanceProperties.getBatchMaxSize();
    }
    
    /**
     * 获取批量操作超时时间
     * 
     * @return 超时时间
     */
    public java.time.Duration getBatchTimeout() {
        return performanceProperties.getBatch().getBatchTimeout();
    }
    
    /**
     * 获取批量操作并发度
     * 
     * @return 并发度
     */
    public int getBatchConcurrency() {
        return performanceProperties.getBatch().getConcurrency();
    }
    
    /**
     * 检查批量操作是否启用
     * 
     * @return 是否启用
     */
    public boolean isBatchEnabled() {
        return performanceProperties.isBatchEnabled();
    }
}
