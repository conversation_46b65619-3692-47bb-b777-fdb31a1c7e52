package com.wormhole.agent.tool.mcp.pool;

import lombok.Builder;
import lombok.Data;

/**
 * 连接池统计信息
 * 提供连接池的运行状态和性能指标
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Data
@Builder
public class ConnectionPoolStats {
    
    /**
     * 连接池名称
     */
    private final String poolName;
    
    /**
     * 总连接数
     */
    private final int totalConnections;
    
    /**
     * 活跃连接数
     */
    private final int activeConnections;
    
    /**
     * 空闲连接数
     */
    private final int idleConnections;
    
    /**
     * 最大连接数
     */
    private final int maxConnections;
    
    /**
     * 连接请求总数
     */
    private final long connectionRequests;
    
    /**
     * 连接命中次数（从池中获取）
     */
    private final long connectionHits;
    
    /**
     * 命中率
     */
    private final double hitRate;
    
    /**
     * 获取连接池使用率
     * 
     * @return 使用率百分比 (0.0 - 1.0)
     */
    public double getUsageRate() {
        return maxConnections > 0 ? (double) totalConnections / maxConnections : 0.0;
    }
    
    /**
     * 获取活跃连接比例
     * 
     * @return 活跃连接比例 (0.0 - 1.0)
     */
    public double getActiveRate() {
        return totalConnections > 0 ? (double) activeConnections / totalConnections : 0.0;
    }
    
    /**
     * 获取空闲连接比例
     * 
     * @return 空闲连接比例 (0.0 - 1.0)
     */
    public double getIdleRate() {
        return totalConnections > 0 ? (double) idleConnections / totalConnections : 0.0;
    }
    
    /**
     * 检查连接池是否健康
     * 
     * @return true如果连接池健康
     */
    public boolean isHealthy() {
        // 连接池健康的判断标准：
        // 1. 使用率不超过90%
        // 2. 有足够的空闲连接
        // 3. 命中率合理（大于50%）
        return getUsageRate() < 0.9 && 
               idleConnections > 0 && 
               (connectionRequests == 0 || hitRate > 0.5);
    }
    
    /**
     * 获取性能等级
     * 
     * @return 性能等级描述
     */
    public String getPerformanceLevel() {
        if (hitRate > 0.8) {
            return "优秀";
        } else if (hitRate > 0.6) {
            return "良好";
        } else if (hitRate > 0.4) {
            return "一般";
        } else {
            return "较差";
        }
    }
    
    /**
     * 获取详细统计信息
     * 
     * @return 格式化的统计信息
     */
    public String getDetailedStats() {
        return String.format(
            "连接池统计 [%s]:\n" +
            "  总连接数: %d/%d (使用率: %.1f%%)\n" +
            "  活跃连接: %d (%.1f%%)\n" +
            "  空闲连接: %d (%.1f%%)\n" +
            "  请求统计: %d 次请求, %d 次命中 (命中率: %.1f%%)\n" +
            "  性能等级: %s\n" +
            "  健康状态: %s",
            poolName,
            totalConnections, maxConnections, getUsageRate() * 100,
            activeConnections, getActiveRate() * 100,
            idleConnections, getIdleRate() * 100,
            connectionRequests, connectionHits, hitRate * 100,
            getPerformanceLevel(),
            isHealthy() ? "健康" : "异常"
        );
    }
    
    /**
     * 获取简要统计信息
     * 
     * @return 简要统计信息
     */
    @Override
    public String toString() {
        return String.format("%s: %d/%d连接 (活跃:%d, 空闲:%d, 命中率:%.1f%%)", 
            poolName, totalConnections, maxConnections, 
            activeConnections, idleConnections, hitRate * 100);
    }
}
