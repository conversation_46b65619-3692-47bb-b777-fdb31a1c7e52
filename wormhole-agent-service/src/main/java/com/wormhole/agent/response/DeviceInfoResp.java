package com.wormhole.agent.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/3/11
 * @Description:
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceInfoResp implements Serializable {

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 用户id
     */
    private String rtcUserId;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 房间号
     */
    private String positionCode;

    /**
     * 房间全称
     */
    private String positionFullName;

    /**
     * 设备类型 （前台设备 & 客房设备）
     */
    private String deviceType;

    /**
     * 登陆的账号
     */
    private String account;

    /**
     * 登陆的用户名
     */
    private String username;

}
