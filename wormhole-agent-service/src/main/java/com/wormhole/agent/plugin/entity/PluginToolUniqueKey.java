package com.wormhole.agent.plugin.entity;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-04-08 14:49:17
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PluginToolUniqueKey {

    private String pluginCode;

    private String pluginToolCode;

    /**
     * 暂时不考虑version
     */
    private Integer version;

    private static final String SEPARATOR_SYMBOL = StrUtil.COLON;

    public PluginToolUniqueKey(String pluginCode, String pluginToolCode) {
        this.pluginCode = pluginCode;
        this.pluginToolCode = pluginToolCode;
    }

    public String toUniqueKey() {
        return StrUtil.join(SEPARATOR_SYMBOL, this.pluginCode, this.pluginToolCode);
    }

    public static PluginToolUniqueKey fromUniqueKey(String uniqueKey) {
        String[] split = uniqueKey.split(SEPARATOR_SYMBOL);
        return new PluginToolUniqueKey(split[0], split[1]);
    }

    public static PluginToolUniqueKey fromToolInfo(PluginToolDetailInfo toolDetailInfo) {
        return new PluginToolUniqueKey(toolDetailInfo.getPluginInfo().getCode(), toolDetailInfo.getCode());
    }
}
