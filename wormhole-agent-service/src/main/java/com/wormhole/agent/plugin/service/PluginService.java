package com.wormhole.agent.plugin.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.wormhole.agent.core.constant.AiConstant;
import com.wormhole.agent.model.openai.OpenAiFunction;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.nacos.listener.PluginListener;
import com.wormhole.agent.plugin.entity.AuthParam;
import com.wormhole.agent.plugin.entity.PluginDetailInfo;
import com.wormhole.agent.plugin.entity.PluginToolDetailInfo;
import com.wormhole.agent.plugin.entity.PluginToolUniqueKey;
import com.wormhole.agent.workflow.model.SchemaTypeEnum;
import com.wormhole.common.enums.HttpRequestParamLocationEnum;
import com.wormhole.common.enums.HttpRequestParamTypeEnum;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.model.RequestParamInfo;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-07 19:45:28
 * @Description:
 */
@Component
@Slf4j
public class PluginService {

    private final WebClient webClient = WebClient.builder().build();

    private static final String PATH_PARAM_FORMAT = "{%s}";

    @Resource
    private PluginListener pluginListener;


    public Mono<Map<String, Object>> execute(PluginToolUniqueKey uniqueKey, Map<String, Object> inputValueMap) {
        Preconditions.checkNotNull(uniqueKey.getPluginCode(), "插件code不能为空");
        Preconditions.checkNotNull(uniqueKey.getPluginToolCode(), "工具code不能为空");

        return execute(uniqueKey.getPluginCode(), uniqueKey.getPluginToolCode(), inputValueMap);
    }

    public Mono<Map<String, Object>> execute(String pluginCode, String pluginToolCode, Map<String, Object> inputValueMap) {
        if (Objects.isNull(pluginCode)) {
            return Mono.empty();
        }
        PluginToolDetailInfo pluginToolDetailInfo = getLatestVersionTool(pluginCode, pluginToolCode);
        log.info("PluginService execute pluginCode:{} pluginToolCode:{},pluginToolDetailInfo:{}", pluginCode, pluginToolCode,JacksonUtils.writeValueAsString(pluginToolDetailInfo));
        return execute(pluginToolDetailInfo, inputValueMap);
    }

    public Mono<Map<String, Object>> execute(PluginToolDetailInfo pluginToolInfo, Map<String, Object> inputValueMap) {
        log.info("pluginCode:{} pluginToolCode:{} inputValue:{}", pluginToolInfo.getPluginInfo().getCode(), pluginToolInfo.getCode(), JacksonUtils.writeValueAsString(inputValueMap));
        List<RequestParamInfo> requestParams = getAllRequestParam(pluginToolInfo);
        setParameterValues(requestParams, inputValueMap);
        Map<Integer, List<RequestParamInfo>> map = groupParametersByLocation(requestParams);

        String path = assemblePathParam(pluginToolInfo.getRequestPath(), map.getOrDefault(HttpRequestParamLocationEnum.PATH.getValue(), new ArrayList<>()));

        String bodyJson = buildBodyJson(map.getOrDefault(HttpRequestParamLocationEnum.BODY.getValue(), new ArrayList<>()));
        Map<String, String> paramsMap = buildParamsMap(map.getOrDefault(HttpRequestParamLocationEnum.QUERY.getValue(), new ArrayList<>()));
        Map<String, String> headerMap = buildParamsMap(map.getOrDefault(HttpRequestParamLocationEnum.HEADER.getValue(), new ArrayList<>()));

        HttpMethod httpMethod = HttpMethod.valueOf(pluginToolInfo.getRequestMethod());

        PluginToolDetailInfo.PluginInfo pluginInfo = pluginToolInfo.getPluginInfo();
        String fullUrl = pluginInfo.getDomain() + path;
        String uriString = buildFullUrl(fullUrl, paramsMap);

        // 调用请求
        return executeHttpRequest(httpMethod, uriString, headerMap, bodyJson, pluginToolInfo.getResponseParams());
    }

    public PluginToolDetailInfo getLatestVersionTool(String pluginCode, String pluginToolCode) {
        // 查询插件
        PluginDetailInfo pluginDetailInfo = pluginListener.getContent(pluginCode);
        if (Objects.isNull(pluginDetailInfo)) {
            log.error("PluginService getLatestVersionTool 插件不存在:{}", pluginCode);
            return null;
        }

        return getPluginTool(pluginDetailInfo, pluginToolCode);
    }


    public List<PluginToolDetailInfo> getLatestVersionTool(Collection<PluginToolUniqueKey> pluginToolUniqueKeys) {
        if (CollUtil.isEmpty(pluginToolUniqueKeys)) {
            return new ArrayList<>();
        }
        return pluginToolUniqueKeys.stream()
                .map(e -> getLatestVersionTool(e.getPluginCode(), e.getPluginToolCode()))
                .toList();
    }

    public List<Optional<OpenAiTool>> convertToOpenAiTool(Collection<PluginToolDetailInfo> pluginTools) {
        return pluginTools.stream().map(this::convertToOpenAiTool).toList();
    }

    public Optional<OpenAiTool> convertToOpenAiTool(PluginToolDetailInfo pluginTool) {
        if (Objects.isNull(pluginTool)) {
            return Optional.empty();
        }

        List<String> required = Lists.newArrayList();

        List<RequestParamInfo> requestParams = pluginTool.getRequestParams();

        Map<String, OpenAiFunction.PropertiesInfo> properties = new LinkedHashMap<>();
        for (RequestParamInfo param : requestParams) {
            String name = param.getName();
            String description = param.getDesc();

            HttpRequestParamTypeEnum httpRequestParamTypeEnum = HttpRequestParamTypeEnum.fromValue(param.getType());

            String type = httpRequestParamTypeEnum.name().toLowerCase();
            if (param.getIsRequired()) {
                required.add(name);
            }
            OpenAiFunction.PropertiesInfo propertiesInfo = OpenAiFunction.PropertiesInfo.builder().type(type).description(description).build();
            properties.put(name, propertiesInfo);
        }

        OpenAiFunction.OpenAiParameters openAiParameters = OpenAiFunction.OpenAiParameters.builder()
                .type(SchemaTypeEnum.OBJECT.getType())
                .required(required)
                .properties(properties)
                .build();


        OpenAiFunction openAiFunction = OpenAiFunction.builder()
                .name(pluginTool.getToolName())
                .description(pluginTool.getDescription())
                .parameters(openAiParameters)
                .build();


        OpenAiTool openAiTool = OpenAiTool.builder()
                .type(AiConstant.FUNCTION)
                .function(openAiFunction)
                .build();
        return Optional.ofNullable(openAiTool);
    }


    private Mono<Map<String, Object>> executeHttpRequest(
            HttpMethod method,
            String url,
            Map<String, String> headers,
            String body,
            List<RequestParamInfo> responseParams) {
        log.info("executeTimeout http request, method:{} url:{} headers:{} body:{}, responseParams:{}", method, url,
                JacksonUtils.writeValueAsString(headers), body, JacksonUtils.writeValueAsString(responseParams));
        return webClient
                .method(method)
                .uri(url)
                .contentType(MediaType.APPLICATION_JSON)
                .headers(httpHeaders -> headers.forEach(httpHeaders::add))
                .body(Mono.just(body), String.class)
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->
                        Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "request error"))
                )
                .bodyToMono(String.class)
                .map(responseBody -> processResponse(responseBody, responseParams))
                .doOnError(e -> {
                    log.error("Error executing plugin request: {}", e.getMessage(), e);
                });
    }

    /**
     * Process the HTTP response and extract fields according to response parameters
     */
    private Map<String, Object> processResponse(
            String responseBody,
            List<RequestParamInfo> responseParams) {
        log.info("executeTimeout http request completed - result:{}", responseBody);
        try {
            ObjectMapper objectMapper = JacksonUtils.mapper();
            JsonNode rootNode = objectMapper.readTree(responseBody);
            return parseResponse(rootNode, responseParams);
        } catch (JsonProcessingException e) {
            log.error("Failed to parse responseBody:{} ,responseParams:{}, errMsg: {}", responseBody, responseParams, e.getMessage(), e);
        }

        return new HashMap<>();
    }

    private String buildFullUrl(String baseUrl, Map<String, String> queryParams) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(baseUrl);
        queryParams.forEach(builder::queryParam);
        return builder.toUriString();
    }

    /**
     * Group parameters by their location type
     */
    private Map<Integer, List<RequestParamInfo>> groupParametersByLocation(List<RequestParamInfo> params) {
        if (params == null)
            return Collections.emptyMap();
        return params.stream()
                .collect(Collectors.groupingBy(RequestParamInfo::getLocation));
    }

    /**
     * Build the body JSON from body parameters
     */
    private String buildBodyJson(List<RequestParamInfo> bodyParams) {
        JSONObject jsonObject = new JSONObject();

        bodyParams.forEach(param -> {
            boolean isObject = Objects.equals(param.getType(), HttpRequestParamTypeEnum.OBJECT.getValue());
            boolean isArray = Objects.equals(param.getType(), HttpRequestParamTypeEnum.ARRAY.getValue());
            String value = Optional.ofNullable(param.getValue())
                    .orElse(param.getDefaultValue());
            Object resultValue = value;
            if (isArray) {
                value = Optional.ofNullable(value)
                        .map(String::trim)
                        .filter(StrUtil::isNotBlank)
                        .orElse(StrUtil.BRACKET_START + StrUtil.BRACKET_END);
                resultValue = JacksonUtils.readValue(value, new TypeReference<List<Object>>() {
                });
            }
            if (isObject) {
                value = Optional.ofNullable(value)
                        .map(String::trim)
                        .filter(StrUtil::isNotBlank)
                        .orElse(StrUtil.EMPTY_JSON);
                resultValue = JacksonUtils.readValue(value);
            }
            jsonObject.set(param.getName(), resultValue);
        });

        return jsonObject.toJSONString(0);
    }


    private Map<String, String> buildParamsMap(List<RequestParamInfo> requestParams) {
        Map<String, String> params = Maps.newHashMap();
        requestParams.forEach(e -> {
            String value = Optional.ofNullable(e.getValue())
                    .orElse(e.getDefaultValue());
            params.put(e.getName(), value);
        });
        return params;
    }


    /**
     * Replace path parameters in the URL template
     */
    private String assemblePathParam(String urlTemplate, List<RequestParamInfo> pathParams) {
        String result = urlTemplate;
        for (RequestParamInfo param : pathParams) {
            result = result.replace(
                    String.format(PATH_PARAM_FORMAT, param.getName()),
                    param.getValue());
        }
        return result;
    }


    /**
     * Parse response JSON according to response parameter definitions
     */
    private Map<String, Object> parseResponse(JsonNode rootNode, List<RequestParamInfo> responseParams) {
        if (responseParams == null || rootNode == null) {
            return Collections.emptyMap();
        }

        Map<String, Object> resultMap = new HashMap<>();

        for (RequestParamInfo param : responseParams) {
            JsonNode node = rootNode.path(param.getName());
            if (node.isMissingNode()) {
                continue;
            }

            resultMap.put(param.getName(), extractValue(node, param));
        }

        return resultMap;
    }

    /**
     * Extract value from a JSON node based on parameter type
     */
    private Object extractValue(JsonNode node, RequestParamInfo param) {
        boolean hasChildren = CollUtil.isNotEmpty(param.getSubParameters());
        boolean isArray = Objects.equals(param.getType(), HttpRequestParamTypeEnum.ARRAY.getValue());

        if (hasChildren && node.isObject()) {
            return parseResponse(node, param.getSubParameters());
        } else if (hasChildren && isArray && node.isArray()) {
            return extractArrayValues(node, param);
        } else {
            return node.asText();
        }
    }

    /**
     * Extract values from a JSON array node
     */
    private List<Object> extractArrayValues(JsonNode arrayNode, RequestParamInfo param) {
        int size = arrayNode.size();
        List<Object> result = new ArrayList<>(size);
        List<RequestParamInfo> subParams = param.getSubParameters();

        for (JsonNode item : arrayNode) {
            if (item.isObject()) {
                // Handle case with nested object in array
                RequestParamInfo firstSubParam = subParams.get(0);
                if (firstSubParam != null
                        && Objects.equals(firstSubParam.getType(), HttpRequestParamTypeEnum.OBJECT.getValue())
                        && CollUtil.isNotEmpty(firstSubParam.getSubParameters())
                ) {
                    result.add(parseResponse(item, firstSubParam.getSubParameters()));
                } else {
                    result.add(parseResponse(item, subParams));
                }
            } else {
                result.add(item.isNull() ? null : item.asText());
            }
        }

        return result;
    }

    private List<RequestParamInfo> getAllRequestParam(PluginToolDetailInfo pluginTool) {

        List<RequestParamInfo> requestParams = pluginTool.getRequestParams();

        List<RequestParamInfo> allRequestParam = new ArrayList<>(requestParams);
        // 插件上的header 和 auth
        if (CollUtil.isNotEmpty(pluginTool.getPluginInfo().getHeaders())) {
            allRequestParam.addAll(pluginTool.getPluginInfo().getHeaders());
        }

        Optional.ofNullable(pluginTool.getPluginInfo().getAuthParam())
                .map(AuthParam::getServiceToken)
                .ifPresent(allRequestParam::add);
        return allRequestParam;
    }

    /**
     * Set values for all parameters from input or default values
     */
    private void setParameterValues(List<RequestParamInfo> params, Map<String, Object> inputValues) {
        if (params == null)
            return;
        params.forEach(param -> {
            if (inputValues.containsKey(param.getName())) {
                Object o = Optional.ofNullable(inputValues.get(param.getName()))
                        .orElse(Optional.ofNullable(param.getDefaultValue()).orElse(StrUtil.EMPTY));
                String value;
                if (ClassUtil.isSimpleValueType(o.getClass())) {
                    value = o.toString();
                } else {
                    value = JacksonUtils.writeValueAsString(o);
                }
                param.setValue(value);
            }

        });
    }


    private PluginToolDetailInfo getPluginTool(PluginDetailInfo pluginDetailInfo, String toolCode) {
        log.info("PluginService getPluginTool, pluginDetailInfo: {}, toolCode: {}", pluginDetailInfo, toolCode);
        List<PluginDetailInfo.PluginTool> pluginTools = pluginDetailInfo.getPluginTools();
        Optional<PluginDetailInfo.PluginTool> first = Optional.ofNullable(pluginTools)
                .stream()
                .flatMap(List::stream)
                .filter(tool -> Objects.equals(tool.getCode(), toolCode))
                .findFirst();
        if (first.isEmpty()) {
            return null;
        }

        PluginDetailInfo.PluginTool pluginTool = first.get();

        PluginToolDetailInfo pluginToolDetailInfo = new PluginToolDetailInfo();
        PluginDetailInfo.PluginTool pluginToolClone = JacksonUtils.readValue(JacksonUtils.writeValueAsString(pluginTool), PluginDetailInfo.PluginTool.class);
        BeanUtils.copyProperties(pluginToolClone, pluginToolDetailInfo);

        PluginToolDetailInfo.PluginInfo pluginInfo = new PluginToolDetailInfo.PluginInfo();
        PluginDetailInfo pluginDetailInfoClone = JacksonUtils.readValue(JacksonUtils.writeValueAsString(pluginDetailInfo), PluginDetailInfo.class);
        BeanUtils.copyProperties(pluginDetailInfoClone, pluginInfo);
        pluginToolDetailInfo.setPluginInfo(pluginInfo);


        return pluginToolDetailInfo;
    }
}
