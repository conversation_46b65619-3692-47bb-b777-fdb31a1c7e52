package com.wormhole.agent.plugin.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.bot.ObjectInfo;
import com.wormhole.common.model.RequestParamInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-28 10:39:52
 * @Description: 插件详细信息，包含下面的工具
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PluginDetailInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private String code;

    private Integer pluginType;

    /**
     * 插件名称
     */
    private String pluginName;

    /**
     * 插件描述
     */
    private String description;

    /**
     * 图标元数据，格式
     */
    private ObjectInfo iconMetadata;
    /**
     * 版本
     */
    private Long version;

    /**
     * 授权方式
     */
    private Integer authType;

    /**
     * 授权相关参数
     */
    private AuthParam authParam;

    /**
     * 请求域名
     */
    private String domain;

    /**
     * 请求头
     */
    private List<RequestParamInfo> headers;
    /**
     * 插件分类名称
     */
    private String categoryName;


    private Boolean isPublished;

    private List<PluginTool> pluginTools;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PluginTool implements Serializable {

        @Serial
        private static final long serialVersionUID = 1L;

        /**
         * 工具ID
         */
        private String code;

        /**
         * 名称
         */
        private String toolName;

        /**
         * 描述
         */
        private String description;

        /**
         * 包含参数类型（header、body、query、path）
         */
        private List<RequestParamInfo> requestParams; // JSON类型

        private List<RequestParamInfo> responseParams; // JSON类型Params; // JSON类型

        /**
         * 请求path
         */
        private String requestPath;

        /**
         * 请求方法
         */
        private String requestMethod;

        /**
         * 创建时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
        private LocalDateTime createdAt;

    }


}
