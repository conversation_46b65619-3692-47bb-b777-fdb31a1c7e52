package com.wormhole.agent.plugin.entity;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.enums.HttpRequestParamLocationEnum;
import com.wormhole.common.model.RequestParamInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-08 10:08:33
 * @Description:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PluginToolDetailInfo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 工具ID
     */
    private String code;

    /**
     * 名称
     */
    private String toolName;

    /**
     * 描述
     */
    private String description;

    /**
     * 包含参数类型（header、body、query、path）
     */
    private List<RequestParamInfo> requestParams; // JSON类型

    private List<RequestParamInfo> responseParams; // JSON类型Params; // JSON类型

    /**
     * 请求path
     */
    private String requestPath;

    /**
     * 请求方法
     */
    private String requestMethod;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    private PluginInfo pluginInfo;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class PluginInfo implements Serializable {
        @Serial
        private static final long serialVersionUID = 1L;

        private String code;

        /**
         * 版本
         */
        private Long version;

        /**
         * 授权方式
         */
        private Integer authType;

        /**
         * 授权相关参数
         */
        private AuthParam authParam;

        /**
         * 请求域名
         */
        private String domain;

        /**
         * 请求头
         */
        private List<RequestParamInfo> headers;

        public List<RequestParamInfo> getHeaders() {
            if (CollUtil.isEmpty(this.headers)) {
                return new ArrayList<>();
            }
            // save 时没有保存 location，只保存了 name value
            for (RequestParamInfo header : this.headers) {
                header.setLocation(HttpRequestParamLocationEnum.HEADER.getValue());
            }
            return headers;
        }
    }

}
