package com.wormhole.agent.feishu.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/22 14:27
 */
@Data
public class CardMsgText {

    private String tag;

    private String content;

    private String text_size;

    private String text_color;

    public CardMsgText(Builder builder) {
        this.content = builder.content;
        this.tag = "plain_text";
        this.text_size = builder.text_size;
        this.text_color = builder.text_color;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static class Builder {

        private String content;

        private String text_size;

        private String text_color;

        public Builder content(String content) {
            this.content = content;
            return this;
        }

        public Builder text_size(String text_size) {
            this.text_size = text_size;
            return this;
        }

        public Builder text_color(String text_color) {
            this.text_color = text_color;
            return this;
        }

        public CardMsgText build() {
            return new CardMsgText(this);
        }
    }
}
