package com.wormhole.agent.feishu.service;

import com.wormhole.agent.feishu.constant.FeishuConstant;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.JsonPathUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/4 09:38
 */
@Service
@Slf4j
public class FeishuApiService {

    /**
     * 获取飞书机器人token
     *
     * @param
     * @return
     */
    public Mono<String> doGetTenantAccessToken(String appId, String secretKey) {
        Map<String, Object> msg = new HashMap<>();
        msg.put("app_id", appId);
        msg.put("app_secret", secretKey);
        return WebClient.create(FeishuConstant.ACCESS_TOKEN_URL)
                .post()
                .contentType(MediaType.APPLICATION_JSON)
                .body(Mono.just(JacksonUtils.writeValueAsString(msg)), String.class)
                .retrieve()
                .bodyToMono(String.class)
                .mapNotNull(s -> {
                    Map<String, Object> objectMap = JacksonUtils.readValue(s);
                    return (String) JsonPathUtils.read(objectMap, "$.tenant_access_token");
                })
                .onErrorResume(t -> {
                    log.error("doGetTenantAccessToken send error", t);
                    return Mono.empty();
                });
    }

    /**
     * 飞书机器人回复消息
     *
     * @param accessToken 飞书机器人token
     * @param messageId   消息id
     * @param content     回复内容
     * @return 回复消息id
     */
    public Mono<String> replyMessage(String accessToken, String messageId, String content) {
        return WebClient.create(String.format(FeishuConstant.REPLY_MESSAGE_URL, messageId))
                .post()
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .body(Mono.just(content), String.class)
                .retrieve()
                .bodyToMono(String.class)
                .mapNotNull(s -> {
                    Map<String, Object> objectMap = JacksonUtils.readValue(s);
                    return (String) JsonPathUtils.read(objectMap, "$.data.message_id");
                })
                .onErrorResume(t -> {
                    log.error("ReplyMessage send error", t);
                    return Mono.empty();
                });
    }

    /**
     * 发送消息
     *
     * @param accessToken
     * @param content
     * @return
     */
    public Mono<String> sendMessage(String accessToken, String content) {
        log.info("sendMessage send request accessToken {} content={}", accessToken, content);
        return WebClient.create(String.format(FeishuConstant.SEND_MESSAGE_URL, "open_id"))
                .post()
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .body(Mono.just(content), String.class)
                .retrieve()
                .bodyToMono(String.class)
                .mapNotNull(s -> {
                    Map<String, Object> objectMap = JacksonUtils.readValue(s);
                    return (String) JsonPathUtils.read(objectMap, "$.data.message_id");
                })
                .onErrorResume(t -> {
                    log.error("sendMessage send error", t);
                    return Mono.empty();
                });
    }


    /**
     * 修改卡片消息
     *
     * @param accessToken
     * @param patchMessageId
     * @param content
     * @return
     */
    public Mono<String> patchCardMessage(String accessToken, String patchMessageId, String content) {
        log.info("patchCardMessage send request content={}", content);
        return WebClient.create(String.format(FeishuConstant.PATCH_MESSAGE_URL, patchMessageId))
                .patch()
                .contentType(MediaType.APPLICATION_JSON)
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + accessToken)
                .body(Mono.just(content), String.class)
                .retrieve()
                .bodyToMono(String.class)
                .onErrorResume(t -> {
                    log.error("patchCardMessage send error", t);
                    return Mono.empty();
                });
    }

    /**
     * 获取用户信息
     *
     * @param userToken
     * @return
     */
    public Mono<String> getFeishuUserInfo(String userToken) {
        return WebClient.create(FeishuConstant.GET_USER_INFO_URL)
                .get()
                .header(HttpHeaders.AUTHORIZATION, "Bearer " + userToken)
                .retrieve()
                .bodyToMono(String.class)
                .onErrorResume(t -> {
                    log.error("getUserInfo send error", t);
                    return Mono.empty();
                });
    }
}
