package com.wormhole.agent.feishu.constant;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/24 19:25
 */
@Data
@ConfigurationProperties(prefix = "wormhole.feishu.robot")
@RefreshScope
public class FeishuRobotProperties {

    //所有人展示
    private List<String> botCode = Lists.newArrayList();

    private List<String> openIdList = Lists.newArrayList();

    //白名单的才能展示
    private List<String> whiteBotCode = Lists.newArrayList();

    private String encryptKey;

    private String defaultBotCode;

    private List<FeishuRobotProperties.FeishuRobotEnum> robotList = Lists.newArrayList();

    public FeishuRobotProperties.FeishuRobotEnum getByVerificationToken(String verificationToken) {
        return robotList.stream()
                .filter(item -> item.getVerificationToken().equalsIgnoreCase(verificationToken))
                .findFirst()
                .orElse(null);
    }

    public FeishuRobotProperties.FeishuRobotEnum getByAppId(String appId) {
        return robotList.stream()
                .filter(item -> item.getAppId().equalsIgnoreCase(appId))
                .findFirst()
                .orElse(null);
    }

    @Data
    public static class FeishuRobotEnum {
        private String appId;

        private String secretKey;

        private String verificationToken;

        private String encryptKey;

        private String defaultBotCode;
    }


}
