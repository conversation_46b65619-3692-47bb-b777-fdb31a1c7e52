package com.wormhole.agent.feishu.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/6 10:45
 */
@Data
public class CardHeader {
    // 卡片主标题。必填。
    private CardMsgText title;
    // 卡片副标题。可选。
    private CardMsgText subtitle;
    // 标题后缀标签，最多设置 3 个 标签，超出不展示。可选。
    private List<CardMsgText> text_tag_list;
    // 国际化标题后缀标签。每个语言环境最多设置 3 个 tag，超出不展示。可选。同时配置原字段和国际化字段，优先生效国际化配置。
    private CardMsgText i18n_text_tag_list;
    // 标题主题颜色。支持 "blue"|"wathet"|"tuiquoise"|"green"|"yellow"|"orange"|"red"|"carmine"|"violet"|"purple"|"indigo"|"grey"|"default"。默认值 default。
    private String template;

    public CardHeader(Builder builder)
    {
        this.title = builder.title;
        this.subtitle = builder.subtitle;
        this.text_tag_list = builder.text_tag_list;
        this.i18n_text_tag_list = builder.i18n_text_tag_list;
        this.template = builder.template;
    }

    public static Builder newBuilder()
    {
        return new Builder();
    }
    public static class Builder {

        private CardMsgText title;
        private CardMsgText subtitle;
        private List<CardMsgText> text_tag_list;
        private CardMsgText i18n_text_tag_list;
        private String template;

        public Builder title(CardMsgText title)
        {
            this.title = title;
            return this;
        }
        public Builder subtitle(CardMsgText subtitle)
        {
            this.subtitle = subtitle;
            return this;
        }
        public Builder text_tag_list(List<CardMsgText> text_tag_list)
        {
            this.text_tag_list = text_tag_list;
            return this;
        }
        public Builder i18n_text_tag_list(CardMsgText i18n_text_tag_list)
        {
            this.i18n_text_tag_list = i18n_text_tag_list;
            return this;
        }
        public Builder template(String template)
        {
            this.template = template;
            return this;
        }
        public CardHeader build()
        {
            return new CardHeader(this);
        }

    }
}
