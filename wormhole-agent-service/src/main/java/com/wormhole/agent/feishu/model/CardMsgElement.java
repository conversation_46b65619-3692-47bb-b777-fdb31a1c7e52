package com.wormhole.agent.feishu.model;

import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/22 14:25
 */
@Data
public class CardMsgElement {

    private String tag;

    private CardMsgText text;

    //下拉选择-单选组件的唯一标识。当下拉选择-单选组件内嵌在表单容器时，该属性生效，用于识别用户提交的文本属于哪个下拉选择-单选组件
    private String name;

    private List<CardMsgSelectText> actions;

    public CardMsgElement(Builder builder) {
        this.text = builder.text;
        this.tag = "div";
        if (StringUtils.isNotBlank(builder.getTag())) {
            this.tag = builder.tag;
        }
        this.name = builder.name;
        this.actions = builder.actions;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static class Builder {
        private CardMsgText text;

        @Getter
        private String tag;

        private String name;

        private List<CardMsgSelectText> actions;

        public Builder text(CardMsgText text) {
            this.text = text;
            return this;
        }

        public Builder tag(String tag) {
            this.tag = tag;
            return this;
        }

        public Builder name(String name) {
            this.name = name;
            return this;
        }

        public Builder actions(List<CardMsgSelectText> actions) {
            this.actions = actions;
            return this;
        }

        public CardMsgElement build() {
            return new CardMsgElement(this);
        }
    }
}
