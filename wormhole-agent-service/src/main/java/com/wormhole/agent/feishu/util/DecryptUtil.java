package com.wormhole.agent.feishu.util;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.Base64;
/**
 * <AUTHOR>
 * @date 2024/12/26 14:07
 */
public class DecryptUtil {
    private static Cache<String, byte[]> secretCache = Caffeine.newBuilder()
            .expireAfterAccess(Duration.ofHours(5L))
            .maximumSize(100)
            .build();

    public static String decrypt(String secret, String base64) throws Exception {
        byte[] keyBs = getKeyByteArray(secret);

        byte[] decode = Base64.getDecoder().decode(base64);
        Cipher cipher = Cipher.getInstance("AES/CBC/NOPADDING");
        byte[] iv = new byte[16];
        System.arraycopy(decode, 0, iv, 0, 16);
        byte[] data = new byte[decode.length - 16];
        System.arraycopy(decode, 16, data, 0, data.length);
        cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(keyBs, "AES"), new IvParameterSpec(iv));
        byte[] r = cipher.doFinal(data);
        if (r.length > 0) {
            int p = r.length - 1;
            for (; p >= 0 && r[p] <= 16; p--) {
            }
            if (p != r.length - 1) {
                byte[] rr = new byte[p + 1];
                System.arraycopy(r, 0, rr, 0, p + 1);
                r = rr;
            }
        }
        return new String(r, StandardCharsets.UTF_8);
    }

    private static byte[] getKeyByteArray(String secret) {
        byte[] keyBs = secretCache.getIfPresent(secret);
        if (null == keyBs) {
            MessageDigest digest = null;
            try {
                digest = MessageDigest.getInstance("SHA-256");
            } catch (NoSuchAlgorithmException e) {
                // won't happen
            }
            keyBs = digest.digest(secret.getBytes(StandardCharsets.UTF_8));
            secretCache.put(secret, keyBs);
        }
        return keyBs;
    }

    public static void main(String[] args) throws Exception {
        //"test key"
        System.out.println(decrypt("L5PvO2KSEs8MKhxymZeAFSwUHXgReZGy","QWJjZGVmZ2hpamtsbW5vcHFyc3R1dnd4eXo="));
    }

}