package com.wormhole.agent.feishu.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/22 11:01
 */
@Data
public class CardMsg implements Serializable {

    //根据json生成bean
     /*{
      "config": {
        "enable_forward": true, // 是否支持转发卡片。默认值 true。
        "update_multi": true, // 是否为共享卡片。为 true 时即更新卡片的内容对所有收到这张卡片的人员可见。默认值 false。
        "width_mode": "fill", // 卡片宽度模式。支持 "compact"（紧凑宽度 400px）模式、"fill"（撑满聊天窗口宽度）模式和 "default" 默认模式(宽度上限为 600px)。
        "compact_width": true, // 已废弃字段。是否为紧凑型卡片宽度。与 width_mode 属性同时设置时，width_mode 将生效。
        "use_custom_translation": false, // 是否使用自定义翻译数据。默认值 false。为 true 时则在用户点击消息翻译后，使用 i18n 对应的目标语种作为翻译结果。若 i18n 取不到，则使用当前内容请求翻译，不使用自定义翻译数据。
        "enable_forward_interaction": false, // 转发的卡片是否仍然支持回传交互。默认值 false。
        "style": { // 添加自定义字号和颜色。可应用在组件 JSON 数据中，设置字号和颜色属性。
          "text_size": { // 分别为移动端和桌面端添加自定义字号，同时添加兜底字号。用于在组件 JSON 中设置字号属性。支持添加多个自定义字号对象。
            "cus-0": {
              "default": "medium", // 在无法差异化配置字号的旧版飞书客户端上，生效的字号属性。选填。
              "pc": "medium", // 桌面端的字号。
              "mobile": "large" // 移动端的字号。
            }
          },
          "color": { // 分别为飞书客户端浅色主题和深色主题添加 RGBA 语法。用于在组件 JSON 中设置颜色属性。支持添加多个自定义颜色对象。
            "cus-0": {
              "light_mode": "rgba(5,157,178,0.52)", // 浅色主题下的自定义颜色语法
              "dark_mode": "rgba(78,23,108,0.49)" // 深色主题下的自定义颜色语法
            }
          }
        }
      },
      "card_link": {
        // 指定卡片整体的跳转链接。
        "url": "https://www.baidu.com", // 默认链接地址。未配置指定端地址时，该配置生效。
        "android_url": "https://developer.android.com/",
        "ios_url": "https://developer.apple.com/",
        "pc_url": "https://www.windows.com"
      },
      "header": {}, // 卡片标题。
      "elements": [
        {}
      ], // 用于传入各个组件的 JSON 数据，组件将按数组顺序纵向流式排列。
      "i18n_elements": {
        // 除标题外其它组件的多语言配置。你可在每个语种下配置完整卡片，卡片将根据用户的飞书客户端语言，自动展示对应语言的卡片内容。
        "en_us": [{}], // 英文
        "zh_cn": [{}], // 简体中文
        "zh_hk": [{}], // 繁体中文（香港）
        "zh_tw": [{}], // 繁体中文（台湾）
        "ja_jp": [{}], // 日语
        "id_id": [{}], // 印尼语
        "vi_vn": [{}], // 越南语
        "th_th": [{}], // 泰语
        "pt_br": [{}], // 葡萄牙语
        "es_es": [{}], // 西班牙语
        "ko_kr": [{}], // 韩语
        "de_de": [{}], // 德语
        "fr_fr": [{}], // 法语
        "it_it": [{}], // 意大利语
        "ru_ru": [{}], // 俄语
        "ms_my": [{}] // 马来语
      },
      "fallback": {
        // 在此处为卡片添加降级规则。触发降级时，卡片将全局展示“请升级客户端至最新版本后查看”占位图。
        // 该字段要求飞书客户端的版本为 V7.7 及以上。
        "trigger_conditions": [
          // 触发规则，满足以下任一条件时，触发降级。
          { // 条件一：设置最低客户端版本。当用户的客户端版本低于该设置时，触发降级。
            "type": "min_client_version",
            "value": "7.4"
          },
          {
            // 条件二：指定组件。当用户的飞书客户端版本低于这些组件支持的最低客户端版本时，触发降级。
            "type": "element_tags",
            "value": [
              ""
            ]
          }
        ]
      }
    }*/

    private CardMsgConfig config;
    private CardHeader header;
    private List<CardMsgElement> elements;
//    private Fallback fallback;


    public CardMsg(Builder builder) {

        this.config = builder.config;

        this.header = builder.header;

        this.elements = builder.elements;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static class Builder {

        private CardMsgConfig config;
        private CardHeader header;
        private List<CardMsgElement> elements;

        public Builder config(CardMsgConfig config) {
            this.config = config;
            return this;
        }

        public Builder header(CardHeader header) {
            this.header = header;
            return this;
        }

        public Builder elements(List<CardMsgElement> elements) {
            this.elements = elements;
            return this;
        }

        public CardMsg build() {
            return new CardMsg(this);
        }
    }

}
