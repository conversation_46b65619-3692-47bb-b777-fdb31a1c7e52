package com.wormhole.agent.feishu.service;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.AgentChatParams;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.feishu.constant.FeishuConstant;
import com.wormhole.agent.feishu.constant.FeishuRobotProperties;
import com.wormhole.agent.feishu.model.CardMsg;
import com.wormhole.agent.feishu.model.CardMsgConfig;
import com.wormhole.agent.feishu.model.CardMsgElement;
import com.wormhole.agent.feishu.model.CardMsgText;
import com.wormhole.agent.feishu.util.DecryptUtil;
import com.wormhole.agent.feishu.util.MessageUtils;
import com.wormhole.agent.model.openai.ChatChoice;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.ChatFinishReason;
import com.wormhole.agent.model.openai.ChatMessage;
import com.wormhole.agent.service.BotService;
import com.wormhole.agent.service.ChatService;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.JsonPathUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.context.Context;
import reactor.util.context.ContextView;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @date 2024/11/19 15:34
 */
@Service
@Slf4j
public class FeishuEventService {

    @Resource
    private ChatService chatService;

    @Resource
    private FeishuApiService feishuApiService;

    @Resource
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    @Resource
    private BotService botService;

    @Resource
    private FeishuRobotProperties feishuRobotProperties;

    public Mono<ResponseEntity<String>> getEntityMono(HttpHeaders httpHeaders, String encryptStr) {
        try {
            String eval = JsonPathUtils.read(JacksonUtils.readValue(encryptStr), "$.encrypt");
            String eventString = StringUtils.isNotBlank(eval) ? DecryptUtil.decrypt(feishuRobotProperties.getEncryptKey(), eval) : encryptStr;
            Map<String, Object> stringObjectMap = JacksonUtils.readValue(eventString);
            String tokenStr = JsonPathUtils.read(stringObjectMap, "$.header.token");
            String tokenStr2 = JsonPathUtils.read(stringObjectMap, "$.token");
            String appId = JsonPathUtils.read(stringObjectMap, "$.header.app_id");
            String challenge = JsonPathUtils.read(stringObjectMap, "$.challenge");
            String reqType = JsonPathUtils.read(stringObjectMap, "$.type");

            String token = StringUtils.isBlank(tokenStr) ? tokenStr2 : tokenStr;
            FeishuRobotProperties.FeishuRobotEnum feishuRobotEnum = feishuRobotProperties.getByAppId(appId);
            // 使用challenge进行鉴权
            if (FeishuConstant.URL_VERIFICATION.equals(reqType)) {
                feishuRobotEnum = feishuRobotProperties.getByVerificationToken(token);
                if (!feishuRobotEnum.getVerificationToken().equals(token)) {
                    throw new RuntimeException("The result of auth by challenge failed");
                }
                log.info("challenge response,challenge:{}", challenge);
                String format = String.format(FeishuConstant.CHALLENGE_RESPONSE_FORMAT, challenge);
                return Mono.just(ResponseEntity.ok().headers(headers -> headers.putAll(httpHeaders)).body(format));
            }
            //业务逻辑
            eventHandler(feishuRobotEnum, eventString)
                    .contextCapture()
                    .subscribeOn(Schedulers.boundedElastic())
                    .subscribe();
            //事件处理
            return Mono.just(ResponseEntity.ok()
                    .headers(headers -> headers.putAll(httpHeaders))
                    .body(String.format(FeishuConstant.RESPONSE_FORMAT, "ok")));
        } catch (Exception e) {
            log.error("handle event failed,eventString:{},err:", encryptStr, e);
            return Mono.just(ResponseEntity.internalServerError()
                    .headers(headers -> headers.putAll(httpHeaders))
                    .body(String.format(FeishuConstant.RESPONSE_FORMAT,
                            e.getMessage())));
        }

    }


    public Mono<Void> eventHandler(FeishuRobotProperties.FeishuRobotEnum feishuRobotEnum, String eventString) {
        String eventType = JsonPathUtils.read(JacksonUtils.readValue(eventString), "$.header.event_type");
        if (Objects.equals(eventType, FeishuConstant.MESSAGE_RECEIVE_EVENT)) {
            return Mono.deferContextual(contextView -> doSendMessage(feishuRobotEnum, eventString, contextView));
        } else if (Objects.equals(eventType, FeishuConstant.BOT_MENU_EVENT)) {
            //redis缓存
            return Mono.deferContextual(contextView -> changeBot(feishuRobotEnum, getOpenId(eventType, eventString)));
        } else if (Objects.equals(eventType, FeishuConstant.BOT_CARD_SELECT_EVENT)) {
            //下拉选择之后需要存储botcode
            return Mono.deferContextual(contextView -> doSelectBotCode(feishuRobotEnum, eventString));
        }else if (Objects.equals(eventType, FeishuConstant.BOT_CHAT_CREATE_EVENT)){
            return Mono.deferContextual(contextView -> changeBot(feishuRobotEnum, getOpenId(eventType, eventString)));
        }
        return Mono.empty();
    }

    private Mono<Void> doSelectBotCode(FeishuRobotProperties.FeishuRobotEnum feishuRobotEnum, String eventString) {
        Map<String, Object> objectMap = JacksonUtils.readValue(eventString);
        String openId = JsonPathUtils.read(objectMap, "$.event.operator.open_id");
        String botCode = JsonPathUtils.read(objectMap, "$.event.action.option");
        if (StringUtils.isBlank(openId) || StringUtils.isBlank(botCode)) {
            return Mono.empty();
        }
        String redisKey = String.format(FeishuConstant.REDIS_KEY_OPENID_BOT_CODE, feishuRobotEnum.getAppId(), openId);
        return reactiveStringRedisTemplate.opsForValue().set(redisKey, botCode)
                .flatMap(aBoolean -> sendWelcomeCard(feishuRobotEnum, botCode, openId))
                .then();
    }

    private String getOpenId(String eventType, String eventString) {
        if (Objects.equals(eventType, FeishuConstant.BOT_MENU_EVENT)) {
            return JsonPathUtils.read(JacksonUtils.readValue(eventString), "$.event.operator.operator_id.open_id");
        }else if (Objects.equals(eventType, FeishuConstant.BOT_CHAT_ENTERED_EVENT)){
            return JsonPathUtils.read(JacksonUtils.readValue(eventString), "$.event.operator_id.open_id");
        }else if (Objects.equals(eventType, FeishuConstant.BOT_CHAT_CREATE_EVENT)){
            return JsonPathUtils.read(JacksonUtils.readValue(eventString), "$.event.user.open_id");
        }
        return JsonPathUtils.read(JacksonUtils.readValue(eventString), "$.event.operator.open_id");
    }

    private Mono<Void> changeBot(FeishuRobotProperties.FeishuRobotEnum feishuRobotEnum, String openId) {
        if (StringUtils.isBlank(openId)) {
            return Mono.empty();
        }
        log.info("changeBot openId:{}", openId);
        String redisKey = String.format(FeishuConstant.REDIS_KEY_OPENID_BOT_CODE, feishuRobotEnum.getAppId(), openId);
        return reactiveStringRedisTemplate.opsForValue().get(redisKey)
                .switchIfEmpty(Mono.just(botService.getDefaultBotInfo(feishuRobotProperties.getDefaultBotCode()).getBotCode()))
                .map(botCode -> {
                    if (StringUtils.isBlank(botCode)) {
                        reactiveStringRedisTemplate.opsForValue().set(redisKey, botCode).subscribe();
                    }
                    return botCode;
                })
                .flatMap(botCode -> sendWelcomeCard(feishuRobotEnum, botCode, openId))
                .onErrorResume(throwable -> {
                    log.error("changeBot error", throwable);
                    return Mono.empty();
                })
                .then();
    }

    private Mono<String> sendWelcomeCard(FeishuRobotProperties.FeishuRobotEnum feishuRobotEnum, String botCode, String openId) {
        return feishuApiService.doGetTenantAccessToken(feishuRobotEnum.getAppId(), feishuRobotEnum.getSecretKey())
                .flatMap(accessToken -> getBotCodeIsNull(openId)
                        .map(botInfoList -> MessageUtils.getBotWelcomeCardAndSelectOption(botInfoList, botCode))
                        .switchIfEmpty(Mono.error(new RuntimeException("botCode is null")))
                        .flatMap(cardMsg -> {
                            String content = MessageUtils.buildInteractiveMsg(cardMsg, openId);
                            return feishuApiService.sendMessage(accessToken, content);
                        })
                        .onErrorResume(throwable -> {
                            log.error("changeBot error", throwable);
                            return Mono.empty();
                        })
                );
    }

    private Mono<List<BotInfo>> getBotCodeIsNull(String openId) {
        log.info("getBotCode {}", JacksonUtils.writeValueAsString(feishuRobotProperties.getBotCode()));
        return botService.getBotInfoList()
                .flatMapMany(Flux::fromIterable)
                .filter(botInfoDTO -> isContains(openId, botInfoDTO.getBotCode())) // 使用botCodeOrder过滤
                .switchIfEmpty(Mono.error(new RuntimeException("botCode is null")))
                .collectList()
                .map(botInfoList -> {
                    // 按照botCodeOrder排序
                    botInfoList.sort(Comparator.comparingInt(botInfo -> feishuRobotProperties.getBotCode().indexOf(botInfo.getBotCode())));
                    return botInfoList;
                });
    }

    private boolean isContains(String openId,String botCode) {
        if (feishuRobotProperties.getOpenIdList().contains(openId)) {
            return feishuRobotProperties.getBotCode().contains(botCode)
                    || feishuRobotProperties.getWhiteBotCode().contains(botCode);
        }else {
            return feishuRobotProperties.getBotCode().contains(botCode);
        }
    }


    public Mono<Void> doSendMessage(FeishuRobotProperties.FeishuRobotEnum feishuRobotEnum, String eventString, ContextView contextView) {
        //区分使用哪个模型
        log.info("eventString = {}", eventString);
        long start = System.currentTimeMillis();
        AtomicReference<String> accessTokenRef = new AtomicReference<>();
        AtomicReference<String> textContentRef = new AtomicReference<>();
        AtomicReference<String> messageIdRef = new AtomicReference<>();
        AtomicBoolean sendFlag = new AtomicBoolean(false);
        String openId = JsonPathUtils.read(JacksonUtils.readValue(eventString), "$.event.sender.sender_id.open_id");
        String chatId = JsonPathUtils.read(JacksonUtils.readValue(eventString), "$.event.message.chat_id");

        return feishuApiService.doGetTenantAccessToken(feishuRobotEnum.getAppId(), feishuRobotEnum.getSecretKey())
                .map(accessToken -> extractMessageDetails(eventString, accessToken, accessTokenRef, textContentRef, messageIdRef))
                .flatMap(textContent ->{
                    if (StringUtils.isBlank(textContent)){
                        return Mono.empty();
                    }
                    return thinking(accessTokenRef.get(), messageIdRef.get());
                })
                .doOnNext(patchMessageId -> processMessageGeneration(contextView, patchMessageId, chatId,
                        accessTokenRef, textContentRef, start, sendFlag, openId, feishuRobotEnum))
                .then();

    }

    private void processMessageGeneration(ContextView contextView, String patchMessageId, String chatId,
                                          AtomicReference<String> accessTokenRef, AtomicReference<String> textContentRef,
                                          long start, AtomicBoolean sendFlag, String openId,
                                          FeishuRobotProperties.FeishuRobotEnum feishuRobotEnum) {
        // 累计输出文本
        StringBuffer accContentBuffer = new StringBuffer();
        StringBuffer errorBuffer = new StringBuffer();
        AtomicBoolean finish = new AtomicBoolean(false);
        AtomicReference<Disposable> intervalRef = new AtomicReference<>();
        // 轮训大模型输出的分片结果
        Disposable subscribe = Flux.interval(Duration.ofSeconds(1), Duration.ofMillis(500L))
                .flatMap(t -> checkMessageStatus(patchMessageId, start, sendFlag, finish, accContentBuffer, errorBuffer, accessTokenRef.get()))
                .doOnNext(s -> checkFinish(finish, intervalRef))
                .subscribe();
        intervalRef.set(subscribe);
        String redisKey = String.format(FeishuConstant.REDIS_KEY_OPENID_BOT_CODE, feishuRobotEnum.getAppId(), openId);
        reactiveStringRedisTemplate.opsForValue().get(redisKey)
                .switchIfEmpty(Mono.just(StringUtils.isNotBlank(feishuRobotEnum.getDefaultBotCode()) ?
                        feishuRobotEnum.getDefaultBotCode() : botService.getDefaultBotInfo(feishuRobotProperties.getDefaultBotCode()).getBotCode()))
                .doOnNext(botCode -> {
                    log.info("botCode = {}", botCode);
                    ChatContext chatContext = ChatContext.builder()
                            .chatParams(AgentChatParams.builder()
                                    .botCode(botCode)
                                    .content(textContentRef.get())
                                    .conversationId(chatId)
                                    .clientReqId(UUID.randomUUID().toString())
                                    .build())
                            .chatType(ChatType.AGENT)
                            .userId(openId)
                            .username(openId)
                            .source("feishu")
                            .build();
                    Flux<ChatCompletions> flux = chatService.chatCompletions(chatContext);
                    flux.doOnNext(chatCompletions -> processChatCompletions(chatCompletions, accContentBuffer, finish))
                            .onErrorResume(throwable -> {
                                log.error("发生异常, 停止生成!", throwable);
                                errorBuffer.append("发生异常, 停止生成!");
                                finish.set(true);
                                return Mono.just(null);
                            }).subscribe();
                }).onErrorResume(throwable -> {
                    log.error("发生异常, 停止生成!", throwable);
                    errorBuffer.append("发生异常, 停止生成!");
                    finish.set(true);
                    return Mono.just(null);
                }).subscribe(s -> {
                }, t -> {
                }, () -> {
                }, Context.of(contextView));
    }

    private void checkFinish(AtomicBoolean finish, AtomicReference<Disposable> intervalRef) {
        if (finish.get()) {
            Mono.delay(Duration.ofSeconds(3L)).doOnNext(c -> {
                if (finish.get()) {
                    if (intervalRef.get() != null) {
                        intervalRef.get().dispose();
                    }
                }
            }).subscribe();
        }
    }

    private void processChatCompletions(ChatCompletions chatCompletions, StringBuffer accContentBuffer, AtomicBoolean finish) {
        log.info("chatCompletions={}", JacksonUtils.writeValueAsString(chatCompletions));
        if (Objects.nonNull(chatCompletions) && CollectionUtils.isNotEmpty(chatCompletions.getChoices())) {

            appendContent(chatCompletions, accContentBuffer);

            String finishReason = Optional.ofNullable(chatCompletions.getChoices())
                    .orElse(Collections.emptyList())
                    .stream()
                    .findFirst()
                    .map(ChatChoice::getFinishReason)
                    .orElse(null);
            if (ChatFinishReason.isFinishReason(finishReason)) {
                finish.compareAndSet(false, true);
            }
        }
    }

    private static void appendContent(ChatCompletions chatCompletions, StringBuffer messageBuffer) {
        if (Objects.isNull(chatCompletions)) {
            return;
        }
        if (CollectionUtils.isEmpty(chatCompletions.getChoices())) {
            return;
        }
        Optional<ChatChoice> firstChatChoiceOptional = chatCompletions.getChoices().stream().findFirst();
        if (firstChatChoiceOptional.isPresent()) {
            ChatChoice chatChoice = firstChatChoiceOptional.get();
            ChatMessage delta = chatChoice.getDelta();
            ChatMessage message = chatChoice.getMessage();
            if (Objects.nonNull(delta)) {
//                if (Objects.nonNull(delta.getReasoningContent())) {
//                    messageBuffer.append(delta.getReasoningContent());
//                }
                if (Objects.nonNull(delta.getContent())) {
                    messageBuffer.append(delta.getContent());
                }
            } else if (Objects.nonNull(message)) {
                if (Objects.nonNull(message.getContent())) {
                    messageBuffer.append(message.getContent());
                }
            }
        }
    }

    private Mono<?> checkMessageStatus(String patchMessageId, long start, AtomicBoolean sendFlag,
                                       AtomicBoolean finish, StringBuffer accContentBuffer,
                                       StringBuffer errorBuffer, String accessToken) {
        if (System.currentTimeMillis() - start > Duration.ofMinutes(10L).toMillis()) {
            finish.set(true);
        }
        String allText = accContentBuffer.toString();
        String errorMsg = errorBuffer.toString();

        log.info("allText={}, errorMsg={}", allText, errorMsg);
        if (sendFlag.get()) {
            return Mono.just(sendFlag.get());
        }

        if (StringUtils.isNotBlank(errorMsg)) {
            finish.set(true);
            sendFlag.set(true);
            return generate(accessToken, patchMessageId, allText, true, errorMsg);
        }
        if (finish.get()) {
            sendFlag.set(true);
            return generate(accessToken, patchMessageId, allText, finish.get());
        }
        return StringUtils.isBlank(allText) ? Mono.empty() : generate(accessToken, patchMessageId, allText, finish.get());
    }

    private String extractMessageDetails(String eventString, String accessToken, AtomicReference<String> accessTokenRef, AtomicReference<String> textContentRef, AtomicReference<String> messageIdRef) {
        Map<String, Object> objectMap = JacksonUtils.readValue(eventString);
        String content = JsonPathUtils.read(objectMap, "$.event.message.content");
        String messageId = JsonPathUtils.read(objectMap, "$.event.message.message_id");
        String textContent = Strings.nullToEmpty(JsonPathUtils.read(JacksonUtils.readValue(content), "$.text"));
        log.info("sendMessage textContent={}", textContent);
        accessTokenRef.set(accessToken);
        textContentRef.set(textContent);
        messageIdRef.set(messageId);
        return textContent;
    }

    /**
     * 获取回复id
     *
     * @param accessToken
     * @param messageId
     * @return
     */
    public Mono<String> thinking(String accessToken, String messageId) {
        //创建Card对象，并设置卡片文本类型参数值为正在思考，请稍后
        CardMsg card = CardMsg.newBuilder()
                .config(CardMsgConfig.newBuilder().enableForward(Boolean.TRUE).build())
                .elements(Collections.singletonList(CardMsgElement.newBuilder()
                        .text(CardMsgText.newBuilder().content("正在思考, 请稍后...").build()).build()))
                .build();
        String content = MessageUtils.buildInteractiveMsg(card, null);
        log.info("thinking send request content={}", content);
        return feishuApiService.replyMessage(accessToken, messageId, content);
    }


    public Mono<String> generate(String accessToken, String patchMessageId, String allContent, boolean isLast, String... note) {
        CardMsg.Builder builder = CardMsg.newBuilder();
        List<CardMsgElement> cardMsgElements = Lists.newArrayList(CardMsgElement.newBuilder()
                .text(CardMsgText.newBuilder().content(allContent).build()).build());
        if (note.length > 0) {
            cardMsgElements = Lists.newArrayList(CardMsgElement.newBuilder()
                    .text(CardMsgText.newBuilder().content(note[0]).build()).build());
        } else if (!isLast) {
            CardMsgElement elementHr = CardMsgElement.newBuilder().tag("hr").build();
            cardMsgElements.add(elementHr);
            CardMsgElement element = CardMsgElement.newBuilder().text(
                    CardMsgText.newBuilder().content("正在生成中...").text_size("notation").text_color("grey-600").build()).build();
            cardMsgElements.add(element);
        } else {
            CardMsgElement elementHr = CardMsgElement.newBuilder().tag("hr").build();
            cardMsgElements.add(elementHr);
            CardMsgElement element = CardMsgElement.newBuilder().text(CardMsgText.newBuilder()
                    .content("生成完成，以上内容为系统学习相关知识后生成，仅供参考").text_size("notation").text_color("grey-600").build()).build();
            cardMsgElements.add(element);
        }
        builder.elements(cardMsgElements).build();
        CardMsg card = builder.build();
        String content = MessageUtils.buildInteractiveMsg(card, null);
        return feishuApiService.patchCardMessage(accessToken, patchMessageId, content);
    }

}
