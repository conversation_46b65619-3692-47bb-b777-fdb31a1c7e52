package com.wormhole.agent.feishu.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/22 14:27
 */
@Data
public class CardMsgSelectText {


    private String tag;

    private List<CardMsgOptionsText> options;

    private String initial_option;


    public CardMsgSelectText(Builder builder) {
        this.tag = builder.tag;
        this.options = builder.options;
        this.initial_option = builder.initial_option;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static class Builder {

        private String tag;

        private List<CardMsgOptionsText> options;

        private String initial_option;

        public Builder tag(String tag) {
            this.tag = tag;
            return this;
        }

        public Builder options(List<CardMsgOptionsText> options) {
            this.options = options;
            return this;
        }

        public Builder initialOption(String initial_option) {
            this.initial_option = initial_option;
            return this;
        }

        public CardMsgSelectText build() {
            return new CardMsgSelectText(this);
        }
    }
}
