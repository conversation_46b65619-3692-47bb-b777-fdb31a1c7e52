package com.wormhole.agent.feishu.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/22 14:27
 */
@Data
public class CardMsgOptionsText {

    private CardMsgText text;

    private String value;


    public CardMsgOptionsText(Builder builder) {
        this.text = builder.text;
        this.value = builder.value;
    }

    public static Builder newBuilder() {
        return new Builder();
    }

    public static class Builder {

        private CardMsgText text;

        private String value;

        public Builder cardMsgText(CardMsgText text) {
            this.text = text;
            return this;
        }

        public Builder value(String value) {
            this.value = value;
            return this;
        }

        public CardMsgOptionsText build() {
            return new CardMsgOptionsText(this);
        }
    }
}
