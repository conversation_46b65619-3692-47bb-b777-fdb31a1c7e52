package com.wormhole.agent.feishu.util;

import com.google.common.collect.Lists;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.feishu.model.CardHeader;
import com.wormhole.agent.feishu.model.CardMsg;
import com.wormhole.agent.feishu.model.CardMsgConfig;
import com.wormhole.agent.feishu.model.CardMsgElement;
import com.wormhole.agent.feishu.model.CardMsgOptionsText;
import com.wormhole.agent.feishu.model.CardMsgSelectText;
import com.wormhole.agent.feishu.model.CardMsgText;
import com.wormhole.agent.feishu.model.FeishuCardElementEnum;
import com.wormhole.agent.feishu.model.FeishuMsgTypeEnum;
import com.wormhole.common.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/12/6 10:33
 */
public class MessageUtils {

    private static final String BOT_TITLE = "你好，我是%s";

    public static CardMsg getBotWelcomeCardAndSelectOption(List<BotInfo> botInfoDTOList, String botCode) {
        BotInfo botInfo = botInfoDTOList.stream().filter(botInfoDTO -> botCode.equals(botInfoDTO.getBotCode()))
                .findFirst().orElse(botInfoDTOList.get(0));
        CardMsg card = CardMsg.newBuilder()
                .config(CardMsgConfig.newBuilder().enableForward(Boolean.TRUE).build())
                .header(CardHeader.newBuilder()
                        .title(CardMsgText.newBuilder().content(String.format(BOT_TITLE,
                                StringUtils.isNotBlank(botInfo.getName())?botInfo.getName():botInfo.getBotCode())).build())
                        .template("blue")
                        .build())
//                .elements()
                .build();
        List<CardMsgElement> msgElements = Lists.newArrayList(CardMsgElement.newBuilder()
                        .text(CardMsgText.newBuilder().content(botInfo.getDescription()).build()).build(),
                CardMsgElement.newBuilder().tag(FeishuCardElementEnum.HR.getValue()).build());
        //下拉框组件
        List<CardMsgOptionsText> cardMsgOptionsTexts = new ArrayList<>();
        botInfoDTOList.stream().forEach(botInfoDTO -> {
            cardMsgOptionsTexts.add(CardMsgOptionsText.newBuilder()
                    .cardMsgText(CardMsgText.newBuilder().content(
                            StringUtils.isNotBlank(botInfoDTO.getName())?botInfoDTO.getName():botInfoDTO.getBotCode()).build())
                    .value(botInfoDTO.getBotCode()).build());
        });
        CardMsgElement element = CardMsgElement.newBuilder()
                .tag(FeishuCardElementEnum.ACTION.getValue())
                .actions(Lists.newArrayList(CardMsgSelectText.newBuilder()
                        .tag(FeishuCardElementEnum.SELECT.getValue())
                        .initialOption(botCode)
                        .options(cardMsgOptionsTexts)
                        .build()))
                .build();
        msgElements.add(element);
        card.setElements(msgElements);
        return card;
    }

    public static String buildInteractiveMsg(CardMsg cardMsg, String openId) {
        String uuid = UUID.randomUUID().toString();
        Map<String, Object> msg = new HashMap<>();
        msg.put("msg_type", FeishuMsgTypeEnum.MSG_TYPE_INTERACTIVE.getValue());
        msg.put("content", JacksonUtils.writeValueAsString(cardMsg));
        msg.put("uuid", uuid);
        if (StringUtils.isNotBlank(openId)) {
            msg.put("receive_id", openId);
        }
        return JacksonUtils.writeValueAsString(msg);
    }
}
