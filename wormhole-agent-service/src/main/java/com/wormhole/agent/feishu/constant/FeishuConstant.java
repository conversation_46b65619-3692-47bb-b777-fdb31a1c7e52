package com.wormhole.agent.feishu.constant;

/**
 * <AUTHOR>
 * @date 2024/12/5 19:21
 */
public final class FeishuConstant {

    public static final String SEND_MESSAGE_URL = "https://open.feishu.cn/open-apis/im/v1/messages?receive_id_type=%s";

    public static final String REPLY_MESSAGE_URL = "https://open.feishu.cn/open-apis/im/v1/messages/%s/reply";

    public static final String ACCESS_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";

    public static final String PATCH_MESSAGE_URL = "https://open.feishu.cn/open-apis/im/v1/messages/%s";

    public static final String GET_USER_INFO_URL = "https://open.feishu.cn/open-apis/authen/v1/user_info";

    public static final String MESSAGE_RECEIVE_EVENT = "im.message.receive_v1";

    public static final String BOT_MENU_EVENT = "application.bot.menu_v6";

    public static final String BOT_CARD_SELECT_EVENT = "card.action.trigger";

    public static final String BOT_CHAT_ENTERED_EVENT = "im.chat.access_event.bot_p2p_chat_entered_v1";

    public static final String BOT_CHAT_CREATE_EVENT = "p2p_chat_create";

    public static final String REDIS_KEY_OPENID_BOT_CODE = "feishu:openid:%s:%s";

    public static final String URL_VERIFICATION = "url_verification";

    public static final String HOTEL_SEARCH_FORMAT = "{\"text\":\"%s\"}";

    public static final String RESPONSE_FORMAT = "{\"msg\":\"%s\"}";

    public static final String CHALLENGE_RESPONSE_FORMAT = "{\"challenge\":\"%s\"}";
}
