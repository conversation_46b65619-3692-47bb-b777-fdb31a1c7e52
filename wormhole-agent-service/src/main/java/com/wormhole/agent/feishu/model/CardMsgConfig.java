package com.wormhole.agent.feishu.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/11/22 14:20
 */
@Setter
@Getter
public class CardMsgConfig {

    private Boolean enableForward; // 是否支持转发卡片。默认值 true。

    private Boolean updateMulti; // 是否为共享卡片。为 true 时即更新卡片的内容对所有收到这张卡片的人员可见。默认值 false。


    public CardMsgConfig(Builder builder) {
        this.enableForward = builder.enableForward;
        this.updateMulti = builder.updateMulti;
    }

    public static Builder newBuilder() {
        return new Builder();
    }


    public static class Builder {

        private Boolean enableForward;
        private Boolean updateMulti;

        public Builder enableForward(Boolean enableForward) {
            this.enableForward = enableForward;
            return this;
        }

        public Builder updateMulti(Boolean updateMulti) {
            this.updateMulti = updateMulti;
            return this;
        }

        public CardMsgConfig build() {
            return new CardMsgConfig(this);
        }

    }
}
