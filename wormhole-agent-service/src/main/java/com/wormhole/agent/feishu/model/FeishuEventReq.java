package com.wormhole.agent.feishu.model;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/11/26 15:43
 */
@Data
public class FeishuEventReq implements Serializable {
    //根据json生成java对象
    private String schema;
    private HeaderBean header;
    private EventBean event;

    @Data
    public static class HeaderBean {
        private String event_id;
        private String token;
        private String create_time;
        private String event_type;
        private String tenant_key;
        private String app_id;
    }

    @Data
    public static class EventBean {
        private MessageBean message;
        private SenderBean sender;

        @Data
        public static class MessageBean {
            private String chat_id;
            private String chat_type;
            private String content;
            private String create_time;
            private String message_id;
            private String message_type;
            private String update_time;

        }

        @Data
        public static class SenderBean {
            private SenderIdBean sender_id;
            private String sender_type;
            private String tenant_key;

            @Data
            public static class SenderIdBean {
                private String open_id;
                private String union_id;
                private String user_id;

            }
        }
    }
}
