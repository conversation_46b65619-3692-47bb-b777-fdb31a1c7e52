package com.wormhole.agent.channel.protocol;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.channel.message.Elem;
import com.wormhole.agent.model.openai.ChatCompletions;
import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ServerSentEvent extends Event {

    private String userId;

    private String botCode;

    private String conversationId;

    private String clientReqId;

    private Elem content;

    private String messageId;
}



