package com.wormhole.agent.channel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConnectionInfo {

    private String clientId;
    private String instanceId;
    @Builder.Default
    private String connectionTime = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

}