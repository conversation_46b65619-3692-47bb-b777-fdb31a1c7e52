package com.wormhole.agent.channel;

import com.wormhole.agent.channel.message.EventType;
import com.wormhole.agent.channel.protocol.ServerSentEvent;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class SseService {

    private final Map<String, Sinks.Many<ServerSentEvent>> sseConnections = new ConcurrentHashMap<>();

    public Flux<ServerSentEvent> subscribe(String clientId) {
        Sinks.Many<ServerSentEvent> sink = Sinks.many().multicast().onBackpressureBuffer();
        sseConnections.put(clientId, sink);
        return sink.asFlux()
                .doFinally(signalType -> {
                    sseConnections.remove(clientId);
                    System.out.println("Client disconnected: " + clientId);
                });
    }

    public void sendEvent(String clientId, String message) {
        Sinks.Many<ServerSentEvent> sink = sseConnections.get(clientId);
        if (sink != null) {
            ServerSentEvent event = new ServerSentEvent();
            event.setEvent(EventType.MESSAGE.getEvent());
            sink.tryEmitNext(event);
        }
    }

    public void broadcast(String message) {
        sseConnections.values().forEach(sink -> {
            ServerSentEvent event = new ServerSentEvent();
            event.setEvent(EventType.MESSAGE.getEvent());
            sink.tryEmitNext(event);
        });
    }
}