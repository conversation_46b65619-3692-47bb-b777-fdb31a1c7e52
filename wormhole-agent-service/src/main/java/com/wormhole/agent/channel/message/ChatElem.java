package com.wormhole.agent.channel.message;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.model.openai.ChatCompletions;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @author: joker.liu
 * @date: 2025/2/17
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChatElem extends Elem {

    /**
     * chat massage
     */
    private ChatCompletions chatCompletions;

    public ChatElem() {
        super(ElemType.ChatElem);
    }

    public ChatElem(ChatCompletions chatCompletions) {
        super(ElemType.ChatElem);
        this.chatCompletions = chatCompletions;
    }

}
