package com.wormhole.agent.channel.message;

import lombok.Getter;

/**
 * @author: joker.liu
 * @date: 2025/2/17
 * @Description:
 */
@Getter
public enum EventType {
    
    HEARTBEAT("heartbeat"),
    
    MESSAGE("message"),
    
    CONNECTION("connection"),
    
    DISCONNECTION("disconnection"),
    
    ;
    
    private final String event;
    
    EventType(String event) {
        this.event = event;
    }

}
