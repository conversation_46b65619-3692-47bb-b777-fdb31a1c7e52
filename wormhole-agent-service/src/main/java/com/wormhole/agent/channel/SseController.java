package com.wormhole.agent.channel;

import com.wormhole.agent.channel.protocol.ServerSentEvent;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/sse")
public class SseController {

    private final SseService sseService;

    public SseController(SseService sseService) {
        this.sseService = sseService;
    }

    /**
     * curl --location 'http://localhost:80/api/sse/subscribe/123'
     *
     * @param clientId
     * @return
     */
    @GetMapping(path = "/subscribe/{clientId}", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent> subscribe(@PathVariable String clientId) {
        return sseService.subscribe(clientId)
                .doOnSubscribe(s -> System.out.println("Client connected: " + clientId));
    }

    /**
     * curl --location 'http://localhost:80/api/sse/send/123' \
     * --header 'Content-Type: application/json' \
     * --data '123'
     *
     * @param clientId
     * @param message
     * @return
     */
    @PostMapping("/send/{clientId}")
    public Mono<Void> sendEvent(@PathVariable String clientId, @RequestBody String message) {
        sseService.sendEvent(clientId, message);
        return Mono.empty();
    }

    @PostMapping("/broadcast")
    public Mono<Void> broadcast(@RequestBody String message) {
        sseService.broadcast(message);
        return Mono.empty();
    }
}