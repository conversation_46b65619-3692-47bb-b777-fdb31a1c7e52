package com.wormhole.agent.channel;

import com.alibaba.nacos.common.utils.UuidUtils;
import com.google.common.base.Preconditions;
import com.wormhole.agent.channel.message.EventType;
import com.wormhole.agent.channel.message.MessageType;
import com.wormhole.agent.channel.protocol.ServerSentEvent;
import com.wormhole.common.constant.HeaderConstant;
import com.wormhole.common.util.IdUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.trace.TraceContext;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.data.redis.connection.ReactiveSubscription;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.redis.listener.ChannelTopic;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class ReactiveSseService implements SmartInitializingSingleton {

    private static final Logger logger = LoggerFactory.getLogger(ReactiveSseService.class);

    private final Map<String, Sinks.Many<ServerSentEvent>> sseConnections = new ConcurrentHashMap<>();

    private final String instanceId = IdUtils.generateId();
    private final String CHANNEL_NAME = "sse:messages";
    private final String DISCONNECT_CHANNEL = "sse:disconnect";

    @Resource
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;

    @Override
    public void afterSingletonsInstantiated() {
        subscribeToMessages();
        subscribeToDisconnectSignals();
    }

    @PreDestroy
    public void destroy() {
        // 清理资源
        sseConnections.keySet().forEach(this::cleanup);
    }

    // 订阅消息通道
    private void subscribeToMessages() {
        reactiveRedisTemplate.listenTo(ChannelTopic.of(CHANNEL_NAME))
                .map(ReactiveSubscription.Message::getMessage)
                .map(message -> JacksonUtils.readValue(message, SseMessage.class))
                .doOnNext(this::handleMessage)
                .doOnError(error -> logger.error("Error processing message: ", error))
                .retry()
                .subscribe();
    }

    // 订阅断开连接信号通道
    private void subscribeToDisconnectSignals() {
        reactiveRedisTemplate.listenTo(ChannelTopic.of(DISCONNECT_CHANNEL))
                .map(ReactiveSubscription.Message::getMessage)
                .map(message -> JacksonUtils.readValue(message, ConnectionInfo.class))
                .doOnNext(connectionInfo -> {
                    if (!instanceId.equals(connectionInfo.getInstanceId())) {
                        Sinks.Many<ServerSentEvent> sink = sseConnections.remove(connectionInfo.getClientId());
                        if (sink != null) {
                            sink.tryEmitComplete();
                        }
                    }
                })
                .subscribe();
    }

    // 处理客户端订阅
    public Flux<ServerSentEvent> subscribe() {

        return Flux.deferContextual(ctx -> {
            String deviceId = ctx.get(HeaderConstant.DEVICE_ID);
            if (StringUtils.isBlank(deviceId)) {
                deviceId = UuidUtils.generateUuid();
            }
            logger.info("Client connected: {}", deviceId);
            return doSubscribe(deviceId);
        });
    }

    private Flux<ServerSentEvent> doSubscribe(String deviceId) {
        return Mono.justOrEmpty(sseConnections.remove(deviceId))
                .doOnNext(sink -> {
                    if (sink != null) {
                        sink.tryEmitComplete();
                    }
                })
                .then(Mono.defer(() -> {
                    ConnectionInfo connectionInfo = ConnectionInfo.builder().clientId(deviceId).instanceId(instanceId).build();
                    return reactiveRedisTemplate.convertAndSend(DISCONNECT_CHANNEL, JacksonUtils.writeValueAsString(connectionInfo));
                }))
                .thenMany(createEventFlux(deviceId));
    }

    private Flux<ServerSentEvent> createEventFlux(String connectionId) {
        return Flux.defer(() -> {
            Sinks.Many<ServerSentEvent> sink = Sinks.many().multicast().onBackpressureBuffer();
            sseConnections.put(connectionId, sink);

            SseMessage sseMessage = SseMessage.builder()
                    .event(EventType.CONNECTION.getEvent())
                    .connectionId(connectionId).traceId(TraceContext.getTraceId()).build();

            // 发送连接成功消息
            sendEventToSink(sink, sseMessage);
            return sink.asFlux().doFinally(signal -> cleanup(connectionId));
        });
    }

    private void cleanup(String clientId) {
        sseConnections.remove(clientId);
        logger.info("Client disconnected: {}", clientId);
    }

    // 发送消息给指定客户端
    public Mono<Void> sendEvent(SseMessage sseMessage) {
        sseMessage.setType(MessageType.SINGLE.name());
        return publishMessage(sseMessage);
    }

    // 广播消息
    public Mono<Void> broadcast(String message) {
        SseMessage broadcastMessage = SseMessage.builder().message(message).type(MessageType.BROADCAST.name()).build();
        return publishMessage(broadcastMessage);
    }

    private Mono<Void> publishMessage(SseMessage message) {
        if (sseConnections.containsKey(message.getConnectionId())) {
            handleMessage(message);
            return Mono.empty();
        }
        String messageJson = JacksonUtils.writeValueAsString(message);
        return reactiveRedisTemplate.convertAndSend(CHANNEL_NAME, messageJson).then();
    }

    private void handleMessage(SseMessage sseMessage) {
        if (MessageType.BROADCAST.name().equals(sseMessage.getType())) {
            sseConnections.values().forEach(sink ->
                    sendEventToSink(sink, sseMessage));
        } else {
            Sinks.Many<ServerSentEvent> sink = sseConnections.get(sseMessage.getConnectionId());
            if (sink != null) {
                // 发送消息给指定客户端
                sendEventToSink(sink, sseMessage);
            }
        }
    }

    private void sendEventToSink(Sinks.Many<ServerSentEvent> sink, SseMessage sseMessage) {

        Preconditions.checkArgument(StringUtils.isNotBlank(sseMessage.getConnectionId()), "连接id 不能为空");

        ServerSentEvent serverSentEvent =
                ServerSentEvent.builder().userId(sseMessage.getUserId())
                        .conversationId(sseMessage.getConversationId())
                        .content(sseMessage.getContent())
                        .clientReqId(sseMessage.getClientReqId())
                        .messageId(sseMessage.getMessageId())
                        .botCode(sseMessage.getBotCode())
                        .userId(sseMessage.getUserId())
                        .build();

        serverSentEvent.setTraceId(sseMessage.getTraceId());
        serverSentEvent.setEvent(sseMessage.getEvent());
        serverSentEvent.setConnectionId(sseMessage.getConnectionId());

        sink.tryEmitNext(serverSentEvent);

    }

    // 心跳服务
    @Scheduled(fixedRate = 30000)
    public void sendHeartbeat() {
        // 使用 Iterator 来安全地遍历和移除连接
        Iterator<Map.Entry<String, Sinks.Many<ServerSentEvent>>> iterator = sseConnections.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, Sinks.Many<ServerSentEvent>> entry = iterator.next();
            String connectionId = entry.getKey();
            Sinks.Many<ServerSentEvent> sink = entry.getValue();

            if (isConnectionInvalid(sink)) {
                iterator.remove();  // 安全地从 Map 中移除
                sink.tryEmitComplete();  // 完成并关闭 sink
                return;
            }

            try {
                ServerSentEvent heartbeat = ServerSentEvent.builder()
                        .build();

                heartbeat.setEvent(EventType.HEARTBEAT.getEvent());
                heartbeat.setConnectionId(connectionId);

                // 尝试发送心跳
                Sinks.EmitResult result = sink.tryEmitNext(heartbeat);

                // 检查发送结果
                if (result == Sinks.EmitResult.FAIL_NON_SERIALIZED ||
                        result == Sinks.EmitResult.FAIL_ZERO_SUBSCRIBER ||
                        result == Sinks.EmitResult.FAIL_TERMINATED ||
                        result == Sinks.EmitResult.FAIL_OVERFLOW) {

                    // 发送失败，说明连接可能已断开
                    logger.warn("Client {} heartbeat failed, removing connection. Emit result: {}", connectionId, result);
                    iterator.remove();
                    sink.tryEmitComplete();
                }
            } catch (Exception e) {
                // 发生异常，移除连接
                logger.error("Error sending heartbeat to client {}: {}", connectionId, e.getMessage());
                iterator.remove();
                sink.tryEmitComplete();
            }
        }
    }

    private boolean isConnectionInvalid(Sinks.Many<ServerSentEvent> sink) {
        return sink.currentSubscriberCount() == 0;
    }
}