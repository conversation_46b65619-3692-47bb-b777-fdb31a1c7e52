package com.wormhole.agent.channel;

import com.wormhole.agent.channel.message.Elem;
import com.wormhole.agent.channel.protocol.ServerSentEvent;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/channel")
public class ChannelController {

    @Resource
    private ReactiveSseService reactiveSseService;

    /**
     * curl --location 'http://localhost:80/api/channel/subscribe'
     *
     * @return
     */
    @RequestMapping(path = "/subscribe", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public Flux<ServerSentEvent> subscribe() {
        return reactiveSseService.subscribe();
    }

    /**
     * curl --location 'http://localhost:80/api/channel/send/1234' \
     * --header 'Content-Type: application/json' \
     * --data 'test-data'
     *
     * @param sseMessage
     * @return
     */
    @PostMapping("/send")
    public Mono<Result<String>> sendEvent( @RequestBody SseMessage sseMessage) {
        return reactiveSseService.sendEvent(sseMessage).thenReturn(StringUtils.EMPTY).flatMap(Result::success);
    }

    /**
     * curl --location 'http://localhost:80/api/channel/broadcast' \
     * --header 'Content-Type: application/json' \
     * --data 'test-data'
     *
     * @param message
     * @return
     */
    @PostMapping("/broadcast")
    public Mono<Result<String>> broadcast(@RequestBody String message) {
        return reactiveSseService.broadcast(message).thenReturn(StringUtils.EMPTY).flatMap(Result::success);
    }
}