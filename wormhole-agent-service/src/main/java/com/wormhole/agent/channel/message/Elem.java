package com.wormhole.agent.channel.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/2/17
 * @Description:
 */
@Data
public abstract class Elem implements Serializable {

    @JsonIgnore
    private ElemType type;

    private Elem() {}

    protected Elem(ElemType type) {
        this.type = type;
    }

}
