package com.wormhole.agent.channel;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.channel.message.ChatElem;
import com.wormhole.agent.channel.message.Elem;
import com.wormhole.agent.channel.protocol.ServerSentEvent;
import com.wormhole.agent.model.openai.ChatCompletions;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SseMessage {

    private String userId;

    private String connectionId;

    private String clientReqId;

    private String conversationId;

    private String message;

    private String botCode;

    private String event;

    private ChatElem content;

    /**
     * 'single' or 'broadcast'
     */
    private String type;
    @Builder.Default
    private String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

    private String traceId;

    private String messageId;

}