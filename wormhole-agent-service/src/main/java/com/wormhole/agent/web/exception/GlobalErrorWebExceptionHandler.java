package com.wormhole.agent.web.exception;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.Result;
import com.wormhole.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.web.WebProperties;
import org.springframework.boot.autoconfigure.web.reactive.error.AbstractErrorWebExceptionHandler;
import org.springframework.boot.web.reactive.error.ErrorAttributes;
import org.springframework.context.ApplicationContext;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.server.RequestPredicates;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

/**
 * GlobalErrorWebExceptionHandler
 *
 * <AUTHOR>
 * @version 2025/1/24
 */
@Slf4j
@Component
public class GlobalErrorWebExceptionHandler extends AbstractErrorWebExceptionHandler {

    public GlobalErrorWebExceptionHandler(ErrorAttributes errorAttributes, WebProperties.Resources resources, ServerCodecConfigurer serverCodecConfigurer, ApplicationContext applicationContext) {
        super(errorAttributes, resources, applicationContext);
        this.setMessageWriters(serverCodecConfigurer.getWriters());
    }

    @Override
    protected RouterFunction<ServerResponse> getRoutingFunction(ErrorAttributes errorAttributes) {
        return RouterFunctions.route(RequestPredicates.all(), request ->
                handleError(getError(request))
                        .flatMap(result -> ServerResponse.ok().bodyValue(result)));
    }

    private Mono<Result<Object>> handleError(Throwable error) {
        if (error instanceof BusinessException ex) {
            log.error(((BusinessException) error).getMsg(), error);
            return Result.failed(ex.getCode(), ex.getMsg());
        }
        log.error("handleError {}",error.getMessage(), error);
        if (error instanceof IllegalArgumentException ex) {
            return Result.failed(ResultCode.INVALID_PARAMETER, ex.getMessage());
        }
        return Result.failed(ResultCode.INTERNAL_SERVER_ERROR, "系统繁忙，请稍后重试!");
    }
}
