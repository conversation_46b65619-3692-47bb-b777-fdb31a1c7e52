package com.wormhole.agent.enums;

import lombok.Getter;

/**
 * @author: joker.liu
 * @date: 2025/3/31
 * @Description:
 */
@Getter
public enum RtcCallType {

    // 前台、客房、智能体
    FRONTEND(1, "前台"),
    ROOM(2, "客房"),
    AGENT(3, "智能体"),
    ;

    private final Integer code;

    private final String desc;

    RtcCallType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
