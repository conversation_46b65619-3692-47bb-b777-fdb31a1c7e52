package com.wormhole.agent.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum BotCategoryEnum {
    DEFAULT("默认", "default"),
    HOT("热门", "Hot"),
    TOOLS("工具", "Tools"),
    OFFICE("办公", "Office"),
    GUEST_EXPERIENCE("客服", "GuestExperience"),
     ROOM_SERVICES("客房服务", "RoomServices"),
     LIFE("生活", "Life"),
     OPERATIONS_MANAGEMENT("运营管理", "Operations"),
     MARKETING_PROMOTION("营销推广", "Marketing"),
     DATA_ANALYTICS("数据分析", "Analytics")
    ;

    private final String categoryName;
    private final String category;

    BotCategoryEnum(String categoryName, String category) {
        this.categoryName = categoryName;
        this.category = category;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public String getCategory() {
        return category;
    }

    // 通过中文名称查找枚举
    public static BotCategoryEnum fromCategory(String category) {
        for (BotCategoryEnum categoryEnum : values()) {
            if (categoryEnum.categoryName.equals(categoryEnum)) {
                return categoryEnum;
            }
        }
        throw new IllegalArgumentException("未知的分类: " + category);
    }

    public static BotCategoryEnum fromCategoryName(String categoryName) {
        String searchName = categoryName.trim().toLowerCase();
        for (BotCategoryEnum category : values()) {
            if (category.category.toLowerCase().equals(searchName)) {
                return category;
            }
        }
        throw new IllegalArgumentException("未知的分类: " + categoryName);
    }

    // 格式化输出
    @Override
    public String toString() {
        return String.format("%s | %s", categoryName, category);
    }

    // 获取所有名称
    public static List<String> getAllCategoryNames() {
        return Arrays.stream(values())
                .map(BotCategoryEnum::getCategoryName)
                .collect(Collectors.toList());
    }

    // 获取所有英文名称
    public static List<String> getAllCategorys() {
        return Arrays.stream(values())
                .map(BotCategoryEnum::getCategory)
                .collect(Collectors.toList());
    }
}
