package com.wormhole.agent.enums;

/**
 * PublishStatusEnum
 *
 * <AUTHOR>
 * @version 2025/02/10
 */
public enum PublishStatusEnum {
    /**
     * 草稿
     */
    draft("draft"),
    /**
     * 线上
     */
    online("online"),
    /**
     * 线下
     */
    offline("offline"),
    /**
     * 删除
     */
    deleted("deleted");

    /**
     * 状态
     */
    private String status;

    PublishStatusEnum(String status) {
        this.status = status;
    }

    /**
     * 获取状态
     *
     * @return
     */
    public String getStatus() {
        return status;
    }

    /**
     * 根据状态获取枚举
     *
     * @param status
     * @return
     */
    public static PublishStatusEnum getEnum(String status) {
        for (PublishStatusEnum item : PublishStatusEnum.values()) {
            if (item.getStatus().equals(status)) {
                return item;
            }
        }
        return null;
    }

}
