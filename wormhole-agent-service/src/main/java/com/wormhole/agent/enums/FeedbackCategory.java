package com.wormhole.agent.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@AllArgsConstructor
@Getter
public enum FeedbackCategory {
    INACCURATE_RESPONSE("回答不准确"),
    DID_NOT_FOLLOW_QUESTION("没有按照问题作答"),
    INAPPROPRIATE_STYLE("回答风格不妥"),
    TOO_SIMPLE_RESPONSE("回复过于简单"),
    NO_NEED_FOR_PREVIOUS_CONTEXT("不需要参考之前的对话"),
    SHOULD_NOT_REFUSE("不应拒绝我的请求"),
    UI_ISSUE("界面显示有问题"),
    OTHER_ISSUE("其他问题");

    private final String description;


    public static FeedbackCategory from(String value) {
        for (FeedbackCategory category : FeedbackCategory.values()) {
            if (Objects.equals(category.name(),value)) {
                return category;
            }
        }
        return null;
    }
}