package com.wormhole.agent.query;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelInfoRedisReq {

    /**
     * 酒店代码
     */
    private String hotelCode;

    /**
     * 信息类别
     */
    private String category;

    /**
     * 酒店信息内容（JSON字符串）
     */
    private String info;

    /**
     * 过期时间（秒），默认为0表示永不过期
     */
    private Long expireSeconds = 0L;
}
