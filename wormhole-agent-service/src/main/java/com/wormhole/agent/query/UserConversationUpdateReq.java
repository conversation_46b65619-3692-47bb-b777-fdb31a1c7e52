package com.wormhole.agent.query;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.bot.ObjectInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/2/18
 * @Description:    修改用户会话请求
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserConversationUpdateReq implements Serializable {

    /**
     * 会话表id
     */
    private Long id;

    /**
     * 会话id
     */
    private String conversationId;

    /**
     * 用户人id
     */
    private String userId;
    private String username;

    /**
     * 智能体code
     */
    private String botCode;

    /**
     * 会话标题
     */
    private String title;
    private String content;
    private String source;

    /**
     * 图形信息
     */
    private ObjectInfo imageJson;

}
