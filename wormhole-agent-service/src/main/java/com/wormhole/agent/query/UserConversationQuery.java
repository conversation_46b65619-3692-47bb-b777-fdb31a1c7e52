package com.wormhole.agent.query;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserConversationQuery extends QueryCondition {

    private String userId;
    /**
     * 智能体编码
     */
    private String botCode;

    /**
     * 指定的conversationId
     */
    private String conversationId;

    private String source;

    private String deviceId;

    private String hotelCode;

    private String positionCode;

    private Integer rowStatus;

}
