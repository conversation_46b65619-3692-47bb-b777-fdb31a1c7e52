package com.wormhole.agent.query;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChatConversationPageReq implements Serializable {
    private String botCode;
    private String userId;
    private String title;
    private String source;

    /**
     * 页码从1开始
     */
    private Integer current;

    /**
     * 每页显示记录数，必须大于0
     */
    private Integer pageSize;
}
