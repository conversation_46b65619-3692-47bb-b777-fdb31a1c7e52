package com.wormhole.agent.query;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date  2024/10/22
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BotQueryCondition extends QueryCondition {

    /**
     * 用户id
     */
    private String userId;
    /**
     * 机器人名称 模糊匹配即可
     */
    private String botName;
    /**
     * 分类
     */
    private String category;

    /**
     * 查询详情时，必填字段；机器人id
     */
    private String botCode;

    /**
     * 是否发布
     */
    private Integer isPublished;

    /**
     * 空间id
     */
    private String spaceCode;

    /**
     * 最近打开
     */
    private Boolean recentlyOpen;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 版本描述
     */
    private String versionDesc;


}
