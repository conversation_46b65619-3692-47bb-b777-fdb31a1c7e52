package com.wormhole.agent.query;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/4/8
 * @Description:
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HotelDeviceReq implements Serializable {

    private String hotelCode;

    private String deviceType;

    private String deviceId;

    private String targetDeviceId;

    private String rtcRoomId;

}
