package com.wormhole.agent.query;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class QueryCondition implements Serializable {

    @Serial
    private static final long serialVersionUID = 178921331529781315L;

    /**
     * 页码从1开始
     */
    protected Integer current = 1;
    /**
     * 每页显示记录数，必须大于0
     */
    protected Integer pageSize = 50;
    /**
     * 排序字段
     */
    protected String sort;
    /**
     * 排序方向：ASC/DESC
     */
    protected String order;
    /**
     * 主键id
     */
    protected Long id;
    /**
     * 创建时间-查询范围的开始时间
     */
    protected String gmtCreateStart;
    /**
     * 创建时间-查询范围的结束时间
     */
    protected String gmtCreateEnd;
    /**
     * 排序语句，譬如：id desc
     */
    private String orderByClause;

    private String searchAfter;
    /**
     * 一般根据id、name、title做模糊检索
     */
    private String searchKey;

}
