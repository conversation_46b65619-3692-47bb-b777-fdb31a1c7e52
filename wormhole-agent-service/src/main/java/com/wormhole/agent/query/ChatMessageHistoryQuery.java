package com.wormhole.agent.query;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChatMessageHistoryQuery extends QueryCondition{
    /**
     * 空间编码（冗余存储）
     */
    private String spaceCode;
    private String userId;

    /**
     * 会话ID（ULID格式）
     */
    private String conversationId;

    /**
     * 父消息ID（构成消息树）
     */
    private String parentMessageId;

    /**
     * 客户端请求ID（用于去重）
     */
    private String clientReqId;

    /**
     * 原始设备ID（用于设备追踪）
     */
    private String deviceId;


    /**
     * 智能体编码
     */
    private String botCode;

    /**
     * 消息类型（user/assistant/system）
     */
    private String messageType;

    private boolean searchAll = false;

    private String maxIdExclusive;
}
