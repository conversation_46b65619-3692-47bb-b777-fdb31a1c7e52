package com.wormhole.agent.query;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.entity.UserChatMessageEntity;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/5
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChatMsgFeedBackReq implements Serializable {
    private String id;
    private Integer feedbackStatus;
    private List<String> feedbackTypes;
    private String feedbackContent;
    private String clientReqId;
}
