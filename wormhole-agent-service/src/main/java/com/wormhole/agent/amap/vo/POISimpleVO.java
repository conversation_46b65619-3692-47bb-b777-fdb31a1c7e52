package com.wormhole.agent.amap.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 封装高德地图返回的兴趣点（POI）简化信息。
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class POISimpleVO {
    /**
     * 兴趣点名称，例如“北京西站”。
     */
    private String name;

    /**
     * 兴趣点类型，例如“交通设施”。
     */
    private String type;

    /**
     * 与当前位置的距离，单位可能是米或千米，具体取决于高德 API 返回内容。
     */
    private String distance;

    /**
     * 兴趣点地址，例如“北京市海淀区中关村大街1号”。
     */
    private String address;
}
