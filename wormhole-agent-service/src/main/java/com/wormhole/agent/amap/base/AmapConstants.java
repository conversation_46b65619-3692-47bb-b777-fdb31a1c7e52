package com.wormhole.agent.amap.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
public class AmapConstants {

    public interface Url{
        public static final String AROUND_SEARCH_API_URL = "/v5/place/around";
        public static final String KEYWORD_SEARCH_API_URL = "/v5/place/text";
        public static final String DRIVING_ROUTE_API_URL = "/v5/direction/driving";
        public static final String WALKING_ROUTE_API_URL = "/v5/direction/walking";
        public static final String BICYCLING_ROUTE_API_URL = "/v5/direction/bicycling";
        public static final String ELECTROBIKE_ROUTE_API_URL = "/v5/direction/electrobike";
        public static final String WEATHER_INFO_API_URL = "/v3/weather/weatherInfo";

        //行政区域查询
        public static final String ADMIN_AREA_API_URL = "/v3/config/district";

        public static final String GEO_RECODE_URL = "/v3/geocode/regeo";

    }
    @AllArgsConstructor
    @Getter
    public enum TrafficTypeEnum {
       //驾车，步行
        DRIVING(1,"驾车"),
        WALKING(2,"步行"),
        ;
        private Integer code;
        private String description;

        public static String getUrlByTrafficType(Integer trafficType) {
            if (Objects.equals(trafficType, DRIVING.getCode())) {
                return Url.DRIVING_ROUTE_API_URL;
            } else if (Objects.equals(trafficType, WALKING.getCode())) {
                return Url.WALKING_ROUTE_API_URL;
            }
            return null;
        }
        public static TrafficTypeEnum findByCode(Integer code){
            for (TrafficTypeEnum value : TrafficTypeEnum.values()) {
                if(Objects.equals(code,value.getCode())) {
                    return value;
                }
            }
            return null;
        }
    }


    @AllArgsConstructor
    @Getter
    public enum ErrorCode {
        OK(10000, "请求正常", "请求正常"),
        INVALID_USER_KEY(10001, "key不正确或过期", "开发者发起请求时，传入的key不正确或者过期"),
        SERVICE_NOT_AVAILABLE(10002, "没有权限使用相应的服务或者请求接口的路径拼写错误",
                "1. 开发者没有权限使用相应的服务。2. 请求接口的路径拼写错误。"),
        DAILY_QUERY_OVER_LIMIT(10003, "访问已超出日访问量", "开发者的日访问量超限，第二天0:00会自动解封。"),
        ACCESS_TOO_FREQUENT(10004, "单位时间内访问过于频繁", "开发者的单位时间内访问量超限，下一分钟自动解封。"),
        INVALID_USER_IP(10005, "IP白名单出错", "开发者在LBS官网控制台设置的IP白名单不正确。"),
        INVALID_USER_DOMAIN(10006, "绑定域名无效", "开发者绑定的域名无效，需要在官网控制台重新设置"),
        INVALID_USER_SIGNATURE(10007, "数字签名未通过验证", "开发者签名未通过验证"),
        INVALID_USER_SCODE(10008, "MD5安全码未通过验证", "需要开发者判定key绑定的SHA1,package是否与sdk包里的一致"),
        USERKEY_PLAT_NOMATCH(10009, "请求key与绑定平台不符", "请求中使用的key与绑定平台不符"),
        IP_QUERY_OVER_LIMIT(10010, "IP访问超限", "未设定IP白名单的开发者使用key发起请求，超出限制。"),
        NOT_SUPPORT_HTTPS(10011, "服务不支持https请求", "服务不支持https请求，需要提交工单联系我们"),
        INSUFFICIENT_PRIVILEGES(10012, "权限不足，服务请求被拒绝", "由于不具备请求该服务的权限，所以服务被拒绝。"),
        USER_KEY_RECYCLED(10013, "Key被删除", "开发者删除了key，key被删除后无法正常使用"),
        QPS_HAS_EXCEEDED_THE_LIMIT(10014, "云图服务QPS超限", "QPS超出限制，超出部分的请求被拒绝。"),
        GATEWAY_TIMEOUT(10015, "受单机QPS限流限制", "受单机QPS限流限制时出现该问题。"),
        SERVER_IS_BUSY(10016, "服务器负载过高", "服务器负载过高，请稍后再试"),
        RESOURCE_UNAVAILABLE(10017, "所请求的资源不可用", "所请求的资源不可用"),
        CQPS_HAS_EXCEEDED_THE_LIMIT(10019, "使用的某个服务总QPS超限", "QPS超出限制，超出部分的请求被拒绝。限流阈值内的请求依旧会正常返回"),
        CKQPS_HAS_EXCEEDED_THE_LIMIT(10020, "某个Key使用某个服务接口QPS超出限制", "QPS超出限制，超出部分的请求被拒绝。限流阈值内的请求依旧会正常返回"),
        CUQPS_HAS_EXCEEDED_THE_LIMIT(10021, "账号使用某个服务接口QPS超出限制", "QPS超出限制，超出部分的请求被拒绝。限流阈值内的请求依旧会正常返回"),
        INVALID_REQUEST(10026, "账号处于被封禁状态", "由于违规行为账号被封禁不可用，如有异议请登录控制台提交工单进行申诉"),
        ABROAD_DAILY_QUERY_OVER_LIMIT(10029, "某个Key的QPS超出限制", "QPS超出限制，超出部分的请求被拒绝。限流阈值内的请求依旧会正常返回"),
        NO_EFFECTIVE_INTERFACE(10041, "请求的接口权限过期", "开发者发起请求时，请求的接口权限过期。请提交工单联系我们"),
        USER_DAILY_QUERY_OVER_LIMIT(10044, "账号维度日调用量超出限制", "账号维度日调用量超出限制，超出部分的请求被拒绝。限流阈值内的请求依旧会正常返回"),
        USER_ABROAD_DAILY_QUERY_OVER_LIMIT(10045, "账号维度海外服务日调用量超出限制", "账号维度海外服务接口日调用量超出限制，超出部分的请求被拒绝。限流阈值内的请求依旧会正常返回"),
        INVALID_PARAMS(20000, "请求参数非法", "请求参数的值没有按照规范要求填写。例如，某参数值域范围为[1,3],开发者误填了’4’"),
        MISSING_REQUIRED_PARAMS(20001, "缺少必填参数", "缺少接口中要求的必填参数"),
        ILLEGAL_REQUEST(20002, "请求协议非法", "请求协议非法，比如某接口仅支持get请求，结果用了POST方式"),

        // 默认错误码
        UNKNOWN_ERROR(20003, "其他未知错误", "其他未知错误"),
        INSUFFICIENT_ABROAD_PRIVILEGES(20011, "INSUFFICIENT_ABROAD_PRIVILEGES", "查询坐标或规划点在海外，但没有海外地图权限"),
        ILLEGAL_CONTENT(20012, "ILLEGAL_CONTENT", "查询信息存在非法内容"),
        OUT_OF_SERVICE(20800, "OUT_OF_SERVICE", "规划点不在中国陆地范围内"),
        NO_ROADS_NEARBY(20801, "NO_ROADS_NEARBY", "划点附近搜不到路"),
        ROUTE_FAIL(20802, "ROUTE_FAIL", "路线计算失败"),
        OVER_DIRECTION_RANGE(20803, "OVER_DIRECTION_RANGE", "起点终点距离过长"),
        ENGINE_RESPONSE_DATA_ERROR(30000, "ENGINE_RESPONSE_DATA_ERROR", "服务响应失败(出现3开头的错误码，建议先检查传入参数是否正确)"),
        QUOTA_PLAN_RUN_OUT(40000, "QUOTA_PLAN_RUN_OUT", "余额耗尽"),
        GEOFENCE_MAX_COUNT_REACHED(40001, "GEOFENCE_MAX_COUNT_REACHED", "围栏个数达到上限"),
        SERVICE_EXPIRED(40002, "SERVICE_EXPIRED", "购买服务到期"),
        ABROAD_QUOTA_PLAN_RUN_OUT(40003, "ABROAD_QUOTA_PLAN_RUN_OUT", "海外服务余额耗尽");

        private final int code;
        private final String description;
        private final String troubleshooting;

        public static ErrorCode findByCode(String code){
            for (ErrorCode value : ErrorCode.values()) {
                if(Objects.equals(Integer.valueOf(code),value.getCode())) {
                    return value;
                }
            }
            return  StringUtils.isNotBlank(code) && code.startsWith("3") ? ENGINE_RESPONSE_DATA_ERROR : UNKNOWN_ERROR;
        }
    }


    @AllArgsConstructor
    @Getter
    public enum LocationType{
        DISTRICT(1,"行政区"),
        ;
        private Integer code;
        private String desc;
    }

    public final static String distanceText1 = "距离%s直线%s,%s约%s"; //距离酒店直线多少米,驾车约多少分钟
    public final static String distanceText2 = "距离%s直线距离%s";  //距离酒店直线距离多少km，距离xx多少km

    @Data
    public static class RedisConstants {
        private static final String  PREFIX = "AMAP:";
        public static final String ListPageNavigateBar = PREFIX + "ListPageNavigateBar"; //hashKey: cityName -> result
        public static final String DetailPageNavigateBar = PREFIX + "DetailPageNavigateBar:%s"; //hashKey: hotelCode->tab_trafficType ->result
    }
}
