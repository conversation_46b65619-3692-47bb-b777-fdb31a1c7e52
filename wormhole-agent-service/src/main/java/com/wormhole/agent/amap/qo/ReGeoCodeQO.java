package com.wormhole.agent.amap.qo;

import com.wormhole.agent.amap.base.AmapRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/6
 */
@Data
public class ReGeoCodeQO extends AmapRequest {
    // 经纬度坐标，经度在前，纬度在后，经纬度间以“,”分割（必填）
    private String location;
    // 返回附近POI类型，extensions参数为all时生效（可选）
    private String poitype;
    // 搜索半径，取值范围0~3000，默认值1000，单位：米（可选，默认1000）
    private int radius = 1000;
    // 返回结果控制，extensions参数默认取值是base，extensions参数取值为all时会返回更多信息（可选，默认base）
    private String extensions = "base";
    // 道路等级，extensions参数为all时生效（可选）
    private int roadlevel;
    // 数字签名（可选）
    private String sig;
    // 返回数据格式类型，可选输入内容包括：JSON，XML（可选，默认JSON）
    private String output = "JSON";
    // 回调函数，只在output参数设置为JSON时有效（可选）
    private String callback;
    // 是否优化POI返回顺序，extensions参数为all时生效（可选，默认0）
    private int homeorcorp = 0;
}
