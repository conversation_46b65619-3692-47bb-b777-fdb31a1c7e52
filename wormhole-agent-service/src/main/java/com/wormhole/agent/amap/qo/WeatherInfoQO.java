package com.wormhole.agent.amap.qo;

import com.wormhole.agent.amap.base.AmapRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class WeatherInfoQO extends AmapRequest {
    /**
     * 城市编码（必填）
     * 输入城市的adcode，adcode信息可参考城市编码表
     */
    private String city;

    /**
     * 气象类型（可选）
     * 可选值：base/all
     * base: 返回实况天气
     * all: 返回预报天气
     */
    private String extensions;

}
