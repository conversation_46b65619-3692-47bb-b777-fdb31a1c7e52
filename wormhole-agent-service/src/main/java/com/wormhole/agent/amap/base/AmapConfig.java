package com.wormhole.agent.amap.base;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.wormhole.agent.amap.enums.LocationNavigateBarEnum.AIRPORT_TRAIN;
import static com.wormhole.agent.amap.enums.LocationNavigateBarEnum.DISTRICT;
import static com.wormhole.agent.amap.enums.LocationNavigateBarEnum.FOOD;
import static com.wormhole.agent.amap.enums.LocationNavigateBarEnum.HOSPITAL;
import static com.wormhole.agent.amap.enums.LocationNavigateBarEnum.SCENIC;
import static com.wormhole.agent.amap.enums.LocationNavigateBarEnum.SHOPPING;
import static com.wormhole.agent.amap.enums.LocationNavigateBarEnum.TRAFFIC;
import static com.wormhole.agent.amap.enums.LocationNavigateBarEnum.UNIVERSITY;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "amap")
public class AmapConfig {
    private String url = "https://restapi.amap.com";

    private String key = "1073cee5a12ac2452e1133cd1b770b76";

    private Map<String,Integer> locationNavigateBarCode2Distance = new HashMap<>();

    private List<String> detailPageNavigateBar = Lists.newArrayList(TRAFFIC.getTypeCode(), SCENIC.getTypeCode(), FOOD.getTypeCode(),SHOPPING.getTypeCode());
    private List<String> listPageNavigateBar = Lists.newArrayList(DISTRICT.getTypeCode(),AIRPORT_TRAIN.getTypeCode(),HOSPITAL.getTypeCode(),UNIVERSITY.getTypeCode());


    private String TOB_TYPES = "050000|060000|110000|150000|141200|090000|080000|011100";



    public String SHOWFIELDS = "business";

    public Integer RADIUS = 10000;


    private Integer detailPageSizeSearchLimit = 10;
    private Integer listPageSizeSearchLimit = 20;

}
