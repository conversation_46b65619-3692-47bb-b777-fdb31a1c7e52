package com.wormhole.agent.amap.qo;

import com.wormhole.agent.amap.base.AmapRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class NavigationSearchQO extends AmapRequest {
    // 起点经纬度，经度在前，纬度在后，用","分割，小数点后不超过6位（必填）
    private String origin;
    // 目的地经纬度，格式同origin（必填）
    private String destination;
    // 终点的POI类别，当知道终点POI类别时建议填充（选填）
    private String destinationType;
    // 目的地POI ID，目的地为POI时建议填充，可提升路径规划准确性（选填）
    private String destinationId;
    // 驾车算路策略，根据不同的策略返回不同的路线（选填，默认32）
    private int strategy;
    // 途经点，多个途经点坐标按顺序以";"分隔，最大支持16个途经点（选填）
    private String waypoints;
    // 避让区域，多个区域坐标按顺序以"|"分隔，最大支持32个避让区域（选填）
    private String avoidpolygons;
    // 车牌号码，用于判断限行相关（选填）
    private String plate;
    // 车辆类型，0：普通燃油汽车，1：纯电动汽车，2：插电式混动汽车（选填，默认0）
    private int cartype;
    // 是否使用轮渡，0：使用，1：不使用（选填，默认0）
    private int ferry;
    // 返回结果控制，筛选response结果中可选字段（选填）
    private String show_fields;
    // 数字签名，用于验证请求的合法性（选填）
    private String sig;
    // 返回结果格式类型，可选值：JSON（选填，默认json）
    private String output;
    // 回调函数，只在output参数设置为JSON时有效（选填）
    private String callback;
}
