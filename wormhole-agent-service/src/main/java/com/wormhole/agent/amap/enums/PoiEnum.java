package com.wormhole.agent.amap.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum PoiEnum {
    FOOD("050000","餐饮"),
    TOURIST_ATTRACTION("110000","风景名胜"),
    SHOPPING("060000","购物相关场所"),
    SHOPPING_PLAZA("060100","商场"),
    TRANSPORTATION("150000","交通服务相关"),
    AIRPORT("150100","机场"),
    TRAIN_STATION("150200","火车站"),
    SUBWAY_STATION("150500","地铁站"),
    BUS_STATION("150700","公交站"),
    SCHOOL("141200","学校"),
    UNIVERSITY("141201","高等院校"),
    MEDICAL("090000","医疗保健服务"),
    SPORTS_RECREATION("080000","体育休闲"),
    CHARGING_AND_POWER_SWAPPING_STATION("011100","充电站"),
    FILLING_STATION("010100","加油站"),
    HOTEL("100100","宾馆酒店"),
    PUBLIC_TOILET("200300","公共厕所")
    ;

    private String code;
    private String name;
}
