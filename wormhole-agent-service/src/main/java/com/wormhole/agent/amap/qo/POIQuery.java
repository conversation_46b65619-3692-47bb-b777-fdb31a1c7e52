package com.wormhole.agent.amap.qo;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.poi.constant.AmapConstants;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class POIQuery {

    private String keywords;
    /**
     * （选填）
     * 地点文本搜索接口支持按照设定的POI类型限定地点搜索结果；
     * 地点类型与 poi typecode 是同类内容，可以传入多个 poi typecode，相互之间用“|”分隔，内容可以参考 POI 分类码表；
     * 地点（POI）列表的排序会按照高德搜索能力进行综合权重排序；
     * 当 keywords 和 types 均为空的时候，默认指定 types 为050000（餐饮服务）、070000（生活服务）、120000（商务住宅）
     *
     * @see AmapConstants.PoiEnum
     *
     * */
    private String types;

    /**
     * （必填）
     * 圆形区域检索中心点的经度，小数点后不得超过6位
     */
    private String longitude;

    /**
     * （必填）
     * 圆形区域检索中心点的纬度，小数点后不得超过6位
     */
    private String latitude;

    /**
     * （选填）
     * 取值范围:0-50000，大于50000时按默认值，单位：米
     */
    private Integer radius;
    /**
     * （选填）
     * page_num 的取值1-100
     */
    private Integer page_num;

    /**
     * （选填）
     * page_size 的取值1-25
     */
    private Integer page_size;

}
