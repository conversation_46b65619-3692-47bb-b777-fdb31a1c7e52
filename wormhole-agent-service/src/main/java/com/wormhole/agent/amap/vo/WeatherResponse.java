package com.wormhole.agent.amap.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.util.List;

/**
 * 天气查询响应结果主类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WeatherResponse {

    /**
     * 返回状态，值为0或1，1：成功；0：失败
     */
    private String status;

    /**
     * 返回结果总数目
     */
    private String count;

    /**
     * 返回的状态信息
     */
    private String info;

    /**
     * 返回状态说明,10000代表正确
     */
    private String infocode;

    /**
     * 实况天气数据信息
     */
    private List<Lives> lives;

    /**
     * 预报天气信息数据
     */
    private List<Forecast> forecasts;

    /**
     * 实况天气数据信息
     */
    @Data
    public static class Lives {

        /**
         * 省份名
         */
        private String province;

        /**
         * 城市名
         */
        private String city;

        /**
         * 区域编码
         */
        private String adcode;

        /**
         * 天气现象（汉字描述）
         */
        private String weather;

        /**
         * 实时气温，单位：摄氏度
         */
        private String temperature;

        /**
         * 风向描述
         */
        private String winddirection;

        /**
         * 风力级别，单位：级
         */
        private String windpower;

        /**
         * 空气湿度
         */
        private String humidity;

        /**
         * 数据发布的时间
         */
        private String reporttime;
    }

    /**
     * 预报天气信息数据
     */
    @Data
    public static class Forecast {

        /**
         * 城市名称
         */
        private String city;

        /**
         * 城市编码
         */
        private String adcode;

        /**
         * 省份名称
         */
        private String province;

        /**
         * 预报发布时间
         */
        private String reporttime;

        /**
         * 预报数据 list 结构，元素 cast,按顺序为当天、第二天、第三天的预报数据
         */
        private List<Cast> casts;
    }


    /**
     * 预报数据详情
     */
    @Data
    public static class Cast {

        /**
         * 日期
         */
        private String date;

        /**
         * 星期几
         */
        private String week;

        /**
         * 白天天气现象
         */
        private String dayweather;

        /**
         * 晚上天气现象
         */
        private String nightweather;

        /**
         * 白天温度
         */
        private String daytemp;

        /**
         * 晚上温度
         */
        private String nighttemp;

        /**
         * 白天风向
         */
        private String daywind;

        /**
         * 晚上风向
         */
        private String nightwind;

        /**
         * 白天风力
         */
        private String daypower;

        /**
         * 晚上风力
         */
        private String nightpower;
    }

}
