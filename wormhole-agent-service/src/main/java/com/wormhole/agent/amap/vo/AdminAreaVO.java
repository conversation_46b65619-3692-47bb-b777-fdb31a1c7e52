package com.wormhole.agent.amap.vo;

import com.wormhole.agent.amap.base.AmapResponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class AdminAreaVO extends AmapResponse {
    // 建议结果列表
    private List<Suggestion> suggestions;

    private List<District> districts;

    @Data
    public static class Suggestion {
        // 建议关键字列表
        private List<String> keywords;
        // 建议城市列表
        private List<City> cities;
    }
    @Data
    public static class City {
        // 城市编码
        private String citycode;
        // 行政区列表
        private List<District> districts;
    }
    @Data
    public static class District {
        // 城市编码
        private String citycode;
        // 区域编码
        private String adcode;
        // 行政区名称
        private String name;
        // 行政区边界坐标点
        private String polyline;
        // 区域中心点
        private String center;
        // 行政区划级别
        private String level;
        // 下级行政区列表
        private List<District> districts;
    }
}
