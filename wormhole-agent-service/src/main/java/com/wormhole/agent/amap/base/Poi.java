package com.wormhole.agent.amap.base;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class Poi implements Serializable {
    // POI名称
    private String name;
    // POI唯一标识
    private String id;
    // POI经纬度
    private String location;
    // POI所属类型
    private String type;
    // POI分类编码
    @JSONField(name = "typecode")
    private String typeCode;
    // POI所属省份
    @JSONField(name = "pname")
    private String pName;
    // POI所属城市
    @JSONField(name = "cityname")
    private String cityName;
    // POI所属区县
    @JSONField(name = "adname")
    private String adName;
    // POI详细地址
    private String address;
    // POI所属省份编码
    @JSONField(name = "pcode")
    private String pCode;
    // POI所属区域编码
    @JSONField(name = "adcode")
    private String adCode;
    // POI所属城市编码
    @JSONField(name = "citycode")
    private String cityCode;

    // 子POI信息，需要通过“show_fields”参数设置才返回
    private Children children;
    // 商业信息，需要通过“show_fields”参数设置才返回
    private Business business;
    // 室内相关信息，需要通过“show_fields”参数设置才返回
    private Indoor indoor;
    // 导航位置相关信息，需要通过“show_fields”参数设置才返回
    private Navi navi;
    // 图片相关信息，需要通过“show_fields”参数设置才返回
    private List<Photo> photos;


    private String distance;


    @Data
    public static class Children {
        // 子POI唯一标识
        private String id;
        // 子POI名称
        private String name;
        // 子POI经纬度
        private String location;
        // 子POI详细地址
        private String address;
        // 子POI所属类型
        private String subtype;
        // 子POI分类编码
        @JSONField(name = "typecode")
        private String typecode;
        // 子POI商业信息
        private Business business;
    }

    @Data
    public static class Indoor {
        // 是否有室内地图标志，1为有，0为没有
        private String indoorMap;
        // 如果当前POI为建筑物类POI，则cpid为自身POI ID；如果当前POI为商铺类POI，则cpid为其所在建筑物的POI ID
        private String cpid;
        // 楼层索引，一般会用数字表示，例如8；indoor_map为0时不返回
        private String floor;
        // 所在楼层，一般会带有字母，例如F8；indoor_map为0时不返回
        private String trueFloor;
    }

    @Data
    public static class Navi {
        // POI对应的导航引导点坐标
        private String naviPoiid;
        // POI的入口经纬度坐标
        private String entrLocation;
        // POI的出口经纬度坐标
        private String exitLocation;
        // POI的地理格ID
        private String gridcode;
    }

    @Data
    public static class Photo {
        // POI的图片介绍
        private String title;
        // POI图片的下载链接
        private String url;
    }
}
