package com.wormhole.agent.amap.qo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wormhole.agent.amap.base.AmapRequest;
import lombok.Data;

@Data
public class PlaceAroundQO extends AmapRequest {
    /**
     * 地点文本搜索接口支持按照设定的POI类型限定地点搜索结果；
     * 地点类型与 poi typecode 是同类内容，可以传入多个 poi typecode，相互之间用“|”分隔，内容可以参考 POI 分类码表；
     * 地点（POI）列表的排序会按照高德搜索能力进行综合权重排序；
     * 当 keywords 和 types 均为空的时候，默认指定 types 为050000（餐饮服务）、070000（生活服务）、120000（商务住宅）、
     *
     *
     * */
    private String types;

    /**
     * 圆形区域检索中心点，不支持多个点。经度和纬度用","分割，经度在前，纬度在后，经纬度小数点后不得超过6位
     * */
    private String location;

    /**
     * 取值范围:0-50000，大于50000时按默认值，单位：米
     * */
    private Integer radius;

    @JsonProperty("page_num")
    private Integer page_num;

    @JsonProperty("page_size")
    private Integer page_size;

    /**
     * show_fields 用来筛选 response 结果中可选字段。show_fields 的使用需要遵循如下规则：
     * 1、具体可指定返回的字段类请见下方返回结果说明中的“show_fields”内字段类型；
     * 2、多个字段间采用“,”进行分割；
     * 3、show_fields 未设置时，只返回基础信息类内字段。
     *
     * business,navi,photos,children
     * */
    @JsonProperty("show_fields")
    private String show_fields;
}
