package com.wormhole.agent.amap.qo;

import com.wormhole.agent.amap.base.AmapRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class AdminAreaQO extends AmapRequest {
    // 查询关键字，支持行政区名称、citycode、adcode
    private String keywords;
    // 子级行政区，设置显示下级行政区级数（可选，默认为1）
    private int subdistrict = 1;
    // 需要第几页数据，最外层的districts最多会返回20个数据（可选，默认为1）
    private int page = 1;
    // 最外层返回数据个数（可选，默认为20）
    private int offset = 20;
    // 返回结果控制，控制行政区信息中返回行政区边界坐标点（可选，默认为"base"）
    private String extensions = "base";
    // 根据区划过滤，只返回该省/直辖市信息，需填入adcode（可选）
    private String filter;
    // 回调函数，只在output参数设置为JSON时有效（可选）
    private String callback;
    // 返回数据格式类型，可选值：JSON，XML（可选，默认为"JSON"）
    private String output = "JSON";

}
