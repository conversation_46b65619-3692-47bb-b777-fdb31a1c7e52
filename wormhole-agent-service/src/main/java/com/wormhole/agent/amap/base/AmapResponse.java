package com.wormhole.agent.amap.base;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class AmapResponse implements Serializable {

    /**
     *  本次 API 访问状态，如果成功返回1，如果失败返回0。
     * */
    private String status;

    /**
     * 访问状态值的说明，如果成功返回"ok"，失败返回错误原因
     * */
    private String info;

    /**
     * 返回状态说明,10000代表正确,详情参阅info状态表
     * */
    private String infocode;
}
