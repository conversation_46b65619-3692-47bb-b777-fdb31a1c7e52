package com.wormhole.agent.amap.enums;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.wormhole.agent.amap.base.LocationNavigateBar;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/4
 */
@AllArgsConstructor
@Getter
public enum LocationNavigateBarEnum {

    TRAFFIC("traffic","交通", Lists.newArrayList(PoiEnum.SUBWAY_STATION,PoiEnum.BUS_STATION)),
    SCENIC("scenic","景点",Lists.newArrayList(PoiEnum.TOURIST_ATTRACTION)),
    FOOD("food","美食",Lists.newArrayList(PoiEnum.FOOD)),
    SHOPPING("shopping","购物",Lists.newArrayList(PoiEnum.SHOPPING)),
    DISTRICT("district","行政区",null),
    AIRPORT_TRAIN("airport_train","机场火车站",Lists.newArrayList(PoiEnum.AIRPORT,PoiEnum.TRAIN_STATION)),
    HOSPITAL("hospital","医院",Lists.newArrayList(PoiEnum.MEDICAL)),
    UNIVERSITY("university","大学",Lists.newArrayList(PoiEnum.UNIVERSITY)),
    //热门位置
    HOT("hot","热门",null),
    ;
    private String  typeCode;
    private String typeName;

    private List<PoiEnum> poiEnums;


    public static List<LocationNavigateBar> buildDTOs(List<String> typeCodes) {
        if (CollUtil.isEmpty(typeCodes)) {
            return Lists.newArrayList();
        }
        List<LocationNavigateBar> locationNavigateBarDTOS = new ArrayList<>();
        for (String typeCode : typeCodes) {
            LocationNavigateBarEnum byTypeCode = getByTypeCode(typeCode);
            if (Objects.nonNull(byTypeCode)) {
                locationNavigateBarDTOS.add(new LocationNavigateBar(byTypeCode.getTypeCode(),byTypeCode.getTypeName(),2));
            }
        }
        return locationNavigateBarDTOS;
    }

    public static  LocationNavigateBarEnum getByTypeCode(String typeCode){
        for (LocationNavigateBarEnum value : LocationNavigateBarEnum.values()) {
            if(Objects.equals(value.getTypeCode(),typeCode)) {
                return value;
            }
        }
        return null;
    }

}
