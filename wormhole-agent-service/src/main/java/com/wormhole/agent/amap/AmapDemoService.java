package com.wormhole.agent.amap;

import com.wormhole.agent.amap.qo.POIQuery;
import com.wormhole.agent.amap.qo.PlaceAroundQO;
import com.wormhole.agent.amap.vo.POISimpleVO;
import com.wormhole.agent.amap.vo.PlaceAroundVO;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.poi.req.KeywordMatchReq;
import com.wormhole.poi.req.PlaceAroundReq;
import com.wormhole.poi.resp.PlaceAroundResp;
import com.wormhole.poi.service.AmapService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmapDemoService {
    @Resource
    private AmapService amapService;

    @Resource
    private AmapPoiService amapPoiService;

    public Mono<String> queryPlaceAroundSimple(POIQuery poiQuery) {
        return Mono.justOrEmpty(poiQuery)
                // 校验并构建location
                .filter(query -> query.getLongitude() != null && query.getLatitude() != null)
                .map(query -> query.getLongitude() + "," + query.getLatitude())
                // 构建请求对象
                .map(location -> {
                    PlaceAroundReq req = new PlaceAroundReq();
                    req.setLocation(location);
                    req.setRadius(poiQuery.getRadius());
                    req.setTypes(poiQuery.getTypes());
                    req.setKeywords(poiQuery.getKeywords());
                    req.setPage_num(poiQuery.getPage_num());
                    req.setPage_size(poiQuery.getPage_size());
                    return req;
                })
                // 调用服务查询 - 直接使用返回的Mono
                .flatMap(req -> amapService.queryPlaceAround(req))
                // 提取pois列表
                .map(PlaceAroundResp::getPois)
                // 过滤空列表
                .filter(pois -> pois != null && !pois.isEmpty())
                // 转换为简单VO
                .map(pois -> pois.stream()
                        .map(poi -> POISimpleVO.builder()
                                .name(poi.getName())
                                .type(poi.getType())
                                .distance(poi.getDistance())
                                .address(poi.getAddress())
                                .build())
                        .collect(Collectors.toList()))
                // 序列化为JSON字符串
                .flatMap(simpleVOs -> {
                    try {
                        return Mono.just(JacksonUtils.writeValueAsString(simpleVOs));
                    } catch (Exception e) {
                        return Mono.just("");
                    }
                })
                // 异常和空值兜底
                .onErrorReturn("")
                .defaultIfEmpty("");
    }



    public Mono<PlaceAroundResp> queryPlaceAround(String seachKey, Boolean isLocation) {
        if (isLocation) {
            PlaceAroundReq qo = new PlaceAroundReq();
            qo.setLocation(seachKey);
            qo.setRadius(5000);
            return amapService.queryPlaceAround(qo);
        } else {
            KeywordMatchReq keywordMatchReq = new KeywordMatchReq();
            keywordMatchReq.setKeywords(seachKey);
            keywordMatchReq.setPage_size(1);
            return amapService.queryKeywordMatchPlace(keywordMatchReq)
                    .map(placeResp -> {
                        if (placeResp == null || CollectionUtils.isEmpty(placeResp.getPois())) {
                            return null;
                        }
                        return placeResp.getPois().get(0).getLocation();
                    })
                    .flatMap(location -> {
                        PlaceAroundReq qo = new PlaceAroundReq();
                        qo.setLocation(location);
                        qo.setRadius(5000);
                        return amapService.queryPlaceAround(qo);
                    });
        }
    }

    // 营业时间待定
    public Mono<PlaceAroundResp> queryAround(boolean haveSearchCenter,String searchKey,String hotelLocation,String radius, String types,String region) {
        PlaceAroundReq qo = new PlaceAroundReq();
        qo.setLocation(StringUtils.isNotBlank(hotelLocation) ? hotelLocation : searchKey);
        qo.setRadius(StringUtils.isNotBlank(radius) ? NumberUtils.toInt(radius) : null);
        qo.setKeywords(searchKey);
        if (StringUtils.isNotBlank(types)){
            qo.setTypes(types);
        }
        if (haveSearchCenter){
            // 查询地点的坐标
            KeywordMatchReq keyword = new KeywordMatchReq();
            keyword.setKeywords(searchKey);
            if (StringUtils.isNotBlank(region)){
                keyword.setRegion(region);
                keyword.setCityLimit(true);
            }
            Mono<PlaceAroundResp> placeAroundVOMono = amapService.queryKeywordMatchPlace(keyword);
            return placeAroundVOMono.flatMap(placeAroundVO -> {
                log.info("queryKeywordMatchPlace keyWords {} res {}",JacksonUtils.writeValueAsString(keyword),JacksonUtils.writeValueAsString(placeAroundVO));
                if (placeAroundVO != null && !CollectionUtils.isEmpty(placeAroundVO.getPois())) {
                    qo.setLocation(placeAroundVO.getPois().get(0).getLocation());
                }
                return Mono.just(qo);
            }).flatMap(this::getPlaceAroundRespMono);
        }
        return getPlaceAroundRespMono(qo);
    }

    private Mono<PlaceAroundResp> getPlaceAroundRespMono(PlaceAroundReq req) {
        if (StringUtils.isBlank(req.getLocation())){
            return Mono.empty();
        }
        return amapService.queryPlaceAround(req)
                .doOnSuccess(placeAroundResp -> {
                    log.info("query amap success req {} | {} ", JacksonUtils.writeValueAsString(req), JacksonUtils.writeValueAsString(placeAroundResp));
                })
                .onErrorResume(e -> {
            log.error("query amap fail | {} ", JacksonUtils.writeValueAsString(req), e);
            return Mono.empty();
        });
    }

    /**
     *
     */
    public PlaceAroundVO queryPlaceAroundForFood() throws ExecutionException, InterruptedException {
        PlaceAroundQO qo = new PlaceAroundQO();
        qo.setLocation("113.899126,22.529931");
        qo.setTypes("050000");
        qo.setRadius(5000);
        qo.setPage_num(1);
        qo.setPage_size(100);
        return amapPoiService.queryPlaceAround(qo);
    }

    /**
     * 旅游景点
     */
    public PlaceAroundVO queryPlaceAroundForTravel() throws ExecutionException, InterruptedException {
        PlaceAroundQO qo = new PlaceAroundQO();
        qo.setLocation("113.899126,22.529931");
        qo.setTypes("110000");
        qo.setRadius(5000);
        qo.setPage_num(1);
        qo.setPage_size(100);
        return amapPoiService.queryPlaceAround(qo);
    }

}
