package com.wormhole.agent.amap.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wormhole.agent.amap.base.AmapResponse;
import com.wormhole.agent.amap.base.Poi;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/6
 */
@Data
public class ReGeoCodeVO extends AmapResponse {

  private Regeocode regeocode;
    @Data
    public static class Regeocode {
        private List<Road> roads;
        @JsonProperty("roadinters")
        private List<RoadInter> roadInters;
        @JsonProperty("formatted_address")
        private String formattedAddress; // 结构化地址信息
        private AddressComponent addressComponent; // 地址元素列表
        private List<Poi> pois; // 兴趣点列表
        private List<Aoi> aois; // 所属AOI列表
        // Getters and Setters
    }

    @Data
    public static class AddressComponent {
        private String country; // 国家名称
        private String province; // 省名称
        private String city; // 城市名称
        private String citycode; // 城市编码
        private String district; // 区名称
        private String adcode; // 行政区编码
        private String township; // 乡镇/街道名称
        private String towncode; // 乡镇街道编码
        private List<Neighborhood> neighborhoodList; // 社区信息列表
        private List<Building> buildingList; // 楼信息列表
        private List<StreetNumber> streetNumberList; // 门牌信息列表

        // Getters and Setters
    }
    @Data
    public static class Neighborhood {
        private String name; // 社区名称
        private String type; // POI类型

        // Getters and Setters
    }
    @Data
    public static class Building {
        private String name; // 建筑名称
        private String type; // 类型

        // Getters and Setters
    }

    @Data
    public static class StreetNumber {
        private String street; // 街道名称
        private String number; // 门牌号

        // Getters and Setters
    }


    @Data
    public static class Location {
        private double longitude; // 经度
        private double latitude; // 纬度

        // Getters and Setters
    }
    @Data
    public static class Road {
        private String id; // 道路id
        private String name; // 道路名称
        private int distance; // 道路到请求坐标的距离
        private String direction; // 方位
        private Location location; // 坐标点

        // Getters and Setters
    }
    @Data
    public static class RoadInter {
        private int distance; // 交叉路口到请求坐标的距离
        private String direction; // 方位
        private Location location; // 路口经纬度
        private String firstId; // 第一条道路id
        private String firstName; // 第一条道路名称
        private String secondId; // 第二条道路id
        private String secondName; // 第二条道路名称

        // Getters and Setters
    }
    @Data
    public static class Aoi {
        private String id; // AOI的id
        private String name; // AOI名称
        private String adcode; // 所在区域编码
        private Location location; // 中心点坐标
        private double area; // 面积，单位：平方米
        private int distance; // 距离AOI的距离
        private String type; // AOI类型

        // Getters and Setters
    }
}
