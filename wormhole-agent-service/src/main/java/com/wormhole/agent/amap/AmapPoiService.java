package com.wormhole.agent.amap;

import cn.hutool.http.HttpUtil;
import com.wormhole.agent.amap.base.AmapConfig;
import com.wormhole.agent.amap.base.AmapConstants;
import com.wormhole.agent.amap.base.Poi;
import com.wormhole.agent.amap.qo.*;
import com.wormhole.agent.amap.vo.*;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.poi.req.PlaceAroundReq;
import com.wormhole.poi.resp.PlaceAroundResp;
import com.wormhole.poi.service.AmapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmapPoiService {
    private static final WebClient WEB_CLIENT;

    static {
        WEB_CLIENT = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    @Resource
    private AmapConfig amapConfig;

    private static ConcurrentHashMap<String, Object> cache = new ConcurrentHashMap<>();

    public PlaceAroundVO queryPlaceAround(PlaceAroundQO qo) {
        UriComponentsBuilder builder =  buildUrlWithQueryParams(amapConfig.getUrl() + AmapConstants.Url.AROUND_SEARCH_API_URL, qo);
        return JacksonUtils.readValue(HttpUtil.get(builder.toUriString()), PlaceAroundVO.class);

    }


    public Mono<String> queryWeather(WeatherInfoQO weatherInfoQO) {
        UriComponentsBuilder builder = buildUrlWithQueryParams( amapConfig.getUrl()+AmapConstants.Url.WEATHER_INFO_API_URL , weatherInfoQO);
        return WEB_CLIENT.get()
                .uri(builder.toUriString())
                .retrieve()
                .bodyToMono(String.class);
    }

    public Mono<NavigationInfoVO> queryNavigationInfo(NavigationSearchQO navigationSearchQo, Integer trafficType) {
        UriComponentsBuilder builder = buildUrlWithQueryParams( amapConfig.getUrl() + AmapConstants.TrafficTypeEnum.getUrlByTrafficType(trafficType), navigationSearchQo);
        builder.queryParam("trafficType", trafficType);
        // WebClient 请求并异步解析为 PlaceAroundVo
        return WEB_CLIENT.get()
                .uri(builder.toUriString())
                .retrieve()
                .bodyToMono(NavigationInfoVO.class);
    }

    public Mono<AdminAreaVO> queryAdminAreaInfo(AdminAreaQO adminAreaQO) {
        UriComponentsBuilder builder =  buildUrlWithQueryParams(amapConfig.getUrl() + AmapConstants.Url.ADMIN_AREA_API_URL, adminAreaQO);
        // WebClient 请求并异步解析为 PlaceAroundVo
        return WEB_CLIENT.get()
                .uri(builder.toUriString())
                .retrieve()
                .bodyToMono(AdminAreaVO.class);
    }

    public Mono<PlaceAroundVO> queryKeywordMatchPlace(KeywordMatchQO qo) {
        UriComponentsBuilder builder =  buildUrlWithQueryParams(amapConfig.getUrl() + AmapConstants.Url.KEYWORD_SEARCH_API_URL, qo);
        // WebClient 请求并异步解析为 PlaceAroundVo
        return WEB_CLIENT.get()
                .uri(builder.toUriString())
                .retrieve()
                .bodyToMono(PlaceAroundVO.class);
    }

    public Mono<ReGeoCodeVO> queryReGeoCode(ReGeoCodeQO qo) {
        UriComponentsBuilder builder =  buildUrlWithQueryParams(amapConfig.getUrl() + AmapConstants.Url.GEO_RECODE_URL, qo);
        // WebClient 请求并异步解析为 PlaceAroundVo
        return WEB_CLIENT.get()
                .uri(builder.toUriString())
                .retrieve()
                .bodyToMono(ReGeoCodeVO.class);
    }

    /**
     * 根据对象的字段动态构建 URL 的查询参数
     */
    private UriComponentsBuilder buildUrlWithQueryParams(String baseUrl, Object queryObject) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(baseUrl);

        // 使用反射获取对象的字段和值
        for (Field field : queryObject.getClass().getDeclaredFields()) {
            field.setAccessible(true); // 允许访问私有字段
            try {
                Object value = field.get(queryObject); // 获取字段值
                if (Objects.nonNull(value)) { // 如果字段值不为空，添加到查询参数
                    builder.queryParam(field.getName(), value);
                }
            } catch (IllegalAccessException e) {
                throw new RuntimeException("Failed to access field: " + field.getName(), e);
            }
        }
        builder.queryParam("key", amapConfig.getKey());

        return builder;
    }
}

