package com.wormhole.agent.amap.vo;

import com.wormhole.agent.amap.base.AmapResponse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class NavigationInfoVO extends AmapResponse {
    // 路径规划方案总数。
    private String count;
    // 返回的规划方案列表。
    private Route route;
    @Data
    public static class Route {
        // 起点经纬度。
        private String origin;
        // 终点经纬度。
        private String destination;
        // 预计出租车费用，单位：元。
        private String taxiCost;
        // 算路方案详情。
        private List<Path> paths;
    }
    @Data
    public static class Path {
        // 方案距离，单位：米。
        private String distance;
        // 0代表限行已规避或未限行，1代表限行无法规避。
        private String restriction;
        // 路线分段。
        private List<Step> steps;

        private Cost cost;
    }

    @Data
    public static class Step {
        // 行驶指示。
        private String instruction;
        // 进入道路方向。
        private String orientation;
        // 分段道路名称。
        private String roadName;
        // 分段距离信息。
        private String stepDistance;
        private Cost cost;
        private List<Tmc> tmcs;
        private Navi navi;
        private List<City> cities;
    }

    @Data
    public static class Cost {
        // 线路耗时，分段step中的耗时。
        private String duration;
        // 此路线道路收费，单位：元，包括分段信息。
        private String tolls;
        // 收费路段里程，单位：米，包括分段信息。
        private String tollDistance;
        // 主要收费道路。
        private String tollRoad;
        // 方案中红绿灯个数，单位：个。
        private String trafficLights;
    }
    @Data
    public static class Tmc {
        // 路况信息，包括：未知、畅通、缓行、拥堵、严重拥堵。
        private String tmcStatus;
        // 从当前坐标点开始step中路况相同的距离。
        private String tmcDistance;
        // 此段路况涉及的道路坐标点串，点间用","分隔。
        private String tmcPolyline;
    }

    @Data
    public static class Navi {
        // 导航主要动作指令。
        private String action;
        // 导航辅助动作指令。
        private String assistantAction;
    }
    @Data
    public static class City {
        // 途径区域编码。
        private String adcode;
        // 途径城市编码。
        private String citycode;
        // 途径城市名称。
        private String city;
    }
    @Data
    public static class District {
        // 途径区县名称。
        private String name;
        // 途径区县adcode。
        private String adcode;

    }
    @Data
    public static class NavigationResponseData {
        // 分段途径城市信息。
        private City cities;
        // 途径区县信息。
        private District district;
        // 分路段坐标点串，两点间用“;”分隔。
        private String polyline;

    }
}
