package com.wormhole.agent.amap.qo;

import com.wormhole.agent.amap.base.AmapRequest;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Data
public class KeywordMatchQO extends AmapRequest {
    // 地点关键字，需要被检索的地点文本信息（必填，keyword 或者 types 二选一必填）
    private String keywords;
    // 指定地点类型，可以传入多个poi typecode，相互之间用“|”分隔（可选，keyword 或者 types 二选一必填）
    private String types;
    // 搜索区划，可输入citycode，adcode，cityname（可选）
    private String region;
    // 指定城市数据召回限制，true或false（可选）
    private boolean cityLimit;
    // 返回结果控制，筛选response结果中可选字段（可选）
    private String showFields;
    // 当前分页展示的数据条数，取值1-25（可选，默认为10）
    private int page_size = 20;
    // 请求第几分页，取值1-100（可选，默认为1）
    private int page_num = 1;
    // 数字签名（可选）
    private String sig;
    // 返回结果格式类型，默认为json，目前只支持json格式（可选）
    private String output = "json";
    // 回调函数，只在output参数设置为JSON时有效（可选）
    private String callback;
}
