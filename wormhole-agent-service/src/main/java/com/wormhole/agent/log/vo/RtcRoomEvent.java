package com.wormhole.agent.log.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RtcRoomEvent {


    /**
     * 所属轮次ID
     */
    private Long roundId;

    /**
     * 事件类型
     */
    private Long type;

    /**
     * 事件阶段
     */
    private String runStage;

    /**
     * 事件时间戳
     */
    private long eventTime;
}
