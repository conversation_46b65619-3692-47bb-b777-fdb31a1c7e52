package com.wormhole.agent.log.stat;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.context.DefaultContext;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.trace.log.ExecutionStatus;
import lombok.*;

import java.time.Instant;


/**
 * ExecutionStat 类
 * 执行统计类，用于记录执行的相关信息。
 *
 * <AUTHOR>
 * @Date 2024/12/12 14:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ExecutionStat extends DefaultContext {
    /**
     * 执行类型。
     */
    private ExecutionStatType type;

    /**
     * 执行的输入数据。
     */
    private Object input;

    /**
     * 执行的输出数据。
     */
    private Object output;

    /**
     * 执行的开始时间。
     */
    private Instant startTime;

    /**
     * 执行的结束时间。
     */
    private Instant endTime;

    /**
     * 执行的耗时（毫秒）。
     * <p>计算方式：结束时间 - 开始时间</p>
     */
    @Builder.Default
    private long elapsedMs = -1L;

    /**
     * 执行状态。
     * <p>可能的状态包括：成功、失败、进行中</p>
     */
    private ExecutionStatus status;

    /**
     * 记录执行过程中发生的异常信息。
     */
    private String errorMsg;

    /**
     * 记录执行过程中发生的堆栈信息。
     */
    private String stackTrace;
    /**
     * 节点时间线
     */
    private Object nodeStatInfo;
    /**
     * 层级1代表整个聊天会话，2中间执行，3内部以此叠加
     */
    private int level;
    private int sort;
    private String model;

}