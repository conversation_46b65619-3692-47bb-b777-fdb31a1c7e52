package com.wormhole.agent.log.enums;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * ChatAccessLogIndexFieldEnum
 * <p>
 * 枚举类用于定义 ChatAccessLogIndex 中的字段信息。
 * 每个枚举值包含字段的索引名称（columnName）及其描述信息（desc）。
 *
 * <AUTHOR>
 * @version 2024/12/26
 */
public enum ChatAccessLogIndexFieldEnum {

    TIMESTAMP("@timestamp", "时间戳，记录日志产生的时间"),
    START_TIME("start_time", "开始时间"),
    END_TIME("end_time", "结束时间"),
    ELAPSED_MS("elapsed_ms", "请求耗时（毫秒）"),
    TRACE_ID("trace_id", "跟踪 ID，用于分布式调用链追踪"),
    USER_ID("user_id", "账户 ID，用于关联用户信息"),
    CONVERSATION_ID("conversation_id", "会话 ID，用于标识同一会话的消息"),
    CHAT_COMPLETION_ID("chat_completion_id", "聊天补全请求 ID"),
    CLIENT_REQ_ID("client_req_id", "客户端请求 ID"),
    CHAT_TYPE("chat_type", "聊天类型"),
    BOT_CODE("bot_code", "机器人编码"),
    QUESTION("question", "用户提问"),
    ANSWER("answer", "机器人回答"),
    BOT_INFO("bot_info", "机器人信息"),
    CODE("code", "状态码"),
    BOT_MODEL("bot_model", "机器人模型"),
    ERROR_MSG("error_msg", "错误信息"),
    EXECUTION_STAT_LIST("execution_stat_list", "执行状态列表"),
    ;

    private final String columnName;
    private final String desc;

    ChatAccessLogIndexFieldEnum(String columnName, String desc) {
        this.columnName = columnName;
        this.desc = desc;
    }

    /**
     * 获取字段的索引名称。
     *
     * @return 字段的索引名称
     */
    public String getColumnName() {
        return columnName;
    }

    /**
     * 获取字段的多匹配名称列表，用于支持拼音搜索等扩展功能。
     *
     * @return 字段的多匹配列表
     */
    public List<String> getMultiMatchColumnNames() {
        return Lists.newArrayList(columnName, String.format("%s.pinyin", columnName));
    }

    /**
     * 获取字段的描述信息。
     *
     * @return 字段的描述信息
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 根据字段的索引名称获取对应的枚举值。
     *
     * @param columnName 字段的索引名称
     * @return 对应的枚举值，如果未找到则返回 null
     */
    public static ChatAccessLogIndexFieldEnum from(String columnName) {
        return Optional.ofNullable(columnName)
                .filter(StringUtils::isNotBlank)
                .flatMap(name -> Arrays.stream(values()).filter(item -> item.getColumnName().equalsIgnoreCase(name)).findFirst())
                .orElse(null);
    }
}
