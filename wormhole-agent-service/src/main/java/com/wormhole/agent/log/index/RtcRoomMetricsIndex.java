package com.wormhole.agent.log.index;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.util.DateParsePatterns;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.Instant;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "rtc-room-metrics")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RtcRoomMetricsIndex {
    /**
     * 对应索引中的 "@timestamp"
     */
    @JsonFormat(pattern = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_DASH)
    @Field(type = FieldType.Date, name = "@timestamp")
    private Instant timestamp;
    /**
     * 房间ID
     */
    @Field(type = FieldType.Keyword, name = "room_id")
    private String roomId;

    /**
     * 应用ID
     */
    @Field(type = FieldType.Keyword, name = "app_id")
    private String appId;


    /**
     * 对应索引中的 "elapsed_ms"
     */
    @Field(type = FieldType.Long, name = "elapsed_ms")
    private Long elapsedMs;

    @JsonFormat(pattern = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_DASH)
    @Field(type = FieldType.Date, name = "session_start_time")
    private Instant sessionStartTime;
    /**
     * 会话结束时间
     */
    @JsonFormat(pattern = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_DASH)
    @Field(type = FieldType.Date, name = "session_end_time")
    private Instant sessionEndTime;


    /**
     * 事件信息
     */
    @Field(type = FieldType.Text, name = "events")
    private String events;

    /**
     * LLM指标
     */
    @Field(type = FieldType.Text, name = "llm_metrics")
    private String llmMetrics;

}
