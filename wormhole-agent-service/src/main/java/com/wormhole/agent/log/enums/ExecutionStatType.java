package com.wormhole.agent.log.enums;

/**
 * ExecutionStatType
 *
 * <AUTHOR>
 * @version 2024/12/11
 **/
public enum ExecutionStatType {
    chat_access("聊天请求"),
    smart_intent("意图识别"),
    read_memory("读取记忆"),
    search_knowledge("知识库"),
    workflow("工作流"),
    tool_use("工具调用"),
    llm_summary("LLM摘要"),
    llm("大模型调用"),
    llm_suggest("LLM建议"),
    store_memory("存储记忆");

    private final String desc;

    ExecutionStatType(String desc) {
        this.desc = desc;
    }

    /**
     * 获取枚举值的中文描述
     * @return 中文描述
     */
    public String getDesc() {
        return this.desc;
    }
    /**
     * 通过枚举类型获取中文描述
     * @param type 枚举类型
     * @return 中文描述，如果传入null则返回空字符串
     */
    public static String getDesc(ExecutionStatType type) {
        return type == null ? "" : type.getDesc();
    }
}