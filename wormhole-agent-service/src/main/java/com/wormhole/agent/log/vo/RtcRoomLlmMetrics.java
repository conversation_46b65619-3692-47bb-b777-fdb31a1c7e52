package com.wormhole.agent.log.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * LLM指标数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RtcRoomLlmMetrics {
    private String rtcRoomId;

    /**
     * 总持续时间(毫秒)
     */
    private long totalElapsedMs;

    /**
     * 开始时间
     */
    private long startTime;

    /**
     * 结束时间
     */
    private long endTime;
    private long firstTokenTime;

    /**
     * 对应索引中的 "trace_id"
     */
    private String traceId;


    /**
     * 对应索引中的 "conversation_id"
     */
    private String conversationId;


    /**
     * 对应索引中的 "client_req_id"
     */
    private String clientReqId;

    private List<Node> nodes;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Node {
        private String name;
        private long elapsedMs;
        private long startTime;
        private long endTime;
        private List<Node> subNodes;

    }

}
