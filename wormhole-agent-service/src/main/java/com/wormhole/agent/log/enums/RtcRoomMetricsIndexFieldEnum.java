package com.wormhole.agent.log.enums;

import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * RtcRoomMetricsIndexFieldEnum
 * <p>
 * 枚举类用于定义 RtcRoomMetricsIndex 中的字段信息。
 * 每个枚举值包含字段的索引名称（columnName）及其描述信息（desc）。
 *
 * @version 2025/05/19
 */
public enum RtcRoomMetricsIndexFieldEnum {
    TIMESTAMP("@timestamp", "时间戳，记录日志产生的时间"),
    ROOM_ID("room_id", "房间ID"),
    APP_ID("app_id", "应用ID"),
    ELAPSED_MS("elapsed_ms", "通话耗时"),
    SESSION_START_TIME("session_start_time", "会话开始时间"),
    SESSION_END_TIME("session_end_time", "会话结束时间"),
    EVENTS("events", "事件信息"),
    LLM_METRICS("llm_metrics", "LLM指标")
    ;

    private final String columnName;
    private final String desc;

    RtcRoomMetricsIndexFieldEnum(String columnName, String desc) {
        this.columnName = columnName;
        this.desc = desc;
    }

    /**
     * 获取字段的索引名称。
     *
     * @return 字段的索引名称
     */
    public String getColumnName() {
        return columnName;
    }

    /**
     * 获取字段的多匹配名称列表，用于支持拼音搜索等扩展功能。
     *
     * @return 字段的多匹配列表
     */
    public List<String> getMultiMatchColumnNames() {
        return Lists.newArrayList(columnName, String.format("%s.pinyin", columnName));
    }

    /**
     * 获取字段的描述信息。
     *
     * @return 字段的描述信息
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 根据字段的索引名称获取对应的枚举值。
     *
     * @param columnName 字段的索引名称
     * @return 对应的枚举值，如果未找到则返回 null
     */
    public static RtcRoomMetricsIndexFieldEnum from(String columnName) {
        return Optional.ofNullable(columnName)
                .filter(StringUtils::isNotBlank)
                .flatMap(name -> Arrays.stream(values()).filter(item -> item.getColumnName().equalsIgnoreCase(name)).findFirst())
                .orElse(null);
    }
}
