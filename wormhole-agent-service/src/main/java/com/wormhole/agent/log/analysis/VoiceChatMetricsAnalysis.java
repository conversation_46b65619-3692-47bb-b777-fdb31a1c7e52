package com.wormhole.agent.log.analysis;

import com.wormhole.agent.log.index.RtcRoomMetricsIndex;
import com.wormhole.agent.log.service.RtcRoomMetricsService;
import com.wormhole.agent.log.vo.RtcRoomEvent;
import com.wormhole.agent.log.vo.RtcRoomLlmMetrics;
import com.wormhole.channel.consts.event.VoiceChatEvent;
import com.wormhole.common.util.DateParsePatterns;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.LocalDateTimeUtils;
import jakarta.annotation.Resource;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class VoiceChatMetricsAnalysis {

    @Resource
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;
    @Resource
    private RtcRoomMetricsService rtcRoomMetricsService;
    private static final String EVENT_KEY_PREFIX = "voice:chat:events";
    private static final String LLM_KEY_PREFIX = "voice:chat:llm";

    /**
     * 记录语音聊天事件
     */
    public Mono<Void> recordEvent(VoiceChatEvent event) {
        // 确保设置时间戳
        if (event.getEventTime() == null) {
            event.setEventTime(System.currentTimeMillis());
        }
        String eventKey = String.format("%s:%s", EVENT_KEY_PREFIX, event.getRoomId());
        // 将事件序列化为JSON字符串
        String eventJson = JacksonUtils.writeValueAsString(event);

        // 将事件添加到Redis列表
        return reactiveStringRedisTemplate.opsForList().rightPush(eventKey, eventJson)
                .then();

    }
    /**
     * 记录语音聊天llm 请求
     */
    public Mono<Void> recordLlmMetrics(RtcRoomLlmMetrics llmMetrics) {
        if(llmMetrics.getRtcRoomId() == null){
            return Mono.empty();
        }
        String llmKey = String.format("%s:%s", LLM_KEY_PREFIX, llmMetrics.getRtcRoomId());
        // 将事件序列化为JSON字符串
        String llmJson = JacksonUtils.writeValueAsString(llmMetrics);
        // 将事件添加到Redis列表
        return reactiveStringRedisTemplate.opsForList().rightPush(llmKey, llmJson)
                .then();
    }

    public Mono<Void> logSaveEs(String roomId) {
        String eventKey = String.format("%s:%s", EVENT_KEY_PREFIX, roomId);
        String llmKey = String.format("%s:%s", LLM_KEY_PREFIX, roomId);

        // 并行获取两个Redis列表数据
        Mono<ExtractedData> extractedDataMono = reactiveStringRedisTemplate.opsForList().range(eventKey, 0, -1)
                .map(jsonStr -> JacksonUtils.readValue(jsonStr, VoiceChatEvent.class))
                .filter(Objects::nonNull)
                .collectList()
                .map(voiceChatEvents -> {
                    // 提取 appId 和 businessId（假设所有事件的这些字段相同，取第一个非空值）
                    String appId = voiceChatEvents.stream()
                            .map(VoiceChatEvent::getAppId)
                            .filter(Objects::nonNull)
                            .findFirst()
                            .orElse("");

                    // 提取时间信息
                    Instant sessionStartTime = voiceChatEvents.stream()
                            .map(event -> Instant.ofEpochMilli(event.getEventTime()))
                            .min(Instant::compareTo)
                            .orElse(Instant.now());

                    Instant sessionEndTime = voiceChatEvents.stream()
                            .map(event -> Instant.ofEpochMilli(event.getEventTime()))
                            .max(Instant::compareTo)
                            .orElse(Instant.now());

                    // 转换为 RtcRoomEvent 列表
                    List<RtcRoomEvent> rtcRoomEvents = voiceChatEvents.stream()
                            .map(event -> RtcRoomEvent.builder()
                                    .eventTime(event.getEventTime())
                                    .roundId(event.getRoundID())
                                    .runStage(event.getRunStage())
                                    .type(event.getEventType())
                                    .build())
                            .collect(Collectors.toList());

                    return new ExtractedData(appId, sessionStartTime, sessionEndTime, rtcRoomEvents);
                });

        Mono<List<RtcRoomLlmMetrics>> llmMono = reactiveStringRedisTemplate.opsForList().range(llmKey, 0, -1)
                .doOnNext(jsonStr ->
                        log.debug("llmMono [Redis数据检查] key={}, value={}", eventKey, jsonStr)
                )
                .map(jsonStr -> JacksonUtils.readValue(jsonStr, RtcRoomLlmMetrics.class))
                .filter(Objects::nonNull)
                .collectList();

        // 使用zip合并两个Mono结果
        return Mono.zip(extractedDataMono, llmMono)
                .flatMap(tuple -> {
                    ExtractedData extractedData = tuple.getT1();
                    List<RtcRoomLlmMetrics> llmMetrics = tuple.getT2();
                    // 这里添加保存到ES的逻辑
                    if (extractedData.getEvents().isEmpty() && llmMetrics.isEmpty()) {
                        return Mono.empty();
                    }

                    // 假设有一个方法saveToEs用于保存数据到ES
                    return saveToEs(extractedData, llmMetrics, roomId);
                });
    }

    // 假设的保存到ES的方法
    private Mono<Void> saveToEs(ExtractedData extractedData, List<RtcRoomLlmMetrics> llmMetrics, String roomId) {

        // 处理火焰图数据的示例代码
        RtcRoomMetricsIndex metricsIndex = RtcRoomMetricsIndex.builder()
                .timestamp(LocalDateTimeUtils.nowToInstant())
                .roomId(roomId)
                .appId(extractedData.getAppId())
                // 处理时间
                .sessionStartTime(extractedData.getSessionStartTime())
                .sessionEndTime(extractedData.getSessionEndTime())
                .elapsedMs(extractedData.getSessionEndTime().toEpochMilli()-extractedData.getSessionStartTime().toEpochMilli())
                // 存储元数据 - 使用JSON格式
                .events(JacksonUtils.writeValueAsString(extractedData.getEvents()))
                .llmMetrics(JacksonUtils.writeValueAsString(llmMetrics))
                .build();
        return rtcRoomMetricsService.save(metricsIndex)
                .then(clearEvents(roomId));
    }


    /**
     * 查询并分析指定房间的所有事件耗时
     */
    public Mono<Void> logEventAnalysis(String roomId) {
        String eventKey = String.format("%s:%s", EVENT_KEY_PREFIX, roomId);
        return reactiveStringRedisTemplate.opsForList().range(eventKey, 0, -1)
                .doOnNext(jsonStr ->
                        log.debug("logEventAnalysis [Redis数据检查] key={}, value={}", eventKey, jsonStr)
                )
                .map(jsonStr -> JacksonUtils.readValue(jsonStr, VoiceChatEvent.class))
                .filter(Objects::nonNull)
                .collectList()
                .flatMap(events -> {
                    if (events.isEmpty()) {
                        log.info("房间 {} 没有记录任何事件", roomId);
                        return Mono.empty();
                    }

                    if (events.size() == 1) {
                        log.info("房间 {} 只有一个事件，无法计算耗时", roomId);
                        return Mono.empty();
                    }

                    // 按时间戳排序
                    events.sort(Comparator.comparing(VoiceChatEvent::getEventTime));

                    // 打印房间信息
                    VoiceChatEvent firstEvent = events.get(0);
                    StringBuilder sb = new StringBuilder();
                    sb.append("\n========== 语音聊天性能分析 ==========\n");
                    sb.append(String.format("房间ID: %s\n", roomId));
                    if (firstEvent.getAppId() != null) {
                        sb.append(String.format("应用ID: %s\n", firstEvent.getAppId()));
                    }
                    if (firstEvent.getBusinessId() != null) {
                        sb.append(String.format("业务ID: %s\n", firstEvent.getBusinessId()));
                    }
                    sb.append(String.format("总事件数: %d\n", events.size()));

                    // 按轮次分组
                    Map<Long, List<VoiceChatEvent>> roundGroups = events.stream()
                            .collect(Collectors.groupingBy(VoiceChatEvent::getRoundID));
                    sb.append(String.format("总轮次数: %d\n", roundGroups.size()));

                    // 计算总体耗时
                    VoiceChatEvent lastEvent = events.get(events.size() - 1);
                    long totalDuration = lastEvent.getEventTime() - firstEvent.getEventTime();
                    sb.append(String.format("总体耗时: %d 毫秒\n", totalDuration));

                    sb.append("\n---------- 轮次耗时分析 ----------\n");
                    // 分析每个轮次
                    for (Map.Entry<Long, List<VoiceChatEvent>> entry : roundGroups.entrySet()) {
                        Long roundId = entry.getKey();
                        List<VoiceChatEvent> roundEvents = entry.getValue();

                        // 按时间戳排序
                        roundEvents.sort(Comparator.comparing(VoiceChatEvent::getEventTime));

                        sb.append(String.format("\n轮次ID: %d\n", roundId));

                        if (roundEvents.size() <= 1) {
                            sb.append("  事件数量不足，无法计算耗时\n");
                            continue;
                        }

                        VoiceChatEvent roundFirstEvent = roundEvents.get(0);
                        VoiceChatEvent roundLastEvent = roundEvents.get(roundEvents.size() - 1);
                        long roundDuration = roundLastEvent.getEventTime() - roundFirstEvent.getEventTime();

                        sb.append(String.format("  事件数量: %d\n", roundEvents.size()));
                        sb.append(String.format("  开始事件: 类型=%d, 阶段=%s, 时间=%s\n",
                                roundFirstEvent.getEventType(),
                                roundFirstEvent.getRunStage(),
                                LocalDateTimeUtils.formatTimestamp(roundFirstEvent.getEventTime(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS)));
                        sb.append(String.format("  结束事件: 类型=%d, 阶段=%s, 时间=%s\n",
                                roundLastEvent.getEventType(),
                                roundLastEvent.getRunStage(),
                                LocalDateTimeUtils.formatTimestamp(roundLastEvent.getEventTime(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS)));
                        sb.append(String.format("  总耗时: %d 毫秒\n", roundDuration));

                        sb.append("  各阶段耗时:\n");

                        // 计算轮次内各阶段耗时
                        for (int i = 1; i < roundEvents.size(); i++) {
                            VoiceChatEvent current = roundEvents.get(i);
                            VoiceChatEvent previous = roundEvents.get(i - 1);

                            long stageDuration = current.getEventTime() - previous.getEventTime();

                            sb.append(String.format("    %d → %d (%s → %s): %d 毫秒\n",
                                    previous.getEventType(), current.getEventType(),
                                    previous.getRunStage(), current.getRunStage(),
                                    stageDuration));
                        }
                    }

                    sb.append("\n---------- 事件顺序与耗时 ----------\n");
                    List<RtcRoomEvent> eventList = new ArrayList<>();
                    // 打印所有事件的顺序和耗时
                    for (int i = 0; i < events.size(); i++) {
                        VoiceChatEvent event = events.get(i);
                        sb.append(String.format("事件 #%d: 轮次=%d, 类型=%d, 阶段=%s, 时间=%s\n",
                                i+1, event.getRoundID(), event.getEventType(),
                                event.getRunStage(),
                                LocalDateTimeUtils.formatTimestamp(event.getEventTime(),DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS)));
                        eventList.add(RtcRoomEvent.builder()
                                .eventTime(event.getEventTime())
                                .roundId(event.getRoundID())
                                .runStage(event.getRunStage())
                                .type(event.getEventType())
                                .build());


                        if (i > 0) {
                            VoiceChatEvent prevEvent = events.get(i-1);
                            long duration = event.getEventTime() - prevEvent.getEventTime();
                            sb.append(String.format("  距上一事件耗时: %d 毫秒\n", duration));
                        }

                        // 如果有错误信息，也打印出来
                        if (event.getErrorInfo() != null && event.getErrorInfo().getErrorcode() != null) {
                            sb.append(String.format("  错误: 代码=%d, 原因=%s\n",
                                    event.getErrorInfo().getErrorcode(),
                                    event.getErrorInfo().getReason()));
                        }
                    }

                    sb.append("\n========== 分析结束 ==========\n");
                    // 打印完整的分析结果
                    log.info(sb.toString());
                    return Mono.empty();
                });
    }

    /**
     * 清除指定房间的事件记录
     */
    public Mono<Void> clearEvents(String roomId) {
        String eventKey = String.format("%s:%s", EVENT_KEY_PREFIX, roomId);
        String llmKey = String.format("%s:%s", LLM_KEY_PREFIX, roomId);
        return reactiveStringRedisTemplate.delete(eventKey)
                .then(reactiveStringRedisTemplate.delete(llmKey))
                .then();

    }

    // 定义一个元组类来存储提取的数据
    @Data
    class ExtractedData {
        private final String appId;
        private final Instant sessionStartTime;
        private final Instant sessionEndTime;
        private final List<RtcRoomEvent> events;

    }
}
