package com.wormhole.agent.log.stat;

import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.log.metrics.PerformanceMonitorUtils;
import com.wormhole.trace.log.ExecutionStatus;
import lombok.Data;

import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ExecutionStatisticsManager 类
 * 用于管理执行上下文的统计信息，包括执行的开始时间、结束时间、输入、输出等。
 */
@Data
public class ExecutionStatManager {

    /**
     * 全局的开始时间。
     */
    private final Instant globalStartTime = Instant.now();

    /**
     * 全局的结束时间。
     */
    private volatile Instant globalEndTime;

    /**
     * 全局耗时（所有 `ExecutionStatType` 统计数据的累计耗时）。
     */
    private long globalElapsedTime;

    /**
     * 用于存储每个执行的统计数据。
     */
    private final ConcurrentHashMap<ExecutionStatType, ExecutionStat> statDataMap = new ConcurrentHashMap<>();
    /**
     * 状态码
     */
    private int code = 0;
    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 开始记录一个执行。
     *
     * @param type 执行类型
     * @param input 执行的输入数据
     */
    public void start(ExecutionStatType type, Object input) {
        ExecutionStat statData = ExecutionStat.builder()
                .type(type)
                .startTime(Instant.now())
                .input(input)
                .status(ExecutionStatus.IN_PROGRESS)
                .level(2)
                .build();
        if (type == ExecutionStatType.chat_access) {
            statData.setLevel(1);
            statData.setSort(-1);
        }
        statDataMap.put(type, statData);
    }

    /**
     * 开始记录一个执行。
     * @param type 执行类型
     */
    public void start(ExecutionStatType type) {
        ExecutionStat statData = ExecutionStat.builder()
                .type(type)
                .startTime(Instant.now())
                .status(ExecutionStatus.IN_PROGRESS)
                .level(2)
                .build();
        if (type == ExecutionStatType.chat_access) {
            statData.setLevel(1);
            statData.setSort(-1);
        }
        statDataMap.put(type, statData);
    }

    /**
     * 结束记录一个执行并记录输出。
     *
     * @param type 执行类型
     * @param output 执行的输出数据
     */
    public void end(ExecutionStatType type, Object output) {
        statDataMap.computeIfPresent(type, (key, statData) -> {
            Instant endTime = Instant.now();
            statData.setOutput(output);
            statData.setEndTime(endTime);

            // 自动计算耗时（毫秒）
            long elapsedMs = Duration.between(statData.getStartTime(), endTime).toMillis();
            statData.setElapsedMs(elapsedMs);

            // 默认设置状态为成功
            statData.setStatus(ExecutionStatus.SUCCESS);

            // 如果是 chat_request 类型，执行特殊逻辑
            if (type == ExecutionStatType.chat_access) {
                // 直接将耗时赋值
                statData.setElapsedMs(elapsedMs);
                globalEndTime = endTime;
                globalElapsedTime= elapsedMs;
                // 可以根据需求添加其他处理逻辑
            }
            PerformanceMonitorUtils.log(statData);
            return statData;
        });
    }
    /**
     * 结束记录一个执行并记录输出。
     *
     * @param type 执行类型
     * @param output 执行的输出数据
     */
    public void endWithStat(ExecutionStatType type, Object output,Object nodeStatInfo) {
        statDataMap.computeIfPresent(type, (key, statData) -> {
            Instant endTime = Instant.now();
            statData.setOutput(output);
            statData.setNodeStatInfo(nodeStatInfo);
            statData.setEndTime(endTime);

            // 自动计算耗时（毫秒）
            long elapsedMs = Duration.between(statData.getStartTime(), endTime).toMillis();
            statData.setElapsedMs(elapsedMs);

            // 默认设置状态为成功
            statData.setStatus(ExecutionStatus.SUCCESS);

            // 如果是 chat_request 类型，执行特殊逻辑
            if (type == ExecutionStatType.chat_access) {
                // 直接将耗时赋值
                statData.setElapsedMs(elapsedMs);
                globalEndTime = endTime;
                globalElapsedTime= elapsedMs;
                // 可以根据需求添加其他处理逻辑
            }
            PerformanceMonitorUtils.log(statData);
            return statData;
        });
    }

    /**
     * 结束记录一个执行。
     * @param type 执行类型
     * @param input 执行的输入数据
     * @param output 执行的输出数据
     */
    public void end(ExecutionStatType type, Object input, Object output) {
        statDataMap.computeIfPresent(type, (key, statData) -> {
            Instant endTime = Instant.now();
            statData.setInput(input);
            statData.setOutput(output);
            statData.setEndTime(endTime);

            // 自动计算耗时（毫秒）
            long elapsedMs = Duration.between(statData.getStartTime(), endTime).toMillis();
            statData.setElapsedMs(elapsedMs);
            // 默认设置状态为成功
            statData.setStatus(ExecutionStatus.SUCCESS);

            // 如果是 chat_request 类型，执行特殊逻辑
            if (type == ExecutionStatType.chat_access) {
                // 更新全局耗时
                globalEndTime = endTime;
                globalElapsedTime = elapsedMs;
                // 可以根据需求添加其他处理逻辑
            }
            PerformanceMonitorUtils.log(statData);
            return statData;
        });
    }

    /**
     * 记录执行失败。
     *
     * @param type 执行类型
     * @param error 发生的错误
     */
    public void fail(ExecutionStatType type, Throwable error) {
        statDataMap.computeIfPresent(type, (key, statData) -> {
            Instant endTime = Instant.now();
            statData.setEndTime(endTime);

            // 自动计算耗时，并记录失败
            long elapsedMs = Duration.between(statData.getStartTime(), endTime).toMillis();
            statData.setElapsedMs(elapsedMs);
            statData.setStatus(ExecutionStatus.FAILED);
            statData.setErrorMsg(error.getMessage());
            statData.setStackTrace(Arrays.toString(error.getStackTrace()));
            // TODO 错误日志记录
            // 更新全局耗时
            globalElapsedTime += elapsedMs;
            globalEndTime = endTime;
            PerformanceMonitorUtils.log(statData);
            return statData;
        });
    }
    /**
     * 获取特定执行的统计数据对象。
     *
     * @param type 执行类型
     * @return 统计数据对象
     */
    public ExecutionStat getStatData(ExecutionStatType type) {
        return statDataMap.get(type);
    }

    /**
     * 获取所有执行统计数据的列表，按照 startTime 排序并设置 sort 值。
     *
     * @return 执行统计数据列表
     */
    public List<ExecutionStat> getExecutionStatsList() {
        // 使用新的列表来存储所有的 ExecutionStat 对象
        List<ExecutionStat> executionStatsList = new ArrayList<>(statDataMap.values());

        // 按照 startTime 进行排序（从早到晚）
        executionStatsList.sort(Comparator.comparing(ExecutionStat::getStartTime, Comparator.nullsLast(Comparator.naturalOrder())));

        // 使用独立的计数器来设置 sort 值
        int sortCounter = 1;

        // 设置 sort 值
        for (ExecutionStat stat : executionStatsList) {
            if (!ExecutionStatType.chat_access.equals(stat.getType())) {
                stat.setSort(sortCounter++);
            }

        }

        return executionStatsList;
    }

}
