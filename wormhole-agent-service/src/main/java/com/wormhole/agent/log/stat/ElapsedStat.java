package com.wormhole.agent.log.stat;

import com.wormhole.agent.log.enums.ExecutionStatType;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Nonnull;
import java.util.Collections;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 时间线
 *
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
public class ElapsedStat {
    private final Map<ExecutionStatType, Long> start = new ConcurrentHashMap<>();
    private final Map<ExecutionStatType, Long> end = new ConcurrentHashMap<>();
    private final Map<ExecutionStatType, String> embeddingCache = new ConcurrentHashMap<>();

    @Nonnull
    public Map<ExecutionStatType, Long> getStart() {
        return Collections.unmodifiableMap(start);
    }

    @Nonnull
    public Map<ExecutionStatType, Long> getEnd() {
        return Collections.unmodifiableMap(end);
    }

    public long getStart(ExecutionStatType type) {
        if (Objects.isNull(type)) {
            return NumberUtils.LONG_MINUS_ONE;
        }
        return Optional.ofNullable(start.get(type)).orElse(NumberUtils.LONG_MINUS_ONE);
    }

    public long getEnd(ExecutionStatType type) {
        if (Objects.isNull(type)) {
            return NumberUtils.LONG_MINUS_ONE;
        }
        return Optional.ofNullable(end.get(type)).orElse(NumberUtils.LONG_MINUS_ONE);
    }

    public void start(ExecutionStatType type, long mills) {
        if (Objects.nonNull(type)) {
            start.put(type, mills);
        }
    }

    public void end(ExecutionStatType type, long mills) {
        if (Objects.nonNull(type)) {
            end.put(type, mills);
        }
    }

    public long elapsed(ExecutionStatType type) {
        if (Objects.nonNull(type)) {
            return getEnd(type) - getStart(type);
        }
        return NumberUtils.LONG_MINUS_ONE;
    }

    public boolean isUseCache(ExecutionStatType executionStatType) {
        return Objects.nonNull(executionStatType) && Objects.nonNull(embeddingCache.get(executionStatType));
    }

    public void usedCache(ExecutionStatType executionStatType) {
        if (Objects.isNull(executionStatType)) {
            return;
        }
        embeddingCache.put(executionStatType, StringUtils.EMPTY);
    }

    public void mergeElapsedStat(ElapsedStat elapsedStat) {
        if (MapUtils.isNotEmpty(elapsedStat.getStart())) {
            start.putAll(elapsedStat.getStart());
        }
        if (MapUtils.isNotEmpty(elapsedStat.getEnd())) {
            end.putAll(elapsedStat.getEnd());
        }
    }

}
