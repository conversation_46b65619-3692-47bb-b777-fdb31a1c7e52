package com.wormhole.agent.log.metrics;

import com.wormhole.agent.client.chat.response.vo.WorkflowNodeSimpleVO;
import com.wormhole.agent.log.stat.ExecutionStat;
import com.wormhole.agent.log.vo.RtcRoomLlmMetrics;
import com.wormhole.agent.tool.core.model.LlmInvocationRecord;
import com.wormhole.agent.workflow.ModelRequestInfo;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.trace.TraceContext;
import com.wormhole.trace.log.ExecutionStatus;
import com.wormhole.trace.log.PerformanceMetric;
import com.wormhole.trace.log.PerformanceMonitor;

import java.util.HashMap;
import java.util.Map;

public class PerformanceMonitorUtils {
  
    /**
     * 将 ExecutionStat 转换为 PerformanceMetric
     *
     * @param stat ExecutionStat 对象
     * @return PerformanceMetric 对象
     */
    public static PerformanceMetric convert(ExecutionStat stat) {
        if (stat == null) {
            return null;
        }

        // 构建额外数据
        Map<String, Object> extraData = new HashMap<>();
        extraData.put("level", stat.getLevel());

        return PerformanceMetric.builder()
                .op(stat.getType() != null ? stat.getType().toString() : "UNKNOWN")
                .opDesc(stat.getType().getDesc())
                .startTime(stat.getStartTime() != null ? stat.getStartTime().toEpochMilli() : 0L)
                .endTime(stat.getEndTime() != null ? stat.getEndTime().toEpochMilli() : 0L)
                .rt(stat.getElapsedMs())
                .status(stat.getStatus())
                .errorMsg(stat.getErrorMsg())
                .traceId(TraceContext.getTraceId())
                .extra(extraData)
                .build();
    }

    /**
     * 将 RtcRoomLlmMetrics 转换为 PerformanceMetric
     *
     * @param llmMetrics RtcRoomLlmMetrics 对象
     * @return PerformanceMetric 对象
     */
    public static PerformanceMetric convert(RtcRoomLlmMetrics llmMetrics) {
        if (llmMetrics == null) {
            return null;
        }
        return PerformanceMetric.builder()
                .op("RTC_LLM")
                .opDesc("通话中的agent单次调用耗时")
                .startTime(llmMetrics.getStartTime())
                .endTime(llmMetrics.getEndTime())
                .rt(llmMetrics.getTotalElapsedMs())
                .status(ExecutionStatus.SUCCESS)
                .traceId(TraceContext.getTraceId())
                .clientReqId(llmMetrics.getClientReqId())
                .convId(llmMetrics.getConversationId())
                .rtcRoomId(llmMetrics.getRtcRoomId())
                .build();
    }

    /**
     * 将 WorkflowNodeSimpleVO 转换为 PerformanceMetric
     *
     * @param simpleVO WorkflowNodeSimpleVO 对象
     * @return PerformanceMetric 对象
     */
    public static PerformanceMetric convert(WorkflowNodeSimpleVO simpleVO) {
        if (simpleVO == null) {
            return null;
        }
        // 构建额外数据
        Map<String, Object> extraData = new HashMap<>();
//        extraData.put("input", simpleVO.getInput());
//        extraData.put("out", simpleVO.getOutput());

        return PerformanceMetric.builder()
                .op(simpleVO.getType())
                .opDesc("工作流" + simpleVO.getName())
                .startTime(simpleVO.getStart())
                .endTime(simpleVO.getEnd())
                .rt(simpleVO.getElapsedMs())
                .status(ExecutionStatus.SUCCESS)
                .traceId(TraceContext.getTraceId())
                .extra(extraData)
                .build();
    }

    /**
     * 将 ModelRequestInfo 转换为 PerformanceMetric
     *
     * @param modelRequestInfo ModelRequestInfo 对象
     * @return PerformanceMetric 对象
     */
    public static PerformanceMetric convert(ModelRequestInfo modelRequestInfo) {
        if (modelRequestInfo == null) {
            return null;
        }
        // 构建额外数据
        Map<String, Object> extraData = new HashMap<>();
//        extraData.put("input", modelRequestInfo.getInput());
//        extraData.put("out", modelRequestInfo.getOutput());

        return PerformanceMetric.builder()
                .op(modelRequestInfo.getName())
                .opDesc(modelRequestInfo.getName())
                .model(modelRequestInfo.getModelName())
                .startTime(modelRequestInfo.getStartTime())
                .endTime(modelRequestInfo.getEndTime())
                .rt(modelRequestInfo.getElapsedMs())
                .status(ExecutionStatus.SUCCESS)
                .traceId(TraceContext.getTraceId())
                .extra(extraData)
                .build();
    }

    /**
     * 将 LlmInvocationRecord.ToolInvocationDetail 转换为 PerformanceMetric
     *
     * @param detail LlmInvocationRecord.ToolInvocationDetail 对象
     * @return PerformanceMetric 对象
     */
    public static PerformanceMetric convert(LlmInvocationRecord.ToolInvocationDetail detail) {
        if (detail == null) {
            return null;
        }
        // 构建额外数据
        Map<String, Object> extraData = new HashMap<>();
//        extraData.put("input", detail.getInput());
//        extraData.put("out", detail.getOutput());

        return PerformanceMetric.builder()
                .op(detail.getToolName())
                .opDesc(detail.getToolName())
                .startTime(detail.getStartTime())
                .endTime(detail.getEndTime())
                .rt(detail.getElapsedMs())
                .status(ExecutionStatus.SUCCESS)
                .traceId(TraceContext.getTraceId())
                .extra(extraData)
                .build();
    }

    public static void log(LlmInvocationRecord llmInvocationRecord) {
         PerformanceMonitor.log(JacksonUtils.writeValueAsString(convert(llmInvocationRecord)));
    }

    /**
     * 将 LlmInvocationRecord 转换为 PerformanceMetric
     *
     * @param llmInvocationRecord LlmInvocationRecord 对象
     * @return PerformanceMetric 对象
     */
    public static PerformanceMetric convert(LlmInvocationRecord llmInvocationRecord) {
        if (llmInvocationRecord == null) {
            return null;
        }
        // 构建额外数据
        Map<String, Object> extraData = new HashMap<>();
//        extraData.put("input", llmInvocationRecord.getInput());
//        extraData.put("out", llmInvocationRecord.getOutput());

        return PerformanceMetric.builder()
                .op("intent_llm")
                .opDesc("意图识别-模型调用")
                .model(llmInvocationRecord.getModelName())
                .startTime(llmInvocationRecord.getLlmStartTime())
                .endTime(llmInvocationRecord.getLlmEndTime())
                .rt(llmInvocationRecord.getLlmElapsedMs())
                .status(ExecutionStatus.SUCCESS)
                .traceId(TraceContext.getTraceId())
                .extra(extraData)
                .build();
    }

    public static void log(ExecutionStat stat) {
         PerformanceMonitor.log(JacksonUtils.writeValueAsString(convert(stat)));
    }

    public static void log(LlmInvocationRecord.ToolInvocationDetail detail) {
         PerformanceMonitor.log(JacksonUtils.writeValueAsString(convert(detail)));
    }

    public static void log(RtcRoomLlmMetrics rtcRoomLlmMetrics) {
         PerformanceMonitor.log(JacksonUtils.writeValueAsString(convert(rtcRoomLlmMetrics)));
    }

    public static void log(WorkflowNodeSimpleVO workflowNodeSimpleVO) {
         PerformanceMonitor.log(JacksonUtils.writeValueAsString(convert(workflowNodeSimpleVO)));
    }

    public static void log(ModelRequestInfo modelRequestInfo) {
         PerformanceMonitor.log(JacksonUtils.writeValueAsString(convert(modelRequestInfo)));
    }

    public static void log(PerformanceMetric metric) {
        PerformanceMonitor.log(JacksonUtils.writeValueAsString(metric));
    }

}
