package com.wormhole.agent.log.service;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.*;
import com.wormhole.agent.client.chat.response.FlameGraph;
import com.wormhole.agent.client.chat.response.FlameNode;
import com.wormhole.common.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

/**
 * FlameGraph 到 SpeedScope 格式的转换器
 */
@Slf4j
public class FlameGraphToSpeedscope {

    // 颜色列表，用于为不同的帧分配颜色
    private static final String[] COLORS = {
            "#8dd3c7", "#ffffb3", "#bebada", "#bc80bd", "#ccebc5",
            "#ffed6f", "#b3de69", "#fccde5", "#d9d9d9", "#80b1d3",
            "#fdb462", "#fb8072", "#b3b3b3"
    };

    /**
     * 将 FlameGraph 对象转换为 SpeedScope 格式的 JSON 字符串
     *
     * @param flameGraph 火焰图对象
     * @return SpeedScope 格式的 JSON 字符串
     */
    public static String convert(FlameGraph flameGraph) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            ObjectNode speedscopeJson = createSpeedscopeFormat(flameGraph, mapper);
            return mapper.writeValueAsString(speedscopeJson);
        } catch (Exception e) {
            e.printStackTrace();
            return "{}";
        }
    }

    /**
     * 创建 SpeedScope 格式的 JSON 对象
     *
     * @param flameGraph 火焰图对象
     * @param mapper JSON 对象映射器
     * @return SpeedScope 格式的 JSON 对象
     */
    private static ObjectNode createSpeedscopeFormat(FlameGraph flameGraph, ObjectMapper mapper) {
        ObjectNode speedscopeJson = mapper.createObjectNode();
        log.info("createSpeedscopeFormat flameGraph:{}",JacksonUtils.writeValueAsString(flameGraph));
        // 设置基本信息
        speedscopeJson.put("$schema", "https://www.speedscope.app/file-format-schema.json");
        speedscopeJson.put("activeProfileIndex", 0);
        speedscopeJson.put("name", flameGraph.getTitle());

        // 复制元数据
        if (flameGraph.getMetadata() != null) {
            ObjectNode metadataNode = mapper.createObjectNode();
            for (Map.Entry<String, Object> entry : flameGraph.getMetadata().entrySet()) {
                addValueToNode(metadataNode, entry.getKey(), entry.getValue());
            }
            speedscopeJson.set("metadata", metadataNode);
        }

        // 创建帧映射和样本
        List<String> frameNames = new ArrayList<>();
        Map<String, Integer> frameNameToId = new HashMap<>();
        Map<String, String> frameColors = new HashMap<>();
        List<List<Integer>> samples = new ArrayList<>();
        List<Integer> weights = new ArrayList<>();

        // 处理所有节点，收集帧和样本
        collectFramesAndSamples(flameGraph.getRoot(), null, frameNames, frameNameToId,
                frameColors, samples, weights, 0);

        // 创建共享帧数组
        ArrayNode sharedFrames = mapper.createArrayNode();
        for (int i = 0; i < frameNames.size(); i++) {
            String frameName = frameNames.get(i);
            ObjectNode frame = mapper.createObjectNode();
            frame.put("name", frameName);
            if (frameColors.containsKey(frameName)) {
                frame.put("color", frameColors.get(frameName));
            }
            sharedFrames.add(frame);
        }

        // 创建shared对象
        ObjectNode shared = mapper.createObjectNode();
        shared.set("frames", sharedFrames);
        speedscopeJson.set("shared", shared);

        // 创建profiles数组
        ArrayNode profiles = mapper.createArrayNode();
        ObjectNode profile = mapper.createObjectNode();
        profile.put("type", "sampled");
        profile.put("name", flameGraph.getTitle());
        profile.put("unit", "milliseconds");
        profile.put("startValue", 0);
        profile.put("endValue", flameGraph.getRoot().getDuration());

        // 添加样本和权重
        ArrayNode samplesNode = mapper.createArrayNode();
        for (List<Integer> sample : samples) {
            ArrayNode sampleNode = mapper.createArrayNode();
            for (int frameId : sample) {
                sampleNode.add(frameId);
            }
            samplesNode.add(sampleNode);
        }
        profile.set("samples", samplesNode);

        ArrayNode weightsNode = mapper.createArrayNode();
        for (int weight : weights) {
            weightsNode.add(weight);
        }
        profile.set("weights", weightsNode);

        profiles.add(profile);
        speedscopeJson.set("profiles", profiles);

        return speedscopeJson;
    }

    /**
     * 将值添加到 JSON 节点
     *
     * @param node JSON 节点
     * @param key 键
     * @param value 值
     */
    private static void addValueToNode(ObjectNode node, String key, Object value) {
        if (value instanceof String) {
            node.put(key, (String) value);
        } else if (value instanceof Integer) {
            node.put(key, (Integer) value);
        } else if (value instanceof Long) {
            node.put(key, (Long) value);
        } else if (value instanceof Double) {
            node.put(key, (Double) value);
        } else if (value instanceof Boolean) {
            node.put(key, (Boolean) value);
        } else if (value != null) {
            node.put(key, value.toString());
        }
    }

    private static void collectFramesAndSamples(FlameNode node, List<Integer> parentPath,
                                                List<String> frameNames, Map<String, Integer> frameNameToId,
                                                Map<String, String> frameColors, List<List<Integer>> samples,
                                                List<Integer> weights, int colorIndex) {
        if (node == null || node.getName() == null) {
            return;
        }

        // 使用更具唯一性的名称，例如添加节点类型或路径信息
        String name = node.getName();
        // 可以考虑添加节点类型作为名称的一部分，如果有的话
        // if (node.getType() != null) {
        //     name = name + " (" + node.getType() + ")";
        // }

        // 获取或创建帧ID
        int frameId;
        if (frameNameToId.containsKey(name)) {
            frameId = frameNameToId.get(name);
        } else {
            frameId = frameNames.size();
            frameNameToId.put(name, frameId);
            frameNames.add(name);

            // 分配颜色 - 使用固定的颜色索引计算方式
            if (node.getDuration() > 0) {
                int stableColorIndex = Math.abs(name.hashCode()) % COLORS.length;
                frameColors.put(name, COLORS[stableColorIndex]);
            }
        }

        // 创建当前路径
        List<Integer> currentPath = new ArrayList<>();
        if (parentPath != null) {
            currentPath.addAll(parentPath);
        }
        currentPath.add(frameId);

        // 如果有持续时间，添加样本
        if (node.getDuration() > 0) {
            samples.add(new ArrayList<>(currentPath)); // 确保添加的是副本
            weights.add((int) node.getDuration());
        }

        // 递归处理子节点
        if (node.getChildren() != null && !node.getChildren().isEmpty()) {
            for (FlameNode child : node.getChildren()) {
                // 为每个子节点传递当前路径的副本，以防止修改
                collectFramesAndSamples(child, new ArrayList<>(currentPath), frameNames, frameNameToId,
                        frameColors, samples, weights, colorIndex + 1);
            }
        }
    }


    /**
     * 使用示例
     */
    public static void main(String[] args) {
        // 创建一个示例 FlameGraph
        FlameNode root = new FlameNode("RTC通话记录", 1747720619973L, 1747720643624L);

        FlameNode round0 = new FlameNode("轮次 0", 1747720619973L, 1747720643624L);
        root.addChild(round0);

        FlameNode task1 = new FlameNode("0→0 (taskStart→answerFinish)", 1747720619973L, 1747720625640L);
        round0.addChild(task1);

        FlameNode task2 = new FlameNode("0→LLM开始 (answerFinish→LLM处理前)", 1747720625640L, 1747720629263L);
        round0.addChild(task2);

        FlameNode llmProcess = new FlameNode("LLM处理", 1747720629263L, 1747720631836L);
        round0.addChild(llmProcess);

        FlameNode readMemory = new FlameNode("读取记忆", 1747720629281L, 1747720629284L);
        llmProcess.addChild(readMemory);

        FlameNode workflow = new FlameNode("工作流", 1747720629285L, 1747720631836L);
        llmProcess.addChild(workflow);

        // 添加工作流的子节点
        workflow.addChild(new FlameNode("开始-开始(start)", 1747720629285L, 1747720629286L));
        workflow.addChild(new FlameNode("模型分类+提参-大模型(llm)", 1747720629286L, 1747720630656L));
        workflow.addChild(new FlameNode("选择器-选择器(conditional_branch)", 1747720630657L, 1747720630659L));
        workflow.addChild(new FlameNode("work_order_operation - get_tickets-插件(plugin)", 1747720630659L, 1747720630667L));
        workflow.addChild(new FlameNode("查询酒店能提供哪些物品-知识库检索(knowledge_search)", 1747720630667L, 1747720630687L));
        workflow.addChild(new FlameNode("送物/洗衣/入离/交通/餐饮-LLM-大模型(llm)", 1747720630688L, 1747720631835L));
        workflow.addChild(new FlameNode("结束-结束(end)", 1747720631835L, 1747720631836L));

        FlameNode storeMemory = new FlameNode("存储记忆", 1747720631837L, 1747720631839L);
        llmProcess.addChild(storeMemory);

        FlameNode task3 = new FlameNode("LLM结束→0 (LLM处理后→taskStop)", 1747720631836L, 1747720643624L);
        round0.addChild(task3);

        FlameNode round1 = new FlameNode("轮次 1", 1747720626791L, 1747720638010L);
        root.addChild(round1);

        round1.addChild(new FlameNode("0→0 (beginAsking→asrFinish)", 1747720626791L, 1747720628995L));
        round1.addChild(new FlameNode("0→0 (asrFinish→answerFinish)", 1747720628995L, 1747720638010L));

        // 创建 FlameGraph 对象
        FlameGraph flameGraph = new FlameGraph("RTC通话性能分析", root, FlameGraph.Type.TRACING);

        // 添加元数据
        flameGraph.addMetadata("total_elapsed_ms", 23651);
        flameGraph.addMetadata("round_count", 2);
        flameGraph.addMetadata("rtc_room_id", "HEB3ZM4_DPRKV6L4_54C8DDA2-F8AB-4881-B9E1-51E59FDB4FB9_1747720616808");
        flameGraph.addMetadata("event_count", 6);
        flameGraph.addMetadata("unused_llm_metrics_count", 0);
        flameGraph.addMetadata("llm_metrics_count", 1);
        flameGraph.addMetadata("created_at", 1747720978445L);
        System.out.println(JSON.toJSONString(flameGraph));
        System.out.println("==================");

        String info  = "{\"root\": {\"name\": \"RTC通话记录\",\"children\": [{\"name\": \"轮次 0\",\"children\": [{\"name\": \"0→0 (taskStart→answerFinish)\",\"children\": [],\"start_time\": 1747720619973,\"start_time_formatted\": \"2025-05-20 13:56:59.973\",\"end_time\": 1747720625640,\"end_time_formatted\": \"2025-05-20 13:57:05.640\",\"value\": 5667,\"type\": \"stage\",\"metadata\": {\"duration_ms\": 5667,\"prev_run_stage\": \"taskStart\",\"curr_event_type\": 0,\"prev_event_type\": 0,\"curr_run_stage\": \"answerFinish\"},\"duration\": 5667},{\"name\": \"0→LLM开始 (answerFinish→LLM处理前)\",\"children\": [],\"start_time\": 1747720625640,\"start_time_formatted\": \"2025-05-20 13:57:05.640\",\"end_time\": 1747720629263,\"end_time_formatted\": \"2025-05-20 13:57:09.263\",\"value\": 3623,\"type\": \"stage\",\"metadata\": {\"prev_run_stage\": \"answerFinish\",\"prev_event_type\": 0},\"duration\": 3623},{\"name\": \"LLM处理\",\"children\": [{\"name\": \"读取记忆\",\"children\": [],\"start_time\": 1747720629281,\"start_time_formatted\": \"2025-05-20 13:57:09.281\",\"end_time\": 1747720629284,\"end_time_formatted\": \"2025-05-20 13:57:09.284\",\"value\": 3,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 3},{\"name\": \"工作流\",\"children\": [{\"name\": \"开始-开始(start)\",\"children\": [],\"start_time\": 1747720629285,\"start_time_formatted\": \"2025-05-20 13:57:09.285\",\"end_time\": 1747720629286,\"end_time_formatted\": \"2025-05-20 13:57:09.286\",\"value\": 1,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 1},{\"name\": \"模型分类+提参-大模型(llm)\",\"children\": [],\"start_time\": 1747720629286,\"start_time_formatted\": \"2025-05-20 13:57:09.286\",\"end_time\": 1747720630656,\"end_time_formatted\": \"2025-05-20 13:57:10.656\",\"value\": 1370,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 1370},{\"name\": \"选择器-选择器(conditional_branch)\",\"children\": [],\"start_time\": 1747720630657,\"start_time_formatted\": \"2025-05-20 13:57:10.657\",\"end_time\": 1747720630659,\"end_time_formatted\": \"2025-05-20 13:57:10.659\",\"value\": 2,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 2},{\"name\": \"work_order_operation - get_tickets-插件(plugin)\",\"children\": [],\"start_time\": 1747720630659,\"start_time_formatted\": \"2025-05-20 13:57:10.659\",\"end_time\": 1747720630667,\"end_time_formatted\": \"2025-05-20 13:57:10.667\",\"value\": 8,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 8},{\"name\": \"查询酒店能提供哪些物品-知识库检索(knowledge_search)\",\"children\": [],\"start_time\": 1747720630667,\"start_time_formatted\": \"2025-05-20 13:57:10.667\",\"end_time\": 1747720630687,\"end_time_formatted\": \"2025-05-20 13:57:10.687\",\"value\": 20,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 20},{\"name\": \"送物/洗衣/入离/交通/餐饮-LLM-大模型(llm)\",\"children\": [],\"start_time\": 1747720630688,\"start_time_formatted\": \"2025-05-20 13:57:10.688\",\"end_time\": 1747720631835,\"end_time_formatted\": \"2025-05-20 13:57:11.835\",\"value\": 1147,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 1147},{\"name\": \"结束-结束(end)\",\"children\": [],\"start_time\": 1747720631835,\"start_time_formatted\": \"2025-05-20 13:57:11.835\",\"end_time\": 1747720631836,\"end_time_formatted\": \"2025-05-20 13:57:11.836\",\"value\": 1,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 1}],\"start_time\": 1747720629285,\"start_time_formatted\": \"2025-05-20 13:57:09.285\",\"end_time\": 1747720631836,\"end_time_formatted\": \"2025-05-20 13:57:11.836\",\"value\": 2551,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 2551},{\"name\": \"存储记忆\",\"children\": [],\"start_time\": 1747720631837,\"start_time_formatted\": \"2025-05-20 13:57:11.837\",\"end_time\": 1747720631839,\"end_time_formatted\": \"2025-05-20 13:57:11.839\",\"value\": 2,\"type\": \"llm_component\",\"metadata\": {},\"duration\": 2}],\"start_time\": 1747720629263,\"start_time_formatted\": \"2025-05-20 13:57:09.263\",\"end_time\": 1747720631836,\"end_time_formatted\": \"2025-05-20 13:57:11.836\",\"value\": 2572,\"type\": \"llm\",\"metadata\": {\"client_req_id\": \"0fb01b39a0f7bbaafcc943fba63f045a\",\"trace_id\": \"858cf131e94a473fb28c1b071bde8b14\",\"conversation_id\": \"7c42f3993ff7572e0abc754ea731d01d\"},\"duration\": 2573},{\"name\": \"LLM结束→0 (LLM处理后→taskStop)\",\"children\": [],\"start_time\": 1747720631836,\"start_time_formatted\": \"2025-05-20 13:57:11.836\",\"end_time\": 1747720643624,\"end_time_formatted\": \"2025-05-20 13:57:23.624\",\"value\": 11788,\"type\": \"stage\",\"metadata\": {\"curr_event_type\": 0,\"curr_run_stage\": \"taskStop\"},\"duration\": 11788}],\"start_time\": 1747720619973,\"start_time_formatted\": \"2025-05-20 13:56:59.973\",\"end_time\": 1747720643624,\"end_time_formatted\": \"2025-05-20 13:57:23.624\",\"value\": 23651,\"type\": \"round\",\"metadata\": {\"round_id\": 0,\"event_count\": 3},\"duration\": 23651},{\"name\": \"轮次 1\",\"children\": [{\"name\": \"0→0 (beginAsking→asrFinish)\",\"children\": [],\"start_time\": 1747720626791,\"start_time_formatted\": \"2025-05-20 13:57:06.791\",\"end_time\": 1747720628995,\"end_time_formatted\": \"2025-05-20 13:57:08.995\",\"value\": 2204,\"type\": \"stage\",\"metadata\": {\"duration_ms\": 2204,\"prev_run_stage\": \"beginAsking\",\"curr_event_type\": 0,\"prev_event_type\": 0,\"curr_run_stage\": \"asrFinish\"},\"duration\": 2204},{\"name\": \"0→0 (asrFinish→answerFinish)\",\"children\": [],\"start_time\": 1747720628995,\"start_time_formatted\": \"2025-05-20 13:57:08.995\",\"end_time\": 1747720638010,\"end_time_formatted\": \"2025-05-20 13:57:18.010\",\"value\": 9015,\"type\": \"stage\",\"metadata\": {\"duration_ms\": 9015,\"prev_run_stage\": \"asrFinish\",\"curr_event_type\": 0,\"prev_event_type\": 0,\"curr_run_stage\": \"answerFinish\"},\"duration\": 9015}],\"start_time\": 1747720626791,\"start_time_formatted\": \"2025-05-20 13:57:06.791\",\"end_time\": 1747720638010,\"end_time_formatted\": \"2025-05-20 13:57:18.010\",\"value\": 11219,\"type\": \"round\",\"metadata\": {\"round_id\": 1,\"event_count\": 3},\"duration\": 11219}],\"start_time\": 1747720619973,\"start_time_formatted\": \"2025-05-20 13:56:59.973\",\"end_time\": 1747720643624,\"end_time_formatted\": \"2025-05-20 13:57:23.624\",\"value\": 23651,\"type\": null,\"metadata\": {},\"duration\": 23651},\"metadata\": {\"total_elapsed_ms\": 23651,\"round_count\": 2,\"rtc_room_id\": \"HEB3ZM4_DPRKV6L4_54C8DDA2-F8AB-4881-B9E1-51E59FDB4FB9_1747720616808\",\"event_count\": 6,\"unused_llm_metrics_count\": 0,\"llm_metrics_count\": 1,\"created_at\": 1747728359375},\"title\": \"RTC通话性能分析\",\"timestamp\": 1747728359375,\"type\": \"TRACING\"}";
        FlameGraph g = JacksonUtils.readValue(info, FlameGraph.class);
        // 转换为 SpeedScope 格式并输出
        String speedscopeJson = convert(g);
        System.out.println(speedscopeJson);
    }
}
