package com.wormhole.agent.log.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RtcRoomRound {
    /**
     * 轮次ID
     */
    private Integer roundId;
    /**
     * 轮次内容描述，说明这轮发生了什么
     */
    private String content;
    /**
     * 事件数量
     */
    private Integer eventCount;

    /**
     * 开始时间
     */
    private long startTime;

    /**
     * 结束时间
     */
    private long endTime;

    /**
     * 持续时间(毫秒)
     */
    private long elapsedMs;
}
