package com.wormhole.agent.log.index;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.util.DateParsePatterns;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.time.Instant;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "chat-access-log")
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ChatAccessLogIndex {

    /**
     * 对应索引中的 "@timestamp"
     */
    @JsonFormat(pattern = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_DASH)
    @Field(type = FieldType.Date, name = "@timestamp")
    private Instant timestamp;

    /**
     * 对应索引中的 "start_time"
     */
    @JsonFormat(pattern = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_DASH)
    @Field(type = FieldType.Date, name = "start_time")
    private Instant startTime;

    /**
     * 对应索引中的 "end_time"
     */
    @JsonFormat(pattern = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_DASH)
    @Field(type = FieldType.Date, name = "end_time")
    private Instant endTime;

    /**
     * 对应索引中的 "elapsed_ms"
     */
    @Field(type = FieldType.Long, name = "elapsed_ms")
    private Long elapsedMs;

    /**
     * 对应索引中的 "trace_id"
     */
    @Field(type = FieldType.Keyword, name = "trace_id")
    private String traceId;

    /**
     * 对应索引中的 "account_id"
     */
    @Field(type = FieldType.Keyword, name = "user_id")
    private String userId;

    /**
     * 对应索引中的 "conversation_id"
     */
    @Field(type = FieldType.Keyword, name = "conversation_id")
    private String conversationId;

    /**
     * TODO 对应索引中的 "chat_completion_id"
     */
    @Field(type = FieldType.Keyword, name = "chat_completion_id")
    private String chatCompletionId;

    /**
     * 对应索引中的 "client_req_id"
     */
    @Field(type = FieldType.Keyword, name = "client_req_id")
    private String clientReqId;

    /**
     * 对应索引中的 "chat_type"
     */
    @Field(type = FieldType.Keyword, name = "chat_type")
    private String chatType;

    /**
     * 对应索引中的 "bot_code"
     */
    @Field(type = FieldType.Keyword, name = "bot_code")
    private String botCode;

    /**
     * 对应索引中的 "question"
     */
    @Field(type = FieldType.Text, name = "question", analyzer = "ik_max_analyzer", searchAnalyzer = "ik_smart")
    private String question;

    /**
     * 对应索引中的 "answer"
     */
    @Field(type = FieldType.Text, name = "answer", analyzer = "ik_max_analyzer", searchAnalyzer = "ik_smart")
    private String answer;

    /**
     * 对应索引中的 "bot_info"
     */
    @Field(type = FieldType.Text, name = "bot_info")
    private String botInfo;

    /**
     * 对应索引中的 "code"
     */
    @Field(type = FieldType.Integer, name = "code")
    private Integer code;


    /**
     * 对应索引中的 "bot_model"
     */
    @Field(type = FieldType.Keyword, name = "bot_model")
    private String botMode;

    /**
     * 对应索引中的 "error_msg"
     */
    @Field(type = FieldType.Text, name = "error_msg", index = false)
    private String errorMsg;

    /**
     * 对应索引中的 "execution_stat_list"（禁用索引）
     */
    @Field(type = FieldType.Text, name = "execution_stat_list", index = false)
    private String executionStatList;
}
