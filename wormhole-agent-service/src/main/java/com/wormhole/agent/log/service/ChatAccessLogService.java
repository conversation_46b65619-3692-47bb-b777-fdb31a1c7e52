package com.wormhole.agent.log.service;

import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.common.base.Preconditions;
import com.google.common.base.Stopwatch;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.query.BotDebugInfoQuery;
import com.wormhole.agent.client.chat.response.BotDebugInfoResponse;
import com.wormhole.agent.client.chat.response.vo.ExecutionStatVO;
import com.wormhole.agent.client.chat.response.vo.WorkflowNodeSimpleVO;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.log.analysis.VoiceChatMetricsAnalysis;
import com.wormhole.agent.log.enums.ChatAccessLogIndexFieldEnum;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.log.index.ChatAccessLogIndex;
import com.wormhole.agent.log.metrics.PerformanceMonitorUtils;
import com.wormhole.agent.log.stat.ExecutionStat;
import com.wormhole.agent.log.stat.ExecutionStatManager;
import com.wormhole.agent.log.vo.RtcRoomLlmMetrics;
import com.wormhole.agent.tool.core.model.LlmInvocationRecord;
import com.wormhole.agent.workflow.ModelRequestInfo;
import com.wormhole.common.util.DateParsePatterns;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.LocalDateTimeUtils;
import com.wormhole.trace.TraceContext;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.ReactiveElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ChatAccessLogService {

    private static final List<String> DEFAULT_SEARCH_FIELDS = List.of("question", "answer");
    private static final String TIME_ZONE = "+00:00";
    @Resource
    private ReactiveElasticsearchOperations reactiveElasticsearchOperations;
    @Resource
    private VoiceChatMetricsAnalysis voiceChatMetricsAnalysis;
    public Mono<Void> saveChatAccessLog(ChatContext chatContext) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        return Mono.justOrEmpty(createChatAccessLogIndex(chatContext))
                .filter(Objects::nonNull)
                .flatMap(chatAccessLogIndex -> reactiveElasticsearchOperations.save(chatAccessLogIndex))
                .doOnError(throwable -> log.error(throwable.getMessage(), throwable))
                .then(printExecutionTimeLog(chatContext))
                .doFinally(s -> {
                    stopwatch.stop();
                    log.info("saveChatAccessLog elapsed time: {} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
                })
                .then();
    }

    private ChatAccessLogIndex createChatAccessLogIndex(ChatContext ctx) {
        ChatAccessLogIndex chatAccessLogIndex = new ChatAccessLogIndex();
        chatAccessLogIndex.setTimestamp(LocalDateTimeUtils.nowToInstant());
        chatAccessLogIndex.setTraceId(TraceContext.getTraceId());
        chatAccessLogIndex.setClientReqId(ctx.getClientReqId());
        chatAccessLogIndex.setUserId(ctx.getUserId()!= null? ctx.getUserId() : "1");
        chatAccessLogIndex.setConversationId(ctx.getConversationId());
        chatAccessLogIndex.setBotCode(ctx.getBotCode());
        chatAccessLogIndex.setChatType(ctx.getChatType() != null ? ctx.getChatType().toString() : null);
        chatAccessLogIndex.setQuestion(ctx.getQuestion());
        chatAccessLogIndex.setAnswer(ctx.getAnswer());
        chatAccessLogIndex.setBotMode(Optional.ofNullable(ctx.getBotInfo()).map(BotInfo::getBotMode).orElse(null));
        chatAccessLogIndex.setBotInfo(Optional.ofNullable(ctx.getBotInfo()).map(JacksonUtils::writeValueAsString).orElse(null));
        chatAccessLogIndex.setStartTime(ctx.getGatewayStartTime());
        chatAccessLogIndex.setEndTime(ctx.getExecutionStatManager().getGlobalEndTime());
        chatAccessLogIndex.setElapsedMs(ctx.getExecutionStatManager().getGlobalElapsedTime());
        // 别换成jacksonUtils，否则会报错
        chatAccessLogIndex.setExecutionStatList(JSON.toJSONString(ctx.getExecutionStatManager().getExecutionStatsList()));
        chatAccessLogIndex.setChatCompletionId(ctx.getChatCompletionId());
        return chatAccessLogIndex;
    }

    /**
     * 打印简版执行耗时日志
     * @param ctx 聊天上下文
     */
    private Mono<Void> printExecutionTimeLog(ChatContext ctx) {
        ExecutionStatManager statManager = ctx.getExecutionStatManager();
        long totalElapsedMs = statManager.getGlobalElapsedTime();
        RtcRoomLlmMetrics llmMetrics = new RtcRoomLlmMetrics();
        llmMetrics.setRtcRoomId(ctx.getRtcRoomId());
        llmMetrics.setTraceId(ctx.getTraceId());
        llmMetrics.setFirstTokenTime(ctx.getFirstTokenTime());
        llmMetrics.setClientReqId(ctx.getClientReqId());
        llmMetrics.setConversationId(ctx.getConversationId());
        List<ExecutionStat> statsList = statManager.getExecutionStatsList();
        if (statsList == null || statsList.isEmpty()) {
            log.info("会话耗时日志 - 会话ID: {} traceId:{}, 总耗时: {}ms, 无执行节点",
                    ctx.getConversationId(), ctx.getTraceId(),totalElapsedMs);
            return Mono.empty();
        }

        // 构建日志头部
        StringBuilder logBuilder = new StringBuilder();
        logBuilder.append(String.format("会话耗时日志 - 会话ID: %s,\n  rtcRoomID: %s,\n,首片token:%s ,耗时%dms\n 总耗时: %dms\n 总开始时间：%s\n,总结束时间：%s\n ",
                ctx.getConversationId(),ctx.getRtcRoomId(),
                LocalDateTimeUtils.formatTimestamp(ctx.getFirstTokenTime(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
                ctx.getFirstTokenTime()-ctx.getGatewayStartTime().toEpochMilli(),
                totalElapsedMs,
                LocalDateTimeUtils.formatTimestamp(statManager.getGlobalStartTime().toEpochMilli(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
                LocalDateTimeUtils.formatTimestamp(statManager.getGlobalEndTime().toEpochMilli(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS)
        ));
        llmMetrics.setStartTime(statManager.getGlobalStartTime().toEpochMilli());
        llmMetrics.setEndTime(statManager.getGlobalEndTime().toEpochMilli());
        llmMetrics.setTotalElapsedMs(totalElapsedMs);
        List<RtcRoomLlmMetrics.Node> rootNodeList = new ArrayList<>();
        llmMetrics.setNodes(rootNodeList);
        if(StringUtils.isNotBlank(llmMetrics.getRtcRoomId())){
            PerformanceMonitorUtils.log(llmMetrics);
        }
        // 打印执行阶段耗时
        logBuilder.append("执行阶段耗时:\n");
        List<RtcRoomLlmMetrics.Node> workflowNodeList = new ArrayList<>();

        for (ExecutionStat stat : statsList) {
            if (stat.getLevel() != 1 && stat.getEndTime() != null) {
                double percentage = (double) stat.getElapsedMs() / totalElapsedMs * 100;
                logBuilder.append(String.format("- %s(%s): %dms (%.1f%%) , start：%s,end：%s\n",
                        stat.getType().getDesc(), stat.getType(),
                        stat.getElapsedMs(), percentage,
                        LocalDateTimeUtils.formatTimestamp(stat.getStartTime().toEpochMilli(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS),
                        LocalDateTimeUtils.formatTimestamp(stat.getEndTime().toEpochMilli(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS)
                        ));
                RtcRoomLlmMetrics.Node node = RtcRoomLlmMetrics.Node.builder()
                        .elapsedMs(stat.getElapsedMs())
                        .startTime(stat.getStartTime().toEpochMilli())
                        .endTime(stat.getEndTime().toEpochMilli())
                        .name(stat.getType().getDesc())
                        .build();
                if(ExecutionStatType.workflow.equals(stat.getType())){
                    node.setSubNodes(workflowNodeList);
                }
                rootNodeList.add(node);

            }
        }

        // 查找工作流节点并打印工作流节点耗时
        for (ExecutionStat stat : statsList) {
            if (ExecutionStatType.workflow.equals(stat.getType()) && stat.getNodeStatInfo() != null) {
                logBuilder.append("\n工作流节点耗时:\n");

                // 从节点统计信息中提取节点耗时数据
                List<WorkflowNodeSimpleVO> nodeStats = (List<WorkflowNodeSimpleVO>) stat.getNodeStatInfo();

                // 按耗时降序排序
                nodeStats.sort((a, b) -> Long.compare(b.getElapsedMs(), a.getElapsedMs()));

                // 统计LLM总耗时
                long totalLlmTime = 0;
                // 统计工具调用总耗时
                long totalToolTime = 0;

                // 打印节点耗时
                for (WorkflowNodeSimpleVO node : nodeStats) {
                    double percentage = (double) node.getElapsedMs() / totalElapsedMs * 100;
                    logBuilder.append(String.format("- %s-%s(%s): %dms (%.1f%%)\n",
                           node.getName(), node.getTypeName(), node.getType(), node.getElapsedMs(), percentage));
                    RtcRoomLlmMetrics.Node subNode = RtcRoomLlmMetrics.Node.builder()
                            .elapsedMs(node.getElapsedMs())
                            .startTime(node.getStart())
                            .endTime(node.getEnd())
                            .name(String.format("%s-%s(%s)",
                                    node.getName(), node.getTypeName(), node.getType()))
                            .build();
                    List<RtcRoomLlmMetrics.Node> subSubNodeList = new ArrayList<>();
                    // 处理模型请求信息
                     ModelRequestInfo modelRequestInfo = ( ModelRequestInfo) node.getModelRequestInfo();
                    if (modelRequestInfo != null) {
                        logBuilder.append("  模型请求信息:\n");
                        logBuilder.append(String.format("  - %s: 模型=%s, 供应商=%s, 耗时=%dms\n",
                                modelRequestInfo.getName(), modelRequestInfo.getModelName(), modelRequestInfo.getModelProvider(),
                                modelRequestInfo.getElapsedMs()));
                        RtcRoomLlmMetrics.Node summaryLLM = RtcRoomLlmMetrics.Node.builder()
                                .elapsedMs(modelRequestInfo.getElapsedMs())
                                .startTime(modelRequestInfo.getStartTime())
                                .endTime(modelRequestInfo.getEndTime())
                                .name(String.format("%s-%s(%s)",
                                        modelRequestInfo.getName(), modelRequestInfo.getModelName(), modelRequestInfo.getModelProvider()))
                                .build();
                        subSubNodeList.add(summaryLLM);
                        // 累加LLM耗时
                        totalLlmTime += modelRequestInfo.getElapsedMs();
                    }

                    // 处理LLM调用记录
                    Map<String, LlmInvocationRecord> llmInvocationRecordsMap = (Map<String, LlmInvocationRecord>) node.getLlmInvocationRecords();
                    if (llmInvocationRecordsMap != null && !llmInvocationRecordsMap.isEmpty()) {
                        logBuilder.append("  LLM调用记录:\n");
                        for (Map.Entry<String, LlmInvocationRecord> entry : llmInvocationRecordsMap.entrySet()) {
                            LlmInvocationRecord record = entry.getValue();
                            logBuilder.append(String.format("  - %s: 状态=%s, 模型=%s, 供应商=%s, LLM耗时=%dms, 总耗时=%dms\n",
                                    entry.getKey(), record.isSuccess() ? "成功" : "失败",record.getModelName(),record.getModelProvider(),
                                    record.getLlmElapsedMs(), record.getTotalElapsedMs()));
                            RtcRoomLlmMetrics.Node llmNode = RtcRoomLlmMetrics.Node.builder()
                                    .elapsedMs(record.getLlmElapsedMs())
                                    .startTime(record.getLlmStartTime())
                                    .endTime(record.getLlmEndTime())
                                    .name(String.format("%s(%s)", record.getModelName(),record.getModelProvider()))
                                    .build();
                            subSubNodeList.add(llmNode);
                            totalLlmTime += record.getLlmElapsedMs();

                            // 处理工具调用记录
                            List<LlmInvocationRecord.ToolInvocationDetail> toolInvocations = record.getToolInvocations();
                            if (toolInvocations != null && !toolInvocations.isEmpty()) {
                                logBuilder.append("    工具调用:\n");

                                // 按耗时降序排序工具调用
                                toolInvocations.sort((a, b) -> Long.compare(b.getElapsedMs(), a.getElapsedMs()));

                                for (LlmInvocationRecord.ToolInvocationDetail tool : toolInvocations) {
                                    logBuilder.append(String.format("    - %s: 状态=%s, 耗时=%dms\n",
                                            tool.getToolName(), tool.isSuccess() ? "成功" : "失败",
                                            tool.getElapsedMs()));
                                    RtcRoomLlmMetrics.Node toolNode = RtcRoomLlmMetrics.Node.builder()
                                            .elapsedMs(tool.getElapsedMs())
                                            .startTime(tool.getStartTime())
                                            .endTime(tool.getEndTime())
                                            .name( tool.getToolName())
                                            .build();
                                    subSubNodeList.add(toolNode);
                                    // 累加工具调用耗时
                                    totalToolTime += tool.getElapsedMs();
                                }
                            }
                        }
                    }
                    if(!subSubNodeList.isEmpty()){
                        subNode.setSubNodes(subSubNodeList);
                    }
                    workflowNodeList.add(subNode);
                }

                // 打印LLM和工具调用总耗时
                if (totalLlmTime > 0) {
                    double llmPercentage = (double) totalLlmTime / totalElapsedMs * 100;
                    logBuilder.append(String.format("\nLLM调用总耗时: %dms (%.1f%%)\n", totalLlmTime, llmPercentage));
                }

                if (totalToolTime > 0) {
                    double toolPercentage = (double) totalToolTime / totalElapsedMs * 100;
                    logBuilder.append(String.format("工具调用总耗时: %dms (%.1f%%)\n", totalToolTime, toolPercentage));
                }

                break;
            } else {
                // 其他类型节点的处理 - 只打印基本耗时信息
                if (stat.getNodeStatInfo() != null) {
                    logBuilder.append(String.format("\n%s节点耗时: %dms (%.1f%%)\n",
                            stat.getType().getDesc(),
                            stat.getElapsedMs(),
                            (double) stat.getElapsedMs() / totalElapsedMs * 100));
                }
            }
        }

        log.info(logBuilder.toString());
        log.info("rtc:{} ,llmMetrics : {}",ctx.getRtcRoomId(),JacksonUtils.writeValueAsString(llmMetrics));
        return voiceChatMetricsAnalysis.recordLlmMetrics(llmMetrics);
    }



    public Mono<List<BotDebugInfoResponse>> query(BotDebugInfoQuery logQuery) {
        Preconditions.checkArgument(Objects.nonNull(logQuery), "logQuery must not be null");
        Preconditions.checkArgument(StringUtils.isNotBlank(logQuery.getBotCode()), "bot code must not be blank");
        Preconditions.checkArgument(StringUtils.isNotBlank(logQuery.getUserId()), "user id must not be blank");

        return Mono.defer(() -> {
            Stopwatch stopwatch = Stopwatch.createStarted();
            Query query = buildSearchQuery(logQuery);
            return executeSearch(query, stopwatch)
                    .map(logIndices -> logIndices.stream()
                            .map(this::convertToVO)
                            .collect(Collectors.toList()))
                    .onErrorResume(e -> { // 替换 onErrorReturn 提供更灵活处理
                        log.error("结果转换异常 ", e); // 假设能获取原始数据
                        return Mono.just(Collections.emptyList());
                    });
        });
    }

    private BotDebugInfoResponse convertToVO(ChatAccessLogIndex source) {
        BotDebugInfoResponse target = new BotDebugInfoResponse();
        target.setAnswer(source.getAnswer());
        target.setBotCode(source.getBotCode());
        target.setBotMode(source.getBotMode());
        target.setChatCompletionId(source.getChatCompletionId());
        target.setChatType(source.getChatType());
        target.setClientReqId(source.getClientReqId());
        target.setConversationId(source.getConversationId());
        target.setQuestion(source.getQuestion());
        target.setTraceId(source.getTraceId());
        target.setUserId(source.getUserId());
        target.setElapsedMs(source.getElapsedMs());

        if(Objects.nonNull(source.getStartTime())){
            target.setStartTime(parseDate(source.getStartTime().toEpochMilli()));
        }
        if(Objects.nonNull(source.getEndTime())){
            target.setEndTime(parseDate(source.getEndTime().toEpochMilli()));
        }
        if(Objects.nonNull(source.getExecutionStatList())){
            List<ExecutionStatVO> statVOList = JSONArray.parseArray(source.getExecutionStatList(), ExecutionStatVO.class);
            statVOList.forEach(statVO -> {
                try {
                    if(Objects.nonNull(statVO.getStartTime())){
                        statVO.setStartTime(parseDate(Long.valueOf(statVO.getStartTime())));
                    }
                    if(Objects.nonNull(statVO.getEndTime())){
                        statVO.setEndTime(parseDate(Long.valueOf(statVO.getEndTime())));
                    }
                }catch (Exception e) {
                    log.error("时间线解析 事件报错。采用兜底");
                }
                statVO.setTypeName(ExecutionStatType.getDesc(ExecutionStatType.valueOf(statVO.getType())));
            });
            target.setExecutionStatList( statVOList);
        }
        return target;
    }
    private static String parseDate(Long timestamp) {
        return LocalDateTimeUtils.formatTimestamp(timestamp, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS);
    }


    private Query buildSearchQuery(BotDebugInfoQuery logQuery) {
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        buildKeywordCondition(logQuery, boolQueryBuilder);
        buildTimeRangeCondition(logQuery, boolQueryBuilder);
        addFilterQueries(logQuery, boolQueryBuilder);

        return NativeQuery.builder()
                .withQuery(q -> q.bool(boolQueryBuilder.build()))
                .withSort(s->s.field(f->f.field(ChatAccessLogIndexFieldEnum.START_TIME.getColumnName())
                        .order(SortOrder.Desc)))
//                .withPageable(PageRequest.of(0, defaultPageSize))
                .build();
    }

    // 构建关键字查询
    private void buildKeywordCondition(BotDebugInfoQuery logQuery, BoolQuery.Builder boolQuery) {
        if (StringUtils.isBlank(logQuery.getKeyword())) return;

        boolQuery.should(s -> s.multiMatch(m -> m
                .fields(DEFAULT_SEARCH_FIELDS)
                .query(logQuery.getKeyword())
                .type(TextQueryType.BestFields)
                .operator(Operator.Or)
                .fuzziness("1")
                .minimumShouldMatch("1")
        ));
    }


    // 构建时间范围查询
    private void buildTimeRangeCondition(BotDebugInfoQuery logQuery, BoolQuery.Builder boolQuery) {
        // 1. 前置校验：如果时间范围参数均为空，直接返回
        if (logQuery.getStartTimeBegin() == null && logQuery.getStartTimeEnd() == null) {
            return;
        }

        // 2. 构建时间范围查询
        boolQuery.must(q -> q.range(RangeQuery.of(r -> r.date(DateRangeQuery.of(d ->{
                    d.field(ChatAccessLogIndexFieldEnum.START_TIME.getColumnName())
                    .format(DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS) // 必须与字段存储格式匹配
                    .timeZone(TIME_ZONE); // 明确指定时区

            // 处理开始时间
            if (logQuery.getStartTimeBegin() != null) {
                d.gte(logQuery.getStartTimeBegin());
            }

            // 处理结束时间
            if (logQuery.getStartTimeEnd() != null) {
                d.lte(logQuery.getStartTimeEnd());
            }

            return d;
        }
        )))));
    }



    // 批量添加字段过滤条件
    private void addFilterQueries(BotDebugInfoQuery logQuery, BoolQuery.Builder boolQueryBuilder) {
        // 定义字段与查询值的映射关系
        List<Pair<String, String>> filterConditions = Arrays.asList(
                Pair.of(ChatAccessLogIndexFieldEnum.TRACE_ID.getColumnName(), logQuery.getTraceId()),
                Pair.of(ChatAccessLogIndexFieldEnum.BOT_CODE.getColumnName(), logQuery.getBotCode()),
                Pair.of(ChatAccessLogIndexFieldEnum.USER_ID.getColumnName(), logQuery.getUserId()),
                Pair.of(ChatAccessLogIndexFieldEnum.CLIENT_REQ_ID.getColumnName(), logQuery.getClientReqId()),
                Pair.of(ChatAccessLogIndexFieldEnum.CONVERSATION_ID.getColumnName(), logQuery.getConversationId()),
                Pair.of(ChatAccessLogIndexFieldEnum.CHAT_COMPLETION_ID.getColumnName(), logQuery.getChatCompletionsId())
        );

        // 遍历并添加过滤条件
        filterConditions.forEach(pair -> addFilterQuery(boolQueryBuilder, pair.getLeft(), pair.getRight()));
    }

    // 添加单个过滤条件
    private void addFilterQuery(BoolQuery.Builder builder, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            builder.filter(f -> f.term(t -> t.field(field).value(value)));
        }
    }


    private Mono<List<ChatAccessLogIndex>> executeSearch(Query query, Stopwatch stopwatch) {
        return reactiveElasticsearchOperations.search(query, ChatAccessLogIndex.class)
                .map(SearchHit::getContent)
                .collectList()
                .defaultIfEmpty(Collections.emptyList())
                .doOnSuccess(results -> logSearchMetrics(results.size(), stopwatch))
                .onErrorResume(throwable -> {
                    log.error("Search execution error: {}", throwable.getMessage(), throwable);
                    return Mono.just(Collections.emptyList());
                });
    }

    private void logSearchMetrics(int resultCount, Stopwatch stopwatch) {
        stopwatch.stop();
        log.info("ES search completed: {} results found in {} ms", resultCount, stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

}