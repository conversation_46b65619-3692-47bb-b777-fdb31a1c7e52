package com.wormhole.agent.log.service;

import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.*;
import com.google.common.base.Preconditions;
import com.google.common.base.Stopwatch;
import com.wormhole.agent.client.chat.params.query.RtcRoomInfoQuery;
import com.wormhole.agent.client.chat.response.FlameGraph;
import com.wormhole.agent.client.chat.response.FlameNode;
import com.wormhole.agent.log.enums.RtcRoomMetricsIndexFieldEnum;
import com.wormhole.agent.log.index.RtcRoomMetricsIndex;
import com.wormhole.agent.log.vo.RtcRoomEvent;
import com.wormhole.agent.log.vo.RtcRoomLlmMetrics;
import com.wormhole.common.util.DateParsePatterns;
import com.wormhole.common.util.JacksonUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.core.ReactiveElasticsearchOperations;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RtcRoomMetricsService {

    private static final List<String> DEFAULT_SEARCH_FIELDS = List.of("room_id");
    private static final String TIME_ZONE = "+00:00";
    @Resource
    private ReactiveElasticsearchOperations reactiveElasticsearchOperations;
    public Mono<Void> save(RtcRoomMetricsIndex index) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        return reactiveElasticsearchOperations.save(index)
                .doOnError(throwable -> log.error(throwable.getMessage(), throwable))
                .doFinally(s -> {
                    stopwatch.stop();
                    log.info("saveChatAccessLog elapsed time: {} ms", stopwatch.elapsed(TimeUnit.MILLISECONDS));
                })
                .then();
    }



    public Mono<List<RtcRoomMetricsIndex>> query(RtcRoomInfoQuery logQuery) {
        Preconditions.checkArgument(Objects.nonNull(logQuery), "logQuery must not be null");
        Preconditions.checkArgument(StringUtils.isNotBlank(logQuery.getRtcRoomId()), "rtc room id must not be blank");

        return Mono.defer(() -> {
            Stopwatch stopwatch = Stopwatch.createStarted();
            Query query = buildSearchQuery(logQuery);
            return executeSearch(query, stopwatch)
                    .map(logIndices -> logIndices.stream()
                            .collect(Collectors.toList()))
                    .onErrorResume(e -> { // 替换 onErrorReturn 提供更灵活处理
                        log.error("结果转换异常 ", e); // 假设能获取原始数据
                        return Mono.just(Collections.emptyList());
                    });
        });
    }
    public Mono<List<FlameGraph>> queryFlame(RtcRoomInfoQuery logQuery) {
        Preconditions.checkArgument(Objects.nonNull(logQuery), "logQuery must not be null");
        Preconditions.checkArgument(StringUtils.isNotBlank(logQuery.getRtcRoomId()), "rtc room id must not be blank");

        return Mono.defer(() -> {
            Stopwatch stopwatch = Stopwatch.createStarted();
            Query query = buildSearchQuery(logQuery);
            return executeSearch(query, stopwatch)
                    .map(logIndices -> logIndices.stream()
                            .map(this::convertToVO)
                            .collect(Collectors.toList()))
                    .onErrorResume(e -> { // 替换 onErrorReturn 提供更灵活处理
                        log.error("结果转换异常 ", e); // 假设能获取原始数据
                        return Mono.just(Collections.emptyList());
                    });
        });
    }
    public Mono<List<String>> queryFlameToSpeedscope(RtcRoomInfoQuery logQuery) {
        Preconditions.checkArgument(Objects.nonNull(logQuery), "logQuery must not be null");
        Preconditions.checkArgument(StringUtils.isNotBlank(logQuery.getRtcRoomId()), "rtc room id must not be blank");

        return Mono.defer(() -> {
            Stopwatch stopwatch = Stopwatch.createStarted();
            Query query = buildSearchQuery(logQuery);
            return executeSearch(query, stopwatch)
                    .map(logIndices -> logIndices.stream()
                            .map(this::convertToVO)
                            .map(FlameGraphToSpeedscope::convert)
                            .collect(Collectors.toList()))
                    .onErrorResume(e -> { // 替换 onErrorReturn 提供更灵活处理
                        log.error("结果转换异常 ", e); // 假设能获取原始数据
                        return Mono.just(Collections.emptyList());
                    });
        });
    }
    private FlameGraph convertToVO(RtcRoomMetricsIndex rtcRoomMetricsIndex) {
        if (rtcRoomMetricsIndex == null) {
            log.warn("RtcRoomMetricsIndex 为空，无法生成火焰图");
            return null;
        }

        // 解析事件和LLM指标，处理可能为空的情况
        List<RtcRoomEvent> events = new ArrayList<>();
        if (StringUtils.isNotBlank(rtcRoomMetricsIndex.getEvents())) {
            events = JacksonUtils.readValues(rtcRoomMetricsIndex.getEvents(), RtcRoomEvent.class);
            events = events.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }

        List<RtcRoomLlmMetrics> llmMetrics = new ArrayList<>();
        if (StringUtils.isNotBlank(rtcRoomMetricsIndex.getLlmMetrics())) {
            llmMetrics = JacksonUtils.readValues(rtcRoomMetricsIndex.getLlmMetrics(), RtcRoomLlmMetrics.class);
            llmMetrics = llmMetrics.stream().filter(Objects::nonNull).collect(Collectors.toList());
        }

        // 检查是否有事件数据
        if (events.isEmpty()) {
            log.warn("没有有效的事件数据，无法生成火焰图");
            return null;
        }

        // 按轮次ID对事件进行分组，并排除轮次0
        Map<Long, List<RtcRoomEvent>> eventsByRound = events.stream()
                .filter(e -> e.getRoundId() != null && e.getRoundId() != 0) // 排除轮次0和没有轮次ID的事件
                .collect(Collectors.groupingBy(RtcRoomEvent::getRoundId));

        // 创建根节点
        long startTime = rtcRoomMetricsIndex.getSessionStartTime() != null ? rtcRoomMetricsIndex.getSessionStartTime().toEpochMilli() : 0;
        long endTime = rtcRoomMetricsIndex.getSessionEndTime() != null ? rtcRoomMetricsIndex.getSessionEndTime().toEpochMilli() : 0;
        long elapsedMs = rtcRoomMetricsIndex.getElapsedMs() != null ? rtcRoomMetricsIndex.getElapsedMs() : 0;

        // 如果没有明确的开始/结束时间，从事件中推断（排除轮次0的事件）
        if (startTime == 0 && !events.isEmpty()) {
            startTime = events.stream()
                    .filter(e -> e.getRoundId() != null && e.getRoundId() != 0)
                    .mapToLong(RtcRoomEvent::getEventTime)
                    .min()
                    .orElse(0);
        }

        if (endTime == 0 && !events.isEmpty()) {
            endTime = events.stream()
                    .filter(e -> e.getRoundId() != null && e.getRoundId() != 0)
                    .mapToLong(RtcRoomEvent::getEventTime)
                    .max()
                    .orElse(0);
        }

        // 如果排除轮次0后没有有效的开始/结束时间，则使用所有事件的时间范围
        if (startTime == 0 && !events.isEmpty()) {
            startTime = events.stream()
                    .mapToLong(RtcRoomEvent::getEventTime)
                    .min()
                    .orElse(0);
        }

        if (endTime == 0 && !events.isEmpty()) {
            endTime = events.stream()
                    .mapToLong(RtcRoomEvent::getEventTime)
                    .max()
                    .orElse(0);
        }

        if (elapsedMs == 0 && startTime > 0 && endTime > 0) {
            elapsedMs = endTime - startTime;
        }

        FlameNode root = new FlameNode("RTC通话记录", startTime, endTime, elapsedMs);

        // 按轮次ID排序
        List<Long> roundIds = new ArrayList<>(eventsByRound.keySet());
        Collections.sort(roundIds);

        // 创建一个Set来跟踪已处理的LLM指标
        Set<String> processedLlmTraceIds = new HashSet<>();

        for (Long roundId : roundIds) {
            if (roundId == null || roundId == 0) continue; // 明确跳过轮次0

            List<RtcRoomEvent> roundEvents = eventsByRound.get(roundId);
            if (roundEvents == null || roundEvents.size() <= 1) {
                continue; // 跳过只有一个或没有事件的轮次
            }

            // 按时间戳排序
            roundEvents.sort(Comparator.comparing(RtcRoomEvent::getEventTime));

            // 计算轮次的开始和结束时间
            RtcRoomEvent firstEvent = roundEvents.get(0);
            RtcRoomEvent lastEvent = roundEvents.get(roundEvents.size() - 1);
            long roundStartTime = firstEvent.getEventTime();
            long roundEndTime = lastEvent.getEventTime();
            long roundDuration = roundEndTime - roundStartTime;

            // 创建轮次节点
            String roundName = String.format("轮次 %d", roundId);
            FlameNode roundNode = new FlameNode(roundName, roundStartTime, roundEndTime, roundDuration);
            roundNode.addMetadata("round_id", roundId);
            roundNode.addMetadata("event_count", roundEvents.size());
            roundNode.setType("round");

            // 找出属于这个轮次的LLM指标（排除已处理的）并修正时间戳
            List<RtcRoomLlmMetrics> roundLlmMetrics = llmMetrics.stream()
                    .filter(llm -> llm != null && llm.getTraceId() != null && !processedLlmTraceIds.contains(llm.getTraceId()))
                    .map(this::correctLlmTimeStamps) // 修正LLM时间戳
                    .filter(llm -> isLlmInRound(llm, roundStartTime, roundEndTime))
                    .collect(Collectors.toList());

            // 创建包含事件和LLM的时间线
            createTimelineWithLlm(roundNode, roundEvents, roundLlmMetrics);

            // 将已处理的LLM指标添加到跟踪集合中
            roundLlmMetrics.forEach(llm -> {
                if (llm.getTraceId() != null) {
                    processedLlmTraceIds.add(llm.getTraceId());
                }
            });

            root.addChild(roundNode);
        }

        // 创建火焰图
        FlameGraph flameGraph = new FlameGraph("RTC通话性能分析", root, FlameGraph.Type.TRACING);

        // 添加全局元数据
        String rtcRoomId = rtcRoomMetricsIndex.getRoomId();
        if (rtcRoomId != null) {
            flameGraph.addMetadata("rtc_room_id", rtcRoomId);
        }
        flameGraph.addMetadata("total_elapsed_ms", elapsedMs);
        flameGraph.addMetadata("event_count", events.size());
        flameGraph.addMetadata("llm_metrics_count", llmMetrics.size());
        flameGraph.addMetadata("round_count", roundIds.size());
        flameGraph.addMetadata("created_at", System.currentTimeMillis());

        // 添加未使用的LLM指标数量的元数据
        int unusedLlmCount = (int) llmMetrics.stream()
                .filter(llm -> llm.getTraceId() != null)
                .filter(llm -> !processedLlmTraceIds.contains(llm.getTraceId()))
                .count();
        flameGraph.addMetadata("unused_llm_metrics_count", unusedLlmCount);

        return flameGraph;
    }


    /**
     * 修正LLM指标的时间戳，如果开始和结束时间相同或无效，则从子节点推断
     */
    private RtcRoomLlmMetrics correctLlmTimeStamps(RtcRoomLlmMetrics llm) {
        if (llm == null) return null;

        // 如果LLM的开始和结束时间相同或无效，尝试从子节点推断
        if (llm.getStartTime() == llm.getEndTime() || llm.getStartTime() <= 0 || llm.getEndTime() <= 0) {
            if (llm.getNodes() != null && !llm.getNodes().isEmpty()) {
                // 找出所有有效子节点的最早开始时间和最晚结束时间
                long earliestStart = Long.MAX_VALUE;
                long latestEnd = 0;

                for (RtcRoomLlmMetrics.Node node : llm.getNodes()) {
                    if (node != null) {
                        if (node.getStartTime() > 0 && node.getStartTime() < earliestStart) {
                            earliestStart = node.getStartTime();
                        }
                        if (node.getEndTime() > latestEnd) {
                            latestEnd = node.getEndTime();
                        }
                    }
                }

                // 如果找到有效的时间范围，则更新LLM的时间戳
                if (earliestStart != Long.MAX_VALUE && latestEnd > 0) {
                    llm.setStartTime(earliestStart);
                    llm.setEndTime(latestEnd);
                    // 如果总耗时为0，也更新它
                    if (llm.getTotalElapsedMs() == 0) {
                        llm.setTotalElapsedMs(latestEnd - earliestStart);
                    }
                    log.debug("已修正LLM时间戳: traceId={}, 开始={}, 结束={}, 耗时={}ms",
                            llm.getTraceId(), earliestStart, latestEnd, latestEnd - earliestStart);
                }
            }
        }

        return llm;
    }

    /**
     * 创建包含事件和LLM的时间线，将LLM节点插入到合适的事件之间
     */
    private void createTimelineWithLlm(FlameNode roundNode, List<RtcRoomEvent> events, List<RtcRoomLlmMetrics> llmMetrics) {
        if (events.size() < 2) return;

        // 按时间排序LLM指标
        llmMetrics.sort(Comparator.comparing(RtcRoomLlmMetrics::getStartTime));

        // 遍历事件对
        for (int i = 1; i < events.size(); i++) {
            RtcRoomEvent prevEvent = events.get(i - 1);
            RtcRoomEvent currEvent = events.get(i);

            long eventStartTime = prevEvent.getEventTime();
            long eventEndTime = currEvent.getEventTime();

            // 查找在这两个事件之间的LLM指标
            List<RtcRoomLlmMetrics> overlappingLlms = findOverlappingLlms(llmMetrics, eventStartTime, eventEndTime);

            if (overlappingLlms.isEmpty()) {
                // 如果没有LLM指标，直接添加事件节点
                addEventStageNode(roundNode, prevEvent, currEvent);
            } else {
                // 处理有LLM指标的情况
                processEventWithLlm(roundNode, prevEvent, currEvent, overlappingLlms);
            }
        }
    }

    /**
     * 查找在指定时间范围内的LLM指标
     */
    private List<RtcRoomLlmMetrics> findOverlappingLlms(List<RtcRoomLlmMetrics> llmMetrics, long startTime, long endTime) {
        return llmMetrics.stream()
                .filter(llm -> {
                    // LLM开始时间在范围内
                    boolean startInRange = llm.getStartTime() >= startTime && llm.getStartTime() < endTime;
                    // LLM结束时间在范围内
                    boolean endInRange = llm.getEndTime() > startTime && llm.getEndTime() <= endTime;
                    // LLM完全包含范围
                    boolean containsRange = llm.getStartTime() <= startTime && llm.getEndTime() >= endTime;

                    return startInRange || endInRange || containsRange;
                })
                .collect(Collectors.toList());
    }

    /**
     * 添加普通的事件阶段节点
     */
    private void addEventStageNode(FlameNode parent, RtcRoomEvent prevEvent, RtcRoomEvent currEvent) {
        if (prevEvent == null || currEvent == null) return;

        long stageDuration = currEvent.getEventTime() - prevEvent.getEventTime();

        String prevType = prevEvent.getType() != null ? prevEvent.getType().toString() : "未知";
        String currType = currEvent.getType() != null ? currEvent.getType().toString() : "未知";
        String prevStage = prevEvent.getRunStage() != null ? prevEvent.getRunStage() : "未知";
        String currStage = currEvent.getRunStage() != null ? currEvent.getRunStage() : "未知";

        String stageName = String.format("%s→%s (%s→%s)",
                prevType, currType, prevStage, currStage);

        FlameNode stageNode = new FlameNode(stageName, prevEvent.getEventTime(), currEvent.getEventTime(), stageDuration);
        stageNode.setType("stage");

        if (prevEvent.getType() != null) {
            stageNode.addMetadata("prev_event_type", prevEvent.getType());
        }
        if (currEvent.getType() != null) {
            stageNode.addMetadata("curr_event_type", currEvent.getType());
        }
        if (prevEvent.getRunStage() != null) {
            stageNode.addMetadata("prev_run_stage", prevEvent.getRunStage());
        }
        if (currEvent.getRunStage() != null) {
            stageNode.addMetadata("curr_run_stage", currEvent.getRunStage());
        }
        stageNode.addMetadata("duration_ms", stageDuration);

        parent.addChild(stageNode);
    }

    /**
     * 处理包含LLM指标的事件
     */
    private void processEventWithLlm(FlameNode parent, RtcRoomEvent prevEvent, RtcRoomEvent currEvent,
                                     List<RtcRoomLlmMetrics> llmMetrics) {
        if (prevEvent == null || currEvent == null || llmMetrics == null || llmMetrics.isEmpty()) {
            return;
        }

        long eventStartTime = prevEvent.getEventTime();
        long eventEndTime = currEvent.getEventTime();

        // 按开始时间排序LLM指标
        llmMetrics.sort(Comparator.comparing(RtcRoomLlmMetrics::getStartTime));

        String prevType = prevEvent.getType() != null ? prevEvent.getType().toString() : "未知";
        String currType = currEvent.getType() != null ? currEvent.getType().toString() : "未知";
        String prevStage = prevEvent.getRunStage() != null ? prevEvent.getRunStage() : "未知";
        String currStage = currEvent.getRunStage() != null ? currEvent.getRunStage() : "未知";

        // 检查每个LLM指标
        for (RtcRoomLlmMetrics llm : llmMetrics) {
            // 确保LLM时间戳有效
            if (llm.getStartTime() <= 0 || llm.getEndTime() <= 0 || llm.getStartTime() >= llm.getEndTime()) {
                log.warn("跳过无效的LLM指标: traceId={}, startTime={}, endTime={}",
                        llm.getTraceId(), llm.getStartTime(), llm.getEndTime());
                continue;
            }

            // 检查LLM是否在当前事件范围内
            if (llm.getEndTime() <= eventStartTime || llm.getStartTime() >= eventEndTime) {
                continue; // LLM不在当前事件范围内
            }

            // 1. 如果LLM开始时间在事件开始之后，添加事件开始到LLM开始的阶段
            if (llm.getStartTime() > eventStartTime) {
                String preStage = String.format("%s→LLM开始 (%s→LLM处理前)", prevType, prevStage);
                FlameNode preNode = new FlameNode(preStage, eventStartTime, llm.getStartTime(),
                        llm.getStartTime() - eventStartTime);
                preNode.setType("stage");
                preNode.addMetadata("prev_event_type", prevEvent.getType());
                preNode.addMetadata("prev_run_stage", prevStage);
                parent.addChild(preNode);
            }

            // 2. 添加LLM节点
            FlameNode llmNode = createLlmNode(llm);
            if (llmNode != null) {
                parent.addChild(llmNode);
            }

            // 3. 如果LLM结束时间在事件结束之前，添加LLM结束到事件结束的阶段
            if (llm.getEndTime() < eventEndTime) {
                String postStage = String.format("LLM结束→%s (LLM处理后→%s)", currType, currStage);
                FlameNode postNode = new FlameNode(postStage, llm.getEndTime(), eventEndTime,
                        eventEndTime - llm.getEndTime());
                postNode.setType("stage");
                postNode.addMetadata("curr_event_type", currEvent.getType());
                postNode.addMetadata("curr_run_stage", currStage);
                parent.addChild(postNode);
            }

            // 已处理此事件范围内的LLM，无需处理其他LLM
            return;
        }

        // 如果没有找到合适的LLM或LLM处理失败，添加普通的事件阶段节点
        addEventStageNode(parent, prevEvent, currEvent);
    }


    /**
     * 判断LLM指标是否属于指定的轮次时间范围
     */
    private boolean isLlmInRound(RtcRoomLlmMetrics llm, long roundStartTime, long roundEndTime) {
        if (llm == null || llm.getStartTime() == 0 || llm.getEndTime() == 0) {
            return false;
        }

        // 判断LLM的时间范围是否与轮次的时间范围有重叠
        return (llm.getStartTime() >= roundStartTime && llm.getStartTime() <= roundEndTime) ||
                (llm.getEndTime() >= roundStartTime && llm.getEndTime() <= roundEndTime) ||
                (llm.getStartTime() <= roundStartTime && llm.getEndTime() >= roundEndTime);
    }

    /**
     * 创建LLM节点及其子节点
     */
    private FlameNode createLlmNode(RtcRoomLlmMetrics llm) {
        if (llm == null || llm.getStartTime() <= 0 || llm.getEndTime() <= 0 || llm.getStartTime() >= llm.getEndTime()) {
            return null;
        }

        // 计算实际持续时间
        long actualDuration = llm.getEndTime() - llm.getStartTime();

        FlameNode llmNode = new FlameNode("LLM处理", llm.getStartTime(), llm.getEndTime(),
                llm.getTotalElapsedMs() > 0 ? llm.getTotalElapsedMs() : actualDuration);
        llmNode.setType("llm");

        if (llm.getTraceId() != null) {
            llmNode.addMetadata("trace_id", llm.getTraceId());
        }
        if (llm.getConversationId() != null) {
            llmNode.addMetadata("conversation_id", llm.getConversationId());
        }
        if (llm.getClientReqId() != null) {
            llmNode.addMetadata("client_req_id", llm.getClientReqId());
        }

        llmNode.addMetadata("first_token_time", llm.getFirstTokenTime());
        llmNode.addMetadata("first_token_time_ms", llm.getFirstTokenTime()-llm.getStartTime());
        // 添加LLM子节点，先按开始时间排序
        if (llm.getNodes() != null && !llm.getNodes().isEmpty()) {
            // 创建一个新的列表，以便可以排序
            List<RtcRoomLlmMetrics.Node> sortedNodes = new ArrayList<>(llm.getNodes());

            // 按开始时间排序，处理可能的空值
            sortedNodes.sort(Comparator.comparing(
                    node -> node != null ? (node.getStartTime() > 0 ? node.getStartTime() : Long.MAX_VALUE) : Long.MAX_VALUE
            ));

            // 添加排序后的节点
            for (RtcRoomLlmMetrics.Node node : sortedNodes) {
                if (node != null) {
                    addLlmNodeRecursively(llmNode, node);
                }
            }
        }

        return llmNode;
    }

    /**
     * 递归添加LLM节点及其子节点
     */
    private void addLlmNodeRecursively(FlameNode parent, RtcRoomLlmMetrics.Node llmNode) {
        if (parent == null || llmNode == null || llmNode.getName() == null) {
            return;
        }

        // 确保时间戳有效
        long startTime = llmNode.getStartTime() > 0 ? llmNode.getStartTime() : 0;
        long endTime = llmNode.getEndTime() > 0 ? llmNode.getEndTime() : 0;
        long elapsedMs = llmNode.getElapsedMs() > 0 ? llmNode.getElapsedMs() : 0;

        // 如果时间信息不完整，尝试计算
        if (startTime > 0 && endTime > 0 && elapsedMs == 0) {
            elapsedMs = endTime - startTime;
        } else if (startTime > 0 && elapsedMs > 0 && endTime == 0) {
            endTime = startTime + elapsedMs;
        } else if (endTime > 0 && elapsedMs > 0 && startTime == 0) {
            startTime = endTime - elapsedMs;
        }

        FlameNode node = new FlameNode(
                llmNode.getName(),
                startTime,
                endTime,
                elapsedMs
        );
        node.setType("llm_component");

        // 处理子节点，先按开始时间排序
        if (llmNode.getSubNodes() != null && !llmNode.getSubNodes().isEmpty()) {
            // 创建一个新的列表，以便可以排序
            List<RtcRoomLlmMetrics.Node> sortedSubNodes = new ArrayList<>(llmNode.getSubNodes());

            // 按开始时间排序，处理可能的空值
            sortedSubNodes.sort(Comparator.comparing(
                    subNode -> subNode != null ? (subNode.getStartTime() > 0 ? subNode.getStartTime() : Long.MAX_VALUE) : Long.MAX_VALUE
            ));

            // 添加排序后的子节点
            for (RtcRoomLlmMetrics.Node subNode : sortedSubNodes) {
                if (subNode != null) {
                    addLlmNodeRecursively(node, subNode);
                }
            }
        }

        parent.addChild(node);
    }



    private Query buildSearchQuery(RtcRoomInfoQuery logQuery) {
        BoolQuery.Builder boolQueryBuilder = new BoolQuery.Builder();

        buildKeywordCondition(logQuery, boolQueryBuilder);
        buildTimeRangeCondition(logQuery, boolQueryBuilder);
        addFilterQueries(logQuery, boolQueryBuilder);

        return NativeQuery.builder()
                .withQuery(q -> q.bool(boolQueryBuilder.build()))
                .withSort(s->s.field(f->f.field(RtcRoomMetricsIndexFieldEnum.SESSION_START_TIME.getColumnName())
                        .order(SortOrder.Desc)))
                .build();
    }

    // 构建关键字查询
    private void buildKeywordCondition(RtcRoomInfoQuery logQuery, BoolQuery.Builder boolQuery) {
        if (StringUtils.isBlank(logQuery.getRtcRoomId())) return;

        boolQuery.should(s -> s.multiMatch(m -> m
                .fields(DEFAULT_SEARCH_FIELDS)
                .query(logQuery.getRtcRoomId())
                .type(TextQueryType.BestFields)
                .operator(Operator.Or)
                .fuzziness("1")
                .minimumShouldMatch("1")
        ));
    }


    // 构建时间范围查询
    private void buildTimeRangeCondition(RtcRoomInfoQuery logQuery, BoolQuery.Builder boolQuery) {
        // 1. 前置校验：如果时间范围参数均为空，直接返回
        if (logQuery.getStartTimeBegin() == null && logQuery.getStartTimeEnd() == null) {
            return;
        }

        // 2. 构建时间范围查询
        boolQuery.must(q -> q.range(RangeQuery.of(r -> r.date(DateRangeQuery.of(d ->{
                    d.field(RtcRoomMetricsIndexFieldEnum.SESSION_START_TIME.getColumnName())
                    .format(DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS) // 必须与字段存储格式匹配
                    .timeZone(TIME_ZONE); // 明确指定时区

            // 处理开始时间
            if (logQuery.getStartTimeBegin() != null) {
                d.gte(logQuery.getStartTimeBegin());
            }

            // 处理结束时间
            if (logQuery.getStartTimeEnd() != null) {
                d.lte(logQuery.getStartTimeEnd());
            }

            return d;
        }
        )))));
    }



    // 批量添加字段过滤条件
    private void addFilterQueries(RtcRoomInfoQuery query, BoolQuery.Builder boolQueryBuilder) {
        // 定义字段与查询值的映射关系
        List<Pair<String, String>> filterConditions = Arrays.asList(
                Pair.of(RtcRoomMetricsIndexFieldEnum.ROOM_ID.getColumnName(), query.getRtcRoomId())
                );

        // 遍历并添加过滤条件
        filterConditions.forEach(pair -> addFilterQuery(boolQueryBuilder, pair.getLeft(), pair.getRight()));
    }

    // 添加单个过滤条件
    private void addFilterQuery(BoolQuery.Builder builder, String field, String value) {
        if (StringUtils.isNotBlank(value)) {
            builder.filter(f -> f.term(t -> t.field(field).value(value)));
        }
    }


    private Mono<List<RtcRoomMetricsIndex>> executeSearch(Query query, Stopwatch stopwatch) {
        return reactiveElasticsearchOperations.search(query, RtcRoomMetricsIndex.class)
                .map(SearchHit::getContent)
                .collectList()
                .defaultIfEmpty(Collections.emptyList())
                .doOnSuccess(results -> logSearchMetrics(results.size(), stopwatch))
                .onErrorResume(throwable -> {
                    log.error("Search execution error: {}", throwable.getMessage(), throwable);
                    return Mono.just(Collections.emptyList());
                });
    }

    private void logSearchMetrics(int resultCount, Stopwatch stopwatch) {
        stopwatch.stop();
        log.info("ES search completed: {} results found in {} ms", resultCount, stopwatch.elapsed(TimeUnit.MILLISECONDS));
    }

}