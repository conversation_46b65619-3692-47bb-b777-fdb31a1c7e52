package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.date.SystemClock;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.channel.consts.message.ServerMessageData;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/4/1
 * @Description:    取消通话
 */
@Component
@RequiredArgsConstructor
public class CancelHandler extends AbstractCommandClientMessageHandler {

    private final RtcRoomCallService rtcRoomCallService;

    @Override
    Mono<Boolean> doHandle(ClientMessageData messageData) {

        String deviceBingRoomKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, messageData.getDeviceId());

        return reactiveStringRedisTemplate.opsForValue().get(deviceBingRoomKey)
                .flatMap(roomId -> connectionService.getDeviceInfo(messageData.getDeviceId(), messageData.getClientType())
                        .flatMap(deviceInfo -> {
                            String callKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, deviceInfo.getRtcUserId());
                            RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                                    .rtcRoomId(roomId)
                                    .rtcUserId(deviceInfo.getRtcUserId())
                                    .clientType(messageData.getClientType())
                                    .username(deviceInfo.getUsername())
                                    .reason("user cancel")
                                    .build();
                            return rtcRoomCallService.cancel(rtcRoomCallDTO)
                                    .then(
                                            reactiveStringRedisTemplate.opsForSet().members(callKey)
                                                    .collectList()
                                                    .flatMap(userIds -> Mono.when(
                                                            sendCancel(userIds, roomId),
                                                            removeUserIds(roomId, deviceInfo.getRtcUserId(), userIds),
                                                            reactiveStringRedisTemplate.delete(deviceBingRoomKey)
                                                    ).thenReturn(Boolean.TRUE))
                                    );

                        })
                );
    }

    @Override
    public String messageType() {
        return Instructions.CANCEL.getCode();
    }

    /**
     * 移除状态数据
     */
    private Mono<Void> removeUserIds(String roomId, String callOriginalUserId, List<String> userIds) {

        String onlineRoomUserKey = String.format(RedisKeyConstant.ONLINE_ROOM_USER_KEY, roomId);

        String fromCallToHumanKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, callOriginalUserId);

        userIds.add(callOriginalUserId);

        return Mono.when(
                reactiveStringRedisTemplate.opsForSet().delete(onlineRoomUserKey),
                reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_BUSY_USER_KEY, userIds.toArray(new Object[0])),
                reactiveStringRedisTemplate.opsForSet().delete(fromCallToHumanKey)
        );
    }

    /**
     * 发送 通话被拒绝 指令
     */
    private Mono<Void> sendCancel(List<String> userIds, String roomId) {

        ServerMessageData serverMessageData = ServerMessageData.createRejectMessage(roomId);
        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand(Instructions.CLOSE_RINGING.getCode())
                .setTimestamp(String.valueOf(SystemClock.now()))
                .setData(serverMessageData);

        return sendDevice(userIds, callbackMessage);

    }

}
