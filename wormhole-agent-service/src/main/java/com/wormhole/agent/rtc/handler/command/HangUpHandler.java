package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.channel.consts.message.ServerMessageData;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.HashSet;
import java.util.Set;

/**
 * @author: joker.liu
 * @date: 2025/3/12
 * @Description:    挂断
 */
@Component
@RequiredArgsConstructor
public class HangUp<PERSON>and<PERSON> extends AbstractCommandClientMessageHandler {

    private final RtcRoomCallService rtcRoomCallService;

    @Override
    public String messageType() {
        return Instructions.HANG_UP.getCode();
    }

    @Override
    public Mono<Boolean> doHandle(ClientMessageData messageData) {
        // Validate device ID
        String deviceId = messageData.getDeviceId();
        if (StrUtil.isBlank(deviceId)) {
            return Mono.just(false);
        }

        // Retrieve device information
        return connectionService.getDeviceInfo(deviceId, messageData.getClientType())
                .flatMap(info -> {
                    String userId = info.getRtcUserId();
                    String bindingRoomKey = String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId);

                    // Retrieve room binding information
                    return reactiveStringRedisTemplate.opsForValue().get(bindingRoomKey)
                            .flatMap(value -> {
                                if (StrUtil.isBlank(value)) {
                                    return deleteRoomBindingByUserId(userId)
                                            .thenReturn(false);
                                }

                                // Parse room and call user information
                                String[] roomInfo = value.split(StrUtil.COLON);
                                String rtcRoomId = roomInfo[0];
                                String callUserId = roomInfo[1];
                                RtcRoomCallDTO rtcRoomCallDTO = convert(rtcRoomId, userId, messageData, info);
                                // Clean up voice chat and send hang-up message
                                return collectCallUserIds(rtcRoomId, callUserId)
                                        .flatMap(userIds -> sendHangUpMessage(rtcRoomId, userIds))
                                        .then(rtcRoomCallService.calledNormalOver(rtcRoomCallDTO));

                            })
                            .switchIfEmpty(Mono.fromCallable(() -> {
                                log.info("User not bound to a room");
                                return false;
                            }));
                })
                .switchIfEmpty(Mono.defer(() -> deleteRoomBindingByDeviceId(deviceId).thenReturn(false)));
    }

    public RtcRoomCallDTO convert(String rtcRoomId, String userId, ClientMessageData messageData, DeviceInfoResp info) {

        return RtcRoomCallDTO.builder()
                .rtcRoomId(rtcRoomId)
                .timestamp(System.currentTimeMillis())
                .rtcUserId(userId)
                .username(info.getUsername())
                .clientType(messageData.getClientType())
                .reason("normal")
                .build();

    }

    /**
     * Collect all user IDs involved in the call
     * @param roomId Current room ID
     * @param callUserId User who initiated the call
     * @return Mono of user IDs set
     */
    private Mono<Set<String>> collectCallUserIds(String roomId, String callUserId) {
        String inRoomUserKey = String.format(RedisKeyConstant.ONLINE_ROOM_USER_KEY, roomId);
        String callUserKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, callUserId);

        return Mono.zip(
                reactiveStringRedisTemplate.opsForSet().members(inRoomUserKey).collectList(),
                reactiveStringRedisTemplate.opsForSet().members(callUserKey).collectList()
        ).flatMap(tuple -> {
            Set<String> userIds = new HashSet<>(tuple.getT1());
            userIds.addAll(tuple.getT2());
            return reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_BUSY_USER_KEY, userIds.toArray(new Object[0]))
                    .then(deleteRoomBindingByUserId(callUserId))
                    .thenReturn(userIds);
        });
    }

    private Mono<Void> deleteRoomBindingByUserId(String userId) {
        return connectionService.getDeviceInfoByUserId(userId)
                .flatMap(deviceInfo -> deleteRoomBindingByDeviceId(deviceInfo.getDeviceId()));
    }

    private Mono<Void> deleteRoomBindingByDeviceId(String deviceId) {
        String roomBindingKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, deviceId);
        return reactiveStringRedisTemplate.delete(roomBindingKey).then();
    }

    /**
     * Send hang-up message to all involved users
     * @param roomId Room ID
     * @param userIds Set of user IDs to send message to
     * @return Mono<Void> representing the completion of sending messages
     */
    private Mono<Void> sendHangUpMessage(String roomId, Set<String> userIds) {
        ServerMessageData serverMessageData = new ServerMessageData();
        serverMessageData.setRoomId(roomId);

        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand(Instructions.SERVER_HANG_UP.getCode())
                .setTimestamp(String.valueOf(System.currentTimeMillis()))
                .setData(serverMessageData);

        return sendDevice(userIds, callbackMessage);
    }

}
