package com.wormhole.agent.rtc.handler.command;

import com.wormhole.common.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: joker.liu
 * @date: 2025/3/11
 * @Description:
 */
@Component
public class CommandHandlerFactory {

    private Map<String, CommandHandler> messageHandlerMap;

    public CommandHandler getEventHandler(String messageType) {
        return Optional.ofNullable(messageHandlerMap.get(messageType)).orElseThrow(() -> new BusinessException("","No message handler found for message type: " + messageType));
    }

    @Autowired
    public void setMessageHandlerMap(List<CommandHandler> messageHandlers) {
        messageHandlerMap = messageHandlers.stream()
                .collect(Collectors.toMap(CommandHandler::messageType, e -> e));
    }

}
