package com.wormhole.agent.rtc.handler.timeout;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: joker.liu
 * @date: 2025/3/10
 * @Description:
 */
@Component
public class TimeoutEventHandleFactory {

    private Map<String, TimeoutMessageHandler> timeoutMessageHandlerMap;

    public TimeoutMessageHandler getEventHandler(String eventType) {
        return Optional.ofNullable(timeoutMessageHandlerMap.get(eventType))
                .orElseThrow(() -> new IllegalArgumentException("No event handler found for event type: " + eventType));
    }

    @Autowired
    public void setEventHandlerMap(List<TimeoutMessageHandler> eventHandlers) {
        timeoutMessageHandlerMap = eventHandlers.stream()
                .collect(Collectors.toMap(TimeoutMessageHandler::messageType, e -> e));
    }

}
