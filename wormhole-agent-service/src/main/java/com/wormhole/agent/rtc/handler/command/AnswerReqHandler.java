package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Preconditions;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.agent.workflow.util.LockUtil;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.channel.consts.message.ServerMessageData;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonReactiveClient;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/3/11
 * @Description:    接听事件
 */
@Component
@RequiredArgsConstructor
public class AnswerReqHandler extends AbstractCommandClientMessageHandler {

    private final RedissonReactiveClient redissonReactiveClient;
    private final RtcRoomCallService rtcRoomCallService;

    @Override
    public String messageType() {
        return Instructions.ANSWER_REQ.getCode();
    }

    @Override
    public Mono<Boolean> doHandle(ClientMessageData messageData) {
        Preconditions.checkNotNull(messageData.getDeviceId(), "device id must not null");

        final String answerUserId = messageData.getUserId();
        final String roomId = messageData.getRoomId();
        final String bindingRoomKey = String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, answerUserId);

        String lockKey = LockUtil.lockName(String.format(RedisKeyConstant.REDISSON_ANSWER_ROOM_KEY, roomId));

        return LockUtil.withMonoLock(lockKey, 2000L, 0,
                redissonReactiveClient, () -> execute(answerUserId, bindingRoomKey, messageData));
    }

    private Mono<Boolean> execute(String answerUserId, String bindingRoomKey, ClientMessageData messageData) {
        return processAnswer(answerUserId, bindingRoomKey, messageData)
                .onErrorResume(error -> {
                    log.error("Answer failure for user: {}", answerUserId, error);
                    if (error instanceof BusinessException) {
                        return Mono.error(error);
                    }
                    return Mono.error(new BusinessException("FRONT-ERROR", "Answer failure"));
                });
    }

    private Mono<Boolean> processAnswer(String answerUserId, String bindingRoomKey, ClientMessageData messageData) {
        log.info("Process answer for user: {} bindingRoomKey:{} ", answerUserId, bindingRoomKey);
        return reactiveStringRedisTemplate.opsForValue().get(bindingRoomKey)
                .flatMap(info -> {
                    if (StrUtil.isBlank(info)) {
                        return Mono.just(false);
                    }

                    String[] split = info.split(StrUtil.COLON);
                    String roomId = split[0];
                    String callerUserId = split[1];
                    String callKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, callerUserId);
                    log.info("Process answer callKey: {} info: {}", callKey, info);
                    return rtcRoomCallService.getOnlineRoomCallInfo(roomId)
                            .flatMap(roomCall -> {
                                if (roomCall.getHasAiParticipant() != 1) {
                                    return handleCallAcceptance(roomId, callKey, answerUserId, callerUserId, messageData);
                                }
                                return rtcRoomCallService.closeAgent(roomCall)
                                        .then(handleCallAcceptance(roomId, callKey, answerUserId, callerUserId, messageData));
                            });
                })
                .switchIfEmpty(Mono.defer(() ->
                        Mono.error(new BusinessException("FRONT-ERROR", "No binding room information found"))
                ));
    }

    private Mono<Boolean> handleCallAcceptance(String roomId, String callKey,
                                               String answerUserId, String callerUserId, ClientMessageData messageData) {
        return reactiveStringRedisTemplate.opsForSet().members(callKey)
                .collectList()
                .flatMap(userIds -> {
                    log.info("handleCallAcceptance: {}", userIds);
                    List<String> otherUsers = userIds.stream()
                            .filter(userId -> !userId.equals(answerUserId) && !userId.equals(callerUserId))
                            .toList();

                    return closeAnswer(roomId, callKey, otherUsers)
                            .then(openAnswer(roomId, answerUserId, callerUserId, messageData))
                            // .then(roomChatService.cleanupVoiceChat(roomId))
                            .thenReturn(true);
                });
    }

    /**
     * 关闭其他用户接听
     * @param roomId        接听信息
     * @param userIds       用户信息
     */
    private Mono<Void> closeAnswer(String roomId,  String callKey, List<String> userIds) {
        if (CollectionUtil.isEmpty(userIds)) {
            return Mono.empty();
        }

        CallbackMessage callbackMessage = new CallbackMessage();
        ServerMessageData serverMessageData = new ServerMessageData();
        serverMessageData.setRoomId(roomId);

        callbackMessage.setCommand(Instructions.SERVER_HANG_UP.getCode())
                .setTimestamp(String.valueOf(System.currentTimeMillis()))
                .setData(serverMessageData);

        List<String> bindingRoomKeys = userIds.stream()
                .map(userId -> String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId))
                .toList();

        log.info("closeAnswer: {}", bindingRoomKeys);

        return Mono.when(
                        reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_BUSY_USER_KEY, userIds.toArray(new Object[0])),
                        reactiveStringRedisTemplate.opsForSet().remove(callKey, userIds.toArray(new Object[0])),
                        deleteDeviceInfo(bindingRoomKeys),
                        sendDevice(userIds, callbackMessage)
        );
    }

    private Mono<Void> deleteDeviceInfo(List<String> bindingRoomKeys) {
        if (CollectionUtil.isEmpty(bindingRoomKeys)) {
            return Mono.empty();
        }
        return reactiveStringRedisTemplate.delete(bindingRoomKeys.toArray(new String[0])).then();
    }

    private Mono<Void> openAnswer(String roomId, String answerUserId, String callerUserId, ClientMessageData messageData) {
        List<String> userIds = List.of(answerUserId, callerUserId);
        return rtcRoomCallService.getOnlineRoomCallInfo(roomId)
                .flatMap(rtcRoomCallEntity -> rtcHttpHelper.generateToken(roomId, userIds)
                        .flatMap(tokenMap -> Mono.when(
                                sendAnswerResponse(roomId, callerUserId, tokenMap.get(callerUserId), rtcRoomCallEntity.getHasAiParticipant() != 1),
                                sendCreateRoomReady(roomId, answerUserId, tokenMap.get(answerUserId), rtcRoomCallEntity.getHasAiParticipant() == 1),
                                userAccept(messageData, roomId)
                        )));
    }

    public Mono<Void> userAccept(ClientMessageData messageData, String roomId) {
        return connectionService.getDeviceInfo(messageData.getDeviceId(), messageData.getClientType())
                .flatMap(deviceInfo -> {
                    String username = deviceInfo.getUsername();
                    RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                            .rtcRoomId(roomId)
                            .rtcUserId(deviceInfo.getRtcUserId())
                            .username(username)
                            .timestamp(System.currentTimeMillis())
                            .clientType(messageData.getClientType())
                            .build();
                    return rtcRoomCallService.accept(rtcRoomCallDTO)
                            .then();
                });
    }

    private Mono<Void> sendAnswerResponse(String roomId, String userId, String token, Boolean isCreateRoom) {
        CallbackMessage callbackMessage = new CallbackMessage();
        ServerMessageData serverMessageData = new ServerMessageData();
        serverMessageData.setRoomId(roomId);
        serverMessageData.setToken(token);
        serverMessageData.setCreateRoom(isCreateRoom);

        callbackMessage.setCommand(Instructions.ANSWER_RES.getCode())
                .setTimestamp(String.valueOf(System.currentTimeMillis()))
                .setData(serverMessageData);

        return sendDevice(userId, callbackMessage);
    }

    private Mono<Void> sendCreateRoomReady(String roomId, String userId, String token, Boolean send) {
        if (!send) {
            return Mono.empty();
        }
        CallbackMessage callbackMessage = new CallbackMessage();
        ServerMessageData serverMessageData = new ServerMessageData();
        serverMessageData.setRoomId(roomId);
        serverMessageData.setToken(token);

        callbackMessage.setCommand(Instructions.CALL_ROOM_READY_FOR_USER.getCode())
                .setTimestamp(String.valueOf(System.currentTimeMillis()))
                .setData(serverMessageData);

        return sendDevice(userId, callbackMessage);
    }

}