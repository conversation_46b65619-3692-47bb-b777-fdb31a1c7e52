package com.wormhole.agent.rtc.handler.event;

import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.service.ConnectionService;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.channel.consts.enums.EventType;
import com.wormhole.channel.consts.event.UserJoinRoomEvent;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/10
 * @Description: 用户加入房间
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RoomJoinHandler extends AbstractEventHandler<UserJoinRoomEvent> {

    private final RtcRoomCallService rtcRoomCallService;

    private final ConnectionService connectionService;

    @Override
    public String messageType() {
        return EventType.USER_JOIN_ROOM.getEventType();
    }

    @Override
    UserJoinRoomEvent convert(Object message) {
        return JacksonUtils.convertValue(message, UserJoinRoomEvent.class);
    }

    @Override
    public Mono<Boolean> doHandle(UserJoinRoomEvent event) {
        log.info("user join room info:{}", JacksonUtils.writeValueAsString(event));
        return Mono.when(
                        userStatusMaintenance(event),
                        bindingUserRoom(event.getUserId(), event.getRoomId())
                ).then(updateRoomCallInfo(event));
    }

    private Mono<Boolean> userStatusMaintenance(UserJoinRoomEvent event) {
        if (event.getUserId().startsWith("chat") || event.getUserId().startsWith("stream")) {
            return Mono.just(true);
        }
        String roomOnlineUserKey = String.format(RedisKeyConstant.ONLINE_ROOM_USER_KEY, event.getRoomId());
        return Mono.when(
                reactiveStringRedisTemplate.opsForSet().add(roomOnlineUserKey, event.getUserId()),
                reactiveStringRedisTemplate.opsForSet().add(RedisKeyConstant.ONLINE_BUSY_USER_KEY, event.getUserId())
        ).thenReturn(true);
    }

    private Mono<Boolean> updateRoomCallInfo(UserJoinRoomEvent event) {
        log.info("Processing user join room event: {}", JacksonUtils.writeValueAsString(event));

        RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                .rtcRoomId(event.getRoomId())
                .agentId(event.getUserId())
                .rtcUserId(event.getUserId())
                .timestamp(event.getTimestamp())
                .build();

        if (isAgentJoinRoom(event.getUserId())) {
            return rtcRoomCallService.calledAgentAccept(rtcRoomCallDTO);
        }

        return connectionService.getDeviceInfoByUserId(event.getUserId())
                .flatMap(deviceInfoResp -> {
                    rtcRoomCallDTO.setUsername(deviceInfoResp.getUsername());
                    return getClientType(deviceInfoResp.getDeviceId())
                            .flatMap(clientType -> {
                                rtcRoomCallDTO.setClientType(clientType);
                                return rtcRoomCallService.accept(rtcRoomCallDTO);
                            });
                });
    }

    private Mono<Void> bindingUserRoom(String rtcUserId, String roomId) {
        String callUserKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, rtcUserId);
        String bindingKey = String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, rtcUserId);

        return reactiveStringRedisTemplate.hasKey(bindingKey)
                .flatMap(exist -> {
                    if (exist) {
                        return Mono.empty();
                    }
                    return reactiveStringRedisTemplate.opsForSet().add(callUserKey,  roomId + StrUtil.COLON + roomId)
                            .then();
                });

    }

    private Boolean isAgentJoinRoom(String userId) {
        return userId.startsWith("chat");
    }

}
