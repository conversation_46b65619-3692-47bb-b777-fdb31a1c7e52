package com.wormhole.agent.rtc.handler.timeout;

import cn.hutool.core.date.DateUtil;
import com.wormhole.agent.rtc.config.RtcVoiceChatProperties;
import com.wormhole.agent.rtc.consts.CallTimeoutEnums;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.mq.producer.ReactiveMessageSender;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/5/21
 * @Description:
 */
@Component
@RequiredArgsConstructor
public class CallAiChatHandle extends AbstractCallTimeoutHandler{

    private final ReactiveMessageSender reactiveMessageSender;

    private final RtcVoiceChatProperties rtcVoiceChatProperties;


    @Value("${rocket.topic.call-overtime-delay-message:call_overtime_delay_message}")
    public String CALL_OVERTIME_DELAY_MESSAGE;

    protected Mono<Boolean> doHandle(CallOvertimeMessage message) {
        long epochSecond = Instant.now().getEpochSecond();
        String roomId = message.getRtcRoomId();

        // 嵌入式 Lua 脚本内容
        String luaScript =
                "local key = KEYS[1]\n" +
                        "local member = ARGV[1]\n" +
                        "local newScore = tonumber(ARGV[2])\n" +
                        "local currentScore = redis.call('ZSCORE', key, member)\n" +
                        "if currentScore == nil or newScore > tonumber(currentScore) then\n" +
                        "    redis.call('ZADD', key, newScore, member)\n" +
                        "    return 1 -- 表示新增或更新成功\n" +
                        "else\n" +
                        "    return 0 -- 表示未更新 (新分数不大于旧分数或相等)\n" +
                        "end";

        // KEYS 参数列表
        List<String> keys = Collections.singletonList(RedisKeyConstant.RTC_ROOM_ID_ACTIVE_KEY);
        // ARGV 参数列表
        List<String> args = Arrays.asList(roomId, String.valueOf(epochSecond));

        return Mono.when(
                sendDelayMessage(roomId),
                // 使用 reactiveStringRedisTemplate 的 execute 方法执行字符串形式的 Lua 脚本
                // 需要将其包装成一个 DefaultRedisScript
                reactiveStringRedisTemplate.execute(
                        new DefaultRedisScript<>(luaScript, Long.class), // 指定返回类型
                        keys,
                        args
                ).doOnNext(result -> {
                    log.info("Embedded Lua script executed for roomId: {} result: {} epochSecond: {}", roomId, result, epochSecond);
                })
        ).thenReturn(Boolean.TRUE);
    }

    private Mono<Boolean> sendDelayMessage(String rtcRoomId) {
        MessageBody messageBody = new MessageBody();
        messageBody.setAction(CallTimeoutEnums.CALL_RESPONSE_TIMEOUT.getType());
        messageBody.setTimestamp(DateUtil.current() + "");

        CallOvertimeMessage callOvertimeMessage = new CallOvertimeMessage();
        callOvertimeMessage.setRtcRoomId(rtcRoomId);
        callOvertimeMessage.setTimeout(rtcVoiceChatProperties.getServerTimeout());
        messageBody.setData(callOvertimeMessage);
        return reactiveMessageSender.sendDelayMessage(CALL_OVERTIME_DELAY_MESSAGE, messageBody, rtcVoiceChatProperties.getServerCallTimeoutDelayLevel())
                .doOnError(e -> log.error("Failed to send subtitle message", e))
                .onErrorResume(e -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "Failed to send subtitle message")))
                .thenReturn(true);
    }

    @Override
    protected String timeoutCommand() {
        return Instructions.CALL_TIMEOUT.getCode();
    }

    @Override
    public String messageType() {
        return CallTimeoutEnums.CALL_AI_CHAT_DEAL.getType();
    }
}
