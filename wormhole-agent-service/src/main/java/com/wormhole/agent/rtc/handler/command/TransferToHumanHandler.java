package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.consts.CallTimeoutEnums;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.agent.util.RtcRoomParseUtil;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.channel.consts.message.ServerMessageData;
import com.wormhole.common.constant.HeaderConstant;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.req.CreateTicketSimpleReq;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.utils.DeviceInitRtcUtil;
import com.wormhole.mq.producer.ReactiveMessageSender;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: joker.liu
 * @date: 2025/3/11
 * @Description:    转接人工的消息回调
 */
@Component
@RequiredArgsConstructor
public class TransferToHumanHandler extends AbstractCommandClientMessageHandler {

    private final RtcRoomCallService rtcRoomCallService;

    private final ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    private final ReactiveMessageSender reactiveMessageSender;

    private final HotelDsApiClient hotelDsApiClient;

    @Value("${rocket.topic.call-overtime-delay-message:call_overtime_delay_message}")
    public String CALL_OVERTIME_DELAY_MESSAGE;


    @Override
    public String messageType() {
        return Instructions.TRANSFER_TO_HUMAN.getCode();
    }

    @Override
    public boolean support(String messageType) {
        return messageType().equals(messageType);
    }

    @Override
    public Mono<Boolean> doHandle(ClientMessageData messageData) {
        log.info("TransferToHumanHandler request: {}", JacksonUtils.writeValueAsString(messageData));

        Long timestamp = StringUtils.isBlank(messageData.getTimestamp()) ? System.currentTimeMillis() : Long.parseLong(messageData.getTimestamp());

        return connectionService.getDeviceInfo(messageData.getDeviceId(), messageData.getClientType())
                .flatMap(deviceInfo -> {
                    String deviceBingRoomKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, deviceInfo.getDeviceId());

                    return reactiveStringRedisTemplate.opsForValue().get(deviceBingRoomKey)
                            .flatMap(roomIdValue -> doHandle(messageData, deviceInfo, roomIdValue, timestamp))
                            .switchIfEmpty(Mono.defer(() -> {
                                String roomId = deviceInfo.getHotelCode() + StrUtil.UNDERLINE + deviceInfo.getPositionCode()
                                        + StrUtil.UNDERLINE + deviceInfo.getDeviceId() + StrUtil.UNDERLINE + timestamp;
                                return doHandle(messageData, deviceInfo, roomId, timestamp);
                            }));
                    }
                );
    }

//    @NotNull
//    private Mono<Boolean> createTicket(ClientMessageData messageData, String roomId,DeviceInfoResp deviceInfo) {
//        CreateTicketSimpleReq createTicketSimpleReq = CreateTicketSimpleReq
//                .builder()
//                .rtcRoomId(roomId)
//                .deviceId(deviceInfo.getDeviceId())
//                .positionCode(deviceInfo.getPositionCode())
//                .hotelCode(deviceInfo.getHotelCode())
//                .build();
//        HashMap<String, String> headerMap = new HashMap<>();
//        headerMap.put(HeaderConstant.CLIENT_TYPE, Objects.isNull(messageData.getClientType()) ? null : String.valueOf(messageData.getClientType()));
//        headerMap.put(HeaderConstant.USER_ID, DeviceInitRtcUtil.getRtcUserId(deviceInfo.getDeviceType(), deviceInfo.getDeviceId()));
//        headerMap.put(HeaderConstant.HOTEL_CODE, deviceInfo.getHotelCode());
//        return hotelDsApiClient.createSosTicket(createTicketSimpleReq,headerMap)
//                .doOnError(e -> log.error("更新通话结束时间异常", e))
//                .doOnSuccess(aVoid -> log.info("更新通话结束时间成功"))
//                .map(Result::getData)
//                .onErrorResume(e -> Mono.just(false));
//    }

    private Mono<Boolean> doHandle(ClientMessageData messageData, DeviceInfoResp deviceInfo, String roomId, Long timestamp) {

        String deviceBingRoomKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, messageData.getDeviceId());

        RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                .rtcRoomId(roomId).timestamp(timestamp).clientType(messageData.getClientType()).language(messageData.getLanguage()).hasAiParticipant(0)
                .build();

        return rtcRoomCallService.create(rtcRoomCallDTO)
                .doOnNext(roomCallEntity -> log.info("create room call info success:{}", JacksonUtils.writeValueAsString(roomCallEntity)))
                .then(reactiveStringRedisTemplate.opsForValue().set(deviceBingRoomKey, roomId))
                .then(Mono.defer(() -> {
                    messageData.setUserId(deviceInfo.getRtcUserId());
                    messageData.setRoomId(roomId);
                    messageData.setRoomNo(deviceInfo.getPositionCode());

                    String targetDeviceId = messageData.getTargetDeviceId();

                    if (StringUtils.isNotBlank(targetDeviceId)) {
                        return connectionService.deviceInitInfo(targetDeviceId, null)
                                .flatMap(deviceInfoResp -> {
                                    return rtcRoomCallService.transfer(roomId, deviceInfo.getRtcUserId(), deviceInfo.getUsername(), deviceInfoResp.getRtcUserId(), deviceInfoResp.getUsername(), 2)
                                            .then(processTransferToHuman(messageData, deviceInfo, roomId));
                                });

                    }
                    return rtcRoomCallService.transfer(roomId, deviceInfo.getRtcUserId(), deviceInfo.getUsername(), null, null, null)
                            .then(processTransferToHuman(messageData, deviceInfo, roomId));

                }));
    }

    /**
     * Process the transfer to human request
     * @param messageData Client message data
     * @param deviceInfo Device information
     * @return Mono of transfer operation result
     */
    private Mono<Boolean> processTransferToHuman(ClientMessageData messageData, DeviceInfoResp deviceInfo, String roomId) {
        return determineTargetUsers(messageData, deviceInfo)
                .flatMap(targetUsers -> initializeCall(messageData, targetUsers, deviceInfo, roomId))
                .thenReturn(true);
    }

    /**
     * Determine target users for transfer
     * @param messageData Client message data
     * @param deviceInfo Device information
     * @return Mono of target user IDs
     */
    private Mono<List<String>> determineTargetUsers(ClientMessageData messageData, DeviceInfoResp deviceInfo) {
        if (DeviceTypeEnum.judgeDeviceFromUser(deviceInfo.getDeviceType())) {
            return getUserIds(deviceInfo.getHotelCode(), DeviceTypeEnum.FRONT.getCode(), null);
        }

        if (StringUtils.isBlank(messageData.getTargetDeviceId())) {
            return Mono.error(new BusinessException("DEVICE-NOT-FOUND", "Target device must not be null"));
        }

        return connectionService.getDeviceInfo(messageData.getTargetDeviceId(), messageData.getClientType())
                .map(targetDevice -> List.of(targetDevice.getRtcUserId()))
                .switchIfEmpty(Mono.defer(() -> Mono.just(List.of())));
    }

    /**
     * Initialize call to target users
     * @param messageData Client message data
     * @param targetUsers Target user IDs
     * @param deviceInfo Source device information
     * @return Mono of void representing call initialization
     */
    private Mono<Void> initializeCall(ClientMessageData messageData, List<String> targetUsers, DeviceInfoResp deviceInfo, String roomId) {
        return findAvailableUsers(targetUsers)
                .flatMap(availableUsers -> {
                    log.info("Available users: {}", availableUsers);
                    if (availableUsers.isEmpty()) {
                        RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                                .rtcRoomId(roomId)
                                .rtcUserId(deviceInfo.getRtcUserId())
                                .username(deviceInfo.getUsername())
                                .clientType(messageData.getClientType())
                                .reason("no answer")
                                .build();
                        return Mono.when(
                                notifyNoAvailableDevice(messageData.getUserId(), roomId),
                                rtcRoomCallService.cancel(rtcRoomCallDTO),
                                reactiveStringRedisTemplate.delete(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, messageData.getDeviceId())
                        );
                    }
                    return processCallToAvailableUsers(messageData, availableUsers, deviceInfo, roomId);
                });
    }

    /*
     * Find available (non-busy) users
     * @param targetUsers Potential target users
     * @return Mono of available user IDs
     */
    /*private Mono<List<String>> findAvailableUsers(List<String> targetUsers) {
        return reactiveStringRedisTemplate.opsForSet()
                .isMember(RedisKeyConstant.ONLINE_BUSY_USER_KEY, targetUsers.toArray())
                .map(busyMap -> targetUsers.stream()
                        .filter(user -> !busyMap.get(user))
                        .collect(Collectors.toList()));
    }*/

    /**
     * Find available (non-busy) users
     * @param targetUsers Potential target users
     * @return Mono of available user IDs
     */
    private Mono<List<String>> findAvailableUsers(List<String> targetUsers) {
        List<String> targetDeviceIds = targetUsers.stream()
                .map(RtcRoomParseUtil::getDeviceIdByUserId).toList();
        log.info("findAvailableUsers targetDeviceIds: {} targetUsers: {}", targetDeviceIds, targetUsers);
        return Flux.fromIterable(targetDeviceIds)
                .flatMapSequential(deviceId -> reactiveStringRedisTemplate.opsForSet().isMember(RedisKeyConstant.ONLINE_DEVICE_KEY, deviceId))
                .collectList()
                .flatMap(results -> {
                    List<String> onlineUsers = new ArrayList<>();
                    log.info("findAvailableUsers targetUsers: {}, results: {}", targetDeviceIds, results);
                    for (int i = 0; i < targetUsers.size(); i++) {
                        if (results.get(i)) {
                            onlineUsers.add(targetUsers.get(i));
                        }
                    }
                    return Mono.just(onlineUsers);
                }).flatMap(onlineUsers -> {
                    log.info("findAvailableUsers onlineUsers: {}", onlineUsers);
                    if (onlineUsers.isEmpty()) {
                        return Mono.just(Collections.emptyList());
                    }

                    Mono<List<Boolean>> listMono = Flux.fromIterable(onlineUsers)
                            .flatMapSequential(userId -> reactiveStringRedisTemplate.opsForSet().isMember(RedisKeyConstant.ONLINE_BUSY_USER_KEY, userId))
                            .collectList();

                    return listMono.flatMap(busyMap -> {
                        List<String> availableUsers = new ArrayList<>();
                        for (int i = 0; i < onlineUsers.size(); i++) {
                            if (!busyMap.get(i)) {
                                availableUsers.add(onlineUsers.get(i));
                            }
                        }
                        return Mono.just(availableUsers);
                    });
                });
    }

    /**
     * Process call to available users
     * @param messageData Client message data
     * @param availableUsers Available user IDs
     * @param deviceInfo Source device information
     * @return Mono of void
     */
    private Mono<Void> processCallToAvailableUsers(
            ClientMessageData messageData,
            List<String> availableUsers,
            DeviceInfoResp deviceInfo,
            String roomId) {

        CallbackMessage callbackMessage = createCallbackMessage(roomId, deviceInfo, Instructions.OPEN_RINGING);
        CallbackMessage transferringCallbackMessage = createCallbackMessage(roomId, deviceInfo, Instructions.TRANSFERRING);

        String callKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, messageData.getUserId());

        return Mono.when(
                sendDevice(availableUsers, callbackMessage),
                sendDevice(messageData.getUserId(), transferringCallbackMessage),
                updateRedisCallStatus(availableUsers, callKey, deviceInfo, roomId),
                sendCallOverTimeEvent(roomId, deviceInfo)
        );
    }

    private Mono<Void> sendCallOverTimeEvent(String rtcRoomId, DeviceInfoResp deviceInfoResp) {
        MessageBody messageBody = new MessageBody();
        messageBody.setAction(CallTimeoutEnums.CALL_ACCEPT_TIMEOUT.getType());
        messageBody.setTimestamp(DateUtil.current() + "");

        CallOvertimeMessage callOvertimeMessage = new CallOvertimeMessage();
        callOvertimeMessage.setRtcRoomId(rtcRoomId);
        callOvertimeMessage.setRtcUserId(deviceInfoResp.getRtcUserId());
        callOvertimeMessage.setRtcUserName(deviceInfoResp.getUsername());
        callOvertimeMessage.setDeviceId(deviceInfoResp.getDeviceId());

        messageBody.setData(callOvertimeMessage);
        return reactiveMessageSender.sendDelayMessage(CALL_OVERTIME_DELAY_MESSAGE, messageBody, 4)
                .doOnSuccess(aVoid -> log.info("sendCallOverTimeEvent success"))
                .doOnError(throwable -> log.error("sendCallOverTimeEvent error", throwable))
                .then();
    }

    /**
     * Notify client when no device is available
     * @param callUserId User initiating the call
     * @param roomId rtc roomId
     * @return Mono of void
     */
    private Mono<Void> notifyNoAvailableDevice(String callUserId, String roomId) {
        ServerMessageData serverMessageData = new ServerMessageData()
                .setRoomId(roomId)
                .setMessage("No idle device answers the call");

        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand(Instructions.BUSY.getCode())
                .setTimestamp(String.valueOf(System.currentTimeMillis()))
                .setData(serverMessageData);

        log.info("No available devices, notifying client: {}", callUserId);
        return sendDevice(callUserId, callbackMessage);
    }

    /**
     * Update Redis status for call and users
     * @param availableUsers Available user IDs
     * @param callKey Redis key for call tracking
     * @param deviceInfo Source device information
     * @return Mono of void
     */
    private Mono<Void> updateRedisCallStatus(List<String> availableUsers, String callKey, DeviceInfoResp deviceInfo, String roomId) {

        String sponsorUserId = deviceInfo.getRtcUserId();

        Map<String, String> roomBindings = availableUsers.stream()
                .collect(Collectors.toMap(
                        userId -> String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId),
                        userId -> roomId + StrUtil.COLON + sponsorUserId
                ));

        // Add sponsor's room binding
        roomBindings.put(
                String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, sponsorUserId),
                roomId + StrUtil.COLON + sponsorUserId
        );

        List<String> busyUserIds = new ArrayList<>(availableUsers);
        busyUserIds.add(sponsorUserId);

        log.info("Updating Redis status for busy users: {}", busyUserIds);

        return reactiveStringRedisTemplate.opsForSet().add(callKey, busyUserIds.toArray(new String[0]))
                .then(reactiveStringRedisTemplate.opsForValue().multiSet(roomBindings))
                .then(reactiveStringRedisTemplate.opsForSet().add(RedisKeyConstant.ONLINE_BUSY_USER_KEY, busyUserIds.toArray(new String[0])))
                .doOnSuccess(aVoid -> log.info("Updated Redis status for call and users"))
                .then();

    }

    /**
     * Handle transfer errors
     * @param error Encountered error
     * @return Mono of false to indicate failure
     */
    private Mono<Boolean> handleTransferError(Throwable error) {
        log.error("Transfer to human failed: {}", error.getMessage(), error);
        return Mono.just(false);
    }

}
