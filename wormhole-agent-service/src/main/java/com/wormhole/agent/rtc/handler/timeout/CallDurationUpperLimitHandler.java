package com.wormhole.agent.rtc.handler.timeout;

import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.entity.RtcRoomCallEntity;
import com.wormhole.agent.rtc.consts.CallTimeoutEnums;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.channel.consts.Instructions;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * @author: joker.liu
 * @date: 2025/6/16
 * @Description:
 */
@Component
public class CallDurationUpperLimitHandler extends AbstractCallTimeoutHandler{
    @Override
    protected Mono<Boolean> doHandle(CallOvertimeMessage message) {

        Mono<RtcRoomCallEntity> roomCallInfoMono = rtcRoomCallService.getRoomCallInfo(message.getRtcRoomId());
        return roomCallInfoMono
                .flatMap(roomCallInfo -> {
                    if (roomCallInfo.getRowStatus() == 1 && roomCallInfo.getHasAiParticipant() == 1 && roomCallInfo.getHasTransfer() == 0) {

                        RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                                .rtcRoomId(message.getRtcRoomId())
                                .timestamp(Instant.now().getEpochSecond())
                                .rtcUserId("99999")
                                .username("system")
                                .reason("call duration upper limit")
                                .build();

                        return executeTimeout(message, roomCallInfo)
                                .then(rtcRoomCallService.calledNormalOver(rtcRoomCallDTO));
                    } else {
                        return Mono.just(Boolean.TRUE);
                    }
                });
    }

    @Override
    protected String timeoutCommand() {
        return Instructions.CALL_DURATION_UPPER_LIMIT.getCode();
    }

    @Override
    public String messageType() {
        return CallTimeoutEnums.CALL_DURATION_UPPER_LIMIT.getType();
    }
}
