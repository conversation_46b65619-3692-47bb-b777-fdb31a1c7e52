package com.wormhole.agent.rtc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * @author: joker.liu
 * @date: 2025/3/17
 * @Description:
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "wormhole.agent.rtc")
public class AgentRtcProperties {

    @NestedConfigurationProperty
    private AgentRtcConfig agentRtcConfig;

}
