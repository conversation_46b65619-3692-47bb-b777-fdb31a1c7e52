package com.wormhole.agent.rtc.handler.event;

import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.channel.consts.MessageBody;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/13
 * @Description:
 */
@Slf4j
public abstract class AbstractEventHandler<T> implements EventHandler {

    @Resource
    protected ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    @Override
    public boolean support(String eventType) {
        return messageType().equals(eventType);
    }

    abstract T convert(Object message);

    abstract Mono<Boolean> doHandle(T eventData);

    @Override
    public Mono<Boolean> handle(MessageBody eventData) {

        log.info("处理事件: eventType={}, eventData={}", messageType(), eventData);

        T convert = convert(eventData.getData());

        return doHandle(convert);
    }

    protected Mono<Integer> getClientType(String deviceId) {
        String clientTypeKey = String.format(RedisKeyConstant.RTC_DEVICE_CLIENT_TYPE, deviceId);
        return reactiveStringRedisTemplate.opsForValue().get(clientTypeKey)
                .map(Integer::parseInt)
                .switchIfEmpty(Mono.just(0))
                .doOnError(error -> log.error("获取设备客户端类型失败: deviceId={}, error={}",
                        deviceId, error.getMessage(), error));
    }
}
