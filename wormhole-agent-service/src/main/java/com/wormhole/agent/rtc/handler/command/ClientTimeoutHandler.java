package com.wormhole.agent.rtc.handler.command;

import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.ClientMessageData;
import org.springframework.stereotype.Component;

/**
 * @author: joker.liu
 * @date: 2025/7/14
 * @Description:
 */
@Component
public class ClientTimeoutHandler extends HangUpHandler{

    public ClientTimeoutHandler(RtcRoomCallService rtcRoomCallService) {
        super(rtcRoomCallService);
    }

    @Override
    public String messageType() {
        return Instructions.CLIENT_TIMEOUT.getCode();
    }

    public RtcRoomCallDTO convert(String rtcRoomId, String userId, ClientMessageData messageData, DeviceInfoResp info) {

        return RtcRoomCallDTO.builder()
                .rtcRoomId(rtcRoomId)
                .timestamp(System.currentTimeMillis())
                .rtcUserId(userId)
                .username(info.getUsername())
                .clientType(messageData.getClientType())
                .reason("client timeout")
                .build();

    }

}
