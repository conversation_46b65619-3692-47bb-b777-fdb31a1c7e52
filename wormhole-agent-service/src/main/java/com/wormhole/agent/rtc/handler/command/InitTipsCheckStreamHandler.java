package com.wormhole.agent.rtc.handler.command;

import com.wormhole.agent.rtc.config.TipsAudioProperties;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/28
 * @Description:    初始化检查
 */
@Component
@RequiredArgsConstructor
public class InitTipsCheckStreamHandler extends AbstractCommandClientMessageHandler {

    private static final Logger log = LoggerFactory.getLogger(InitTipsCheckStreamHandler.class);

    private final TipsAudioProperties tipsAudioProperties;

    private final RtcRoomCallService rtcRoomCallService;


    @Override
    public String messageType() {
        return Instructions.INIT_START_CHECK_STREAM.getCode();
    }

    @Override
    public Mono<Boolean> doHandle(ClientMessageData messageData) {
        log.info("开始处理语音检查请求: deviceId={}", messageData.getDeviceId());
        String deviceBingRoomKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, messageData.getDeviceId());
        return reactiveStringRedisTemplate.opsForValue().get(deviceBingRoomKey)
                .flatMap(roomIdValue -> {
                    if (StringUtils.isBlank(roomIdValue)) {
                        return Mono.just(false);
                    }
                    messageData.setRoomId(roomIdValue);
                    return startRelayStreamAsync(roomIdValue)
                            .onErrorResume(e -> {
                                log.error("开启在线音频流推送失败: deviceId={}", messageData.getDeviceId(), e);
                                return Mono.just(false);
                            });
                });
    }

    private Mono<Boolean> startRelayStreamAsync(String roomId) {
        return rtcRoomCallService.getRoomCallInfo(roomId)
                .flatMap(rtcRoomCall ->
                        rtcHelper.startRelayStreamAsync(rtcRoomCall.getRtcRoomId(), rtcRoomCall.getRtcRoomId() + "-stream",
                                "stream-" + rtcRoomCall.getInitiatorId(), true, tipsAudioProperties.getCheckStreamUrl())
                ).thenReturn(true);
    }

}
