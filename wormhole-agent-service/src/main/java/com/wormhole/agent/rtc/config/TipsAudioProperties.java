package com.wormhole.agent.rtc.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * <AUTHOR>
 * @date 2025-03-13 15:06:53
 * @Description: 提示音频配置
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "wormhole.tips.audio")
public class TipsAudioProperties {

    private String checkStreamUrl;


}
