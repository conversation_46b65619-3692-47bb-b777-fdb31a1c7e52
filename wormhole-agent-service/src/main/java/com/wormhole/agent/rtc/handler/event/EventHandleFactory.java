package com.wormhole.agent.rtc.handler.event;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: joker.liu
 * @date: 2025/3/10
 * @Description:
 */
@Component
public class EventHandleFactory {

    private Map<String, EventHandler> eventHandlerMap;

    public EventHandler getEventHandler(String eventType) {
        return Optional.ofNullable(eventHandlerMap.get(eventType))
                .orElseThrow(() -> new IllegalArgumentException("No event handler found for event type: " + eventType));
    }

    @Autowired
    public void setEventHandlerMap(List<EventHandler> eventHandlers) {
        eventHandlerMap = eventHandlers.stream()
                .collect(Collectors.toMap(EventHandler::messageType, e -> e));
    }

}
