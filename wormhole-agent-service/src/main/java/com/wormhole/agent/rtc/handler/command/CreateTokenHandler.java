package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.config.RtcVoiceChatProperties;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.channel.consts.message.ServerMessageData;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-03-13 21:18:00
 * @Description:
 */
@Component
public class CreateTokenHandler extends AbstractCommandClientMessageHandler {

    @Resource
    private RtcVoiceChatProperties rtcVoiceChatProperties;

    @Override
    Mono<Boolean> doHandle(ClientMessageData messageData) {
        Mono<DeviceInfoResp> deviceInfoMono = connectionService.getDeviceInfo(messageData.getDeviceId(), messageData.getClientType());

        return deviceInfoMono.flatMap(deviceInfo -> {

            String rtcRoomId = getRtcRoomId(deviceInfo);

            String deviceBingRoomKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, deviceInfo.getDeviceId());

            return reactiveStringRedisTemplate.opsForValue().set(deviceBingRoomKey, rtcRoomId)
                    .then(sendTokenMessage(deviceInfo.getRtcUserId(), rtcRoomId));

        });
    }

    public String getRtcRoomId(DeviceInfoResp deviceInfo) {
        return deviceInfo.getHotelCode() + StrUtil.UNDERLINE + deviceInfo.getPositionCode()
                + StrUtil.UNDERLINE + deviceInfo.getDeviceId() + StrUtil.UNDERLINE + System.currentTimeMillis();
    }

    @Override
    public String messageType() {
        return Instructions.TOKEN_REQ.getCode();
    }

    /**
     * 发送 通话被拒绝 指令
     */
    private Mono<Boolean> sendTokenMessage(String userId, String roomId) {

        return rtcHttpHelper.generateToken(roomId,userId)
                .flatMap(token -> {
                    ServerMessageData serverMessageData = new ServerMessageData();
                    serverMessageData.setRoomId(roomId);
                    serverMessageData.setToken(token);
                    serverMessageData.setTimeout(Optional.ofNullable(rtcVoiceChatProperties.getClientTimeout()).orElse(30));

                    CallbackMessage callbackMessage = new CallbackMessage()
                            .setCommand(Instructions.TOKEN_RES.getCode())
                            .setTimestamp(String.valueOf(SystemClock.now()))
                            .setData(serverMessageData);

                    return super.sendDevice(userId, callbackMessage)
                            .thenReturn(true);
                })
                .onErrorResume(e -> {
                    log.error("sendTokenMessage error", e);
                    return Mono.just(false);
                });
    }
}
