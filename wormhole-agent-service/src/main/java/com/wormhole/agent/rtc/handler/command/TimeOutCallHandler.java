package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.channel.consts.message.ServerMessageData;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-13 11:48:12
 * @Description:
 */
@Component
public class TimeOutCallHandler extends AbstractCommandClientMessageHandler {

    @Override
    public String messageType() {
        return Instructions.TIME_OUT_CALL.getCode();
    }


    @Override
    public boolean support(String messageType) {
        return messageType().equals(messageType);
    }

    @Override
    public Mono<Boolean> doHandle(ClientMessageData messageData) {
        log.info("receive timeOutCall instruction,data:{}", JacksonUtils.writeValueAsString(messageData));
        // 获取对应的房间id
        String userId = messageData.getUserId();
        String bindingKey = String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId);
        return reactiveStringRedisTemplate.opsForValue().get(bindingKey)
                .flatMap(value -> {
                    if (StrUtil.isBlank(value)) {
                        return Mono.just(true);
                    }
                    String[] split = value.split(StrUtil.COLON);
                    String roomId = split[0];
                    String callOriginalUserId = split[1];
                    // 理论上来说 不会有这种情况，我不能拒绝我自己发起的通话请求，目前这种情况 定义为 “挂断”
                    if (userId.equals(callOriginalUserId)) {
                        // 给接收端发送 通话被拒绝
                        return getUserIds(callOriginalUserId)
                                .flatMap(userIds -> this.sendCallTimeOut(userIds, roomId))
                                .thenReturn(true);
                    }
                    return doCallTimeOutHandler(userId, callOriginalUserId, roomId);

                }).switchIfEmpty(Mono.error(new BusinessException("USER-NOT-BINDING-ROOM", "用户未绑定房间")))
                ;

    }

    private Mono<Boolean> doCallTimeOutHandler(String userId, String callOriginalUserId, String roomId) {

        String onlineRoomUserKey = String.format(RedisKeyConstant.ONLINE_ROOM_USER_KEY, roomId);

        String fromCallToHumanKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, callOriginalUserId);

        // 移除房间内的用户id
        return reactiveStringRedisTemplate.opsForSet().remove(onlineRoomUserKey, userId)
                // 移除忙线用户
                .then(reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_BUSY_USER_KEY, userId))
                // 移除 通话绑定数据
                .then(reactiveStringRedisTemplate.opsForSet().remove(fromCallToHumanKey, userId))
                // 移除后判断是否为空或只有自己，如果还有，则直接return，如果除了拨号用户外，没有别的用户了，则移除拨号用户，并通知拨号用户请求已经被拒绝
                .flatMap(v -> getUserIds(callOriginalUserId)
                        .flatMap(userIds -> {
                            log.info("userId:{}", userIds);
                            if (userIds.isEmpty()) {
                                return sendCallTimeOut(CollUtil.toList(callOriginalUserId), roomId);
                            }
                            return Mono.just(true);
                        }));
    }

    /**
     * 通过发起者，获取接受者用户
     */
    private Mono<List<String>> getUserIds(String userId) {

        String callKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, userId);

        return reactiveStringRedisTemplate.opsForSet().members(callKey)
                .collectList();
    }


    /**
     * 发送 通话被拒绝 指令
     */
    private Mono<Boolean> sendCallTimeOut(List<String> userIds, String roomId) {

        ServerMessageData serverMessageData = ServerMessageData.createCallTimeOutMessage(roomId, "No answer",
                StringUtils.EMPTY);
        serverMessageData.setMessage("");   // todo
        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand(Instructions.NO_ANSWER.getCode())
                .setTimestamp(String.valueOf(SystemClock.now()))
                .setData(serverMessageData);

        return super.sendDevice(userIds, callbackMessage)
                .thenReturn(true);

    }

}
