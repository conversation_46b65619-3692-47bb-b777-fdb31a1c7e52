package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.SystemClock;
import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.channel.consts.message.ServerMessageData;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

/**
 * <PERSON><PERSON> for RTC call rejection
 */
@Component
@RequiredArgsConstructor
public class RejectHandler extends AbstractCommandClientMessageHandler {

    private final RtcRoomCallService rtcRoomCallService;

    @Override
    public String messageType() {
        return Instructions.REJECT.getCode();
    }

    @Override
    public boolean support(String messageType) {
        return messageType().equals(messageType);
    }

    @Override
    public Mono<Boolean> doHandle(ClientMessageData messageData) {
        log.info("Received reject instruction, data: {}", JacksonUtils.writeValueAsString(messageData));

        return connectionService.getDeviceInfo(messageData.getDeviceId(), messageData.getClientType())
                .flatMap(deviceInfo -> {
                    String userId = deviceInfo.getRtcUserId();
                    return findActiveRoomInfo(userId)
                            .flatMap(roomInfo -> processRejection(userId, roomInfo));
                })
                .defaultIfEmpty(false);
    }

    /**
     * Find active room information for the user
     */
    private Mono<RoomInfo> findActiveRoomInfo(String userId) {
        String bindingKey = String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId);

        return reactiveStringRedisTemplate.opsForValue().get(bindingKey)
                .filter(StrUtil::isNotBlank)
                .map(value -> {
                    String[] parts = value.split(StrUtil.COLON);
                    return new RoomInfo(parts[0], parts[1], bindingKey);
                });
    }

    /**
     * Process the rejection based on who initiated the call
     */
    private Mono<Boolean> processRejection(String userId, RoomInfo roomInfo) {
        // If user is rejecting their own call (hangup scenario)
        if (userId.equals(roomInfo.callOriginalUserId)) {
            return handleSelfRejection(roomInfo);
        }

        // Normal rejection flow
        return handleNormalRejection(userId, roomInfo);
    }

    /**
     * Handle case when initiator rejects their own call
     */
    private Mono<Boolean> handleSelfRejection(RoomInfo roomInfo) {
        return getTargetUserIds(roomInfo.callOriginalUserId)
                .flatMap(userIds -> cleanupRoomKeys(userIds)
                        .then(sendServerReject(userIds, roomInfo.roomId)))
                .thenReturn(true);
    }

    /**
     * Handle normal rejection case (receiver rejects an incoming call)
     */
    private Mono<Boolean> handleNormalRejection(String userId, RoomInfo roomInfo) {
        return performRejectionCleanup(userId, roomInfo)
                .then(notifyRejection(userId, roomInfo))
                .thenReturn(true);
    }

    /**
     * Clean up Redis keys when a rejection occurs
     */
    private Mono<Void> performRejectionCleanup(String userId, RoomInfo roomInfo) {
        String fromCallToHumanKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, roomInfo.callOriginalUserId);
        List<String> userIds = Arrays.asList(userId, roomInfo.callOriginalUserId);
        return Mono.when(
                reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_BUSY_USER_KEY, userIds.toArray(new Object[0])),
                reactiveStringRedisTemplate.opsForSet().remove(fromCallToHumanKey, userId),
                reactiveStringRedisTemplate.delete(roomInfo.bindingKey)
        );
    }

    private Mono<Void> deleteBindingRoomKey(String userId) {

        return connectionService.getDeviceInfoByUserId(userId)
                .flatMap(deviceInfo -> reactiveStringRedisTemplate
                        .delete(String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, deviceInfo.getDeviceId()))
                        .then()
                );
    }

    /**
     * Cleanup all room keys during rejection
     */
    private Mono<Void> cleanupRoomKeys(List<String> userIds) {
        return Flux.fromIterable(userIds)
                .flatMap(id -> {
                    String key = String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, id);
                    return reactiveStringRedisTemplate.delete(key);
                })
                .then();
    }

    /**
     * Notify appropriate parties about the rejection
     */
    private Mono<Void> notifyRejection(String userId, RoomInfo roomInfo) {
        log.info("Notifying rejection for userId: {}, roomId: {}", userId, roomInfo.roomId);
        return getTargetUserIds(roomInfo.callOriginalUserId)
                .flatMap(targetUserIds -> connectionService.getDeviceInfoByUserId(userId)
                        .flatMap(deviceInfo -> {
                            log.info("Rejecting call for userId: {}, roomId: {}, targetUserIds: {} deviceInfo: {}", userId, roomInfo.roomId,
                                    targetUserIds, JacksonUtils.writeValueAsString(deviceInfo));
                            String username = deviceInfo.getUsername();
                            RtcCallStatusEnum status = (targetUserIds.size() == 1) ?
                                    RtcCallStatusEnum.REJECTED : RtcCallStatusEnum.CALLING;

                            RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                                    .rtcRoomId(roomInfo.roomId)
                                    .rtcUserId(userId)
                                    .username(username)
                                    .status(status)
                                    .build();

                            Mono<Boolean> updateCall = rtcRoomCallService.reject(rtcRoomCallDTO);

                            if (targetUserIds.size() == 1) {
                                return updateCall
                                        .then(deleteBindingRoomKey(roomInfo.callOriginalUserId))
                                        .then(sendServerReject(CollUtil.toList(roomInfo.callOriginalUserId), roomInfo.roomId));
                            }
                            return updateCall;
                        }).then());
    }

    /**
     * Get list of users who were called by the initiator
     */
    private Mono<List<String>> getTargetUserIds(String initiatorUserId) {
        String callKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, initiatorUserId);
        return reactiveStringRedisTemplate.opsForSet().members(callKey).collectList();
    }

    /**
     * Send rejection notification to specified users
     */
    private Mono<Void> sendServerReject(List<String> userIds, String roomId) {
        ServerMessageData messageData = ServerMessageData.createRejectMessage(roomId);
        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand(Instructions.SERVER_REJECT.getCode())
                .setTimestamp(String.valueOf(SystemClock.now()))
                .setData(messageData);

        return sendDevice(userIds, callbackMessage);
    }

    private record RoomInfo(String roomId, String callOriginalUserId, String bindingKey) { }
}