package com.wormhole.agent.rtc.handler.command;

import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.common.util.JacksonUtils;
import reactor.core.publisher.Mono;

import java.time.Duration;

/**
 * @author: joker.liu
 * @date: 2025/5/6
 * @Description:
 */
public abstract class AbstractCommandClientMessageHandler extends AbstractCommandHandler<ClientMessageData>{

    @Override
    Mono<Boolean> handle(ClientMessageData messageData) {
        return preOperate(messageData)
                .then(doHandle(messageData));
    }

    abstract Mono<Boolean> doHandle(ClientMessageData message);

    @Override
    ClientMessageData convert(Object message) {
        return JacksonUtils.convertValue(message, ClientMessageData.class);
    }

    protected Mono<Boolean> preOperate(ClientMessageData clientMessageData) {
        Integer clientType = clientMessageData.getClientType();
        String clientTypeKey = String.format(RedisKeyConstant.RTC_DEVICE_CLIENT_TYPE, clientMessageData.getDeviceId());
        return reactiveStringRedisTemplate.opsForValue().set(clientTypeKey, String.valueOf(clientType), Duration.ofDays(1));
    }

}
