package com.wormhole.agent.rtc.consts;

import lombok.Getter;

/**
 * @author: joker.liu
 * @date: 2025/4/27
 * @Description:
 */
@Getter
public enum CallTimeoutEnums {

    CALL_RESPONSE_TIMEOUT("call_response_timeout", "通话响应超时"),
    CALL_ACCEPT_TIMEOUT("call_accept_timeout", "通话接听超时"),
    CALL_AI_CHAT_DEAL("call_ai_chat_deal", "通话AI对话处理"),
    CALL_DURATION_UPPER_LIMIT("call_duration_upper_limit", "通话上限"),

    ;

    private final String type;
    private final String description;

    CallTimeoutEnums(String type, String description) {
        this.type = type;
        this.description = description;
    }


}
