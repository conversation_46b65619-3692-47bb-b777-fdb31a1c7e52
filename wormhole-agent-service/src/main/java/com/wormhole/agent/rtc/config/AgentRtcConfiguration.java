package com.wormhole.agent.rtc.config;

import com.wormhole.channel.consts.ChannelConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

import java.util.Optional;

/**
 * @author: joker.liu
 * @date: 2025/3/17
 * @Description:
 */
@Configuration
@EnableConfigurationProperties(AgentRtcProperties.class)
public class AgentRtcConfiguration {

    @Bean
    public AgentRtcConfig channelConfig(AgentRtcProperties agentRtcProperties) {
        return agentRtcProperties.getAgentRtcConfig();
    }

    @Bean(ChannelConstant.RTC_POOL)
    public Scheduler rtcScheduler(@Autowired(required = false) AgentRtcConfig agentRtcConfig) {

        int heartCore = Optional.ofNullable(agentRtcConfig).map(AgentRtcConfig::getRtcPoolCore).orElse(ChannelConstant.DEFAULT_CORE);
        int heartMax = Optional.ofNullable(agentRtcConfig).map(AgentRtcConfig::getRtcQueuedMax).orElse(Integer.MAX_VALUE);
        return Schedulers.newBoundedElastic(heartCore, heartMax, "rtc-pool");

    }

}
