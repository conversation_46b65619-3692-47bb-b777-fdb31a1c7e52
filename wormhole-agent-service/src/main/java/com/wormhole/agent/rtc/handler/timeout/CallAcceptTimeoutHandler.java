package com.wormhole.agent.rtc.handler.timeout;

import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.rtc.consts.CallTimeoutEnums;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.agent.workflow.util.LockUtil;
import com.wormhole.channel.consts.Instructions;
import lombok.RequiredArgsConstructor;
import org.redisson.api.RedissonReactiveClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/4/23
 * @Description:
 */
@Component
@RequiredArgsConstructor
public class CallAcceptTimeoutHandler extends AbstractCallTimeoutHandler {

    private static final Logger log = LoggerFactory.getLogger(CallAcceptTimeoutHandler.class);

    private final RedissonReactiveClient redissonReactiveClient;


    @Override
    public String messageType() {
        return CallTimeoutEnums.CALL_ACCEPT_TIMEOUT.getType();
    }

    @Override
    protected Mono<Boolean> doHandle(CallOvertimeMessage message) {
        String lockKey = LockUtil.lockName(String.format(RedisKeyConstant.REDISSON_ANSWER_ROOM_KEY, message.getRtcRoomId()));

        return LockUtil.withMonoLock(lockKey, 3000L, 0L,
                redissonReactiveClient,
                () -> deal(message)
        );
    }

    @Override
    protected String timeoutCommand() {
        return Instructions.CALL_TIMEOUT.getCode();
    }

    private Mono<Boolean> deal(CallOvertimeMessage message) {

        return rtcRoomCallService.getRoomCallInfo(message.getRtcRoomId())
                .flatMap(roomCallInfo -> {
                    if (roomCallInfo.getCallStatus() == 1) {
                        RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                                .rtcRoomId(message.getRtcRoomId())
                                .rtcUserId(message.getRtcUserId())
                                .username(message.getRtcUserName())
                                .reason("timeout")
                                .build();
                        return executeTimeout(message, roomCallInfo)
                                .then(rtcRoomCallService.cancel(rtcRoomCallDTO));
                    }
                    return Mono.just(Boolean.FALSE);
                });
    }
}
