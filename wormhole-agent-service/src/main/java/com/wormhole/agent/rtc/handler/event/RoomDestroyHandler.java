package com.wormhole.agent.rtc.handler.event;

import cn.hutool.core.collection.CollectionUtil;
import com.wormhole.agent.log.analysis.VoiceChatMetricsAnalysis;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.channel.consts.enums.EventType;
import com.wormhole.channel.consts.event.RoomDestroyEvent;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/3/10
 * @Description: 房间销毁处理器
 */
@Slf4j
@Component
public class RoomDestroyHandler extends AbstractEventHandler<RoomDestroyEvent> {
    @Resource
    private VoiceChatMetricsAnalysis voiceChatMetricsAnalysis;
    @Override
    public String messageType() {
        return EventType.ROOM_DESTROY.getEventType();
    }

    @Override
    RoomDestroyEvent convert(Object message) {
        return JacksonUtils.convertValue(message, RoomDestroyEvent.class);
    }

    @Override
    public Mono<Boolean> doHandle(RoomDestroyEvent event) {
        log.info("Processing room destroy event: {}", JacksonUtils.writeValueAsString(event));

        return processRoomDestroy(event)
                .onErrorResume(e -> {
                    log.error("Failed to handle room destroy event for roomId: {}", event.getRoomId(), e);
                    return Mono.just(false);
                });
    }

    /**
     * 处理房间销毁事件的主要流程
     */
    private Mono<Boolean> processRoomDestroy(RoomDestroyEvent event) {
        String roomId = event.getRoomId();
        String onlineRoomKey = String.format(RedisKeyConstant.ONLINE_ROOM_USER_KEY, roomId);

        return cleanupOnlineUsers(onlineRoomKey)
                .flatMap(result -> cleanupRoomRedisKey(onlineRoomKey))
                .flatMap(result -> {
                    // 无论主操作成功与否，都执行这两个分析操作
                    Mono<Void> eventAnalysis = voiceChatMetricsAnalysis.logEventAnalysis(roomId)
                            .doOnSuccess(success -> log.info("Event analysis logged successfully for room: {}", roomId))
                            .doOnError(error -> log.error("Failed to log event analysis for room: {}", roomId, error))
                            .onErrorResume(e -> Mono.empty());

                    Mono<Void> esSave = voiceChatMetricsAnalysis.logSaveEs(roomId)
                            .doOnSuccess(success -> log.info("Event saved to ES successfully for room: {}", roomId))
                            .doOnError(error -> log.error("Failed to save event to ES for room: {}", roomId, error))
                            .onErrorResume(e -> Mono.empty());

                    // 并行执行两个分析操作
                    return Mono.when(eventAnalysis, esSave).thenReturn(result);
                });
    }

    /**
     * 清理在线用户
     */
    private Mono<Boolean> cleanupOnlineUsers(String onlineRoomKey) {
        return reactiveStringRedisTemplate.opsForSet().members(onlineRoomKey)
                .collectList()
                .flatMap(userIds -> {

                    log.info("room destroy remove userIds: {}", userIds);
                    if (CollectionUtil.isEmpty(userIds)) {
                        return Mono.just(true);
                    }

                    List<String> removeKeys = new ArrayList<>();

                    userIds.forEach(userId -> {
                        removeKeys.add(String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, userId));
                        removeKeys.add(String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId));
                    });

                    // 从忙碌用户集合中移除这些用户
                    Object[] userIdArray = userIds.toArray(new Object[0]);
                    return reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_BUSY_USER_KEY, userIdArray)
                            .then(reactiveStringRedisTemplate.delete(removeKeys.toArray(new String[0])))
                            .thenReturn(true);
                });
    }



    /**
     * 清理Redis中房间相关的键
     */
    private Mono<Boolean> cleanupRoomRedisKey(String roomKey) {
        return reactiveStringRedisTemplate.opsForSet().delete(roomKey)
                .thenReturn(true);
    }
}