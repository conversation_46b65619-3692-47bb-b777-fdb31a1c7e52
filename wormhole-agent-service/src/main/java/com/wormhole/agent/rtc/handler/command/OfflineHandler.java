package com.wormhole.agent.rtc.handler.command;

import com.wormhole.agent.hotelds.HotelDsApiRpc;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/19
 * @Description:
 */
@Component
@RequiredArgsConstructor
public class OfflineHandler extends AbstractCommandClientMessageHandler {

    private final HotelDsApiRpc hotelDsApiRpc;

    @Override
    Mono<Boolean> doHandle(ClientMessageData messageData) {
        log.info("offline messageData:{}", JacksonUtils.writeValueAsString(messageData));
        return reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_DEVICE_KEY, messageData.getDeviceId())
                .flatMap( ele-> hotelDsApiRpc.deviceHeartAlive(messageData.getDeviceId(), false))
                .thenReturn(true);
    }

    @Override
    public String messageType() {
        return Instructions.OFFLINE.getCode();
    }
}
