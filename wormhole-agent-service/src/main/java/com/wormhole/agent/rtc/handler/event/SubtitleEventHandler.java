package com.wormhole.agent.rtc.handler.event;

import cn.hutool.core.date.DateUtil;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.agent.util.RtcRoomParseUtil;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.enums.EventType;
import com.wormhole.channel.consts.event.SubtitleEvent;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.mq.producer.ReactiveMessageSender;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * @author: joker.liu
 * @date: 2025/4/23
 * @Description:
 */
@Component
@RequiredArgsConstructor
public class SubtitleEventHandler extends AbstractEventHandler<SubtitleEvent>{

    private final static Logger log = LoggerFactory.getLogger(SubtitleEventHandler.class);

    private final RtcRoomCallService rtcRoomCallService;

    private final ReactiveMessageSender reactiveMessageSender;

    @Value("${rocket.topic.subtitle-message:subtitle_message}")
    public String SUBTITLE_MESSAGE_TOPIC;

    @Value("${rocket.topic.call-overtime-delay-message:call_overtime_delay_message}")
    public String CALL_OVERTIME_DELAY_MESSAGE;

    @Override
    SubtitleEvent convert(Object message) {
        return JacksonUtils.convertValue(message, SubtitleEvent.class);
    }

    @Override
    Mono<Boolean> doHandle(SubtitleEvent eventData) {

        return rtcRoomCallService.getRoomCallInfo(eventData.getRtcRoomId())
                .flatMap(roomCall -> {
                    if (eventData.getUserId().startsWith("chat")) {
                        return Mono.just(true);
                    }
                    if (roomCall.getHasAiParticipant() == 0 || roomCall.getHasTransfer() == 1) {
                        return sendSubtitleMessage(eventData);
                    }
                    return Mono.just(true);
                });
    }

    private Mono<Boolean> sendSubtitleMessage(SubtitleEvent eventData) {

        MessageBody messageBody = new MessageBody();
        messageBody.setAction("subtitle");
        messageBody.setTimestamp(DateUtil.current() + "");

        String hotelCode = RtcRoomParseUtil.getHotelCode(eventData.getRtcRoomId());
        String positionCode = RtcRoomParseUtil.getPositionCode(eventData.getRtcRoomId());
        String deviceId = RtcRoomParseUtil.getDeviceIdByUserId(eventData.getRtcRoomId());

        eventData.setHotelCode(hotelCode);
        eventData.setPositionCode(positionCode);
        eventData.setDeviceId(deviceId);

        messageBody.setData(eventData);

        return reactiveMessageSender.sendMessage(SUBTITLE_MESSAGE_TOPIC, messageBody)
                .doOnError(e -> log.error("Failed to send subtitle message", e))
                .onErrorResume(e -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "Failed to send subtitle message")))
                .thenReturn(true);
    }

    @Override
    public String messageType() {
        return EventType.SUBTITLE.getEventType();
    }
}
