package com.wormhole.agent.rtc.handler.command;

import com.wormhole.agent.hotelds.HotelDsApiRpc;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/19
 * @Description:
 */
@Component
@RequiredArgsConstructor
public class OnlineHandler extends AbstractCommandClientMessageHandler {

    private final HotelDsApiRpc hotelDsApiRpc;

    @Override
    Mono<Boolean> doHandle(ClientMessageData messageData) {

        log.info("online messageData:{}", JacksonUtils.writeValueAsString(messageData));
        return reactiveStringRedisTemplate.opsForSet().add(RedisKeyConstant.ONLINE_DEVICE_KEY, messageData.getDeviceId())
                .flatMap( ele-> hotelDsApiRpc.deviceHeartAlive(messageData.getDeviceId(), true))
                .thenReturn(true);

    }

    @Override
    public String messageType() {
        return Instructions.ONLINE.getCode();
    }
}
