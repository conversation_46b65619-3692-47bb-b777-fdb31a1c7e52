package com.wormhole.agent.rtc.consumer;

import com.wormhole.agent.rtc.handler.event.EventHandleFactory;
import com.wormhole.agent.rtc.handler.event.EventHandler;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import jakarta.annotation.Resource;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/17
 * @Description:
 */
@Component
@RocketMQMessageListener(topic = "${rocketmq.topic.channelEvent:channel_event}",
        consumerGroup = "${rocketmq.consumer.eventGroup:channel_agent_event_processor_group}",
        enableMsgTrace = true
)
public class EventListener extends AbstractReactiveMessageListener<MessageBody> {

    @Resource
    private EventHandleFactory eventHandleFactory;

    @Override
    protected Mono<Void> processMessage(MessageBody payload) {

        EventHandler eventHandler = eventHandleFactory.getEventHandler(payload.getAction());

        return eventHandler.handle(payload)
                .flatMap(result -> {
                    if (result) {
                        return Mono.empty();
                    } else {
                        return Mono.error(new BusinessException("MESSAGE-PROCESSING-FAILURE", "Message processing failure"));
                    }
                });
    }

}
