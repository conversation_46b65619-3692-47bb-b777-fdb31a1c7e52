package com.wormhole.agent.rtc.handler.event;

import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.rtc.consts.RoomUserHeaderConstant;
import com.wormhole.agent.service.ConnectionService;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.channel.consts.enums.EventType;
import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import com.wormhole.channel.consts.event.UserLeaveRoomEvent;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * @author: joker.liu
 * @date: 2025/3/11
 * @Description:    用户离开房间事件处理
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class UserLeaveRoomHandler extends AbstractEventHandler<UserLeaveRoomEvent>{

    private final RtcHelper rtcHelper;
    private final RtcRoomCallService rtcRoomCallService;
    private final ConnectionService connectionService;

    @Override
    public String messageType() {
        return EventType.USER_LEAVE_ROOM.getEventType();
    }

    @Override
    UserLeaveRoomEvent convert(Object message) {
        return JacksonUtils.convertValue(message, UserLeaveRoomEvent.class);
    }

    @Override
    public Mono<Boolean> doHandle(UserLeaveRoomEvent eventData) {
        log.info("user leave room info:{}", JacksonUtils.writeValueAsString(eventData));

        String redisSetKey = String.format(RedisKeyConstant.ONLINE_ROOM_USER_KEY, eventData.getRoomId());
        String bindingRoomKey = String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, eventData.getUserId());
        String callKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, eventData.getUserId());

        // 主要操作链
        return Mono.when(
                        reactiveStringRedisTemplate.opsForSet().remove(redisSetKey, eventData.getUserId()),
                        reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_BUSY_USER_KEY, eventData.getUserId()),
                        reactiveStringRedisTemplate.delete(bindingRoomKey, callKey),
                        removeDeviceRoomBinding(eventData.getUserId())
                )
                .then(Mono.defer(() -> dismissRoomAsync(redisSetKey, eventData.getRoomId())
                        .then(callEnd(eventData.getRoomId(), eventData.getUserId()))))
                .thenReturn(true)
                .onErrorResume(e -> {
                    log.error("Failed to handle user leave room event", e);
                    return Mono.just(false);
                });
    }


    private Mono<Void> removeDeviceRoomBinding(String userId) {
        if (userId.startsWith(RoomUserHeaderConstant.CHAT.getHeader())) {
            return Mono.empty();
        }
        String[] split = userId.split(StrUtil.UNDERLINE);
        if (split.length < 2) {
            return Mono.empty();
        }
        String deviceId = split[1];
        String deviceBingRoomKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, deviceId);
        return reactiveStringRedisTemplate.opsForValue().delete(deviceBingRoomKey).then();
    }

    private Mono<Boolean> callEnd(String roomId, String userId) {
        if (userId.startsWith(RoomUserHeaderConstant.CHAT.getHeader())) {
            return Mono.just(true);
        }
        return connectionService.getDeviceInfoByUserId(userId)
                .flatMap(deviceInfoResp -> rtcRoomCallService.getOnlineRoomCallInfo(roomId)
                        .flatMap(call -> {

                            RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                                    .rtcRoomId(roomId)
                                    .rtcUserId(userId)
                                    .username(deviceInfoResp.getUsername())
                                    .build();

                            if (Objects.equals(call.getCallStatus(), RtcCallStatusEnum.CALLING.getCode())) {
                                rtcRoomCallDTO.setReason("exited abnormally");
                                return rtcRoomCallService.cancel(rtcRoomCallDTO);
                            } else {
                                rtcRoomCallDTO.setReason("normal");
                                return rtcRoomCallService.calledNormalOver(rtcRoomCallDTO)
                                        .then(rtcRoomCallService.cleanupVoiceChat(roomId))
                                        .thenReturn(true);
                            }
                        }).then(rtcRoomCallService.cleanupVoiceChat(roomId))
                        .thenReturn(true)
                );
    }

    private Mono<Boolean> dismissRoomAsync(String onlineUserKey, String roomId) {

        return reactiveStringRedisTemplate.opsForSet().members(onlineUserKey)
                .collectList()
                .flatMap(userIds -> {
                    log.info("dismissRoomAsync userIds: {}", userIds);
                    if (userIds.isEmpty()) {
                        return doDismissRoomAsync(roomId);
                    }
                    if (userIds.size() == 1
                            && userIds.get(0).startsWith(RoomUserHeaderConstant.CHAT.getHeader())
                            && userIds.get(0).startsWith(RoomUserHeaderConstant.STREAM.getHeader())) {
                        return doDismissRoomAsync(roomId)
                                .then(rtcRoomCallService.cleanupVoiceChat(roomId))
                                .thenReturn(true);
                    }
                    return Mono.just(true);
                });
    }

    private Mono<Boolean> doDismissRoomAsync(String roomId) {
        return rtcHelper.dismissRoomAsync(roomId)
                .flatMap(result -> {
                    log.info("dismiss room result:{}", result);
                    return Mono.just(true);
                }).onErrorResume(e -> {
                    log.error("Failed to dismiss room", e);
                    return Mono.just(false);
                });
    }
}
