package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.service.ConnectionService;
import com.wormhole.channel.consts.ChannelConstant;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.channel.consts.helper.RtcHttpHelper;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ServerMessageData;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.req.SearchDeviceReq;
import com.wormhole.hotelds.core.model.resp.DeviceRtcInfoResp;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 消息处理器抽象基类
 *
 * @author: joker.liu
 * @date: 2025/3/12
 */
public abstract class AbstractCommandHandler<T> implements CommandHandler {

    protected static final Logger log = LoggerFactory.getLogger(AbstractCommandHandler.class);

    @Resource
    protected HotelDsApiClient hotelDsApiClient;
    @Resource
    protected RtcHelper rtcHelper;
    @Resource
    protected RtcHttpHelper rtcHttpHelper;
    @Resource(name = ChannelConstant.RTC_POOL)
    protected Scheduler rtcScheduler;
    @Resource
    protected ReactiveStringRedisTemplate reactiveStringRedisTemplate;
    @Resource
    protected ConnectionService connectionService;

    /**
     * 将消息体转换为特定类型
     *
     * @param message 原始消息对象
     * @return 转换后的类型化消息
     */
    abstract T convert(Object message);

    /**
     * 处理特定类型的消息
     *
     * @param messageData 类型化消息数据
     * @return 处理结果
     */
    abstract Mono<Boolean> handle(T messageData);

    @Override
    public boolean support(String messageType) {
        return Objects.equals(messageType(), messageType);
    }

    @Override
    public Mono<Boolean> handle(MessageBody message) {
        if (message == null || message.getData() == null) {
            log.warn("接收到空消息或消息内容为空");
            return Mono.just(false);
        }

        try {
            T convert = convert(message.getData());
            return handle(convert)
                    .doOnError(error -> log.error("处理消息失败: messageType={}, error={}",
                            messageType(), error.getMessage(), error));
        } catch (Exception e) {
            log.error("消息转换失败: messageType={}, error={}",
                    messageType(), e.getMessage(), e);
            return Mono.just(false);
        }
    }

    protected Mono<Integer> getClientType(String deviceId) {
        String clientTypeKey = String.format(RedisKeyConstant.RTC_DEVICE_CLIENT_TYPE, deviceId);
        return reactiveStringRedisTemplate.opsForValue().get(clientTypeKey)
                .map(Integer::parseInt)
                .switchIfEmpty(Mono.just(0))
                .doOnError(error -> log.error("获取设备客户端类型失败: deviceId={}, error={}",
                        deviceId, error.getMessage(), error));
    }

    /**
     * 根据条件获取酒店对应的用户ID列表
     *
     * @param hotelCode 酒店代码
     * @param type 设备类型
     * @param roomNo 房间号
     * @return 用户ID列表
     */
    protected Mono<List<String>> getUserIds(String hotelCode, String type, String roomNo) {
        if (StrUtil.isBlank(hotelCode)) {
            log.warn("获取用户ID失败: 酒店代码为空");
            return Mono.just(Collections.emptyList());
        }

        SearchDeviceReq req = buildDeviceInfoRequest(hotelCode, type);

        return hotelDsApiClient.getAvailableDevices(req)
                .map(deviceInfoEntities -> deviceInfoEntities.stream()
                        .filter(Objects::nonNull)
                        .map(DeviceRtcInfoResp::getRtcUserId)
                        .toList())
                .switchIfEmpty(Mono.defer(() -> {
                    log.info("未找到符合条件的设备: hotelCode={}, type={}, roomNo={}",
                            hotelCode, type, roomNo);
                    return Mono.just(Collections.emptyList());
                }))
                .doOnError(error -> log.error("查询设备信息失败: hotelCode={}, type={}, roomNo={}, error={}",
                        hotelCode, type, roomNo, error.getMessage(), error));
    }

    /**
     * Create callback message
     * @param deviceInfo Source device information
     * @return CallbackMessage
     */
    protected CallbackMessage createCallbackMessage(String roomId, DeviceInfoResp deviceInfo, Instructions instructions) {
        ServerMessageData serverMessageData = new ServerMessageData()
                .setRoomId(roomId)
                .setRoomCode(deviceInfo.getPositionFullName())
                .setUserId(deviceInfo.getRtcUserId());

        return new CallbackMessage()
                .setCommand(instructions.getCode())
                .setTimestamp(String.valueOf(System.currentTimeMillis()))
                .setData(serverMessageData);
    }


    /**
     * 构建设备信息请求对象
     *
     * @param hotelCode 酒店代码
     * @param type 设备类型
     * @return 设备信息请求对象
     */
    private SearchDeviceReq buildDeviceInfoRequest(String hotelCode, String type) {
        SearchDeviceReq searchDeviceReq = new SearchDeviceReq();
        searchDeviceReq.setDeviceType(type);
        searchDeviceReq.setHotelCode(hotelCode);
        searchDeviceReq.setEmployeeType(1);
        return searchDeviceReq;
    }

    /**
     * 向单个用户发送消息
     *
     * @param userId 用户ID
     * @param callbackMessage 回调消息
     * @return 完成信号
     */
    protected Mono<Void> sendDevice(String userId, CallbackMessage callbackMessage) {
        if (StrUtil.isBlank(userId) || callbackMessage == null) {
            log.warn("发送消息失败: userId为空或消息为空");
            return Mono.empty();
        }

        return sendDevice(Collections.singletonList(userId), callbackMessage);
    }

    /**
     * 向多个用户发送消息
     *
     * @param userIds 用户ID列表
     * @param callbackMessage 回调消息
     * @return 完成信号
     */
    protected Mono<Void> sendDevice(Collection<String> userIds, CallbackMessage callbackMessage) {
        if (userIds == null || userIds.isEmpty() || callbackMessage == null) {
            log.warn("发送消息失败: 用户列表为空或消息为空");
            return Mono.empty();
        }

        String message = JacksonUtils.writeValueAsString(callbackMessage);
        if (StrUtil.isBlank(message)) {
            log.warn("发送消息失败: 序列化消息为空");
            return Mono.empty();
        }

        log.debug("开始发送广播消息: userIds={}, messageType={}", userIds, callbackMessage);

        for (String userId : userIds) {
            rtcHelper.sendUnicastTextMessageAsync(userId, message)
                    .subscribeOn(rtcScheduler)
                    .doOnSuccess(result -> log.debug("发送广播消息成功: userId={}", userId))
                    .doOnError(error -> log.error("发送广播消息失败: userId={}, error={}",
                            userId, error.getMessage(), error))
                    .subscribe();
        }

        return Mono.empty();
    }
}