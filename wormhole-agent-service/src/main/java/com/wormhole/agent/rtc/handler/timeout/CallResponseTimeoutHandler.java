package com.wormhole.agent.rtc.handler.timeout;

import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.rtc.consts.CallTimeoutEnums;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.channel.consts.Instructions;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.Instant;

/**
 * @author: joker.liu
 * @date: 2025/4/27
 * @Description:
 */
@Component
public class CallResponseTimeoutHandler extends AbstractCallTimeoutHandler {

    @Override
    public String messageType() {
        return CallTimeoutEnums.CALL_RESPONSE_TIMEOUT.getType();
    }

    @Override
    protected Mono<Boolean> doHandle(CallOvertimeMessage message) {

        Mono<Double> scoreMono = reactiveStringRedisTemplate.opsForZSet().score(RedisKeyConstant.RTC_ROOM_ID_ACTIVE_KEY, message.getRtcRoomId());

        return rtcRoomCallService.getRoomCallInfo(message.getRtcRoomId())
                .flatMap(roomCallInfo -> {
                    if (roomCallInfo.getRowStatus() == 0) {
                        return reactiveStringRedisTemplate.opsForZSet().remove(RedisKeyConstant.RTC_ROOM_ID_ACTIVE_KEY, message.getRtcRoomId())
                                .thenReturn(Boolean.TRUE);
                    }
                    if (roomCallInfo.getHasAiParticipant() == 0 || roomCallInfo.getHasTransfer() == 1) {
                        return Mono.fromCallable(() -> true);
                    }
                    return scoreMono.flatMap(score -> {
                        long epochSecond = Instant.now().getEpochSecond();
                        log.info("call response timeout, roomId:{}, score:{}, now:{} result: {}", message.getRtcRoomId(), score.longValue(), epochSecond, epochSecond - score);
                        if (epochSecond - message.getTimeout() + 1 > score.longValue()) {
                            RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                                    .rtcRoomId(message.getRtcRoomId())
                                    .timestamp(epochSecond)
                                    .rtcUserId("99999")
                                    .username("system")
                                    .reason("call response timeout")
                                    .build();
                            return reactiveStringRedisTemplate.opsForZSet().remove(RedisKeyConstant.RTC_ROOM_ID_ACTIVE_KEY, message.getRtcRoomId())
                                    .then(Mono.defer(() -> {
                                        if (roomCallInfo.getHasAiParticipant() == 1 && roomCallInfo.getHasTransfer() == 0) {
                                            return executeTimeoutAgent(message, roomCallInfo);
                                        }
                                        return executeTimeout(message, roomCallInfo);
                                    })
                                    .then(rtcRoomCallService.calledNormalOver(rtcRoomCallDTO)));
                        }
                        return Mono.fromCallable(() -> true);
                    });
                });
    }

    @Override
    protected String timeoutCommand() {
        return Instructions.CALL_TIMEOUT.getCode();
    }
}
