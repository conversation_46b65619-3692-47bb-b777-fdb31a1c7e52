package com.wormhole.agent.rtc.handler.timeout;

import cn.hutool.core.date.SystemClock;
import com.wormhole.agent.entity.RtcRoomCallEntity;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.agent.service.ConnectionService;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.agent.util.RtcRoomParseUtil;
import com.wormhole.channel.consts.ChannelConstant;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ServerMessageData;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Scheduler;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: joker.liu
 * @date: 2025/4/27
 * @Description:
 */
public abstract class AbstractCallTimeoutHandler implements TimeoutMessageHandler{

    protected static final Logger log = LoggerFactory.getLogger(AbstractCallTimeoutHandler.class);

    @Resource
    protected RtcRoomCallService rtcRoomCallService;

    @Resource
    protected ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    @Resource
    protected RtcHelper rtcHelper;

    @Resource(name = ChannelConstant.RTC_POOL)
    protected Scheduler rtcScheduler;

    @Resource
    protected ConnectionService connectionService;

    @Override
    public boolean support(String eventType) {
        return messageType().equals(eventType);
    }

    @Override
    public Mono<Boolean> handle(CallOvertimeMessage message) {
        return doHandle(message);
    }

    protected abstract Mono<Boolean> doHandle(CallOvertimeMessage message);

    protected Mono<Boolean> executeTimeout(CallOvertimeMessage message, RtcRoomCallEntity roomCallInfo) {

        if (Objects.isNull(roomCallInfo)) {
            return Mono.empty();
        }
        String callKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, roomCallInfo.getInitiatorId());

        Mono<List<String>> userIdsMono = reactiveStringRedisTemplate.opsForSet().members(callKey).collectList();
        return userIdsMono.flatMap(userIds -> {

            log.info("executeTimeout userIds={}, roomId={}", userIds, roomCallInfo.getRtcRoomId());

            if (userIds.isEmpty()) {
                return Mono.empty();
            }

            List<String> bindKeys = userIds.stream()
                    .map(userId -> String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId))
                    .collect(Collectors.toList());
            String roomBindingKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, message.getDeviceId());

            bindKeys.add(roomBindingKey);
            bindKeys.add(callKey);

            return Mono.when(
                    reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_BUSY_USER_KEY, userIds.toArray(new Object[0])),
                    reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_DEVICE_KEY, userIds.toArray(new Object[0])),
                    reactiveStringRedisTemplate.delete(bindKeys.toArray(new String[0]))
            ).then(sendTimeout(userIds, roomCallInfo.getRtcRoomId()));
        })
        .thenReturn(true);
    }

    protected Mono<Boolean> executeTimeoutAgent(CallOvertimeMessage callOvertimeMessage, RtcRoomCallEntity rtcRoomCallEntity) {

        String deviceId = RtcRoomParseUtil.getDeviceId(callOvertimeMessage.getRtcRoomId());

        Mono<DeviceInfoResp> deviceInfoMono = connectionService.getDeviceInfo(deviceId, null);

        return deviceInfoMono.flatMap(deviceInfo -> {
            String deviceBingRoomKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, deviceInfo.getDeviceId());

            return Mono.when(
                    reactiveStringRedisTemplate.delete(deviceBingRoomKey),
                    reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_DEVICE_KEY, rtcRoomCallEntity.getInitiatorId()),
                    reactiveStringRedisTemplate.opsForSet().remove(RedisKeyConstant.ONLINE_BUSY_USER_KEY, rtcRoomCallEntity.getInitiatorId())
            ).then(sendTimeout(List.of(rtcRoomCallEntity.getInitiatorId()), rtcRoomCallEntity.getRtcRoomId()))
                    .thenReturn(Boolean.TRUE);
        });
    }

    private Mono<Void> sendTimeout(Collection<String> userIds, String roomId) {

        ServerMessageData messageData = ServerMessageData.createRejectMessage(roomId);
        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand(timeoutCommand())
                .setTimestamp(String.valueOf(SystemClock.now()))
                .setData(messageData);
        messageData.setMessage("call timeout");

        String message = JacksonUtils.writeValueAsString(callbackMessage);

        for (String userId : userIds) {
            rtcHelper.sendUnicastTextMessageAsync(userId, message)
                    .subscribeOn(rtcScheduler)
                    .doOnSuccess(result -> log.debug("发送超时消息成功: userId={}", userId))
                    .doOnError(error -> log.error("发送超时消息失败: userId={}, error={}",
                            userId, error.getMessage()))
                    .subscribe();
        }
        return Mono.empty();
    }

    protected abstract String timeoutCommand();

    protected Mono<Integer> getClientType(String deviceId) {
        String clientTypeKey = String.format(RedisKeyConstant.RTC_DEVICE_CLIENT_TYPE, deviceId);
        return reactiveStringRedisTemplate.opsForValue().get(clientTypeKey)
                .map(Integer::parseInt)
                .switchIfEmpty(Mono.just(0))
                .doOnError(error -> log.error("获取设备客户端类型失败: deviceId={}, error={}",
                        deviceId, error.getMessage(), error));
    }

}
