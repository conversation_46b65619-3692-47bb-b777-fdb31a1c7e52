package com.wormhole.agent.rtc.handler.command;

import com.wormhole.agent.dto.SaveConversationDTO;
import com.wormhole.agent.hotelds.HotelDsApiRpc;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.config.RtcVoiceChatProperties;
import com.wormhole.agent.service.ConnectionService;
import com.wormhole.agent.service.UserConversationService;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.common.util.HeaderUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/28
 * @Description:    创建新的会话
 */
@Component
@RequiredArgsConstructor
public class CleanUpHandler extends AbstractCommandClientMessageHandler {

    private final RtcVoiceChatProperties rtcVoiceChatProperties;
    private final UserConversationService userConversationService;
    private final ConnectionService connectionService;
    private final HotelDsApiRpc hotelDsApiRpc;

    @Override
    Mono<Boolean> doHandle(ClientMessageData messageData) {

        return Mono.zip(connectionService.getDeviceInfo(messageData.getDeviceId(), messageData.getClientType()),HeaderUtils.getHeaderInfo())
                .flatMap(tuple -> {
                    DeviceInfoResp deviceInfo = tuple.getT1();
                    String language = tuple.getT2().getLanguage();
                    SaveConversationDTO conversationDTO = buildConversationDTO(deviceInfo,language);
                    return userConversationService.createConversation(conversationDTO)
                            .flatMap(vo -> {
                                return hotelDsApiRpc.cleanDeviceTickets(vo.getHotelCode(), vo.getPositionCode())
                                        .onErrorResume(e -> {
                                            log.error("工单过期失败: hotelCode={}, positionCode={}", vo.getHotelCode(), vo.getPositionCode(), e);
                                            return Mono.just(false);
                                        })
                                        .thenReturn(true);
                            });
                });
    }

    @Override
    public String messageType() {
        return Instructions.CLEAN_ROOM.getCode();
    }

    private SaveConversationDTO buildConversationDTO(DeviceInfoResp deviceInfo, String language) {
        return  SaveConversationDTO.builder()
                    .userId(deviceInfo.getPositionCode())
                    .username(deviceInfo.getPositionCode())
                    .source(SourcePlatform.HOTEL.getCode())
                    .botCode(rtcVoiceChatProperties.getChatBot(language, deviceInfo.getDeviceId()))
                    .isDebug(false)
                    .hotelCode(deviceInfo.getHotelCode())
                    .deviceId(deviceInfo.getPositionCode())
                    .title(deviceInfo.getHotelCode())
                    .positionCode(deviceInfo.getPositionCode())
                    .create(Boolean.TRUE)
                    .build();
    }

}
