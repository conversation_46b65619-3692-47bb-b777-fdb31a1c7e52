package com.wormhole.agent.rtc.handler.event;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.rtc.config.RtcVoiceChatProperties;
import com.wormhole.agent.rtc.consts.CallTimeoutEnums;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.enums.EventType;
import com.wormhole.channel.consts.event.RoomCreateEvent;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.channel.consts.helper.RtcHttpHelper;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ServerMessageData;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.mq.producer.ReactiveMessageSender;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Instant;

/**
 * @author: joker.liu
 * @date: 2025/3/10
 * @Description:    房间创建
 */
@Component
@RequiredArgsConstructor
public class RoomCreateHandler extends AbstractEventHandler<RoomCreateEvent> {

    private static final Logger log = LoggerFactory.getLogger(RoomCreateHandler.class);

    private final RtcRoomCallService rtcRoomCallService;

    private final RtcHelper rtcHelper;

    private final RtcHttpHelper rtcHttpHelper;

    private final ReactiveMessageSender reactiveMessageSender;

    private final RtcVoiceChatProperties rtcVoiceChatProperties;

    @Value("${rocket.topic.call-overtime-delay-message:call_overtime_delay_message}")
    public String CALL_OVERTIME_DELAY_MESSAGE;

    @Override
    public String messageType() {
        return EventType.ROOM_CREATE.getEventType();
    }

    @Override
    RoomCreateEvent convert(Object message) {
        return JacksonUtils.convertValue(message, RoomCreateEvent.class);
    }

    @Override
    public Mono<Boolean> doHandle(RoomCreateEvent event) {
        RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder().rtcRoomId(event.getRoomId()).timestamp(event.getTimestamp()).hasAiParticipant(1).build();
        return rtcRoomCallService.create(rtcRoomCallDTO)
                .doOnNext(roomCallEntity -> log.info("create room call info success:{}", JacksonUtils.writeValueAsString(roomCallEntity)))
                .flatMap(rtcRoomCallEntity ->
                        Mono.when(
                                sendDeviceToAcceptUser(rtcRoomCallEntity.getInitiatorId(), rtcRoomCallEntity.getRtcRoomId()),
                                sendRoomReadyForAgent(rtcRoomCallEntity.getInitiatorId(), rtcRoomCallEntity.getRtcRoomId()),
                                rtcRoomCallService.openSubtitle(rtcRoomCallEntity.getRtcRoomId(), rtcRoomCallEntity.getRtcRoomId() + "-subtitle"),
                                sendCallTimeoutMessage(event),
                                sendCallUpperLimitTime(event),
                                reactiveStringRedisTemplate.opsForZSet().add(RedisKeyConstant.RTC_ROOM_ID_ACTIVE_KEY, event.getRoomId(), Instant.now().getEpochSecond()),
                                saveUserBindingRoom(rtcRoomCallEntity.getInitiatorId(), rtcRoomCallEntity.getRtcRoomId())

                        ).thenReturn(Boolean.TRUE)
                );
    }

    private Mono<Void> saveUserBindingRoom(String userId, String rtcRoomId) {
        String key = String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId);
        return reactiveStringRedisTemplate.opsForValue().get(key)
                .switchIfEmpty(Mono.just(userId))
                .flatMap(value -> reactiveStringRedisTemplate.opsForValue().set(key, rtcRoomId + StrUtil.COLON + userId).then());
    }

    private Mono<Void> sendCallTimeoutMessage(RoomCreateEvent event) {

        MessageBody messageBody = new MessageBody();
        messageBody.setAction(CallTimeoutEnums.CALL_RESPONSE_TIMEOUT.getType());
        messageBody.setTimestamp(DateUtil.current() + "");

        CallOvertimeMessage callOvertimeMessage = new CallOvertimeMessage();
        callOvertimeMessage.setRtcRoomId(event.getRoomId());
        callOvertimeMessage.setTimeout(rtcVoiceChatProperties.getServerTimeout());
        messageBody.setData(callOvertimeMessage);

        return reactiveMessageSender.sendDelayMessage(CALL_OVERTIME_DELAY_MESSAGE, messageBody, rtcVoiceChatProperties.getServerCallTimeoutDelayLevel())
                .doOnSuccess(aVoid -> log.info("sendCallOverTimeEvent success"))
                .doOnError(throwable -> log.error("sendCallOverTimeEvent error", throwable))
                .then();
    }

    private Mono<Void> sendCallUpperLimitTime(RoomCreateEvent event) {
        MessageBody messageBody = new MessageBody();
        messageBody.setAction(CallTimeoutEnums.CALL_DURATION_UPPER_LIMIT.getType());

        CallOvertimeMessage callOvertimeMessage = new CallOvertimeMessage();
        callOvertimeMessage.setRtcRoomId(event.getRoomId());
        messageBody.setData(callOvertimeMessage);

        return reactiveMessageSender.sendDelayMessage(CALL_OVERTIME_DELAY_MESSAGE, messageBody, rtcVoiceChatProperties.getServerCallDurationDelayLevel())
                .doOnSuccess(aVoid -> log.info("sendCallUpperLimitTime success"))
                .doOnError(throwable -> log.error("sendCallUpperLimitTime error", throwable))
                .then();
    }


    private Mono<Boolean> sendDeviceToAcceptUser(String callUserId, String roomId) {
        String callKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, callUserId);
        return reactiveStringRedisTemplate.opsForSet().members(callKey)
                .collectList()
                .flatMap(userIds -> {
                    if (userIds.isEmpty()) {
                        return Mono.just(true);
                    }
                    log.info("room_create sendRoomReadyForAgent userId={}, roomId={}, userIds={}", callUserId, roomId, userIds);
                    userIds.forEach(userId -> {
                        if (userId.equals(callUserId)) {
                            return;
                        }
                        sendRoomReadyForUser(userId, roomId).subscribeOn(Schedulers.boundedElastic()).subscribe();
                    });
                    return Mono.just(true);
                });
    }

    private Mono<Void> sendRoomReadyForAgent(String userId, String roomId) {
        ServerMessageData serverMessageData = new ServerMessageData();
        serverMessageData.setRoomId(roomId);

        CallbackMessage callbackMessage = new CallbackMessage()
                .setCommand(Instructions.CALL_ROOM_READY_FOR_AGENT.getCode())
                .setTimestamp(String.valueOf(System.currentTimeMillis()))
                .setData(serverMessageData);

        String message = JacksonUtils.writeValueAsString(callbackMessage);
        return rtcHelper.sendUnicastTextMessageAsync(userId, message)
                .doOnSuccess(result -> log.debug("create handler send message success: userId={}",  userId))
                .doOnError(error -> log.error("create handler send message error: userId={}, error={}", userId, error.getMessage(), error))
                .then();
    }

    private Mono<Void> sendRoomReadyForUser(String userId, String roomId) {
        return rtcHttpHelper.generateToken(roomId, userId)
                .flatMap(token -> {
                    ServerMessageData serverMessageData = new ServerMessageData();
                    serverMessageData.setRoomId(roomId);
                    serverMessageData.setToken(token);

                    CallbackMessage callbackMessage = new CallbackMessage()
                            .setCommand(Instructions.CALL_ROOM_READY_FOR_USER.getCode())
                            .setTimestamp(String.valueOf(System.currentTimeMillis()))
                            .setData(serverMessageData);

                    String message = JacksonUtils.writeValueAsString(callbackMessage);

                    log.info("create handler : userId={}, messageType={}", userId, callbackMessage);

                    return rtcHelper.sendUnicastTextMessageAsync(userId, message)
                            .doOnSuccess(result -> log.info("create handler send message success: userId={}",  userId))
                            .doOnError(error -> log.error("create handler send message error: userId={}, error={}", userId, error.getMessage(), error))
                            .then();
                });
    }
}
