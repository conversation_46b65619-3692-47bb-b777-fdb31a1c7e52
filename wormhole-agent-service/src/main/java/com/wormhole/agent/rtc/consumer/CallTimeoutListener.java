package com.wormhole.agent.rtc.consumer;

import com.wormhole.agent.rtc.handler.timeout.CallAcceptTimeoutHandler;
import com.wormhole.agent.rtc.handler.timeout.TimeoutEventHandleFactory;
import com.wormhole.agent.rtc.handler.timeout.TimeoutMessageHandler;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import lombok.RequiredArgsConstructor;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/4/23
 * @Description:
 */
@Component
@RocketMQMessageListener(topic = "${rocket.topic.call-overtime-delay-message:call_overtime_delay_message}",
        consumerGroup = "${rocketmq.consumer.call-overtime-group:agent_call_overtime_processor_group}",
        enableMsgTrace = true
)
@RequiredArgsConstructor
public class CallTimeoutListener extends AbstractReactiveMessageListener<MessageBody> {

    private static final Logger log = LoggerFactory.getLogger(CallTimeoutListener.class);

    private final TimeoutEventHandleFactory timeoutEventHandleFactory;

    @Override
    protected Mono<Void> processMessage(MessageBody messageBody) {

        log.info("CallTimeoutListener processMessage messageBody:{}", messageBody);

        CallOvertimeMessage callOvertimeMessage = JacksonUtils.convertValue(messageBody.getData(), CallOvertimeMessage.class);

        TimeoutMessageHandler eventHandler = timeoutEventHandleFactory.getEventHandler(messageBody.getAction());

        return eventHandler.handle(callOvertimeMessage).then();
    }
}
