package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.channel.consts.Instructions;
import org.springframework.stereotype.Component;

/**
 * @author: joker.liu
 * @date: 2025/4/24
 * @Description:
 */
@Component
public class InitTokenHandler extends CreateTokenHandler {

    @Override
    public String getRtcRoomId(DeviceInfoResp deviceInfo) {
        return "check-" + deviceInfo.getHotelCode() + StrUtil.UNDERLINE + deviceInfo.getPositionCode()
                + StrUtil.UNDERLINE + deviceInfo.getDeviceId() + StrUtil.UNDERLINE + System.currentTimeMillis();
    }

    @Override
    public String messageType() {
        return Instructions.INIT_TOKEN_REQ.getCode();
    }
}
