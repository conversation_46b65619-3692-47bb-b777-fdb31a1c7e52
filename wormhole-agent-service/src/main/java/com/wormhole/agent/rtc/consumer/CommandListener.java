package com.wormhole.agent.rtc.consumer;

import com.wormhole.agent.rtc.handler.command.CommandHandler;
import com.wormhole.agent.rtc.handler.command.CommandHandlerFactory;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.mq.consumer.AbstractReactiveMessageListener;
import jakarta.annotation.Resource;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Objects;

/**
 * @author: joker.liu
 * @date: 2025/3/17
 * @Description:
 */
@Component
@RocketMQMessageListener(topic = "${rocketmq.topic.channelCommand:channel_command}",
        consumerGroup = "${rocketmq.consumer.commandGroup:channel_agent_command_processor_group}",
        enableMsgTrace = true
)
public class CommandListener extends AbstractReactiveMessageListener<MessageBody> {

    private final Logger logger = LoggerFactory.getLogger(CommandListener.class);

    @Resource
    private CommandHandlerFactory commandHandlerFactory;

    @Override
    protected Mono<Void> processMessage(MessageBody payload) {

        CommandHandler messageHandler = commandHandlerFactory.getEventHandler(payload.getAction());

        return messageHandler.handle(payload)
                .flatMap(result -> {
                    if (result) {
                        return Mono.empty();
                    } else {
                        return Mono.error(new BusinessException("MESSAGE-PROCESSING-FAILURE", "Message processing failure"));
                    }
                });
    }

    public Mono<String> processCommand(Map<String, Object> inputParams) {

        logger.info("CommandListener processCommand inputParams:{}", inputParams);

        MessageBody messageBody = buildMessageBody(inputParams);
        if (Objects.equals(messageBody.getAction(), "no_command")) {
            return Mono.just("Message processing success");
        }

        CommandHandler messageHandler = commandHandlerFactory.getEventHandler(messageBody.getAction());

        return messageHandler.handle(messageBody)
                .flatMap(result -> {
                    if (result) {
                        return Mono.just("Message processing success");
                    } else {
                        return Mono.just("Message processing failure");
                    }
                });
    }

    private MessageBody buildMessageBody(Map<String, Object> inputParams) {
        MessageBody messageBody = new MessageBody();
        messageBody.setAction(inputParams.get("command").toString());
        messageBody.setTimestamp(String.valueOf(System.currentTimeMillis()));

        ClientMessageData data = new ClientMessageData();
        data.setDeviceId(inputParams.get("device_id").toString());

        messageBody.setData(data);
        return messageBody;
    }

}
