package com.wormhole.agent.rtc.consts;

/**
 * @author: joker.liu
 * @date: 2025/3/19
 * @Description:
 */
public interface RedisKeyConstant {

    /**
     * 在线设备，用来维持rtc状态
     */
    String ONLINE_DEVICE_KEY = "wormhole:rtc:online:device";

    /**
     * 锁，用来防止answer请求重复
     */
    String REDISSON_ANSWER_ROOM_KEY = "wormhole:answer:lock:%s";

    /**
     * 房间内用户列表维护,房间内通话中
     */
    String ONLINE_ROOM_USER_KEY = "wormhole:rtc:room:%s";

    /**
     * 通话中的用户列表维护，所有的包括响铃、通话中
     */
    String ONLINE_BUSY_USER_KEY = "wormhole:rtc:busy:user";

    /**
     * 设备信息维护，通过设备号获取
     */
    String DEVICE_INFO_DEVICE_KEY = "wormhole:device:info:device:%s";

    String DEVICE_INFO_USER_KEY = "wormhole:device:info:user:%s";

    /**
     * 通话状态维护，通过拨打人获取
     */
    String FROM_CALL_TO_HUMAN_KEY = "wormhole:from:call:to:human:%s";

    /**
     * 用户绑定房间，获取云端房间号   value: roomId:userId
     */
    String USER_BINDING_ROOM_KEY = "wormhole:user:binding:%s:room";


    String CONVERSATION_TICKETS_KEY = "wormhole:conversation:tickets:%s";

    /**
     * 设备与房间号的绑定
     */
    String DEVICE_BINDING_ROOM_KEY = "wormhole:device:binding:%s:room";

    /**
     * 房间活跃状态维护，如果超过一定的时间，没有流产生，自动挂断
     */
    String RTC_ROOM_ID_ACTIVE_KEY = "wormhole:rtc:room:active";

    /**
     * 对应的设备类型
     */
    String RTC_DEVICE_CLIENT_TYPE = "wormhole:rtc:device:client_type:%s";

    /**
     * 房间挂断标记
     */
    String ROOM_HANG_UP_MARK_KEY = "wormhole:rtc:hang_up:rtc_room_id:%s";
}
