package com.wormhole.agent.rtc.handler.command;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.entity.RtcRoomCallEntity;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.config.RtcVoiceChatProperties;
import com.wormhole.agent.rtc.consts.CallTimeoutEnums;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.message.CallbackMessage;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.channel.consts.request.RtcApiRequest;
import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.IdUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.mq.producer.ReactiveMessageSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @author: joker.liu
 * @date: 2025/3/11
 * @Description: 智能体启动处理器
 */
@Slf4j
@Component
public class StartAgentHandler extends AbstractCommandClientMessageHandler {

    private static final String DEFAULT_PROLOGUE = "您好，有什么帮助您的吗";
    private static final int MAX_ROOM_CAPACITY = 2;
    @Resource
    protected RtcVoiceChatProperties rtcVoiceChatProperties;
    @Resource
    protected RtcRoomCallService rtcRoomCallService;
    @Resource
    private ReactiveMessageSender reactiveMessageSender;

    @Value("${rocket.topic.call-overtime-delay-message:call_overtime_delay_message}")
    public String CALL_OVERTIME_DELAY_MESSAGE;

    @Override
    public String messageType() {
        return Instructions.START_AGENT.getCode();
    }

    @Override
    public boolean support(String messageType) {
        return messageType().equals(messageType);
    }

    @Override
    public Mono<Boolean> doHandle(ClientMessageData messageData) {
        log.info("开始处理启动智能体请求: deviceId={}", messageData.getDeviceId());
        String deviceBingRoomKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, messageData.getDeviceId());
        return reactiveStringRedisTemplate.opsForValue().get(deviceBingRoomKey)
                .flatMap(roomIdValue -> {
                    if (StringUtils.isBlank(roomIdValue)) {
                        return Mono.just(false);
                    }
                    messageData.setRoomId(roomIdValue);
                    return createHandlerPipeline(messageData)
                            .doOnSuccess(result -> log.info("启动智能体处理完成: deviceId={}, result={}",
                                    messageData.getDeviceId(), result))
                            .onErrorResume(e -> {
                                log.error("启动智能体处理失败: deviceId={}", messageData.getDeviceId(), e);
                                return Mono.just(false);
                            });
                });
    }

    protected Mono<Boolean> createHandlerPipeline(ClientMessageData messageData) {
        return connectionService.getDeviceInfo(messageData.getDeviceId(), messageData.getClientType())
                .flatMap(deviceInfo -> processDeviceInfo(deviceInfo, messageData))
                .switchIfEmpty(Mono.defer(() -> {
                    log.warn("设备信息不存在: deviceId={}", messageData.getDeviceId());
                    return Mono.just(false);
                }));
    }

    private Mono<Boolean> processDeviceInfo(DeviceInfoResp deviceInfo, ClientMessageData messageData) {
        return checkRoomAvailability(messageData.getRoomId())
                .flatMap(isAvailable -> {
                    if (!isAvailable) {
                        log.warn("房间已满: roomId={}", messageData.getRoomId());
                        return Mono.just(false);
                    }
                    return findOrCreateRoomChat(deviceInfo, messageData);
                });
    }

    private Mono<Boolean> checkRoomAvailability(String roomId) {
        String onlineRoomKey = String.format(RedisKeyConstant.ONLINE_ROOM_USER_KEY, roomId);
        return reactiveStringRedisTemplate.opsForSet().size(onlineRoomKey)
                .map(size -> size < MAX_ROOM_CAPACITY)
                .doOnNext(isAvailable -> {
                    if (!isAvailable) {
                        log.warn("房间人数已达上限: roomId={}", roomId);
                    }
                });
    }

    private Mono<Boolean> findOrCreateRoomChat(DeviceInfoResp deviceInfo, ClientMessageData messageData) {
        log.info("findOrCreateRoomChat deviceInfo:{} messageData: {}", deviceInfo, messageData);
        return rtcRoomCallService.getOnlineRoomCallInfo(messageData.getRoomId())
                .doOnNext(roomChatEntity -> log.info("findOrCreateRoomChat have call info: {}", roomChatEntity))
                .flatMap(roomChatEntity -> {
                    String chatUserId = IdUtils.generatePrefixId("chat");
                    RtcRoomCallDTO rtcRoomCallDTO = RtcRoomCallDTO.builder()
                            .rtcRoomId(roomChatEntity.getRtcRoomId())
                            .rtcUserId(deviceInfo.getRtcUserId())
                            .agentId(chatUserId)
                            .clientType(messageData.getClientType())
                            .build();
                    return Mono.when(
                            rtcRoomCallService.callAgent(rtcRoomCallDTO),startVoiceChat(deviceInfo, messageData, roomChatEntity, chatUserId))
                            .then(sendDelayMessage(roomChatEntity.getRtcRoomId()))
                            .thenReturn(true);
                })
                .switchIfEmpty(Mono.defer(() -> startAgentError(messageData.getRoomId(), deviceInfo)));
    }


    private Mono<Boolean> sendDelayMessage(String rtcRoomId) {
        MessageBody messageBody = new MessageBody();
        messageBody.setAction(CallTimeoutEnums.CALL_RESPONSE_TIMEOUT.getType());
        messageBody.setTimestamp(DateUtil.current() + "");

        CallOvertimeMessage callOvertimeMessage = new CallOvertimeMessage();
        callOvertimeMessage.setRtcRoomId(rtcRoomId);
        callOvertimeMessage.setTimeout(rtcVoiceChatProperties.getServerTimeout());
        messageBody.setData(callOvertimeMessage);
        return reactiveMessageSender.sendDelayMessage(CALL_OVERTIME_DELAY_MESSAGE, messageBody, rtcVoiceChatProperties.getServerCallTimeoutDelayLevel())
                .doOnError(e -> log.error("Failed to send subtitle message", e))
                .onErrorResume(e -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "Failed to send subtitle message")))
                .thenReturn(true);
    }


    private Mono<Boolean> startAgentError(String roomId, DeviceInfoResp deviceInfo) {
       CallbackMessage callbackMessage = createCallbackMessage(roomId, deviceInfo, Instructions.START_AGENT_ERROR);
        return sendDevice(deviceInfo.getRtcUserId(), callbackMessage)
                .thenReturn(false);
    }

    private Mono<Boolean> startVoiceChat(
            DeviceInfoResp deviceInfo,
            ClientMessageData messageData,
            RtcRoomCallEntity rtcRoomCallEntity,
            String chatUserId) {

        String llmUrl = buildLlmUrl(deviceInfo, rtcRoomCallEntity.getConversationId(), messageData, rtcRoomCallEntity.getRtcRoomId(), messageData.getClientType());

        String prologue = determinePrologue(messageData.getLanguage());
        log.info("startVoiceChat prologue: {}", prologue);

        RtcApiRequest startVoiceChatRequest = RtcApiRequest.createStartVoiceChatRequest(
                messageData.getRoomId(),
                rtcRoomCallEntity.getRtcRoomId(),
                messageData.getUserId(),
                chatUserId,
                llmUrl,
                rtcVoiceChatProperties.getVoiceType(),
                prologue,
                messageData.getLanguage()
        );
        log.info("开始启动智能体: startVoiceChatRequest={}", JacksonUtils.writeValueAsString(startVoiceChatRequest));
        return rtcHelper.startVoiceChatAsync(startVoiceChatRequest)
                .thenReturn(true);
    }

    private String buildLlmUrl(DeviceInfoResp deviceInfo, String conversationId, ClientMessageData messageData, String rtcRoomId, Integer clientType) {
        String deviceId = deviceInfo.getDeviceId();
        return String.format("%s?bot_code=%s&conversation_id=%s&hotel_code=%s&device_id=%s&room_no=%s&position_name=%s&source=%s&user-id=%s&rtc_room_id=%s&client_type=%s",
                rtcVoiceChatProperties.getLlmUrl(),
                rtcVoiceChatProperties.getChatBot(messageData.getLanguage(), deviceId),
                conversationId,
                deviceInfo.getHotelCode(),
                deviceInfo.getDeviceId(),
                deviceInfo.getPositionCode(),
                URLEncoder.encode(ttsPositionFullName(deviceInfo.getPositionFullName()), StandardCharsets.UTF_8),
                SourcePlatform.HOTEL.getCode(),
                messageData.getUserId(),
                rtcRoomId,
                clientType);
    }

    private String ttsPositionFullName(String positionFullName) {
        // 将 100 => 1·0·0
        char[] charArray = positionFullName.toCharArray();
        StringBuilder sb = new StringBuilder();
        for (char c : charArray) {
            sb.append(c).append("·");
        }
        return sb.substring(0, sb.length() - 1);
    }

    public String determinePrologue(String language) {
        if (StrUtil.isBlank(language)) {
            language = "zh_CN";
        }
        Map<String, String> prologuesMap = rtcVoiceChatProperties.getPrologues();
        log.info("determinePrologue language:{}, prologues:{}", language, prologuesMap);

        return prologuesMap.getOrDefault(language, DEFAULT_PROLOGUE);
    }

    /*private Mono<String> determinePrologue(String conversationId) {
        return userConversationService.findByConversationId(conversationId)
                .flatMap(conversationVO -> {
                    String onBoardingPrologue = Optional.ofNullable(conversationVO.getOnboardingInfo())
                            .map(info -> StrUtil.emptyToDefault(info.getPrologue(), DEFAULT_PROLOGUE))
                            .orElse(DEFAULT_PROLOGUE);
                    return Mono.just(onBoardingPrologue);
                });
    }*/

}