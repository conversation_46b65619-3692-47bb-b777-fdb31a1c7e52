package com.wormhole.agent.rtc.handler.command;

import com.wormhole.agent.hotelds.HotelDsApiRpc;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.TicketCommentMessage;
import com.wormhole.common.util.JacksonUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/19
 * @Description:
 */
@Component
@RequiredArgsConstructor
public class TicketVoteHandler extends AbstractCommandHandler<TicketCommentMessage> {

    private final HotelDsApiRpc hotelDsApiRpc;

    @Override
    TicketCommentMessage convert(Object message) {
        return JacksonUtils.convertValue(message, TicketCommentMessage.class);
    }

    @Override
    Mono<Boolean> handle(TicketCommentMessage messageData) {
        log.info("TicketVoteHandler messageData:{}", JacksonUtils.writeValueAsString(messageData));
        return hotelDsApiRpc.updateFeedbackStatus(messageData);
    }

    @Override
    public String messageType() {
        return Instructions.TICKET_LIKE_DISLIKE.getCode();
    }
}
