package com.wormhole.agent.rtc.handler.command;

import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.channel.consts.Instructions;
import com.wormhole.channel.consts.message.ClientMessageData;
import com.wormhole.common.util.JacksonUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/19
 * @Description:
 */
@Component
public class Heartbeat<PERSON>and<PERSON> extends AbstractCommandClientMessageHandler {

    @Override
    Mono<Boolean> doHandle(ClientMessageData messageData) {
        log.info("heartbeat messageData:{}", JacksonUtils.writeValueAsString(messageData));
        return reactiveStringRedisTemplate.opsForSet().add(RedisKeyConstant.ONLINE_DEVICE_KEY, messageData.getDeviceId())
                .thenReturn(Boolean.TRUE);
    }

    @Override
    public String messageType() {
        return Instructions.HEARTBEAT.getCode();
    }
}
