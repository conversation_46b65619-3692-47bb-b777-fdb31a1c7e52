package com.wormhole.agent.rtc.handler.event;

import com.google.common.collect.Maps;
import com.wormhole.agent.entity.RtcRoomCallEntity;
import com.wormhole.agent.log.analysis.VoiceChatMetricsAnalysis;
import com.wormhole.agent.rtc.config.RtcVoiceChatProperties;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.service.RtcRoomCallService;
import com.wormhole.agent.util.RtcRoomParseUtil;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.service.WorkflowService;
import com.wormhole.channel.consts.enums.EventType;
import com.wormhole.channel.consts.event.VoiceChatEvent;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.Map;

/**
 * @author: joker.liu
 * @date: 2025/3/13
 * @Description:
 */
@Slf4j
@Component
public class VoiceChatHandler extends AbstractEventHandler<VoiceChatEvent>{

    @Resource
    private VoiceChatMetricsAnalysis voiceChatMetricsAnalysis;
    @Resource
    private RtcRoomCallService rtcRoomCallService;
    @Resource
    private RtcVoiceChatProperties rtcVoiceChatProperties;
    @Resource
    private WorkflowService workflowService;


    @Override
    VoiceChatEvent convert(Object message) {
        return JacksonUtils.convertValue(message, VoiceChatEvent.class);
    }

    @Override
    Mono<Boolean> doHandle(VoiceChatEvent eventData) {
        log.info("voice chat info:{}", JacksonUtils.writeValueAsString(eventData));
        return Mono.when(
                voiceChatMetricsAnalysis.recordEvent(eventData),
                executeHangUpWorkflow(eventData)
        ).thenReturn(Boolean.TRUE);
    }

    private Mono<Void> executeHangUpWorkflow(VoiceChatEvent eventData) {

        String runStage = eventData.getRunStage();
        if (!runStage.equalsIgnoreCase("answerFinish")) {
            return Mono.empty();
        }

        String redisKey = String.format(RedisKeyConstant.ROOM_HANG_UP_MARK_KEY, eventData.getRoomId());

        return reactiveStringRedisTemplate.opsForValue().get(redisKey)
                .flatMap(operationTime -> {
                    log.info("execute hang up workflow, roomId={} time={}", eventData.getRoomId(), operationTime);

                    if (Long.parseLong(operationTime) > eventData.getEventTime()) {
                        return Mono.empty();
                    }
                    log.info("do execute hang up workflow, roomId={}", eventData.getRoomId());
                    Mono<RtcRoomCallEntity> onlineRoomCallInfoMono = rtcRoomCallService.getOnlineRoomCallInfo(eventData.getRoomId());
                    return onlineRoomCallInfoMono.flatMap(this::doExecuteHangUpWorkflow);
                })
                .then();

    }

    private Mono<Void> doExecuteHangUpWorkflow(RtcRoomCallEntity rtcRoomCallEntity) {

        Map<String, Object> initialInput = Maps.newHashMap();
        initialInput.put("USER_INPUT", "挂断通话");
        initialInput.put("conversation_id", rtcRoomCallEntity.getConversationId());
        initialInput.put("rtc_room_id", rtcRoomCallEntity.getRtcRoomId());
        initialInput.put("hotel_code", RtcRoomParseUtil.getHotelCode(rtcRoomCallEntity.getRtcRoomId()));
        initialInput.put("device_id", RtcRoomParseUtil.getDeviceId(rtcRoomCallEntity.getRtcRoomId()));
        initialInput.put("sys_device_id", RtcRoomParseUtil.getDeviceId(rtcRoomCallEntity.getRtcRoomId()));
        initialInput.put("position_code", rtcRoomCallEntity.getPositionCode());
        initialInput.put("client_type", rtcRoomCallEntity.getInitiatorClientType());
        initialInput.put("sys_user_name", rtcRoomCallEntity.getInitiatorName());
        initialInput.put("sys_user_id", rtcRoomCallEntity.getInitiatorId());

        // 构建工作流上下文
        WorkflowContext workflowContext = WorkflowContext.builder()
                .workflowCode(rtcVoiceChatProperties.getAutoHangUpWorkflow())
                .initialInput(initialInput)
                .isDebug(false)
                .sinks(Sinks.many().multicast().onBackpressureBuffer())
                .build();

        // 执行工作流，但不等待其结果，而是使用onErrorResume处理任何错误
        Mono<Void> workflowExecution = workflowService.executeWorkflow(workflowContext)
                .then()
                .onErrorResume(e -> {
                    log.error("执行挂断工作流失败", e);
                    return Mono.empty();
                });

        return workflowExecution.then();
    }

    @Override
    public String messageType() {
        return EventType.VOICE_CHAT.getEventType();
    }

}
