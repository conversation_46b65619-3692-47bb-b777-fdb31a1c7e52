package com.wormhole.agent.rtc.config;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;
import java.util.Map;

/**
 * @author: xiekangchen
 * @date: 2025/3/18
 * @Description:
 */
@Data
@RefreshScope
@Slf4j
@ConfigurationProperties(prefix = "wormhole.voicechat")
public class RtcVoiceChatProperties {

    private String botCode;

    private String voiceType;

    private String llmUrl;

    private Map<String, String> prologues = Map.of();
    /**
     * 挂断执行的工作流
     */
    private String hangUpWorkflow;
    /**
     * 国际版挂断执行工作流
     */
    private String i18nHangUpWorkflow;

    /**
     * 自动挂断的工作流
     */
    private String autoHangUpWorkflow;

    /**
     * v2版本的bot
     */
    private String v2Bot;

    /**
     * 国际版版本bot
     */
    private String i18nBot;
    /**
     * 走v2版本的设备白名单
     */
    private List<String> deviceWhiteList = Lists.newArrayList();

    /**
     * 超时时间处理
     */
    private Integer clientTimeout = 30;

    private Integer serverTimeout = 180;

    private Integer serverCallTimeoutDelayLevel = 7;

    private Integer serverCallDurationDelayLevel = 16;

    private Integer txtInterval = 10;

    public String getChatBot(String language, String device){
        log.info("RtcVoiceChatProperties.getChatBot device:{} ,language:{}",device,language);
        if (StrUtil.isNotBlank(language) && ObjectUtil.notEqual(language, "zh_CN") && ObjectUtil.notEqual(language, "cn")) {
            log.info("device切换国际版bot {} {}",device,language);
            return i18nBot;
        }
        return deviceWhiteList.contains(device) ? v2Bot : botCode;
    }

    public String getWorkFlowCodeAfterHangUp(String language){
        log.info("RtcVoiceChatProperties.getWorkFlowCodeAfterHangUp language:{}",language);
        if (StrUtil.isNotBlank(language) && ObjectUtil.notEqual(language, "zh_CN") && ObjectUtil.notEqual(language, "cn")) {
            log.info("getWorkFlowCodeAfterHangUpe切换国际版 {} ",language);
            return i18nHangUpWorkflow;
        }
        return hangUpWorkflow;
    }

}
