package com.wormhole.agent.util;

import com.wormhole.agent.client.chat.mcp.dto.McpClientRequest;
import com.wormhole.agent.tool.mcp.config.McpProperties;

public class McpUtils {

    /**
     * 转换为McpClientConfig
     */
    public static McpProperties.McpClientConfig toMcpClientConfig( McpClientRequest request) {
        McpProperties.McpClientConfig config = new McpProperties.McpClientConfig();
        config.setName(request.getName());
        config.setTransport(request.getTransport());
        config.setStatus(request.getStatus());
        config.setDescription(request.getDescription());
        config.setUrl(request.getUrl());
        config.setCommand(request.getCommand());
        config.setTimeout(request.getTimeoutSeconds().intValue());
        config.setLogEvents(request.getLogEvents() != null ? request.getLogEvents() : false);
        config.setMetadata(request.getMetadata());

        return config;
    }

    /**
     * 从McpClientConfig创建
     */
    public static McpClientRequest fromMcpClientConfig(McpProperties.McpClientConfig config) {
        if (config == null) {
            return null;
        }

        return McpClientRequest.builder()
                .name(config.getName())
                .transport(config.getTransport())
                .status(config.getStatus())
                .description(config.getDescription())
                .url(config.getUrl())
                .command(config.getCommand())
                .timeoutSeconds(config.getTimeoutDuration() != null ?
                        config.getTimeoutDuration().getSeconds() : 30L)
                .logEvents(config.isLogEvents())
                .metadata(config.getMetadata())
                .build();
    }
}
