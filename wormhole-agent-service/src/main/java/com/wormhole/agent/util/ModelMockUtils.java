package com.wormhole.agent.util;

import com.wormhole.agent.ai.model.*;
import com.wormhole.agent.core.model.*;
import com.wormhole.agent.vo.*;

import java.util.*;
import java.util.stream.*;

public class ModelMockUtils {
    /**
     * rerank模型
     */
    private static final List<UnifiedModelEnum> RERANK_MODEL_LIST = List.of(
            UnifiedModelEnum.SF_BAAI_BGE_RERANKER_V2_M3,
            UnifiedModelEnum.SF_NETEASE_YOUDAO_BCE_RERANKER_BASE_V1,
            UnifiedModelEnum.JINA_RERANKER_V2_BASE_MULTILINGUAL
    );

    /**
     * 排除的模型
     */
    private static final List<UnifiedModelEnum> EXCLUDE_MODEL_LIST = List.of(
            UnifiedModelEnum.TEXT_EMBEDDING_3_LARGE,
            UnifiedModelEnum.TEXT_EMBEDDING_3_SMALL,
            UnifiedModelEnum.QWEN_TEXT_EMBEDDING_V2,
            UnifiedModelEnum.QWEN_TEXT_EMBEDDING_V3,
            UnifiedModelEnum.ALIYUN_BGE_RERANK,
            UnifiedModelEnum.DOUBAO_EMBEDDING_TEXT_240715,
            UnifiedModelEnum.DOUBAO_EMBEDDING_LARGE_TEXT_240915,
            UnifiedModelEnum.JINA_SEGMENT,
//            UnifiedModelEnum.JINA_EMBEDDINGS_V3,
            UnifiedModelEnum.O1_PREVIEW_2024_09_12,
            UnifiedModelEnum.O1_MINI_2024_09_12,

            UnifiedModelEnum.ALIYUN_DEEPSEEK_R1_DISTILL_QWEN_1_5B,
            UnifiedModelEnum.ALIYUN_DEEPSEEK_R1_DISTILL_QWEN_7B,
            UnifiedModelEnum.ALIYUN_DEEPSEEK_R1_DISTILL_QWEN_14B,
            UnifiedModelEnum.ALIYUN_DEEPSEEK_R1_DISTILL_QWEN_32B,
            UnifiedModelEnum.ALIYUN_DEEPSEEK_R1_DISTILL_LLAMA_70B

    );

    public static List<ModelInfoVO> chatModelList() {
        return Arrays.stream(UnifiedModelEnum.values())
                .filter(model -> !RERANK_MODEL_LIST.contains(model) && !EXCLUDE_MODEL_LIST.contains(model))
                .filter(model -> !ModelProviderEnum.SILICONFLOW.equals(model.getModelProvider())) // 过滤掉Siliconflow的模型
                .map(ModelMockUtils::convert)
                .collect(Collectors.toList());
    }

    public static List<ModelInfoVO> rerankModelList() {
        return RERANK_MODEL_LIST.stream().map(ModelMockUtils::convert).collect(Collectors.toList());
    }

    public static ModelInfoVO convert(UnifiedModelEnum model) {
        return ModelInfoVO.builder()
                .model(model.getModel())
                .modelName(model.getModelName())
                .modelProvider(model.getModelProvider().getProvider())
                .modelProviderName(model.getModelProvider().getProviderName())
                .modelProviderDesc(model.getModelProvider().getDesc())
                .build();
    }


    public static void main(String[] args) {

        System.out.println(chatModelList());
        System.out.println("-------");
        System.out.println(rerankModelList());


    }
}
