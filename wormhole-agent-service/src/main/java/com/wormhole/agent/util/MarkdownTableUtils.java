package com.wormhole.agent.util;

import com.google.common.base.Joiner;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * MarkdownTableUtils
 *
 * <AUTHOR>
 * @version 2025/4/16
 */
public class MarkdownTableUtils {

    public static String convertToMarkdownTable(List<Map<String, Object>> dataList, List<String> headers) {
        return convertToMarkdownTable(null, 2, dataList, headers);
    }

    /**
     * Converts a list of maps to a Markdown table string with specified column headers.
     *
     * @param title    title
     * @param dataList The list of data maps.
     * @param headers  The headers for the table with their corresponding key in the map as the value.
     * @return A string representing the markdown table.
     */
    public static String convertToMarkdownTable(String title, int titleLevel, List<Map<String, Object>> dataList, List<String> headers) {
        if (CollectionUtils.isEmpty(dataList) || CollectionUtils.isEmpty(headers)) {
            return StringUtils.EMPTY;
        }
        // Start building the table string
        StringBuilder table = new StringBuilder();

        // Add header row
        String headerRow = headers.stream().collect(Collectors.joining(" | "));
        table.append("| ").append(headerRow).append(" |\n");

        // Add separator row
        String separatorRow = headers.stream().map(key -> "---").collect(Collectors.joining(" | "));
        table.append("| ").append(separatorRow).append(" |\n");

        // Add data rows
        for (Map<String, Object> row : dataList) {
            String dataRow = headers.stream()
                    .map(field -> {
                        Object object = MapUtils.getObject(row, field);
                        if (Objects.nonNull(object)) {
                            return object.toString();
                        }
                        return StringUtils.EMPTY;
                    })
                    .collect(Collectors.joining(" | "));
            table.append("| ").append(dataRow).append(" |\n");
        }
        if (StringUtils.isBlank(title)) {
            return table.toString();
        }
        String level = StringUtils.repeat("#", titleLevel);
        return Joiner.on("\n\n").join(String.format("%s %s", level, title), table.toString());
    }

    public static String sanitizeForRagTable(String text) {
        if (StringUtils.isEmpty(text)) {
            return StringUtils.EMPTY;
        }
        // 1. 移除换行符和多余空白
        text = text.replaceAll("\\r?\\n", " ").replaceAll("\\s+", " ").trim();
        // 2. 转义HTML实体（防止HTML注入）
        text = StringEscapeUtils.escapeHtml4(text);
        // 3. 转义Markdown表格关键字符（重点是处理表格分隔符）
        text = text.replace("|", "\\|");
        // 4. 有选择地转义其他可能干扰Markdown的字符
        text = text.replace("*", "\\*")
                .replace("_", "\\_")
                .replace("`", "\\`");
        return text;
    }
}
