package com.wormhole.agent.util;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 随机过渡语
 */
public class TransitionPhraseUtil {
    private static final Random random = new Random();
    public static final List<String> TRANSITION_PHRASES = Arrays.asList(
            "嗯好的，稍等一下，我帮您看看。",
            "明白了，请稍候，我来为您处理。",
            "好的，让我为您查询一下。",
            "收到，请给我一点时间处理。",
            "了解，我马上为您安排。",
            "好的，请稍等片刻。",
            "没问题，我立即为您确认。",
            "好的，正在为您处理。",
            "收到您的请求，马上为您安排。",
            "好的，我来协助您解决这个问题。",
            "这个需求我记下了，请稍等。",
            "我明白您的需要，正在处理中。",
            "好的，我这就为您安排。",
            "收到，请允许我花一点时间来处理。",
            "好的，我来看看怎么帮您解决。",
            "了解您的需求，正在为您准备。",
            "好的，我来帮您处理这件事。",
            "没问题，我会立即跟进。",
            "收到您的信息，正在为您安排。",
            "好的，我来为您解决这个问题。",
            "我理解您的需求，马上为您处理。",
            "好的，请稍等，我来为您确认一下。",
            "收到，我会尽快为您安排。",
            "明白了，我来为您解决这个问题。",
            "好的，让我来协助您完成这个请求。",
            "这个我来处理，请您稍等一下。",
            "好的，我立即为您查看。",
            "我了解了，正在为您安排相关服务。",
            "收到您的要求，马上为您办理。",
            "好的，我来看看如何能最好地帮到您。"
    );

    // 获取随机过渡语
    public static String getRandomTransitionPhrase() {
        int index = random.nextInt(TRANSITION_PHRASES.size());
        return TRANSITION_PHRASES.get(index);
    }
}
