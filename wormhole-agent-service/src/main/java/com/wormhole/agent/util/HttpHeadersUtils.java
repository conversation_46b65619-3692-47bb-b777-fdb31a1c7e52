package com.wormhole.agent.util;

import com.wormhole.agent.chat.model.HttpHeaders;
import com.wormhole.common.util.JacksonUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

public class HttpHeadersUtils {

    public static HttpHeaders getHttpHeaders(org.springframework.http.HttpHeaders httpHeaders) {
        Map<String, String> httpHeadersMap = getHttpHeadersMap(httpHeaders);
        return JacksonUtils.readValue(JacksonUtils.writeValueAsString(httpHeadersMap), HttpHeaders.class);
    }

    public static Map<String, String> getHttpHeadersMap(org.springframework.http.HttpHeaders httpHeaders) {
        if (MapUtils.isEmpty(httpHeaders)) {
            return Collections.emptyMap();
        }
        return httpHeaders.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().isEmpty() ? StringUtils.EMPTY : entry.getValue().get(0)));
    }
}
