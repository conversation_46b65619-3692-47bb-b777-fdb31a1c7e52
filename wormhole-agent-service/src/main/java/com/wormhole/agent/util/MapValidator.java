package com.wormhole.agent.util;

import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MapValidator {

    /**
     * 校验Map的有效性，收集所有问题并统一抛出异常
     * @param map 待校验的Map
     * @throws BusinessException 当Map无效时抛出业务异常，包含所有错误信息
     */
    public static void validateMapAndThrow(Map<String, Object> map) {
        List<String> errors = new ArrayList<>();

        // 校验Map本身
        if (map == null) {
//            throw new BusinessException(ResultCode.INVALID_PARAMETER, "参数Map不能为空");
            return;
        }

        if (map.isEmpty()) {
//            throw new BusinessException(ResultCode.INVALID_PARAMETER, "参数Map不能为空");
            return;
        }

        // 收集所有key-value的问题
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            // 校验key
            if (key == null) {
                errors.add("参数名不能为空");
            } else if (key.trim().isEmpty()) {
                errors.add("参数名不能为空");
            } else {
                // key有效时才校验value（避免重复报错）
                if (value == null) {
                    errors.add("key[" + key + "]对应的value不能为空");
                } else if (value instanceof String && ((String) value).trim().isEmpty()) {
                    errors.add("key[" + key + "]对应的value不能为空");
                }
            }
        }

        // 如果有错误，汇总后抛出异常
        if (!errors.isEmpty()) {
            String errorMessage = String.join("; ", errors);
            throw new BusinessException(ResultCode.INVALID_PARAMETER, errorMessage);
        }
    }

    /**
     * 校验Map的有效性（重载方法，支持自定义参数名称）
     * @param map 待校验的Map
     * @param paramName 参数名称，用于错误信息中标识
     * @throws BusinessException 当Map无效时抛出业务异常
     */
    public static void validateMapAndThrow(Map<String, Object> map, String paramName) {
        List<String> errors = new ArrayList<>();
        String prefix = (paramName != null && !paramName.isEmpty()) ? paramName : "参数";

        if (map == null) {
            throw new BusinessException(ResultCode.INVALID_PARAMETER, prefix + "不能为空");
        }

        if (map.isEmpty()) {
            throw new BusinessException(ResultCode.INVALID_PARAMETER, prefix + "不能为空");
        }

        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (key == null) {
                errors.add(prefix + "中存在空的key");
            } else if (key.trim().isEmpty()) {
                errors.add(prefix + "中存在空的key");
            } else {
                if (value == null) {
                    errors.add(prefix + "中key[" + key + "]对应的value不能为空");
                } else if (value instanceof String && ((String) value).trim().isEmpty()) {
                    errors.add(prefix + "中key[" + key + "]对应的value不能为空");
                }
            }
        }

        if (!errors.isEmpty()) {
            String errorMessage = String.join("; ", errors);
            throw new BusinessException(ResultCode.INVALID_PARAMETER, errorMessage);
        }
    }

    /**
     * 批量校验多个Map
     * @param maps 待校验的Map列表
     * @param paramNames 对应的参数名称列表（可选）
     * @throws BusinessException 当有Map无效时抛出业务异常
     */
    public static void validateMapsAndThrow(List<Map<String, Object>> maps, List<String> paramNames) {
        if (maps == null || maps.isEmpty()) {
            return;
        }

        List<String> allErrors = new ArrayList<>();

        for (int i = 0; i < maps.size(); i++) {
            Map<String, Object> map = maps.get(i);
            String paramName = (paramNames != null && i < paramNames.size())
                    ? paramNames.get(i) : "参数[" + i + "]";

            try {
                validateMapAndThrow(map, paramName);
            } catch (BusinessException e) {
                allErrors.add(e.getMessage());
            }
        }

        if (!allErrors.isEmpty()) {
            String errorMessage = String.join("; ", allErrors);
            throw new BusinessException(ResultCode.INVALID_PARAMETER, errorMessage);
        }
    }
}

