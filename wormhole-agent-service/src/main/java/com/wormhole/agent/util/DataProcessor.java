package com.wormhole.agent.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import com.wormhole.common.util.JacksonUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataProcessor {

    public static Map<String, Object> convertToResultMap(String content, Node node, LlmInputs.LlmParams llmParams) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Node.Output> outputs = node.getData().getOutputs();
        // llmParams 的解析先移除，变成自动解析

        // 尝试自动检测content的格式
        if (DataProcessor.isValidJsonFormat(content)) {
            // 如果是有效的JSON，直接解析成Map
            try {
                resultMap.putAll(JacksonUtils.readValue(content, new TypeReference<Map<String, Object>>() {}));
            } catch (Exception e) {
                // 解析失败，回退到文本处理
                outputs.forEach(key -> resultMap.put(key.getName(), content));
            }
        } else {
            // 不是JSON格式，作为纯文本/Markdown处理
            outputs.forEach(key -> resultMap.put(key.getName(), content));
        }
        return resultMap;
    }

    // 辅助方法：检测字符串是否为有效的JSON
    public static boolean isValidJsonFormat(String jsonString) {
        if (StringUtils.isBlank(jsonString)) {
            return false;
        }

        jsonString = jsonString.trim();
        // 检查是否以{ 开头和以 }结尾，或者以[ 开头和以 ]结尾
        boolean startsWithBrace = jsonString.startsWith("{") && jsonString.endsWith("}");
        boolean startsWithBracket = jsonString.startsWith("[") && jsonString.endsWith("]");

        if (!startsWithBrace && !startsWithBracket) {
            return false; // 不是JSON对象或数组
        }

        try {
            // 尝试用Jackson解析，如果成功则是有效的JSON
            JacksonUtils.readValue(jsonString, Object.class);
            return true;
        } catch (Exception e) {
            return false; // 解析失败，不是有效的JSON
        }
    }
}
