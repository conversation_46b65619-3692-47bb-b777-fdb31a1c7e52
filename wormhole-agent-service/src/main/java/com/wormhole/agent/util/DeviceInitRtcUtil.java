package com.wormhole.agent.util;

import cn.hutool.core.util.StrUtil;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;

import java.util.Arrays;

public class DeviceInitRtcUtil {

    public static String getRtcRoomId(String hotelCode, String positionCode, String deviceId) {
        return hotelCode + StrUtil.UNDERLINE + positionCode + StrUtil.UNDERLINE + deviceId;
    }

    public static String getRtcUserId(String deviceType, String deviceId) {
        return  deviceType + StrUtil.UNDERLINE + deviceId;
    }

    public static String getDeviceIdFromRtcUserId(String rtcUserId, String deviceType) {
        if (rtcUserId == null || deviceType == null) {
            return null;
        }

        String prefix = deviceType + StrUtil.UNDERLINE;

        if (rtcUserId.startsWith(prefix)) {
            return rtcUserId.substring(prefix.length());
        }

        return null;
    }

    public static String getDeviceIdByRtcUserId(String rtcUserId){
       return Arrays.stream(DeviceTypeEnum.values())
                .filter(deviceTypeEnum -> rtcUserId.startsWith(deviceTypeEnum.getCode() + StrUtil.UNDERLINE))
                .findFirst()
                .map(deviceTypeEnum -> rtcUserId.substring(deviceTypeEnum.getCode().length() + 1)).orElse(rtcUserId);
    }
}