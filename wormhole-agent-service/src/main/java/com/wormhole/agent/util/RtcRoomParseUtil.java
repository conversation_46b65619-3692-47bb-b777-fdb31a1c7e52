package com.wormhole.agent.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 根据room_id获取相关信息
 * <AUTHOR>
 */
public class RtcRoomParseUtil {

    // 匹配模式：hotelCode_positionCode_deviceId_timestamp
    private final static Pattern ROOM_ID_PATTERN = Pattern.compile("^([^_]+)+_([^_]+)+_(.+)_(\\d+)$");

    private final static Pattern USER_ID_PATTERN = Pattern.compile("^(.+)+_(.+)$");

    /**
     * 从房间ID中获取酒店编号
     * @param roomId 房间ID，格式如：HEB3ZM4_DPOKN1C3_DCD02AFB-7F25-4811-A9B1-0E3D42BF3E6B_1745985134289
     * @return 酒店编号，如果格式不匹配则返回空字符串
     */
    public static String getHotelCode(String roomId) {
        Matcher matcher = ROOM_ID_PATTERN.matcher(roomId);
        return matcher.matches() ? matcher.group(1) : "";
    }

    /**
     * 从房间ID中获取位置编号
     * @param roomId 房间ID
     * @return 位置编号，如果格式不匹配则返回空字符串
     */
    public static String getPositionCode(String roomId) {
        Matcher matcher = ROOM_ID_PATTERN.matcher(roomId);
        return matcher.matches() ? matcher.group(2) : "";
    }

    /**
     * 从房间ID中获取设备号
     * @param roomId 房间ID
     * @return 设备号，如果格式不匹配则返回空字符串
     */
    public static String getDeviceId(String roomId) {
        Matcher matcher = ROOM_ID_PATTERN.matcher(roomId);
        return matcher.matches() ? matcher.group(3) : "";
    }

    public static String getDeviceIdByUserId(String userId) {
        Matcher matcher = USER_ID_PATTERN.matcher(userId);
        return matcher.matches() ? matcher.group(2) : "";
    }

    /**
     * 从房间ID中获取时间戳
     * @param roomId 房间ID
     * @return 时间戳，如果格式不匹配则返回空字符串
     */
    public static String getTimestamp(String roomId) {
        Matcher matcher = ROOM_ID_PATTERN.matcher(roomId);
        return matcher.matches() ? matcher.group(4) : "";
    }

    /**
     * 从房间ID中解析所有信息
     * @param roomId 房间ID
     * @return 包含所有解析信息的Map，如果格式不匹配则返回空Map
     */
    public static Map<String, String> parseRoomId(String roomId) {
        Map<String, String> result = new HashMap<>();
        Matcher matcher = ROOM_ID_PATTERN.matcher(roomId);

        if (matcher.matches()) {
            result.put("hotelCode", matcher.group(1));
            result.put("positionCode", matcher.group(2));
            result.put("deviceId", matcher.group(3));
            result.put("timestamp", matcher.group(4));
        }

        return result;
    }
}
