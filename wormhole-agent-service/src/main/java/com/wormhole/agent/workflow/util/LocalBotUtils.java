package com.wormhole.agent.workflow.util;

import com.google.common.collect.ImmutableMap;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.common.util.JacksonUtils;

import java.util.List;
import java.util.Optional;

/**
 * BaseBot
 *
 * <AUTHOR>
 * @version 2024/12/9
 */
public class LocalBotUtils {

    public static Node.InputParameter getLiteralInputParameter(String inputKey, String content) {
        String template = """
                {
                    "name": "${inputKey}",
                    "input": {
                        "type": "string",
                        "value": {
                            "type": "literal",
                            "content": "${content}"
                        }
                    }
                }
                """;
        String json = FreeMarkerTemplateCacheUtils.processTemplateIntoString(template, ImmutableMap.of(
                "inputKey", inputKey, "content", content
        ));
        return JacksonUtils.readValue(json, Node.InputParameter.class);
    }

    public static Node.InputParameter getRefInputParameter(String inputKey, Node node, String refKey) {
        List<Node.Output> outputs = node.getData().getOutputs();
        Optional<Node.Output> first = outputs.stream().filter(output -> output.getName().equalsIgnoreCase(refKey)).findFirst();
        String template = """
                {
                    "name": "${inputKey}",
                    "input": {
                        "type": "${type}",
                        "value": {
                            "type": "ref",
                            "content": {
                                "block_id": "${block_id}",
                                "name": "${name}"
                            }
                        }
                    }
                }
                """;
        String json = FreeMarkerTemplateCacheUtils.processTemplateIntoString(template, ImmutableMap.of(
                "inputKey", inputKey, "type", first.get().getType(), "block_id", node.getId(), "name", refKey
        ));
        return JacksonUtils.readValue(json, Node.InputParameter.class);
    }

}