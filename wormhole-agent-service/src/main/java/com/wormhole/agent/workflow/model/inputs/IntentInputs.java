package com.wormhole.agent.workflow.model.inputs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.workflow.model.Node;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * IntentInputs
 *
 * <AUTHOR>
 * @version 2024/10/24
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class IntentInputs extends Node.Inputs {

    private LlmParams llmParams;

    private List<Intent> intents;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class LlmParams {

        private String modelProvider;
        /**
         * balance
         */
        private String generationDiversity;

        @Builder.Default
        private boolean enableChatHistory = false;
        /**
         * text/markdown/json
         */
        private String responseFormat;
        private String model;
        private Double temperature;
        private Double frequencyPenalty;
        /**
         * maxTokens
         */
        private Integer maxTokens;
        /**
         * 系统提示词
         */
        private String systemPrompt;
        /**
         * 用户提示词
         */
        private String userPrompt;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Intent {

        private String name;

    }

    /**
     * 意图识别分类结果
     * classificationId=0 对应sourcePortId=default
     * [
     * {"classificationId":0, "reason": "Other intentions"},
     * {"classificationId":1, "reason":"intent1"}
     * ]
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ClassificationResult {
        private Integer classificationId;
        private String reason;
    }

}