package com.wormhole.agent.workflow.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.jayway.jsonpath.JsonPath;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.response.vo.WorkflowNodeSimpleVO;
import com.wormhole.agent.core.constant.AiConstant;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.log.metrics.PerformanceMonitorUtils;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.OpenAiFunction;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.workflow.ModelRequestInfo;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.InputValueTypeEnum;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeExecutionStatus;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.SchemaTypeEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.common.util.DateParsePatterns;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.LocalDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * WorkflowUtils
 *
 * <AUTHOR>
 * @version 2024/11/28
 */
@Slf4j
public class WorkflowUtils {

    public static String getBotUserInput(WorkflowContext workflowContext) {
        Map<String, Object> initialInput = workflowContext.getInitialInput();
        return MapUtils.getString(initialInput, WorkflowConstant.USER_INPUT);
    }

    public static String getEnhancedQuery(WorkflowContext workflowContext) {
        Map<String, Object> initialInput = workflowContext.getInitialInput();
        return MapUtils.getString(initialInput, WorkflowConstant.ENHANCED_QUERY);
    }

    public static <T extends Node.Inputs> T getInputs(Object inputs, Class<T> clazz) {
        if (clazz.isInstance(inputs)) {
            // 代码构建场景
            return clazz.cast(inputs);
        }
        // json转换场景
        String json = JacksonUtils.writeValueAsString(inputs);
        return JacksonUtils.readValue(json, clazz);
    }

    /**
     * 根据 inputParameters 中的定义读取上游节点的变量值, key为用户定义的
     *
     * @param inputs
     * @param workflowContext
     * @return
     */
    public static Map<String, Object> getInputParametersValue(Node.Inputs inputs, WorkflowContext workflowContext) {
        List<Node.InputParameter> inputParameters = inputs.getInputParameters();
        return getInputParametersValue(inputParameters, workflowContext);
    }

    public static Map<String, Object> getInputParametersValue(List<Node.InputParameter> inputParameters, WorkflowContext workflowContext) {
        Map<String, Object> resultMap = Maps.newHashMap();

        if (CollectionUtils.isEmpty(inputParameters)) {
            return resultMap;
        }
        inputParameters.forEach(item -> {
            if (Objects.isNull(item) || Objects.isNull(item.getInput())) {
                return;
            }
            Node.Input input = item.getInput();
            Object contentValue = getContentValue(input, workflowContext);
            if (Objects.nonNull(contentValue) ) {
                 resultMap.put(item.getName(), contentValue);
            } else {
                Map<String, Object> initialInput = workflowContext.getInitialInput();
                resultMap.put(item.getName(), initialInput.get(item.getName()));
            }
        });

        return resultMap;
    }

    /**
     * 从上下文中读取 Node.Input 中引用节点或者输入的值
     *
     * @param input           输入节点
     * @param workflowContext 上下文
     * @return
     */
    public static Object getContentValue(Node.Input input, WorkflowContext workflowContext) {
        Node.Value inputValue = input.getValue();
        // 可能是字符串或者对象
        Object content = inputValue.getContent();
        InputValueTypeEnum inputValueTypeEnum = InputValueTypeEnum.from(inputValue.getType());
        Object realValue = null;
        if (InputValueTypeEnum.ref.equals(inputValueTypeEnum) && content instanceof Map refContentMap) {
            Node.RefContent refContent = JacksonUtils.readValue(JacksonUtils.writeValueAsString(refContentMap), Node.RefContent.class);
            String blockId = refContent.getBlockId();
            NodeExecutionInfo nodeExecutionInfo = workflowContext.getNodeExecutionInfo(blockId);
            if (Objects.nonNull(nodeExecutionInfo)) {
                Map<String, Object> output = nodeExecutionInfo.getOutput();
                String keyPath = refContent.getName();
                if (keyPath.indexOf(".") != -1) {
                    realValue = JsonPath.read(output, "$." + keyPath);
                } else {
                    realValue = MapUtils.getObject(output, keyPath);
                }
            }
        } else {
            realValue = Optional.ofNullable(content).map(Objects::toString).orElse(null);
            if (Objects.nonNull(realValue) && realValue.toString().contains("${")) {
                Map<String, Object> initialInput = workflowContext.getInitialInput();
                realValue = FreeMarkerTemplateCacheUtils.processTemplateIntoString(realValue.toString(), initialInput);
            }
        }
        return realValue;
    }

    public static Object getRightValue(Node.Input input, Map<String, Object> contextMap) {
        String type = input.getValue().getType();
        InputValueTypeEnum inputValueTypeEnum = InputValueTypeEnum.from(type);
        switch (inputValueTypeEnum) {
            case ref -> {
                return MapUtils.getMap(contextMap, getRefJsonKey(input));
            }
            case literal -> {
                return input.getValue().getContent();
            }
        }
        return null;
    }

    public static String getRefJsonPath(Node.Input input) {
        return String.format("$.%s", getRefJsonKey(input));
    }

    /**
     * 读取ref节点的key
     *
     * @param input
     * @return
     */
    public static String getRefJsonKey(Node.Input input) {
        Node.RefContent content = JacksonUtils.readValue(JacksonUtils.writeValueAsString(input.getValue().getContent()), Node.RefContent.class);
        String blockId = content.getBlockId();
        // 小数点为jsonpath关键字符
        String name = StringUtils.replace(content.getName(), ".", "#");
        // $.100001#output_list#output
        return String.format("%s#%s", blockId, name);
    }

    /**
     * 工作流转为tools
     *
     * @param workflowDefinition
     * @return
     */
    public static Optional<OpenAiTool> getOpenAiTool(WorkflowDefinition workflowDefinition) {
        if (Objects.isNull(workflowDefinition) || CollectionUtils.isEmpty(workflowDefinition.getNodes())) {
            return Optional.empty();
        }
        Optional<Node> first = workflowDefinition.getNodes().stream().filter(Objects::nonNull).filter(node -> NodeTypeEnum.START.equals(NodeTypeEnum.getByType(node.getType()))).findFirst();
        if (first.isEmpty()) {
            return Optional.empty();
        }
        List<String> required = Lists.newArrayList();
        Map<String, OpenAiFunction.PropertiesInfo> properties = Maps.newLinkedHashMap();
        for (Node.Output output : first.get().getData().getOutputs()) {
            String name = output.getName();
            String description = output.getDescription();
            String type = output.getType();
            if (output.isRequired()) {
                required.add(name);
            }
            OpenAiFunction.PropertiesInfo propertiesInfo = OpenAiFunction.PropertiesInfo.builder().type(type).description(description).build();
            properties.put(name, propertiesInfo);
        }

        OpenAiFunction.OpenAiParameters openAiParameters = OpenAiFunction.OpenAiParameters.builder()
                .type(SchemaTypeEnum.OBJECT.getType())
                .required(required)
                .properties(properties)
                .build();

        OpenAiFunction openAiFunction = OpenAiFunction.builder()
                .name(workflowDefinition.getWorkflowName())
                .description(workflowDefinition.getWorkflowDesc())
                .parameters(openAiParameters)
                .build();

        OpenAiTool openAiTool = OpenAiTool.builder()
                .type(AiConstant.FUNCTION)
                .function(openAiFunction)
                .build();
        return Optional.ofNullable(openAiTool);
    }

    public static String getGroovyClassName(String workflowCode, String nodeId) {
        return String.format("Clz_%s_%s", workflowCode, nodeId);
    }

    /**
     * 获取工作流的结果
     *
     * @param workflowContext
     * @return
     */
    public static Map<String, Object> getWorkflowOutput(WorkflowContext workflowContext) {
        Workflow workflow = workflowContext.getWorkflow();
        Map<String, NodeExecutionInfo<Node.Inputs>> nodeExecutionInfoMap = workflowContext.getNodeExecutionInfoMap();
        return getWorkflowOutput(workflow, nodeExecutionInfoMap);
    }

    public static Map<String, Object> getWorkflowOutput(Workflow workflow, Map<String, NodeExecutionInfo<Node.Inputs>> nodeExecutionInfoMap) {
        Node endNode = workflow.getEndNode();
        NodeExecutionInfo nodeExecutionInfo = MapUtils.getObject(nodeExecutionInfoMap, endNode.getId());
        return Optional.ofNullable(nodeExecutionInfo).map(NodeExecutionInfo::getOutput).orElse(Maps.newHashMap());
    }

    public static void handlerUseAnswerContent(ChatContext chatContext, Map<String, Object> output) {
        Object content = MapUtils.getObject(output, WorkflowConstant.INNER_OUTPUT);
        Object answerObj = Objects.nonNull(content) ? content : output;
        String answer = StringUtils.EMPTY;
        if (Objects.nonNull(answerObj)) {
            if (answerObj instanceof String) {
                answer = (String) answerObj;
            } else {
                if (answerObj instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) answerObj;
                    if (MapUtils.isEmpty(map)) {
                        return;
                    }
                }
                answer = JacksonUtils.writeValueAsString(answerObj);
            }
        }
        ChatCompletions chatCompletions = ChatCompletionUtils.buildChatCompletions(answer);
        chatContext.sinkNext(chatCompletions);
        chatContext.setAnswer(answer);
    }

    /**
     * 从指定节点的下个节点中读取输出第一个输出节点
     *
     * @param node
     * @param workflowContext
     * @return
     */
    public static Optional<Node> getNextOutputNode(Node node, WorkflowContext workflowContext) {
        List<Edge> outgoingEdges = workflowContext.getWorkflow().getOutgoingEdges(node.getId());
        for (Edge edge : outgoingEdges) {
            Node nextNode = workflowContext.getWorkflow().getNodeById(edge.getTargetNodeId());

            if (Objects.equals(NodeTypeEnum.MID_OUTPUT.getType(), nextNode.getType()) || Objects.equals(NodeTypeEnum.END.getType(), nextNode.getType())) {
                return Optional.of(nextNode);
            }
        }
        return Optional.empty();
    }

    /**
     * 从指定节点的上个节点是大模型节点
     *
     * @param node
     * @param workflowContext
     * @return
     */
    public static Optional<Node> getPreOutputNode(Node node, WorkflowContext workflowContext) {
        List<Edge> incomingEdges = workflowContext.getWorkflow().getIncomingEdges(node.getId());
        for (Edge edge : incomingEdges) {
            Node preNode = workflowContext.getWorkflow().getNodeById(edge.getSourceNodeId());
            if (Objects.equals(NodeTypeEnum.LLM.getType(), preNode.getType())) {
                return Optional.of(preNode);
            }
        }
        return Optional.empty();
    }

    public static List<WorkflowNodeSimpleVO> getWorkflowSimpleList(WorkflowContext workflowContext) {
        List<WorkflowNodeSimpleVO> simpleVOList = Lists.newArrayList();
        Map<String, ModelRequestInfo> modelRequestInfoMap = workflowContext.getModelRequestInfoMap();
        ToolChainContext toolChainContext = workflowContext.getToolChainContext();
        try {
            Map<String, NodeExecutionInfo<Node.Inputs>> nodeExecutionInfoMap = workflowContext.getNodeExecutionInfoMap();
            nodeExecutionInfoMap.forEach((key, value) -> {
                if (value.getStatus() == NodeExecutionStatus.SKIPPED || value.getStatus() == NodeExecutionStatus.PENDING) {
                    return;
                }
                Node node = workflowContext.getWorkflow().getNodeById(key);
                WorkflowNodeSimpleVO simpleVO = new WorkflowNodeSimpleVO();
                simpleVO.setId(key);
                simpleVO.setInput(value.getInput());
                simpleVO.setOutput(value.getOutput());
                simpleVO.setStartTime(LocalDateTimeUtils.formatTimestamp(value.getStartTime(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));
                simpleVO.setStart(value.getStartTime());
                simpleVO.setEnd(value.getEndTime());
                simpleVO.setEndTime(LocalDateTimeUtils.formatTimestamp(value.getEndTime(), DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS));
                Long elapsedMs = Optional.of(value.getStartTime())
                        .flatMap(start -> Optional.ofNullable(value.getEndTime())
                                .map(end -> end - start))
                        .orElse(0L);

                simpleVO.setElapsedMs(elapsedMs);
                simpleVO.setStatus(value.getStatus().getStatus());
                simpleVO.setType(node.getType());
                simpleVO.setTypeName(node.getData().getNodeMeta().getTitle());
                simpleVO.setIcon(node.getData().getNodeMeta().getIcon());
                simpleVO.setName(node.getData().getNodeMeta().getSubTitle());
                simpleVO.setModelRequestInfo(modelRequestInfoMap.get(key));
                simpleVO.setContext(value.getContext());
                if (Objects.nonNull(toolChainContext)) {
                    simpleVO.setLlmInvocationRecords(toolChainContext.getNodeId().equals(key) ? toolChainContext.getIntentProcessingRecords() : Maps.newHashMap());
                }
                PerformanceMonitorUtils.log(simpleVO);
                simpleVOList.add(simpleVO);
                // 按startTime排序（降序排列，最新的在前）
                simpleVOList.sort((o1, o2) -> {
                    Long startTime1 = nodeExecutionInfoMap.get(o1.getId()).getStartTime();
                    Long startTime2 = nodeExecutionInfoMap.get(o2.getId()).getStartTime();
                    return startTime1.compareTo(startTime2); // 升序排列
                });
            });
        } catch (Exception e) {
            log.error("getWorkflowSimpleList error", e);
        }
        return simpleVOList;
    }

    public static String getBranchKey(Edge edge) {
        return String.format(WorkflowConstant.BRANCH_KEY_TEMPLATE, edge.getSourceNodeId(), edge.getTargetNodeId());
    }

    public static String getBranchKey(String sourceNodeId, String targetNodeId) {
        return String.format(WorkflowConstant.BRANCH_KEY_TEMPLATE, sourceNodeId, targetNodeId);
    }

    public static Map<String, Object> getDataOnErr(Node.SettingOnError settingOnError) {
        Object dataOnErr = settingOnError.getDataOnErr();
        if (Objects.isNull(dataOnErr)) {
            return Maps.newHashMap();
        }
        if (dataOnErr instanceof String) {
            try {
                Map<String, Object> res = JacksonUtils.readValue(dataOnErr.toString(), new TypeReference<Map<String, Object>>() {
                });
            } catch (Exception e) {
                //ignore
            }
        } else if (dataOnErr instanceof Map) {
            return (Map<String, Object>) dataOnErr;
        }
        return Maps.newHashMap();
    }

}
