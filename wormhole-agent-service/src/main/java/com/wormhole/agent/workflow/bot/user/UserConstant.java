package com.wormhole.agent.workflow.bot.user;

import com.google.common.collect.Maps;

import java.util.HashMap;
import java.util.Map;

public class UserConstant {

    public static final String USER_ID_QUERY = "id";
    public static final String USER_RESULT_KEY = "userDetail";
    public static final String NICK_NAME = "昵称";
    public static final String TIME_VALUE_TOTAL = "当前累计时光值";
    public static final String TIME_VALUE_REMAIN = "当前时光值余额";
    public static final String USER_LEVEL = "用户等级" ;
    public static final Map<Integer, String> USER_LEVEL_MAP= Maps.newHashMap();
    public static final String USER_CREATE_TIME = "用户注册时间";
    public static final String MOBILE_QUERY = "mobile";

    static {
        USER_LEVEL_MAP.put(1, "地球");
        USER_LEVEL_MAP.put(2, "月球");
        USER_LEVEL_MAP.put(3, "火星");
        USER_LEVEL_MAP.put(4, "海王星");
        USER_LEVEL_MAP.put(5, "百达星");
    }
}
