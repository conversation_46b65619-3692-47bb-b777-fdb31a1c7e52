package com.wormhole.agent.workflow.node;

import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import reactor.core.publisher.Mono;

import java.lang.reflect.ParameterizedType;
import java.util.Map;
import java.util.Objects;

/**
 * AbstractNodeExecutor
 *
 * <AUTHOR>
 * @version 2024/12/4
 */
public abstract class AbstractNodeExecutor<T extends Node.Inputs> implements NodeExecutor<T> {

    private final Class<T> inputClass;

    protected AbstractNodeExecutor() {
        this.inputClass = initInputClass();
    }

    private Class<T> initInputClass() {
        return (Class<T>) ((ParameterizedType) getClass().getGenericSuperclass()).getActualTypeArguments()[0];
    }

    @Override
    public Class<T> getInputClass() {
        return inputClass;
    }

    /**
     * 节点执行前的处理
     */
    @Override
    public Mono<NodeExecutionInfo<T>> before(Node node, NodeExecutionInfo<T> executionInfo, WorkflowContext workflowContext) {
        NodeTypeEnum nodeTypeEnum = NodeTypeEnum.getByType(node.getType());
        if (Objects.equals(NodeTypeEnum.START, nodeTypeEnum)) {
            // 开始节点不处理
            return Mono.just(executionInfo);
        }
        T inputs = WorkflowUtils.getInputs(node.getData().getInputs(), getInputClass());
        return Mono.fromCallable(() -> {
            Map<String, Object> inputParametersValue = WorkflowUtils.getInputParametersValue(inputs, workflowContext);
            // 在 Callable 中执行可能抛出异常的校验逻辑
//             MapValidator.validateMapAndThrow(inputParametersValue);

            // 设置执行信息
            executionInfo.setStartTime(System.currentTimeMillis());
            executionInfo.setNodeDataInputs(inputs);
            executionInfo.getInput().putAll(inputParametersValue);

            return executionInfo;
        });
    }

    @Override
    public Mono<WorkflowContext> process(Node node, NodeExecutionInfo<T> executionInfo, WorkflowContext workflowContext) {
        return before(node, executionInfo, workflowContext)
                .flatMap(updatedExecutionInfo -> execute(node, updatedExecutionInfo, workflowContext))
                .flatMap(newContext -> after(node, executionInfo, newContext));
    }

}
