package com.wormhole.agent.workflow.model;

/**
 * WorkflowConstant
 *
 * <AUTHOR>
 * @version 2024/10/31
 */
public final class WorkflowConstant {

    public static final String USER_INPUT = "USER_INPUT";
    public static final String USER_ID = "user_id";
    /**
     * 增强和改写后的query
     */
    public static final String ENHANCED_QUERY = "enhanced_query";

    public static final String BOT_USER_INPUT_DESC = "用户本轮对话输入内容";

    public static final String INNER_OUTPUT = "_output";

    public static final String OUTPUT = "output";

    public static final String OUTPUT_LIST = "output_list";

    public static final String KNOWLEDGE_BIZ_C0DE_LIST = "biz_code_list";

    public static final String KNOWLEDGE_BIZ_C0DE_LIST_DESC = "知识库过滤条件";

    public static final String WF_OUTPUT_JSON = "wf_output_json";

    public static final String BDW_USER_NAME = "username";
    public static final String CONVERSATION_ID = "conversation_id";

    public static final String CONVERSATION_ID_CAPITAL = "CONVERSATION_ID";

    public static final String CLIENT_REQ_ID = "client_req_id";
    public static final String DEVICE_ID = "device_id";

    public static final String HOTEL_CODE = "hotel_code";
    public static final String ROOM_NO = "room_no";
    public static final String RTC_ROOM_ID = "rtc_room_id";
    public static final String THIRD_USER_ID = "third_user_id";
    public static final String BRANCH_KEY_TEMPLATE = "%s#%s";

    public static final String CLIENT_TYPE = "client_type";

    public static final String POSITION_CODE = "position_code";

    public static final String POSITION_NAME = "position_name";

    public static final String DATE = "date";

    public static final String BOT_CODE = "bot_code";

    public static final String SPACE_CODE = "space_code";

}
