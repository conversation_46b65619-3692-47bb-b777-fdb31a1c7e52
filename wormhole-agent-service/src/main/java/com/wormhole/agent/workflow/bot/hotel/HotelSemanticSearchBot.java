package com.wormhole.agent.workflow.bot.hotel;

import com.google.common.collect.Lists;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.ai.siliconflow.model.rerank.SiliconflowRerankParams;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.knowledge.model.constant.SearchStrategyEnum;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * HotelFullTextSearchBot
 *
 * <AUTHOR>
 * @version 2024/12/9
 */
@BotEnabled
@Component
public class HotelSemanticSearchBot implements Bot {

    public static final String CODE = "hotel_semantic_search";

    @Resource
    private HotelHybridSearchBot hotelHybridSearchBot;

    @Override
    public BotInfo createBot() {
        BotInfo source = hotelHybridSearchBot.createBot();
        BotInfo target = JacksonUtils.readValue(JacksonUtils.writeValueAsString(source), BotInfo.class);
        target.setName("百小达搜索（向量检索）");
        target.setBotCode(CODE);
        target.setWorkflowCodeList(Lists.newArrayList(CODE));
        return target;
    }

    @Override
    public Workflow createWorkflow() {
        Workflow source = hotelHybridSearchBot.createWorkflow();
        Workflow target = Workflow.fromJson(JacksonUtils.writeValueAsString(source.getWorkflowDefinition()));

        WorkflowDefinition workflowDefinition = target.getWorkflowDefinition();
        workflowDefinition.setWorkflowCode(CODE);

        workflowDefinition.getNodes().stream()
                .filter(node -> node.getType().equalsIgnoreCase(NodeTypeEnum.KNOWLEDGE_SEARCH.getType()))
                .forEach(node -> {
                    Map<String, Object> inputs = (Map<String, Object>) node.getData().getInputs();
                    inputs.put("strategy", SearchStrategyEnum.SEMANTIC.getValue());
                    inputs.put("rerank_strategy", JacksonUtils.readValue(JacksonUtils.writeValueAsString(SiliconflowRerankParams.builder()
                            .model(UnifiedModelEnum.SF_BAAI_BGE_RERANKER_V2_M3.getModel())
                            .returnDocuments(false)
                            .maxChunksPerDoc(1024)
                            .overlapTokens(80)
                            .build())));
                });
        return target;
    }

}
