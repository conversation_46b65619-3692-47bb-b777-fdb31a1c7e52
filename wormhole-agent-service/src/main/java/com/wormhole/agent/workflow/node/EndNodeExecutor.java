package com.wormhole.agent.workflow.node;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.*;
import com.wormhole.agent.workflow.model.inputs.EndInputs;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JsonPathUtils;
import dev.langchain4j.model.input.PromptTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Objects;

/**
 * EndNodeExecutor
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Component
public class EndNodeExecutor extends AbstractNodeExecutor<EndInputs> {

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.END;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<EndInputs> executionInfo, WorkflowContext workflowContext) {
        EndInputs inputs = executionInfo.getNodeDataInputs();
        TerminatePlanEnum terminatePlanEnum = TerminatePlanEnum.from(inputs.getTerminatePlan());
        Preconditions.checkArgument(Objects.nonNull(terminatePlanEnum), "terminate_plan must not be blank");
        workflowContext.setTerminatePlan(terminatePlanEnum);
        switch (terminatePlanEnum) {
            case USE_ANSWER_CONTENT -> {
                return executeUseAnswerContent(executionInfo, workflowContext);
            }
            case RETURN_VARIABLES -> {
                return executeReturnVariables(executionInfo, workflowContext);
            }
            default -> throw new IllegalStateException("Unexpected value: " + terminatePlanEnum);
        }
    }

    /**
     * 使用设定的内容直接回复
     *
     * @param workflowContext
     * @return
     */
    private Mono<WorkflowContext> executeUseAnswerContent(NodeExecutionInfo<EndInputs> executionInfo, WorkflowContext workflowContext) {
        EndInputs inputs = executionInfo.getNodeDataInputs();
        Node.Input inputsContent = inputs.getContent();
        String template = (String) inputsContent.getValue().getContent();
        Object content;
        Map<String, Object> varMap = WorkflowUtils.getInputParametersValue(inputs, workflowContext);
        if (StringUtils.isBlank(template)) {
            // 直接输出变量
            content = varMap;
        } else {
            // 变量格式化输出
            String formatType = inputs.getTemplateEngineType();
            TemplateEngineType templateEngineType = TemplateEngineType.from(formatType);
            Preconditions.checkNotNull(templateEngineType, "template_engine_type must not be null");
            switch (templateEngineType) {
                // string
                case FREEMARKER -> content = FreeMarkerTemplateCacheUtils.processTemplateIntoString(template, varMap);
                // string
                case JINJA -> content = PromptTemplate.from(template).apply(varMap).text();
                // object
                case JSON_PATH -> content = JsonPathUtils.read(varMap, template);
                default -> throw new IllegalStateException("Unexpected value: " + templateEngineType);
            }
        }
        trySinkNext(workflowContext, content);
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put(WorkflowConstant.INNER_OUTPUT, content);
        executionInfo.setOutput(resultMap);

        return Mono.just(workflowContext);
    }

    /**
     * 返回变量，由智能体生成回答
     *
     * @param executionInfo
     * @param workflowContext
     * @return
     */
    private Mono<WorkflowContext> executeReturnVariables(NodeExecutionInfo<EndInputs> executionInfo, WorkflowContext workflowContext) {
        EndInputs inputs = executionInfo.getNodeDataInputs();
        Map<String, Object> dataMap = WorkflowUtils.getInputParametersValue(inputs, workflowContext);
        executionInfo.setOutput(dataMap);
        trySinkNext(workflowContext, dataMap);
        return Mono.just(workflowContext);
    }

    public void trySinkNext(WorkflowContext workflowContext, Object output) {
        if (workflowContext.getWorkflowRequestSource() == WorkflowRequestSourceEnum.MAIN) {
            if (output instanceof String) {
                if (StrUtil.isNotBlank(output.toString())) {
                    workflowContext.sinkNext(ChatCompletionUtils.buildChatCompletionsDelta(output.toString()));
                }
            } else if (output instanceof Map) {
                if (!((Map<?, ?>) output).isEmpty()) {
                    workflowContext.sinkNext(ChatCompletionUtils.buildChatCompletionsDelta(output.toString()));
                }
            }
            ChatCompletions chatCompletions = ChatCompletionUtils.buildChatCompletionsDeltaLast();
            workflowContext.sinkNext(chatCompletions);
        }
    }
}