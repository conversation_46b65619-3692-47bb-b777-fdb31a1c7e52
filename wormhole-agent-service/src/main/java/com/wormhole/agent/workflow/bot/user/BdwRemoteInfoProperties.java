package com.wormhole.agent.workflow.bot.user;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

/**
 * @author: joker.liu
 * @date: 2025/2/20
 * @Description:
 */
@Data
@RefreshScope
@ConfigurationProperties(prefix = "wormhole.remote.bdw")
public class BdwRemoteInfoProperties {

    private String bdwUserBaseHost = "http://localhost:9303";

    private String bdwUserInfoUri = "/user/getUserInfoByUserIdOrMobile";

}
