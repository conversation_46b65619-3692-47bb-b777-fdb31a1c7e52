package com.wormhole.agent.workflow.model;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * 智能体回复方式
 *
 * <AUTHOR>
 * @version 2024/10/25
 */
@Getter
public enum TerminatePlanEnum {

    /**
     * 返回变量，由智能体生成回答。需要调用大模型
     */
    RETURN_VARIABLES("returnVariables"),

    /**
     * 使用设定的内容直接回复。不需要调用大模型
     */
    USE_ANSWER_CONTENT("useAnswerContent"),

    ;

    /**
     * 智能体回复方式
     */
    private final String plan;

    TerminatePlanEnum(String plan) {
        this.plan = plan;
    }

    public static TerminatePlanEnum from(String plan) {
        return Optional.ofNullable(plan)
                .filter(StringUtils::isNotBlank)
                .flatMap(t -> Arrays.stream(values()).filter(item -> item.getPlan().equalsIgnoreCase(plan)).findFirst())
                .orElse(null);
    }

}