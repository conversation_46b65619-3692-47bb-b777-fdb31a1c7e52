package com.wormhole.agent.workflow.model;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * LogicEnum
 *
 * <AUTHOR>
 * @version 2024/11/26
 */
public enum LogicEnum {
    /**
     * and
     */
    AND(1),
    /**
     * or
     */
    OR(2),
    ;

    /**
     * 操作符
     */
    private Integer type;

    LogicEnum(Integer type) {
        this.type = type;
    }

    public static LogicEnum getByType(Integer type) {
        return Optional.ofNullable(type)
                .flatMap(t -> Arrays.stream(LogicEnum.values())
                        .filter(e -> Objects.equals(e.getType(), type))
                        .findFirst())
                .orElse(null);
    }

    public Integer getType() {
        return type;
    }
}
