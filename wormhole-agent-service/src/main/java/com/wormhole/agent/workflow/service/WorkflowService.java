package com.wormhole.agent.workflow.service;

import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wormhole.agent.chat.intent.SmartIntentService;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.AgentChatParams;
import com.wormhole.agent.client.chat.params.ChatParams;
import com.wormhole.agent.core.intent.SmartIntentResult;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.model.openai.ChatFunctionCall;
import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.nacos.listener.WorkflowListener;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.WorkflowEngine;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.WorkflowRequestSourceEnum;
import com.wormhole.agent.workflow.model.WorkflowResult;
import com.wormhole.agent.workflow.util.SystemVariableUtils;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.constant.HeaderConstant;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * WorkflowService
 *
 * <AUTHOR>
 * @version 2024/11/20
 */
@Slf4j
@Component
@RefreshScope
public class WorkflowService {
    @Lazy
    @Resource
    private WorkflowEngine workflowEngine;
    @Resource
    private SmartIntentService smartIntentService;
    @Resource
    private WorkflowListener workflowListener;
    @Value("${wormhole.workflow.wakeup:柠檬的咖啡在哪}")
    private String wakeup;
    /**
     * 主工作流调用
     *
     * @param chatContext 对话上下文
     * @return
     */
    public Mono<ChatContext> executeWorkflow(ChatContext chatContext) {
        return Mono.defer(() -> {
            BotInfo botInfo = chatContext.getBotInfo();
            // bot绑定的工作流
            Optional<List<String>> workflowCodeListOptional = Optional.ofNullable(botInfo).map(BotInfo::getWorkflowCodeList);
            if (workflowCodeListOptional.isEmpty()) {
                return Mono.just(chatContext);
            }
            List<String> workflowCodeList = Lists.newArrayList();
            BotModeEnum botMode = BotModeEnum.from(botInfo.getBotMode());
            if (BotModeEnum.WORKFLOW_AGENT.equals(botMode) || BotModeEnum.CHATFLOW_AGENT.equals(botMode)) {
                // 单个工作流，直接执行
                workflowCodeList.addAll(workflowCodeListOptional.get());
            } else if (BotModeEnum.LLM_AGENT.equals(botMode)) {
                // 多个需要意图识别
                Optional.ofNullable(chatContext.getSmartIntentResult())
                        .map(SmartIntentResult::getChatToolCallList)
                        .map(toolCalls -> toolCalls.stream()
                                .filter(Objects::nonNull)
                                .map(ChatToolCall::getFunction)
                                .filter(Objects::nonNull)
                                .map(ChatFunctionCall::getName)
                                .map(workflowListener::getWorkflowByName)
                                .filter(Optional::isPresent)
                                .map(Optional::get)
                                .map(workflow -> workflow.getWorkflowDefinition().getWorkflowCode())
                                .filter(StringUtils::isNotBlank)
                                .collect(Collectors.toList()))
                        .ifPresent(workflowCodeList::addAll);
            } else {
                throw new BusinessException(ResultCode.NOT_IMPLEMENTED);
            }

            if (CollectionUtils.isEmpty(workflowCodeList)) {
                return Mono.just(chatContext);
            }

            return Flux.fromIterable(workflowCodeList)
                    .flatMap(workflowCode -> startWorkflow(workflowCode, chatContext), Math.max(workflowCodeList.size(), 10))
                    .doOnError(error -> {
                        log.error("Error in executeWorkflow: {}", error.getMessage(), error);
                        throw new BusinessException(error);
                    })
                    .then(Mono.just(chatContext));
        });
    }

    /**
     * 执行单个工作流
     *
     * @param workflowCode workflowCode
     * @param chatContext
     * @return
     */
    private Mono<WorkflowContext> startWorkflow(String workflowCode, ChatContext chatContext) {
        return Mono.defer(() -> {
                    chatContext.getExecutionStatManager().start(ExecutionStatType.workflow, chatContext.getQuestion());
                    ChatToolCall chatToolCall = smartIntentService.getChatToolCall(workflowCode, chatContext.getSmartIntentResult()).orElse(null);
                    Map<String, Object> inputMap = getWorkflowInput(chatToolCall, chatContext);
                    WorkflowContext workflowContext = WorkflowContext.builder()
                            .workflowCode(workflowCode)
                            .chatToolCall(chatToolCall)
                            .initialInput(inputMap)
                            .recentMessageList(chatContext.getRecentMessageList())
                            .workflowRequestSource(WorkflowRequestSourceEnum.MAIN)
                            .modelLogContext(chatContext.bulidModelLogContext())
                            .sinks(chatContext.getSinks())
                            .userId(chatContext.getUserId())
                            .conversationId(chatContext.getChatParams().getConversationId())
                            .traceId(chatContext.getTraceId())
                            .build();
                    if(inputMap.get(WorkflowConstant.USER_INPUT).equals(wakeup)){
                        chatContext.getSinks().tryEmitNext(ChatCompletionUtils.buildChatCompletionsDelta("我是"+chatContext.getBotInfo().getName()));
                    }
                    return executeWorkflow(workflowContext)
                            .flatMap(w -> handleWorkflowResult(chatContext, workflowContext));
                })
                .doOnSuccess(workflowContext -> {
                    chatContext.getExecutionStatManager().endWithStat(ExecutionStatType.workflow, WorkflowUtils.getWorkflowOutput(workflowContext), WorkflowUtils.getWorkflowSimpleList(workflowContext));
                })
                .doOnError(error -> {
                    chatContext.getExecutionStatManager().fail(ExecutionStatType.workflow, error);
                });
    }

    public Mono<WorkflowContext> executeWorkflowNode(WorkflowContext workflowContext,String nodeId) {
        String workflowCode = workflowContext.getWorkflowCode();
        return workflowListener.getWorkflowByCodeList(Lists.newArrayList(workflowCode))
                .stream()
                .filter(workflow -> StringUtils.equalsIgnoreCase(workflow.getWorkflowDefinition().getWorkflowCode(), workflowCode))
                .findFirst()
                .map(workflow -> {
                    workflowContext.setWorkflow(workflow);
                    return workflowEngine.executeWorkflow(workflowContext,nodeId);
                })
                .orElse(Mono.just(workflowContext));
    }

    /**
     * 子工作流调用
     *
     * @param workflowContext 工作流上下文
     * @return 工作流上下文
     */
    public Mono<WorkflowContext> executeWorkflow(WorkflowContext workflowContext) {
        String workflowCode = workflowContext.getWorkflowCode();
        return workflowListener.getWorkflowByCodeList(Lists.newArrayList(workflowCode))
                .stream()
                .filter(workflow -> {
                    log.info("executeWorkflow  work_definition: {}, workflow_code: {}", workflow.getWorkflowDefinition().getWorkflowCode(), workflowCode);
                    return StringUtils.equalsIgnoreCase(workflow.getWorkflowDefinition().getWorkflowCode(), workflowCode);
                })
                .findFirst()
                .map(workflow -> {
                    workflowContext.setWorkflow(workflow);
                    log.info("workflowCode :{} ,rtcWorkflowOutput : {} ,initialInput:{}",workflowCode, WorkflowUtils.getWorkflowOutput(workflowContext),workflowContext.getInitialInput());
                    return workflowEngine.executeWorkflow(workflowContext);
                })
                .orElse(Mono.just(workflowContext));
    }

    /**
     * 从入参或者意图识别结果中读取工作流入参
     *
     * @param chatToolCall 意图识别结果
     * @param chatContext  对话上下文
     * @return
     */
    private Map<String, Object> getWorkflowInput(ChatToolCall chatToolCall, ChatContext chatContext) {
        String botMode = chatContext.getBotInfo().getBotMode();
        Map<String, Object> inputMap = Maps.newHashMap();
        if (!StringUtils.equalsIgnoreCase(BotModeEnum.WORKFLOW_AGENT.getValue(), botMode) && Objects.nonNull(chatToolCall)) {
            String arguments = chatToolCall.getFunction().getArguments();
            if (StringUtils.isNotBlank(arguments)) {
                try {
                    inputMap = JacksonUtils.readValue(arguments, new TypeReference<>() {
                    });
                } catch (Exception e) {
                    ContextedRuntimeException cre = new ContextedRuntimeException(e).addContextValue("chatToolCall", chatToolCall);
                    log.error(cre.getMessage(), cre);
                }
            }
        }
        inputMap.put(WorkflowConstant.USER_INPUT, chatContext.getQuestion());
        inputMap.put(WorkflowConstant.BDW_USER_NAME, chatContext.getUsername());
        inputMap.put(WorkflowConstant.HOTEL_CODE, chatContext.getHotelCode());
        inputMap.put(WorkflowConstant.DEVICE_ID, chatContext.getDeviceId());
        inputMap.put(WorkflowConstant.CONVERSATION_ID_CAPITAL, chatContext.getConversationId());
        inputMap.put(WorkflowConstant.CONVERSATION_ID, chatContext.getConversationId());
        inputMap.put(WorkflowConstant.CLIENT_REQ_ID, chatContext.getClientReqId());
        inputMap.put(WorkflowConstant.ROOM_NO, chatContext.getRoomNo());
        inputMap.put(WorkflowConstant.USER_ID, chatContext.getUserId());
        inputMap.put(WorkflowConstant.RTC_ROOM_ID, chatContext.getRtcRoomId());
        inputMap.put(WorkflowConstant.CLIENT_TYPE, chatContext.getClientType());
        inputMap.put(WorkflowConstant.POSITION_CODE, chatContext.getPositionCode());
        inputMap.put(WorkflowConstant.POSITION_NAME, chatContext.getPositionName());

        BotInfo botInfo = chatContext.getBotInfo();
        if (Objects.nonNull(botInfo)) {
            inputMap.put(WorkflowConstant.SPACE_CODE, botInfo.getSpaceCode());
            inputMap.put(WorkflowConstant.BOT_CODE, botInfo.getBotCode());
        }

        String thirdUserId = chatContext.getHttpHeaders().getFirst(HeaderConstant.THIRD_USER_ID);
        if (StringUtils.isNotBlank(thirdUserId)) {
            inputMap.put(WorkflowConstant.THIRD_USER_ID, thirdUserId);

        }

//        inputMap.put(WorkflowConstant.RTC_ROOM_ID,chatContext.())
        ChatParams chatParams = chatContext.getChatParams();
        if (chatParams instanceof AgentChatParams) {
            Map<String, Object> payload = chatParams.getPayload();
            if (MapUtils.isNotEmpty(payload)) {
                inputMap.putAll(payload);
            }
        }

        // 系统变量 chatContext的优先级比 payload高一点,chatContext有值的话就会替换掉
        Map<String, Object> systemVariableMap = SystemVariableUtils.getSystemVariable(chatContext, inputMap);
        inputMap.putAll(systemVariableMap);

        MapUtil.removeNullValue(inputMap);

        log.info("inputMap:{}",JacksonUtils.writeValueAsString(inputMap));

        return inputMap;
    }

    private Mono<WorkflowContext> handleWorkflowResult(ChatContext chatContext, WorkflowContext workflowContext) {
        Map<String, Object> output = WorkflowUtils.getWorkflowOutput(workflowContext);
        WorkflowResult workflowResult = WorkflowResult.builder()
                .chatToolCall(workflowContext.getChatToolCall())
                .workflow(workflowContext.getWorkflow())
                .initialInput(workflowContext.getInitialInput())
                .nodeExecutionInfoMap(workflowContext.getNodeExecutionInfoMap())
                .terminatePlan(workflowContext.getTerminatePlan())
                .output(output)
                .nodeOutPutFlag(workflowContext.isNodeOutPutFlag())
                .build();
        chatContext.getWorkflowResultList().add(workflowResult);
        return Mono.just(workflowContext);
    }

}