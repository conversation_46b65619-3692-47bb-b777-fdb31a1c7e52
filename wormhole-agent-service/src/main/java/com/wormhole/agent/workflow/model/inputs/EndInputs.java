package com.wormhole.agent.workflow.model.inputs;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.workflow.model.Node;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 结束节点 inputs 定义
 *
 * <AUTHOR>
 * @version 2024/10/25
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class EndInputs extends Node.Inputs {
    /**
     * 回复方式
     */
    private String terminatePlan;
    /**
     * 是否流式输出
     */
    @Builder.Default
    private Boolean streamingOutput = false;
    /**
     * terminatePlan=useAnswerContent时的回复内容
     */
    private Node.Input content;
    /**
     * 格式化类型：json_path，freemarker, jinja
     */
    @JsonAlias(value = {"template_engine_type", "format_type"})
    private String templateEngineType;

}
