{"nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": 115.38095238095238, "y": -2.761904761904759}, "testRun": {}}, "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start.png", "subTitle": "", "title": "开始"}, "outputs": [{"type": "string", "name": "USER_INPUT", "required": false, "description": "用户本轮对话输入内容"}]}}, {"id": "900001", "type": "2", "meta": {"position": {"x": 1024, "y": 119}, "testRun": {}}, "data": {"nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End.png", "subTitle": "", "title": "结束"}, "inputs": {"terminatePlan": "useAnswerContent", "streamingOutput": false, "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "189384", "name": "output"}}}}], "content": {"type": "string", "value": {"type": "literal", "content": "{{output}}"}}}}}, {"id": "189384", "type": "9", "meta": {"position": {"x": 567.952380952381, "y": 11.907142857142901}, "testRun": {}}, "data": {"inputs": {"inputDefs": [{"description": "用户本轮对话输入内容", "input": {}, "name": "USER_INPUT", "required": false, "type": "string"}], "inputParameters": [{"name": "USER_INPUT", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockID": "100001", "name": "USER_INPUT"}}}}], "settingOnError": {}, "spaceId": "7412820484326424587", "type": 0, "workflowId": "7419999819673206821"}, "nodeMeta": {"description": "天气信息查询", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Workflow.png", "isImageflow": false, "title": "llm"}, "outputs": [{"type": "string", "name": "output", "required": false}]}}], "edges": [{"sourceNodeID": "189384", "targetNodeID": "900001"}, {"sourceNodeID": "100001", "targetNodeID": "189384"}]}