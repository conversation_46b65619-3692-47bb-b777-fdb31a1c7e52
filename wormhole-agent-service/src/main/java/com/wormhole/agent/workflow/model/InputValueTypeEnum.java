package com.wormhole.agent.workflow.model;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * InputValueTypeEnum
 *
 * <AUTHOR>
 * @version 2024/10/25
 */
public enum InputValueTypeEnum {
    /**
     * 引用
     */
    ref("ref"),
    /**
     * 直接输入
     */
    literal("literal"),

    ;

    private final String type;

    InputValueTypeEnum(String type) {
        this.type = type;
    }

    public static InputValueTypeEnum from(String type) {
        return Optional.ofNullable(type)
                .filter(StringUtils::isNotBlank)
                .flatMap(t -> Arrays.stream(values()).filter(item -> item.getType().equalsIgnoreCase(type)).findFirst())
                .orElse(null);
    }

    public String getType() {
        return type;
    }
}
