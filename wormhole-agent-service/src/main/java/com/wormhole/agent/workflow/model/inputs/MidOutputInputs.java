package com.wormhole.agent.workflow.model.inputs;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.workflow.model.Node;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 支持中间结果的消息输出，支持流式和非流式两种方式
 *
 * <AUTHOR>
 * @version 2025/2/16
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MidOutputInputs extends Node.Inputs {

    /**
     * 是否流式输出
     */
    @Builder.Default
    private Boolean streamingOutput = false;

    /**
     * 格式化类型：json_path，freemarker, jinja
     */
    @JsonAlias(value = {"template_engine_type", "format_type"})
    private String templateEngineType;

    /**
     * 输出内容
     * 编辑智能体的回复内容,即工作流运行过程中，智能体将直接用这里编辑的内容原文回复对话。可以使用{{变量名}}的方式引用输出参数中的变量。
     */
    private Node.Input content;

}
