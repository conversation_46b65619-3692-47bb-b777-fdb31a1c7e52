package com.wormhole.agent.workflow.node;

import com.google.common.collect.Maps;
import com.wormhole.agent.knowledge.core.es.KnowledgeChunkService;
import com.wormhole.agent.knowledge.params.KnowledgeChunkSearchParams;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.KnowledgeSearchInputs;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * HotelSearchNodeExecutor
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Component
@Slf4j
public class SearchNodeExecutor extends AbstractNodeExecutor<KnowledgeSearchInputs> {

    @Resource
    private KnowledgeChunkService knowledgeChunkService;

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.SEARCH;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<KnowledgeSearchInputs> executionInfo, WorkflowContext workflowContext) {
        KnowledgeSearchInputs inputs = executionInfo.getNodeDataInputs();
        Map<String, Object> inputMap = executionInfo.getInput();
        String text = MapUtils.getString(inputMap, "content");
        KnowledgeChunkSearchParams searchParams = KnowledgeChunkSearchParams.builder()
                .query(text)
                .pageSize(10)
                .build();
        return knowledgeChunkService.search(searchParams)
                .doOnNext(resultList -> {
                    Map<String, Object> resultMap = Maps.newHashMapWithExpectedSize(1);
                    resultMap.put(WorkflowConstant.OUTPUT_LIST, JacksonUtils.writeValueAsString(resultList));
                    executionInfo.setOutput(resultMap);
                })
                .then(Mono.just(workflowContext));
    }

}