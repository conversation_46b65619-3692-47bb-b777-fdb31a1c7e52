package com.wormhole.agent.workflow.model.inputs;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.workflow.model.Node;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-03 13:58:48
 * @Description:
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HttpNodeInputs extends Node.Inputs {

    private ApiInfo apiInfo;

    private Body body;

    private List<Node.InputParameter> headers;

    private List<Node.InputParameter> params;

    private Setting setting;

    private Auth auth;


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class ApiInfo {

        private String url;

        private String method;

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Body {

        /**
         * JSON
         * form-data
         */
        private String bodyType;

        private BodyData bodyData;


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class BodyData{

        private String json;

        private FormData formData;


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Setting {
        /**
         * 超时时间 秒
         */
        private Integer timeout;

        /**
         * 重试次数
         */
        private Integer retryTimes;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Auth {
        /**
         * 是否开启授权
         */
        private Boolean authOpen;

        /**
         * 授权类型
         * 1. BEARER_AUTH    key为token
         * 2. CUSTOM_AUTH
         */
        private AuthType authType;

        private AuthData authData;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class AuthData{
        private List<Node.InputParameter> bearerTokenData;

        private CustomData customData;
    }




    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class CustomData {

        /**
         * header
         * query
         */
        private String addTo;


        private List<Node.InputParameter> data;


    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class FormData {

        private List<Node.InputParameter> data;

    }


    @AllArgsConstructor
    @Getter
    public enum BodyType {
        JSON("JSON"),
        FORM_DATA("FORM_DATA"),

        ;
        private final String desc;

        public static BodyType of(String desc) {
            for (BodyType value : values()) {
                if (value.desc.equalsIgnoreCase(desc)) {
                    return value;
                }
            }
            return BodyType.JSON;
        }
    }

    @AllArgsConstructor
    @Getter
    public enum AuthType {
        BEARER_AUTH,
        CUSTOM_AUTH,
        ;
    }

}
