package com.wormhole.agent.workflow.node;

import com.google.common.collect.Maps;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * StartNodeExecutor
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Component
public class StartNodeExecutor extends AbstractNodeExecutor<Node.Inputs> {

    private static final Logger log = LoggerFactory.getLogger(StartNodeExecutor.class);

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.START;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<Node.Inputs> executionInfo, WorkflowContext workflowContext) {
        Node.NodeData nodeData = node.getData();
        List<Node.Output> outputs = nodeData.getOutputs();
        Map<String, Object> initialInput = workflowContext.getInitialInput();
        Map<String, Object> resultMap = Maps.newHashMap();
        for (Node.Output output : outputs) {
            String key = output.getName();
            Object value = MapUtils.getObject(initialInput, key);
            if (Objects.nonNull(value)) {
                resultMap.put(key, value);
            }
        }
        executionInfo.setInput(resultMap);
        executionInfo.setOutput(resultMap);
        return Mono.just(workflowContext);
    }
}