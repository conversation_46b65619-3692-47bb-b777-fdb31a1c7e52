package com.wormhole.agent.workflow.model;

import lombok.Data;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * NodeExecutionInfo
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Data
public class NodeExecutionInfo<T extends Node.Inputs> {
    private T nodeDataInputs;
    private Map<String, Object> input;
    private Map<String, Object> output;
    private long startTime;
    private Long endTime;
    private volatile NodeExecutionStatus status;
    private Map<String, Object> context;
    private volatile boolean active;

    public NodeExecutionInfo() {
        this.input = new HashMap<>();
        this.output = new HashMap<>();
        this.context = new HashMap<>();
        this.startTime = System.currentTimeMillis();
        this.status = NodeExecutionStatus.PENDING;
        this.active = true;
    }

    public static NodeExecutionInfo createPending() {
        return new NodeExecutionInfo();
    }

    public void markStarted() {
        if (this.status == NodeExecutionStatus.PENDING) {
            this.status = NodeExecutionStatus.STARTED;
        }
    }

    public void complete() {
        this.endTime = System.currentTimeMillis();
        this.status = NodeExecutionStatus.COMPLETED;
    }

    public void fail(String errorMessage) {
        this.endTime = System.currentTimeMillis();
        this.status = NodeExecutionStatus.FAILED;
        this.output.put("error", errorMessage);
    }

    public Object getOutputAsObject(String key) {
        return MapUtils.getObject(output, key);
    }

    public String getOutputAsString(String key) {
        return MapUtils.getString(output, key);
    }

}