package com.wormhole.agent.workflow.bot.hotel;

import com.google.common.collect.Lists;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.knowledge.model.constant.KnowledgeCodeEnum;
import com.wormhole.agent.knowledge.params.KnowledgeSearchParams;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.SchemaTypeEnum;
import com.wormhole.agent.workflow.model.TerminatePlanEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.EndInputs;
import com.wormhole.agent.workflow.model.inputs.KnowledgeSearchInputs;
import com.wormhole.agent.workflow.util.LocalBotUtils;
import com.wormhole.common.util.JacksonUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * HotelSearchBot
 *
 * <AUTHOR>
 * @version 2024/12/9
 */
@BotEnabled
@Component
public class HotelSearchOpenAiBot implements Bot {

    public static final BotInfo BOT_INFO;

    public static final Workflow WORKFLOW;

    static {
        String botCode = "hotel_search_openai_bot";
        Node startNode = Node.builder()
                .id("start")
                .type(NodeTypeEnum.START.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("开始").build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name(WorkflowConstant.USER_INPUT).type(SchemaTypeEnum.STRING.getType()).description(WorkflowConstant.BOT_USER_INPUT_DESC).required(true).build(),
                                Node.Output.builder().name(WorkflowConstant.KNOWLEDGE_BIZ_C0DE_LIST).type(SchemaTypeEnum.LIST.getType()).description(WorkflowConstant.KNOWLEDGE_BIZ_C0DE_LIST_DESC).required(false).build()
                        ))
                        .build())
                .build();

        Node hotelSearchNode = Node.builder()
                .id("hotel_search")
                .type(NodeTypeEnum.KNOWLEDGE_SEARCH.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder()
                                .title("酒店信息检索")
                                .description("酒店信息检索")
                                .subTitle("酒店信息检索")
                                .build())
                        .inputs(KnowledgeSearchInputs.builder()
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("query", startNode, WorkflowConstant.USER_INPUT),
                                        LocalBotUtils.getRefInputParameter(WorkflowConstant.KNOWLEDGE_BIZ_C0DE_LIST, startNode, WorkflowConstant.KNOWLEDGE_BIZ_C0DE_LIST)
                                ))
                                .knowledgeCodeList(Lists.newArrayList(KnowledgeCodeEnum.HOTEL.getCode()))
                                .embeddingModel(UnifiedModelEnum.TEXT_EMBEDDING_3_SMALL.getModel())
//                                .rerankStrategy(JacksonUtils.readValue(JacksonUtils.writeValueAsString(SiliconflowRerankParams.builder()
//                                        .model(UnifiedModelEnum.SF_BAAI_BGE_RERANKER_V2_M3.getModel())
//                                        .returnDocuments(false)
//                                        .maxChunksPerDoc(1024)
//                                        .overlapTokens(80)
//                                        .build())))
                                .rerankConfig(KnowledgeSearchParams.RerankConfig.builder()
                                        .maxChunksPerDoc(1024)
                                        .model(UnifiedModelEnum.SF_BAAI_BGE_RERANKER_V2_M3.getModel())
                                        .returnDocuments(false)
                                        .overlapTokens(80)
                                        .build())
                                .build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output_list").type(SchemaTypeEnum.LIST.getType()).build()
                        ))
                        .build())
                .build();

        Node endNode = Node.builder()
                .id("end")
                .type(NodeTypeEnum.END.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("结束节点").build())
                        .inputs(EndInputs.builder()
                                .terminatePlan(TerminatePlanEnum.USE_ANSWER_CONTENT.getPlan())
                                .streamingOutput(false)
                                .templateEngineType("json_path")
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("output", hotelSearchNode, "output_list")
                                ))
                                .content(
                                        Node.Input.builder()
                                                .type(SchemaTypeEnum.STRING.getType())
                                                .value(Node.Value.builder().type(SchemaTypeEnum.LITERAL.getType()).content("$.output").build())
                                                .build())
                                .build())
                        .outputs(Lists.newArrayList())
                        .build())
                .build();

        List<Node> nodes = Lists.newArrayList(startNode, hotelSearchNode, endNode);
        List<Edge> edges = Lists.newArrayList(
                Edge.builder().sourceNodeId("start").targetNodeId("hotel_search").build(),
                Edge.builder().sourceNodeId("hotel_search").targetNodeId("end").build()
        );

        WorkflowDefinition workflowDefinition = WorkflowDefinition.builder()
                .workflowCode(botCode)
                .workflowName("openai酒店搜索机器人")
                .nodes(nodes)
                .edges(edges)
                .build();
        WORKFLOW = new Workflow(workflowDefinition);

        BOT_INFO = BotInfo.builder()
                .botCode(botCode)
                .name("酒店搜索专家-openai")
                .description("酒店搜索专家")
                .botMode(BotModeEnum.WORKFLOW_AGENT.getValue())
                .modelInfo(ModelInfo.builder().recentRound(3).build())
                .workflowCodeList(Lists.newArrayList(botCode))
                .build();
    }

    @Override
    public BotInfo createBot() {
        return BOT_INFO;
    }

    @Override
    public Workflow createWorkflow() {
        return WORKFLOW;
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(BOT_INFO));
        System.out.println(JacksonUtils.writeValuePretty(WORKFLOW));
    }
}
