package com.wormhole.agent.workflow.node;

import com.google.common.collect.Maps;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.inputs.CodeInputs;
import com.wormhole.agent.workflow.script.groovy.GroovyScriptExecutor;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * CodeNodeExecutor
 *
 * <AUTHOR>
 * @version 2024/11/28
 */
@Component
@Slf4j
public class CodeNodeExecutor extends AbstractNodeExecutor<CodeInputs> {

    @Resource
    private GroovyScriptExecutor groovyScriptExecutor;

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.CODE;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<CodeInputs> executionInfo, WorkflowContext workflowContext) {
        Map<String, Object> contextData = executionInfo.getInput();
        //初始化脚本
        String scriptName = WorkflowUtils.getGroovyClassName(workflowContext.getWorkflowCode(), node.getId());
        Object scriptResult = groovyScriptExecutor.runScript(scriptName, contextData);
        if (!(scriptResult instanceof Map)) {
            throw new BusinessException(ResultCode.INVALID_PARAMETER, "result type must be map");
        }
        Map<String, Object> scriptResultMap = (Map) scriptResult;
        final Map<String, Object> resultMap = Maps.newHashMap();

        List<Node.Output> outputs = node.getData().getOutputs();
        for (Node.Output output : outputs) {
            String name = output.getName();
            resultMap.put(name, MapUtils.getObject(scriptResultMap, name));
        }
        executionInfo.setOutput(resultMap);
        return Mono.just(workflowContext);
    }


}