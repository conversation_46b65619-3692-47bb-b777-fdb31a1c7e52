package com.wormhole.agent.workflow.node.plugin;

import com.wormhole.agent.plugin.service.PluginService;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.inputs.PluginInputs;
import com.wormhole.agent.workflow.node.AbstractNodeExecutor;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-31 18:48:54
 * @Description:
 */
@Slf4j
@Component
public class PluginNodeExecutor extends AbstractNodeExecutor<PluginInputs> {


    @Resource
    private PluginService pluginService;


    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.PLUGIN;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<PluginInputs> executionInfo, WorkflowContext workflowContext) {
        PluginInputs inputs = executionInfo.getNodeDataInputs();
        PluginInputs.PluginParams pluginParams = inputs.getPluginParam();
        Map<String, Object> inputValue = executionInfo.getInput();
        log.info("Plugin request input: {}", inputValue);

        return pluginService.execute(pluginParams.getPluginCode(), pluginParams.getPluginToolCode(), inputValue)
                .onErrorResume(e -> {
                    log.error("Error executing plugin request: {}", e.getMessage(), e);
                    Node.SettingOnError settingOnError = inputs.getSettingOnError();
                    if (!Boolean.FALSE.equals(settingOnError.getEnabled())) {
                        return Mono.just(WorkflowUtils.getDataOnErr(settingOnError));
                    }
                    return Mono.just(new HashMap<>());
                })
                .map(resultMap -> {
                    log.info("Plugin request result: {}", resultMap);
                    executionInfo.setOutput(resultMap);
                    return workflowContext;
                });

    }


}
