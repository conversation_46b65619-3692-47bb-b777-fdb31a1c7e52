package com.wormhole.agent.workflow.node;

import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.mcp.McpToolProvider;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.inputs.McpToolInputs;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * @author: joker.liu
 * @date: 2025/7/21
 * @Description:
 */
@Component
public class McpToolExecutor extends AbstractNodeExecutor<McpToolInputs> {

    @Resource
    private McpToolProvider mcpToolProvider;

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.MCP_TOOL;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<McpToolInputs> executionInfo, WorkflowContext workflowContext) {

        McpToolInputs nodeDataInputs = executionInfo.getNodeDataInputs();

        ToolChainContext context = new ToolChainContext();

        String mcpServerName = nodeDataInputs.getMcpServerName();
        McpToolInputs.McpToolParams mcpToolParams = nodeDataInputs.getMcpToolParams();

        // 预先建立工具与客户端的映射关系
        context.getMcpToolClientMap().put(mcpToolParams.getToolName(), mcpServerName);

        Map<String, Object> input = executionInfo.getInput();

        Mono<String> mcpToolResultMono = mcpToolProvider.executeMcpTool(mcpToolParams.getToolName(), input, context);

        return mcpToolResultMono.map(result -> {
            executionInfo.setOutput(Map.of("output", result));
            return workflowContext;
        });
    }
}
