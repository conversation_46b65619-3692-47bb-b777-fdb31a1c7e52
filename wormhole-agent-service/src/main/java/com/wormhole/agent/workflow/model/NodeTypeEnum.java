package com.wormhole.agent.workflow.model;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * NodeTypeEnum
 *
 * <AUTHOR>
 * @version 2024/9/30
 */
@Getter
public enum NodeTypeEnum {
    /**
     * 开始节点
     */
    START("start", "1"),
    /**
     * 结束节点
     */
    END("end", "2"),
    /**
     * 文本处理节点
     */
    TEXT("text", "15"),
    /**
     * 代码节点
     */
    CODE("code", "5"),
    /**
     * 知识库
     */
    KNOWLEDGE_SEARCH("knowledge_search"),
    /**
     * 测试酒店搜索
     */
    SEARCH("search"),
    /**
     * 大模型节点
     */
    LLM("llm", "3"),
    /**
     * 分支节点
     */
    CONDITIONAL_BRANCH("conditional_branch", "8"),
    /**
     * 工作流节点
     */
    WORKFLOW("workflow", "9"),
    /**
     * 输出节点
     */
    MID_OUTPUT("mid_output", "13"),
    /**
     * 用于用户输入的意图识别，并将其与预设意图选项进行匹配
     */
    INTENT_RECOGNITION("intent_recognition", "22"),

    PLUGIN("plugin","4"),

    HTTP("http","45"),

    MCP_TOOL("mcp_tool"),

    ;

    /**
     * type code
     */
    private final String type;
    /**
     * coze type id
     */
    private final String cozeTypeId;


    NodeTypeEnum(String type) {
        this(type, null);
    }

    NodeTypeEnum(String type, String cozeTypeId) {
        this.type = type;
        this.cozeTypeId = cozeTypeId;
    }

    public static NodeTypeEnum getByType(String type) {
        return Optional.ofNullable(type)
                .filter(StringUtils::isNotBlank)
                .flatMap(t -> Arrays.stream(NodeTypeEnum.values())
                        .filter(item -> StringUtils.equalsIgnoreCase(item.getType(), type) || StringUtils.equalsIgnoreCase(item.getCozeTypeId(), type))
                        .findFirst())
                .orElse(null);
    }
}
