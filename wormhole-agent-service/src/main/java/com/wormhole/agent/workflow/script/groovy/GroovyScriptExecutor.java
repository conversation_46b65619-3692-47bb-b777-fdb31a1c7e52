package com.wormhole.agent.workflow.script.groovy;

import com.google.common.collect.ImmutableMap;
import groovy.lang.Binding;
import groovy.lang.GroovyClassLoader;
import groovy.lang.GroovyObject;
import groovy.lang.Script;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * GroovyScriptExecutor
 *
 * <AUTHOR>
 * @version 2024/10/9
 */
@Slf4j
@Component
public class GroovyScriptExecutor implements InitializingBean, BeanPostProcessor {

    private static final ConcurrentHashMap<String, Class<?>> SCRIPT_CLASSES = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<String, Object> GLOBAL_VARIABLES = new ConcurrentHashMap<>();

    private static final String CTX_KEY = "ctx";

    @Override
    public void afterPropertiesSet() throws Exception {
        this.addComponent("log", log);
    }

    @Override
    public Object postProcessBeforeInitialization(@NotNull Object bean, @NotNull String beanName) throws BeansException {
        this.addComponent(beanName, bean);
        return bean;
    }

    /**
     * 获取或创建脚本实例
     *
     * @param scriptName 脚本名称
     * @return 脚本实例
     * @throws ClassNotFoundException 如果找不到脚本类
     * @throws IllegalAccessException 如果无法访问脚本类
     * @throws InstantiationException 如果无法实例化脚本类
     */
    private Script getScript(String scriptName) throws Exception {
        Class<?> scriptClass = SCRIPT_CLASSES.get(scriptName);
        if (Objects.isNull(scriptClass)) {
            throw new ContextedRuntimeException("scriptName is not exist").addContextValue("scriptName", scriptName);
        }
        GroovyObject script = (GroovyObject) scriptClass.getDeclaredConstructor().newInstance();
        return (Script) script;
    }

    /**
     * 当脚本更新时，更新缓存的实例。
     *
     * @param scriptName 脚本名称
     */
    public void updateScript(String scriptName, String scriptContent) {
        try (GroovyClassLoader groovyClassLoader = new GroovyClassLoader()) {
            Class<?> scriptClass = groovyClassLoader.parseClass(scriptContent, "Script_" + scriptName + ".groovy");
            SCRIPT_CLASSES.put(scriptName, scriptClass);
        } catch (Exception e) {
            log.error("updateScript: {}", scriptName, e);
        }
    }

    /**
     * 执行脚本，适用于没有方法调用的脚本，直接运行脚本主体。
     *
     * @param scriptName      脚本名称
     * @param scriptVariables 脚本上下文变量
     */
    public Object runScript(String scriptName, Map<String, Object> scriptVariables) {
        Script script;
        try {
            script = getScript(scriptName);
        } catch (Exception e) {
            throw new ContextedRuntimeException(e).addContextValue("scriptName", scriptName);
        }
        // 设置脚本上下文
        Binding binding = new CustomBinding(GLOBAL_VARIABLES, ImmutableMap.of(CTX_KEY, scriptVariables));
        script.setBinding(binding);
        return script.run();
    }

    /**
     * 添加脚本中可以访问的变量
     *
     * @param key   对象key
     * @param value 对象value
     */
    public void addComponent(String key, Object value) {
        if (StringUtils.isNotBlank(key) && Objects.nonNull(value)) {
            GLOBAL_VARIABLES.put(key, value);
        }
    }

}