package com.wormhole.agent.workflow.bot.customer;

import com.google.common.collect.Lists;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import com.wormhole.agent.knowledge.model.constant.KnowledgeCodeEnum;
import com.wormhole.agent.knowledge.model.constant.SearchStrategyEnum;
import com.wormhole.agent.knowledge.params.KnowledgeSearchParams;
import com.wormhole.agent.nacos.config.TemplateEnum;
import com.wormhole.agent.nacos.listener.TemplateService;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.agent.workflow.bot.hotel.HotelHybridSearchBot;
import com.wormhole.agent.workflow.model.ContentFormatTypeEnum;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.SchemaTypeEnum;
import com.wormhole.agent.workflow.model.TerminatePlanEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.CodeInputs;
import com.wormhole.agent.workflow.model.inputs.EndInputs;
import com.wormhole.agent.workflow.model.inputs.KnowledgeSearchInputs;
import com.wormhole.agent.workflow.util.LocalBotUtils;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

@BotEnabled
@Component
public class SmartCustomerBot implements Bot {

    public static final String BOT_CODE = "smart_customer_bot";

    @Resource
    private TemplateService templateService;

    @Override
    public BotInfo createBot() {
        BotInfo botInfoDTO = BotInfo.builder()
                .botCode(BOT_CODE)
                .name("智能客服（知识库检索）")
                .description("你好，我是知识库搜索专家")
                .botMode(BotModeEnum.LLM_AGENT.getValue())
                .modelInfo(ModelInfo.builder()
                        .model(UnifiedModelEnum.GPT_4_O.getModel())
                        .modelProvider(UnifiedModelEnum.GPT_4_O.getProvider())
                        .temperature(0.5d)
                        .recentRound(5)
                        .build())
                .promptInfo(PromptInfo.builder()
                        .systemPrompt(templateService.getTemplate(TemplateEnum.knowledge_search_system_prompt))
                        .userPrompt(templateService.getTemplate(TemplateEnum.knowledge_search_user_prompt))
                        .build())
                .workflowCodeList(Lists.newArrayList("knowledge_search"))
                .build();
        return botInfoDTO;
    }

    @Override
    public Workflow createWorkflow() {
        Node startNode = Node.builder()
                .id("start")
                .type(NodeTypeEnum.START.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("开始").build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name(WorkflowConstant.USER_INPUT).type(SchemaTypeEnum.STRING.getType()).description(WorkflowConstant.BOT_USER_INPUT_DESC).required(true).build()
                        ))
                        .build())
                .build();

        Node hotelSearchNode = Node.builder()
                .id("knowledge_search")
                .type(NodeTypeEnum.KNOWLEDGE_SEARCH.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder()
                                .title("知识库检索")
                                .description("知识库检索")
                                .subTitle("知识库检索")
                                .build())
                        .inputs(KnowledgeSearchInputs.builder()
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("query", startNode, WorkflowConstant.USER_INPUT)
                                ))
                                .knowledgeCodeList(Lists.newArrayList(KnowledgeCodeEnum.INTELLIGENT_CUSTOMER_SERVICE.getCode()))
                                .embeddingModel(UnifiedModelEnum.TEXT_EMBEDDING_3_SMALL.getModel())
                                .rerankConfig(KnowledgeSearchParams.RerankConfig.builder()
                                        .model(UnifiedModelEnum.SF_BAAI_BGE_RERANKER_V2_M3.getModel())
                                        .maxChunksPerDoc(1024)
                                        .returnDocuments(true)
                                        .overlapTokens(120)
                                        .topN(20)
                                        .build())
                                .strategy(SearchStrategyEnum.HYBRID.getValue())
                                .topK(20)
                                .useRewrite(false)
                                .build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output_list").type(SchemaTypeEnum.LIST.getType()).build()
                        ))
                        .build())
                .build();

        Node codeNode = Node.builder()
                .id("code")
                .type(NodeTypeEnum.CODE.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("代码节点").build())
                        .inputs(CodeInputs.builder()
                                .code(templateService.getTemplate(TemplateEnum.hotel_full_text_search_code))
                                .language(1)
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("output_list", hotelSearchNode, "output_list")
                                ))
                                .build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output").type(SchemaTypeEnum.STRING.getType()).description("大模型输出").build()
                        ))
                        .build())
                .build();

        Node endNode = Node.builder()
                .id("end")
                .type(NodeTypeEnum.END.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("结束节点").build())
                        .inputs(EndInputs.builder()
                                .terminatePlan(TerminatePlanEnum.RETURN_VARIABLES.getPlan())
//                                .streamingOutput(false)
                                .templateEngineType(ContentFormatTypeEnum.JSON_PATH.getType())
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("output", codeNode, "output")
                                ))
//                                .content(
//                                        Node.Input.builder()
//                                                .type(SchemaTypeEnum.STRING.getType())
//                                                .value(Node.Value.builder().type(SchemaTypeEnum.LITERAL.getType()).content("$.output").build())
//                                                .build())
                                .build())
//                        .outputs(Lists.newArrayList())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output").type(SchemaTypeEnum.STRING.getType()).description("大模型结果输出").build()
                        ))
                        .build())
                .build();

        List<Node> nodes = Lists.newArrayList(startNode, hotelSearchNode, codeNode, endNode);
        List<Edge> edges = Lists.newArrayList(
                Edge.builder().sourceNodeId(startNode.getId()).targetNodeId(hotelSearchNode.getId()).build(),
                Edge.builder().sourceNodeId(hotelSearchNode.getId()).targetNodeId(codeNode.getId()).build(),
                Edge.builder().sourceNodeId(codeNode.getId()).targetNodeId(endNode.getId()).build()
        );

        WorkflowDefinition workflowDefinition = WorkflowDefinition.builder()
                .workflowCode("knowledge_search")
                .workflowName("智能客服知识库搜索机器人")
                .nodes(nodes)
                .edges(edges)
                .build();
        return new Workflow(workflowDefinition);
    }


    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(new HotelHybridSearchBot().createBot()));
        System.out.println(JacksonUtils.writeValuePretty(new HotelHybridSearchBot().createWorkflow()));
    }
}

