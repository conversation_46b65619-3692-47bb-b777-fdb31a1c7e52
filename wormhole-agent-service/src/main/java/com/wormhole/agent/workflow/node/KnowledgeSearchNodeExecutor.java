package com.wormhole.agent.workflow.node;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.enums.MetadataFilterMode;
import com.wormhole.agent.knowledge.model.dto.KnowledgeMessageDTO;
import com.wormhole.agent.knowledge.model.dto.MetadataFiltering;
import com.wormhole.agent.knowledge.params.KnowledgeSearchParams;
import com.wormhole.agent.knowledge.search.KnowledgeSearchContext;
import com.wormhole.agent.knowledge.search.KnowledgeSearchService;
import com.wormhole.agent.util.MarkdownTableUtils;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.KnowledgeSearchInputs;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.BeanUtils;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * KnowledgeSearchNodeExecutor
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Component
@Slf4j
public class KnowledgeSearchNodeExecutor extends AbstractNodeExecutor<KnowledgeSearchInputs> {

    @Resource
    private KnowledgeSearchService knowledgeSearchService;


    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.KNOWLEDGE_SEARCH;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<KnowledgeSearchInputs> executionInfo, WorkflowContext workflowContext) {
        KnowledgeSearchInputs inputs = executionInfo.getNodeDataInputs();

        // 节点输入
        Map<String, Object> input = executionInfo.getInput();
        String json = JacksonUtils.writeValueAsString(input);

        KnowledgeSearchParams knowledgeSearchParams = BeanUtils.copy(inputs, KnowledgeSearchParams.class);
        // knowledgeSearchParams.setEmbeddingModel(UnifiedModelEnum.WORMHOLE_QWEN3_EMBEDDING_8B.getModel());
        KnowledgeSearchParams.SearchInput searchInput = JacksonUtils.readValue(json, KnowledgeSearchParams.SearchInput.class);
        // query改写
        String enhancedQuery = WorkflowUtils.getEnhancedQuery(workflowContext);
        searchInput.setEnhancedQuery(enhancedQuery);

        // 检索请求上下文
        KnowledgeSearchContext searchContext = KnowledgeSearchContext.builder()
                .searchInput(searchInput)
                .knowledgeSearchParams(knowledgeSearchParams)
                .recentMessageList(workflowContext.getRecentMessageList())
                .build();

        MetadataFilterMode metadataFilterMode = Optional.ofNullable(inputs.getMetadataFilterMode()).orElse(MetadataFilterMode.DISABLED);
        switch (metadataFilterMode) {
            case AUTOMATIC:
                // TODO 调用大模型生成过滤条件

                break;
            case MANUAL:
                knowledgeSearchParams.setMetadataFiltering(buildMetadataFiltering(inputs.getMetadataFilteringConditions(), workflowContext));
                break;
            default:
                break;
        }

        return knowledgeSearchService.search(searchContext)
                .doOnNext(context -> {
                    List<KnowledgeMessageDTO> searchResultList = searchContext.getSearchResultList();
                    List<Map<String, Object>> dataList = Lists.newArrayList();
                    for (int i = 0; i < searchResultList.size(); i++) {
                        KnowledgeMessageDTO item = searchResultList.get(i);
                        Map<String, Object> dataMap = Maps.newHashMap();
                        dataMap.put("排名", i + 1);
                        dataMap.put("重排序得分", item.getDistance());
                        dataMap.put("检索方式", item.getSource());
                        dataMap.put("标题", MapUtils.getString(item.getMetadata(), "title"));
                        dataMap.put("内容摘要", MarkdownTableUtils.sanitizeForRagTable(item.getContent()));
                        dataMap.put("metadata", item.getMetadata());
                        dataList.add(dataMap);
                    }
                    List<String> headers = Lists.newArrayList("排名", "重排序得分", "检索方式", "标题", "内容摘要","metadata");
                    String content = MarkdownTableUtils.convertToMarkdownTable(dataList, headers);
                    Map<String, Object> resultMap = Maps.newHashMapWithExpectedSize(1);
                    resultMap.put(WorkflowConstant.OUTPUT, StringUtils.defaultIfBlank(content, StringUtils.EMPTY));
                    resultMap.put(WorkflowConstant.OUTPUT_LIST, JacksonUtils.writeValueAsString(searchResultList));
                    executionInfo.setOutput(resultMap);
                    Map<String, Object> resultList = Map.of(
                            "semanticResultList", Optional.ofNullable(context.getSemanticResultList()).orElse(Collections.emptyList()),
                            "fullTextResultList", Optional.ofNullable(context.getFullTextResultList()).orElse(Collections.emptyList()),
                            "hybridResultList", Optional.ofNullable(context.getHybridResultList()).orElse(Collections.emptyList())
                    );

                    executionInfo.setContext(resultList);
                })
                .then(Mono.just(workflowContext));
    }

    private MetadataFiltering buildMetadataFiltering(KnowledgeSearchInputs.MetadataFilteringConditions metadataFilteringConditions, WorkflowContext workflowContext) {
        MetadataFiltering metadataFiltering = new MetadataFiltering();
        metadataFiltering.setFilterConnector(metadataFilteringConditions.getFilterConnector());
        List<MetadataFiltering.MetadataFilteringCondition> list = metadataFilteringConditions.getConditions().stream().map(condition -> {
            MetadataFiltering.MetadataFilteringCondition metadataFilteringCondition = new MetadataFiltering.MetadataFilteringCondition();
            metadataFilteringCondition.setName(condition.getName());
            metadataFilteringCondition.setComparisonOperator(condition.getComparisonOperator());
            metadataFilteringCondition.setValue(WorkflowUtils.getContentValue(condition.getValue(), workflowContext));
            return metadataFilteringCondition;
        }).toList();
        metadataFiltering.setConditions(list);
        return metadataFiltering;
    }

}