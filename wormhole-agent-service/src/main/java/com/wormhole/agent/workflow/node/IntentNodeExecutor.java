package com.wormhole.agent.workflow.node;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wormhole.agent.ai.core.service.ChatClientService;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.core.util.ChatMessageUtils;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.model.openai.OpenAiChatParams;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.inputs.IntentInputs;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.LocalResourceUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 意图识别节点
 *
 * <AUTHOR>
 * @version 2025/3/14
 */
@Slf4j
@Component
public class IntentNodeExecutor extends AbstractNodeExecutor<IntentInputs> {

    @Resource
    private ChatClientService chatClientService;

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.INTENT_RECOGNITION;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<IntentInputs> executionInfo, WorkflowContext workflowContext) {
        Workflow workflow = workflowContext.getWorkflow();
        IntentInputs inputs = executionInfo.getNodeDataInputs();
        // 大模型参数
        IntentInputs.LlmParams llmParams = inputs.getLlmParams();
        // 用户录入的意图
        List<IntentInputs.Intent> intents = inputs.getIntents();

        // intent_list, system_prompt
        Map<String, Object> inputMap = executionInfo.getInput();
        inputMap.put("system_prompt", StringUtils.defaultIfBlank(llmParams.getSystemPrompt(), StringUtils.EMPTY));
        /**
         * [
         * {"classification_id":0,"reason": "Other intentions"},
         * {"classification_id":1,"reason":"intent0"}
         * ]
         */
        List<IntentInputs.ClassificationResult> intentList = Lists.newArrayList();

        Map<String, Edge> sourcePortIdToEdgeMap = Maps.newHashMap();
        List<Edge> outgoingEdges = workflow.getOutgoingEdges(node.getId());
        for (Edge edge : outgoingEdges) {
            sourcePortIdToEdgeMap.put(edge.getSourcePortId(), edge);
        }

        for (int i = 0; i < intents.size(); i++) {
            IntentInputs.Intent intent = intents.get(i);
            intentList.add(IntentInputs.ClassificationResult.builder().classificationId(i + 1).reason(intent.getName()).build());
        }
        intentList.add(IntentInputs.ClassificationResult.builder().classificationId(0).reason("Unclear intentions").build());
        inputMap.put("intent_list", JacksonUtils.writeValueAsString(intentList));


        List<OpenAiChatMessage> chatMessageList = Lists.newArrayList();

        // system
        String systemTemplate = LocalResourceUtils.readLocalFile("template/intent_node_system_prompt.ftl");
        String systemPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(systemTemplate, inputMap);
        OpenAiChatMessage systemChatMessage = OpenAiChatMessage.builder().role(ChatRole.SYSTEM.getValue()).content(systemPrompt).build();
        chatMessageList.add(systemChatMessage);

        // recent
        int recentRound = workflowContext.getRecentRound();
        chatMessageList.addAll(ChatMessageUtils.getRecentMessageList(workflowContext.getRecentMessageList(), recentRound));

        // user query
        Map<String, Object> inputValues = executionInfo.getInput();
        String query = MapUtils.getString(inputValues, "query", StringUtils.EMPTY);
        OpenAiChatMessage userChatMessage = OpenAiChatMessage.builder().role(ChatRole.USER.getValue()).content(query).build();
        chatMessageList.add(userChatMessage);

        OpenAiChatParams openAiChatParams = OpenAiChatParams.builder()
                .stream(false)
                .model(llmParams.getModel())
                .modelProvider(llmParams.getModelProvider())
                .temperature(llmParams.getTemperature())
                .maxTokens(Objects.nonNull(llmParams.getMaxTokens()) && llmParams.getMaxTokens() > 0 ? llmParams.getMaxTokens() : null)
                .frequencyPenalty(llmParams.getFrequencyPenalty())
                .messages(chatMessageList)
                .build();

        ModelContext modelContext = ModelContext.builder()
                .openAiChatParams(openAiChatParams)
                .modelLogContext(Optional.ofNullable(workflowContext.getModelLogContext()).orElse(ModelContext.ModelLogContext.builder().build()))
                .build();

        return chatClientService.chatCompletions(modelContext)
                .collectList()
                .flatMap(list -> {
                    String content = list.stream().filter(Objects::nonNull).map(ChatCompletionUtils::getContent).collect(Collectors.joining());

                    IntentInputs.ClassificationResult classificationResult = JacksonUtils.readValue(content, IntentInputs.ClassificationResult.class);
                    // 0-others, 1对应用户定义的第一个意图
                    String reason = classificationResult.getReason();

                    Map<String, Object> checkedResultMap = Maps.newHashMap();

                    // 默认分支检测结果
                    boolean defaultIntentCheckResult = true;

                    // 用户定义的意图列表
                    for (int i = 0; i < intents.size(); i++) {
                        // 用户定义的意图从1开始。others对应的编号为0
                        IntentInputs.Intent intent = intents.get(i);
                        String intentName = intent.getName();
                        Edge edge = MapUtils.getObject(sourcePortIdToEdgeMap, String.format("intent_%s", i + 1));
                        String branchKey = WorkflowUtils.getBranchKey(edge);
                        boolean checkResult = StringUtils.equalsIgnoreCase(intentName, reason);
                        if (checkResult && defaultIntentCheckResult) {
                            defaultIntentCheckResult = false;
                        }
                        checkedResultMap.put(branchKey, checkResult);
                    }
                    // 设置默认分支
                    Edge defaultEdge = MapUtils.getObject(sourcePortIdToEdgeMap, "default_output");
                    String defaultBranchKey = WorkflowUtils.getBranchKey(defaultEdge);
                    if (!checkedResultMap.containsKey(defaultBranchKey)) {
                        checkedResultMap.put(defaultBranchKey, defaultIntentCheckResult);
                    }

                    // 设置输出
                    executionInfo.setOutput(checkedResultMap);
                    return Mono.just(workflowContext);
                })
                .onErrorResume(t -> {
                    log.error(t.getMessage(), t);
                    // sourcePortId=default设置为true，其他节点为false
                    Map<String, Object> checkedResultMap = Maps.newHashMap();
                    for (Edge edge : outgoingEdges) {
                        String sourcePortId = edge.getSourcePortId();
                        String branchKey = WorkflowUtils.getBranchKey(edge);
                        checkedResultMap.put(branchKey, StringUtils.equalsIgnoreCase(sourcePortId, "default_output"));
                    }
                    executionInfo.setOutput(checkedResultMap);
                    return Mono.just(workflowContext);
                });
    }

}
