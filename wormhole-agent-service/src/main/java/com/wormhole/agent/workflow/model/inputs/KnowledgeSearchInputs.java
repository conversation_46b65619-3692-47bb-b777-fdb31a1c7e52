package com.wormhole.agent.workflow.model.inputs;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.enums.MetadataFilterMode;
import com.wormhole.agent.knowledge.model.constant.FilterConnector;
import com.wormhole.agent.knowledge.model.constant.FilterOperator;
import com.wormhole.agent.knowledge.params.KnowledgeSearchParams;
import com.wormhole.agent.workflow.model.Node;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * KnowledgeSearchInputs
 *
 * <AUTHOR>
 * @version 2024/12/27
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class KnowledgeSearchInputs extends Node.Inputs {
    /**
     * 知识库列表
     */
    private List<String> knowledgeCodeList;
    /**
     * 最大召回数量
     */
    private Integer topK;
    /**
     * 结果重排
     * 根据相关性或质量对检索到的文档切片进行重新排序,以提高生成答案的准确性
     */
    @Builder.Default
    private boolean useRerank = true;
    /**
     * 查询改写
     * 对输入的查询语句进行优化重构，从而能够更准确地捕捉用户意图，提升信息检索的效率
     */
    @Builder.Default
    private boolean useRewrite = true;
    /**
     * TODO 仅查看个人文档
     */
    private boolean isPersonalOnly;
    /**
     * TODO 最小匹配度
     */
    private double minScore;
    /**
     * 搜索策略：语义、混合、全文
     */
    private Integer strategy;
//    /**
//     * 重排序的模型
//     */
//    private Map<String, Object> rerankStrategy;
    /**
     * 重排序的模型参数
     */
    private KnowledgeSearchParams.RerankConfig rerankConfig;
    /**
     * 检索时文本转向量的模型
     */
    private String embeddingModel;

    /**
     * 元数据过滤模式
     */
    private MetadataFilterMode metadataFilterMode;

    /**
     * 元数据过滤模型配置 AUTOMATIC
     */
    private LlmInputs.LlmParams metadataModelConfig;

    /**
     * 元数据过滤条件 MANUAL
     */
    private MetadataFilteringConditions metadataFilteringConditions;


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class MetadataFilteringConditions {

        /**
         * 连接操作符
         */
        private FilterConnector filterConnector;

        /**
         * 条件
         */
        private List<MetadataFilteringCondition> conditions;

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class MetadataFilteringCondition {

        /**
         * 元数据名称
         */
        private String name;

        /**
         * 比较操作符
         */
        private FilterOperator comparisonOperator;

        /**
         * 元数据值
         */
        private Node.Input value;
    }

}
