package com.wormhole.agent.workflow.bot.test;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.model.*;
import com.wormhole.agent.workflow.model.inputs.ConditionInputs;
import com.wormhole.agent.workflow.model.inputs.EndInputs;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import com.wormhole.agent.workflow.util.ConditionInputsBuilder;
import com.wormhole.agent.workflow.util.LocalBotUtils;
import com.wormhole.common.util.JacksonUtils;

import java.util.List;

/**
 * WorkflowAgentCondTestBot
 *
 * <AUTHOR>
 * @version 2024/12/26
 */
public class WorkflowAgentCondTestBot {

    public static final BotInfo BOT_INFO;

    public static final Workflow WORKFLOW;

    static {
        String botCode = "workflow_agent_cond_test_bot";
        Node startNode = Node.builder()
                .id("100001")
                .type(NodeTypeEnum.START.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("开始节点").build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name(WorkflowConstant.USER_INPUT).type(SchemaTypeEnum.STRING.getType()).description(WorkflowConstant.BOT_USER_INPUT_DESC).required(true).build(),
                                Node.Output.builder().name("str").type(SchemaTypeEnum.STRING.getType()).description("输入内容").required(true).build()

                        ))
                        .build())
                .build();
        ConditionInputs condInputs = ConditionInputsBuilder.create()
                .addBranch(ConditionInputsBuilder.BranchBuilder.create()
                        .withCondition(ConditionInputsBuilder.ConditionBuilder.create()
                                .setLogic(1)
                                .addConditionItem(ConditionInputsBuilder.ConditionItemBuilder.create()
                                        .setLeft(WorkflowConstant.BOT_USER_INPUT_DESC, SchemaTypeEnum.STRING, InputValueTypeEnum.ref)
                                        .setRight("t1", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
                                        .setOperator("equal")
                                        .build())
//                                .addConditionItem(ConditionInputsBuilder.ConditionItemBuilder.create()
//                                        .setLeft("$.str", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setRight("t2", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setOperator("equal")
//                                        .build())
                                .build())
//                        .withCondition(ConditionInputsBuilder.ConditionBuilder.create()
//                                .setLogic(2)
//                                .addConditionItem(ConditionInputsBuilder.ConditionItemBuilder.create()
//                                        .setLeft("$.str", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setRight("t2", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setOperator("equal")
//                                        .build())
//                                .addConditionItem(ConditionInputsBuilder.ConditionItemBuilder.create()
//                                        .setLeft("$.str", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setRight("ooo", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setOperator("equal")
//                                        .build())
//                                .build())
                        .build())
//                .addBranch(ConditionInputsBuilder.BranchBuilder.create()
//                        .withCondition(ConditionInputsBuilder.ConditionBuilder.create()
//                                .setLogic(1)
//                                .addConditionItem(ConditionInputsBuilder.ConditionItemBuilder.create()
//                                        .setLeft("$.str", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setRight("t3", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setOperator("equal")
//                                        .build())
//                                .addConditionItem(ConditionInputsBuilder.ConditionItemBuilder.create()
//                                        .setLeft("$.str", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setRight("llm", SchemaTypeEnum.STRING, InputValueTypeEnum.literal)
//                                        .setOperator("equal")
//                                        .build())
//                                .build())
//                        .build())
                .build();


        Node condNode = Node.builder()
                .id("1033333")
                .type(NodeTypeEnum.CONDITIONAL_BRANCH.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("条件分支").build())
                        .inputs(condInputs)
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output").type(SchemaTypeEnum.STRING.getType()).description("条件分支").build()
                        ))
                        .build())
                .build();
        System.out.println(JacksonUtils.writeValueAsString(condNode));
        System.out.println("---------");
        Node llmNode = Node.builder()
                .id("1077777")
                .type(NodeTypeEnum.LLM.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("llm").build())
                        .inputs(LlmInputs.builder()
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("text", startNode, WorkflowConstant.USER_INPUT)
                                ))
                                .llmParams(LlmInputs.LlmParams.builder()
                                        .enableChatHistory(false)
                                        .systemPrompt("""
                                                你是一个有帮助的助手。
                                                """)
                                        .userPrompt("""
                                                用户问题：${text}
                                                """)
                                        .model(UnifiedModelEnum.GPT_4_O_MINI.getModel())
                                        .modelProvider(UnifiedModelEnum.GPT_4_O_MINI.getProvider())
                                        .temperature(0.3d)
                                        .build())
                                .build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output").type(SchemaTypeEnum.STRING.getType()).description("大模型结果输出").build()
                        ))
                        .build())
                .build();

        Node endNode = Node.builder()
                .id("900001")
                .type(NodeTypeEnum.END.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("结束节点").build())
                        .inputs(EndInputs.builder()
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("output", condNode, "output")
                                ))
                                .terminatePlan(TerminatePlanEnum.USE_ANSWER_CONTENT.getPlan())
                                .templateEngineType(ContentFormatTypeEnum.JSON_PATH.getType())
                                .content(
                                        Node.Input.builder()
                                                .type(SchemaTypeEnum.STRING.getType())
                                                .value(Node.Value.builder().type(InputValueTypeEnum.literal.getType()).content("$.output").build())
                                                .build()
                                )
                                .build())
                        .build())
                .build();

        List<Node> nodes = Lists.newArrayList(startNode, condNode, endNode);
        List<Edge> edges = Lists.newArrayList(
                Edge.builder().sourceNodeId(startNode.getId()).targetNodeId(condNode.getId()).build(),
//                Edge.builder().sourceNodeId(condNode.getId()).targetNodeId(llmNode.getId()).build(),
                Edge.builder().sourceNodeId(condNode.getId()).targetNodeId(endNode.getId()).build()
        );
        WorkflowDefinition workflowDefinition = WorkflowDefinition.builder()
                .workflowCode(botCode)
                .workflowName("测试")
                .nodes(nodes)
                .edges(edges)
                .build();
        WORKFLOW = new Workflow(workflowDefinition);

        BOT_INFO = BotInfo.builder()
                .botCode(botCode)
                .botMode(BotModeEnum.WORKFLOW_AGENT.getValue())
                .workflowCodeList(Lists.newArrayList(botCode))
                .build();
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(WORKFLOW));
    }
}
