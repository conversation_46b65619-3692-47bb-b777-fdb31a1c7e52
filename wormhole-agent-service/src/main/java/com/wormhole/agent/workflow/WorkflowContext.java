package com.wormhole.agent.workflow;

import com.google.common.collect.Maps;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.workflow.model.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.MapUtils;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WorkflowContext
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowContext {
    /**
     * 工作流code
     */
    private String workflowCode;
    /**
     * 工作流详情
     */
    private Workflow workflow;
    /**
     * 意图识别结果
     */
    private ChatToolCall chatToolCall;
    /**
     * 工作流执行实例id
     */
    @Builder.Default
    private String executionId = UUID.randomUUID().toString();
    /**
     * 节点上下文
     */
    @Builder.Default
    private Map<String, NodeExecutionInfo<Node.Inputs>> nodeExecutionInfoMap = new ConcurrentHashMap<>();
    /**
     * 节点执行缓存
     */
    @Builder.Default
    private Map<String, Mono<WorkflowContext>> nodeExecutions = new ConcurrentHashMap<>();
    /**
     * 工作流输入
     */
    @Builder.Default
    private Map<String, Object> initialInput = Maps.newHashMap();
    /**
     * 智能体回复方式
     */
    private TerminatePlanEnum terminatePlan;
    /**
     * 智能体最近对话记录
     */
    private List<OpenAiChatMessage> recentMessageList;
    /**
     * 工作流调用来源
     */
    private WorkflowRequestSourceEnum workflowRequestSource;
    /**
     * 开始时间
     */
    @Builder.Default
    private long startTime = System.currentTimeMillis();
    /**
     * 结束时间
     */
    private Long endTime;

    private ModelContext.ModelLogContext modelLogContext;

    private boolean isDebug = false;

    @Builder.Default
    private int recentRound = 3;

    private String userId;

    private Sinks.Many<ChatCompletions> sinks;

    private String conversationId;

    private String traceId;

    @Builder.Default
    private boolean nodeOutPutFlag = false;

    /**
     * 工具调用上下文
     */
    private ToolChainContext toolChainContext;

    @Builder.Default
    private boolean run = true;
    @Builder.Default
    private Map<String, ModelRequestInfo> modelRequestInfoMap =  Maps.newHashMap();

    public void sinkNext(ChatCompletions chatCompletions) {
        if (Objects.nonNull(chatCompletions)) {
            ChatCompletions.Metadata metadata = chatCompletions.getMetadata();
            if (Objects.nonNull(metadata)) {
                metadata.setConversationId(getConversationId());
                metadata.setTraceId(getTraceId());
            }
            getSinks().tryEmitNext(chatCompletions);
            nodeOutPutFlag = true;
        }
    }


    public void initializeNodeExecutions() {
        workflow.getWorkflowDefinition().getNodes().forEach(node -> {
            if (!nodeExecutionInfoMap.containsKey(node.getId())) {
                nodeExecutionInfoMap.put(node.getId(), NodeExecutionInfo.createPending());
            }
        });
    }

    public void recordNodeExecution(String nodeId, NodeExecutionInfo<Node.Inputs> executionInfo) {
        if (nodeId != null && executionInfo != null) {
            nodeExecutionInfoMap.put(nodeId, executionInfo);
        }
    }

    public Map<String, NodeExecutionInfo<Node.Inputs>> getNodeExecutionInfoMap() {
        return Collections.unmodifiableMap(new HashMap<>(nodeExecutionInfoMap));
    }

    public NodeExecutionInfo<Node.Inputs> getNodeExecutionInfo(String nodeId) {
        return nodeExecutionInfoMap.get(nodeId);
    }

    public NodeExecutionInfo<Node.Inputs> getStartNodeExecutionInfo() {
        return nodeExecutionInfoMap.get(workflow.getStartNode().getId());
    }

    public NodeExecutionInfo<Node.Inputs> getEndNodeExecutionInfo() {
        return nodeExecutionInfoMap.get(workflow.getEndNode().getId());
    }

    public void setNodeExecution(String nodeId, Mono<WorkflowContext> execution) {
        if (nodeId != null && execution != null) {
            nodeExecutions.put(nodeId, execution);
        }
    }

    public Mono<WorkflowContext> getNodeExecution(String nodeId) {
        return MapUtils.getObject(nodeExecutions, nodeId);
    }

    public void markCompleted() {
        this.endTime = System.currentTimeMillis();
    }

    public void skipUnexecutedNodes() {
        nodeExecutionInfoMap.values().stream()
                .filter(info -> info.getStatus() == NodeExecutionStatus.PENDING)
                .forEach(info -> {
                    info.setStatus(NodeExecutionStatus.SKIPPED);
                    info.setActive(false);
                });
    }
}
