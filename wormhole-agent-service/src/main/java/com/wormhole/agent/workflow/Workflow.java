package com.wormhole.agent.workflow;

import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ContextedRuntimeException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Workflow
 *
 * <AUTHOR>
 * @version 2024/9/20
 */
public class Workflow {
    /**
     * 工作流定义
     */
    private final WorkflowDefinition workflowDefinition;
    /**
     * node_id -> node
     */
    private final Map<String, Node> nodeMap;
    /**
     * source_node_id -> edge
     */
    private final Map<String, List<Edge>> outgoingEdges;
    /**
     * target_node_id -> edge
     */
    private final Map<String, List<Edge>> incomingEdges;

    public Workflow(WorkflowDefinition workflowDefinition) {
        OpenAiTool openAiTool = WorkflowUtils.getOpenAiTool(workflowDefinition).orElse(null);
        workflowDefinition.setTool(openAiTool);
        this.workflowDefinition = workflowDefinition;
        if (CollectionUtils.isEmpty(workflowDefinition.getNodes())) {
            workflowDefinition.setNodes(new ArrayList<>());
        }
        if (CollectionUtils.isEmpty(workflowDefinition.getEdges())) {
            workflowDefinition.setEdges(new ArrayList<>());
        }
        this.nodeMap = Collections.unmodifiableMap(workflowDefinition.getNodes().stream().collect(Collectors.toMap(Node::getId, n -> n)));
        Map<String, List<Edge>> outgoing = new HashMap<>();
        Map<String, List<Edge>> incoming = new HashMap<>();
        for (Edge edge : workflowDefinition.getEdges()) {
            outgoing.computeIfAbsent(edge.getSourceNodeId(), k -> new ArrayList<>()).add(edge);
            incoming.computeIfAbsent(edge.getTargetNodeId(), k -> new ArrayList<>()).add(edge);
        }
        this.outgoingEdges = Collections.unmodifiableMap(outgoing);
        this.incomingEdges = Collections.unmodifiableMap(incoming);
    }

    public static Workflow fromJson(String json) {
        try {
            WorkflowDefinition workflowDefinition = JacksonUtils.readValue(json, WorkflowDefinition.class);
            return new Workflow(workflowDefinition);
        } catch (Exception e) {
            throw new ContextedRuntimeException(e).addContextValue("json", json);
        }
    }

    public WorkflowDefinition getWorkflowDefinition() {
        return workflowDefinition;
    }

    public Node getNodeById(String id) {
        return nodeMap.get(id);
    }

    public List<Edge> getOutgoingEdges(String nodeId) {
        return outgoingEdges.getOrDefault(nodeId, Collections.emptyList());
    }

    public List<Edge> getIncomingEdges(String nodeId) {
        return incomingEdges.getOrDefault(nodeId, Collections.emptyList());
    }

    public Node getStartNode() {
        return workflowDefinition.getNodes().stream()
                .filter(node -> StringUtils.equalsAnyIgnoreCase(node.getType(), NodeTypeEnum.START.getType(), NodeTypeEnum.START.getCozeTypeId()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No start node found"));
    }

    public Node getEndNode() {
        return workflowDefinition.getNodes().stream()
                .filter(node -> StringUtils.equalsAnyIgnoreCase(node.getType(), NodeTypeEnum.END.getType(), NodeTypeEnum.END.getCozeTypeId()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("No end node found"));
    }
}
