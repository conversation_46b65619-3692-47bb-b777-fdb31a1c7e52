{"workflowDefinition": {"workflow_code": "hotel_search", "workflow_name": "酒店搜索机器人", "nodes": [{"id": "start", "type": "start", "data": {"node_meta": {"title": "开始"}, "outputs": [{"type": "string", "name": "USER_INPUT", "required": true, "description": "用户本轮对话输入内容"}]}}, {"id": "hotel_search", "type": "hotel_search", "data": {"node_meta": {"title": "知识库", "description": "在选定的知识中,根据输入变量召回最匹配的信息,并以列表形式返回", "sub_title": "知识库"}, "inputs": {"input_parameters": [{"name": "Query", "input": {"type": "string", "value": {"type": "ref", "content": {"block_id": "start", "name": "USER_INPUT"}}}}]}, "outputs": [{"type": "list", "name": "output_list", "required": false, "schema": {"type": "object", "schema": [{"type": "string", "name": "output"}]}}]}}, {"id": "end", "type": "end", "data": {"node_meta": {"title": "结束节点"}, "inputs": {"input_parameters": [{"name": "output", "input": {"type": "list", "value": {"type": "ref", "content": {"block_id": "hotel_search", "name": "output_list"}}}}], "terminate_plan": "useAnswerContent", "streaming_output": false, "content": {"type": "string", "value": {"type": "literal", "content": "$.output"}}, "format_type": "json_path"}, "outputs": []}}], "edges": []}, "startNode": {"id": "start", "type": "start", "data": {"node_meta": {"title": "开始"}, "outputs": [{"type": "string", "name": "USER_INPUT", "required": true, "description": "用户本轮对话输入内容"}]}}, "endNode": {"id": "end", "type": "end", "data": {"node_meta": {"title": "结束节点"}, "inputs": {"input_parameters": [{"name": "output", "input": {"type": "list", "value": {"type": "ref", "content": {"block_id": "hotel_search", "name": "output_list"}}}}], "terminate_plan": "useAnswerContent", "streaming_output": false, "content": {"type": "string", "value": {"type": "literal", "content": "$.output"}}, "format_type": "json_path"}, "outputs": []}}}