{"workflow_id": 20001, "workflow_code": "condition_test", "workflow_name": "condition_test", "nodes": [{"id": "100001", "type": "1", "meta": {"position": {"x": -3764.670835046193, "y": -2015.7229296668634}}, "data": {"node_meta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start.png", "sub_title": "", "title": "开始"}, "outputs": [{"type": "string", "name": "USER_INPUT", "required": false, "description": "用户本轮对话输入内容"}]}}, {"id": "900001", "type": "2", "meta": {"position": {"x": -2180.231039051738, "y": -1895.97279620334}}, "data": {"node_meta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End.png", "sub_title": "", "title": "结束"}, "inputs": {"terminate_plan": "use_answer_content", "streaming_output": false, "input_parameters": [{"name": "c0", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "ref", "content": {"source": "block_output", "block_id": "127068", "name": "output"}}}}, {"name": "c1", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block_output", "block_id": "172152", "name": "output"}}}}, {"name": "c2", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block_output", "block_id": "151541", "name": "output"}}}}], "content": {"type": "string", "value": {"type": "literal", "content": "{{t}}"}}}}}, {"id": "102910", "type": "8", "meta": {"position": {"x": -3346.18033638468, "y": -2012.3016436145876}}, "data": {"node_meta": {"description": "连接多个下游分支，若设定的条件成立则仅运行对应的分支，若均不成立则只运行'否则'分支", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Condition.png", "sub_title": "选择器", "title": "选择器"}, "inputs": {"branches": [{"condition": {"logic": 1, "conditions": [{"operator": "equal", "left": {"input": {"type": "string", "value": {"type": "ref", "content": {"source": "block_output", "block_id": "100001", "name": "USER_INPUT"}}}}, "right": {"input": {"type": "string", "value": {"type": "literal", "content": "123"}}}}, {"operator": "containsany", "left": {"input": {"type": "string", "value": {"type": "ref", "content": {"source": "block_output", "block_id": "100001", "name": "USER_INPUT"}}}}, "right": {"input": {"type": "string", "value": {"type": "literal", "content": "12"}}}}]}}, {"condition": {"logic": 2, "conditions": [{"operator": "equal", "left": {"input": {"type": "string", "value": {"type": "ref", "content": {"source": "block_output", "block_id": "100001", "name": "USER_INPUT"}}}}, "right": {"input": {"type": "string", "value": {"type": "literal", "content": "456"}}}}]}}]}}}, {"id": "127068", "type": "15", "meta": {"position": {"x": -2862.7410695324647, "y": -2106.72516647395}}, "data": {"node_meta": {"description": "用于处理多个字符串类型变量的格式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Text.png", "sub_title": "文本处理", "title": "文本处理"}, "inputs": {"method": "split", "input_parameters": [{"name": "String", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block_output", "block_id": "100001", "name": "USER_INPUT"}}}}], "split_params": [{"name": "delimiters", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "literal", "content": [" "]}}}, {"name": "all_delimiters", "input": {"type": "list", "schema": {"type": "object", "schema": [{"type": "string", "name": "label", "required": true}, {"type": "string", "name": "value", "required": true}, {"type": "boolean", "name": "is_default", "required": true}]}, "value": {"type": "literal", "content": [{"label": "换行", "value": "\n", "is_default": true}, {"label": "制表符", "value": "\t", "is_default": true}, {"label": "句号", "value": "。", "is_default": true}, {"label": "逗号", "value": "，", "is_default": true}, {"label": "分号", "value": "；", "is_default": true}, {"label": "空格", "value": " ", "is_default": true}]}}}]}, "outputs": [{"type": "list", "name": "output", "schema": {"type": "string"}, "required": true}]}}, {"id": "172152", "type": "15", "meta": {"position": {"x": -2866.698752432159, "y": -1930.8433774661978}}, "data": {"node_meta": {"description": "用于处理多个字符串类型变量的格式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Text.png", "sub_title": "文本处理", "title": "文本处理_1"}, "inputs": {"method": "concat", "input_parameters": [{"name": "String1", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block_output", "block_id": "100001", "name": "USER_INPUT"}}}}, {"name": "String2", "input": {"type": "string", "value": {"type": "literal", "content": "1"}}}], "concat_params": [{"name": "concat_result", "input": {"type": "string", "value": {"type": "literal", "content": "{{String1}}-1"}}}, {"name": "array_item_concat_char", "input": {"type": "string", "value": {"type": "literal", "content": "，"}}}, {"name": "all_array_item_concat_chars", "input": {"type": "list", "schema": {"type": "object", "schema": [{"type": "string", "name": "label", "required": true}, {"type": "string", "name": "value", "required": true}, {"type": "boolean", "name": "is_default", "required": true}]}, "value": {"type": "literal", "content": [{"is_default": true, "label": "换行", "value": "\n"}, {"is_default": true, "label": "制表符", "value": "\t"}, {"is_default": true, "label": "句号", "value": "。"}, {"is_default": true, "label": "逗号", "value": "，"}, {"is_default": true, "label": "分号", "value": "；"}, {"is_default": true, "label": "空格", "value": " "}]}}}]}, "outputs": [{"type": "string", "name": "output", "required": true}]}}, {"id": "151541", "type": "15", "meta": {"position": {"x": -2868.772657627684, "y": -1706.9453255128105}}, "data": {"node_meta": {"description": "用于处理多个字符串类型变量的格式", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Text.png", "sub_title": "文本处理", "title": "文本处理_2"}, "inputs": {"method": "concat", "input_parameters": [{"name": "String1", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block_output", "block_id": "100001", "name": "USER_INPUT"}}}}], "concat_params": [{"name": "concat_result", "input": {"type": "string", "value": {"type": "literal", "content": "{{String1}}"}}}, {"name": "array_item_concat_char", "input": {"type": "string", "value": {"type": "literal", "content": "-"}}}, {"name": "all_array_item_concat_chars", "input": {"type": "list", "schema": {"type": "object", "schema": [{"type": "string", "name": "label", "required": true}, {"type": "string", "name": "value", "required": true}, {"type": "boolean", "name": "is_default", "required": true}]}, "value": {"type": "literal", "content": [{"is_default": true, "label": "换行", "value": "\n"}, {"is_default": true, "label": "制表符", "value": "\t"}, {"is_default": true, "label": "句号", "value": "。"}, {"is_default": true, "label": "逗号", "value": "，"}, {"is_default": true, "label": "分号", "value": "；"}, {"is_default": true, "label": "空格", "value": " "}, {"label": "-", "value": "-", "is_default": false}]}}}]}, "outputs": [{"type": "string", "name": "output", "required": true}]}}], "edges": [{"source_node_id": "100001", "target_node_id": "102910"}, {"source_node_id": "127068", "target_node_id": "900001"}, {"source_node_id": "172152", "target_node_id": "900001"}, {"source_node_id": "151541", "target_node_id": "900001"}, {"source_node_id": "102910", "target_node_id": "127068", "source_port_id": "true"}, {"source_node_id": "102910", "target_node_id": "172152", "source_port_id": "true_1"}, {"source_node_id": "102910", "target_node_id": "151541", "source_port_id": "false"}]}