package com.wormhole.agent.workflow.node;

import com.google.common.base.Preconditions;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * NodeExecutorFactory
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Component
public class NodeExecutorFactory {

    private static final Map<NodeTypeEnum, NodeExecutor> NODE_EXECUTOR_MAP = new HashMap<>(16);

    @Autowired
    public NodeExecutorFactory(List<NodeExecutor> nodeExecutors) {
        for (NodeExecutor executor : nodeExecutors) {
            NODE_EXECUTOR_MAP.put(executor.getType(), executor);
        }
    }

    public NodeExecutor getExecutor(String type) {
        NodeTypeEnum nodeTypeEnum = NodeTypeEnum.getByType(type);
        Preconditions.checkArgument(Objects.nonNull(nodeTypeEnum), "type[%s] is not valid", type);
        return NODE_EXECUTOR_MAP.get(nodeTypeEnum);
    }
}