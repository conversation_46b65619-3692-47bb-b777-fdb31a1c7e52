package com.wormhole.agent.workflow.bot.user;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.wormhole.agent.core.model.entity.UserChatMessageFieldEnum;
import com.wormhole.agent.core.model.entity.UserEntity;
import com.wormhole.agent.core.model.entity.UserFieldEnum;
import com.wormhole.agent.workflow.bot.user.model.ApiResponse;
import com.wormhole.agent.workflow.bot.user.model.RemoteUserLevelInfo;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class UserSearchHttpService {
    private final BdwRemoteInfoProperties bdwRemoteInfoProperties;

    private final WebClient webClient;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public UserSearchHttpService(BdwRemoteInfoProperties bdwRemoteInfoProperties) {

        this.webClient = WebClient.builder()
                .baseUrl(bdwRemoteInfoProperties.getBdwUserBaseHost())
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
        this.bdwRemoteInfoProperties = bdwRemoteInfoProperties;
    }

    public Mono<Map<String, Object>> remoteBdwUserLevelInfo(String userId,String thirdUserId) {
        Map<String,Object> body = new HashMap<>();
        if (StringUtils.isNotBlank(thirdUserId)){
            log.info("remoteBdwUserLevelInfo thirdUserId not null {}",thirdUserId);
            body.put(UserConstant.USER_ID_QUERY, NumberUtils.toLong(thirdUserId));
            return postUserInfo(body);
        } else if (StringUtils.isNotBlank(userId)){
            Criteria criteria = Criteria.where(UserFieldEnum.user_id.name()).is(userId);
            Query limitOne = Query.query(criteria)
                    .limit(1)
                    .sort(Sort.by(Sort.Direction.DESC, UserFieldEnum.id.name()));
            return r2dbcEntityTemplate.selectOne(limitOne, UserEntity.class)
                    .map(userOne -> {
                        log.info("remoteBdwUserLevelInfo phone not null {}", userOne.getPhone());
                        if (StringUtils.isNotBlank(userOne.getPhone())){
                            body.put(UserConstant.MOBILE_QUERY, userOne.getPhone());
                        }
                        return body;
                    })
                    .flatMap(this::postUserInfo);
        }
        log.info("remoteBdwUserLevelInfo userId and thirdUserId is null {} {}",userId,thirdUserId);
        return Mono.just(body);
    }

    private Mono<Map<String, Object>> postUserInfo( Map<String, Object> body) {
        return webClient.post()
                .uri(userBuilder -> userBuilder.path(bdwRemoteInfoProperties.getBdwUserInfoUri()).build())
                .bodyValue(body)
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER))
                                ))
                .bodyToMono(ApiResponse.class)
                .flatMap(this::handleResponse)
                .doOnSubscribe(sub -> log.debug("Requesting BDW user info for: {}", JSONUtil.toJsonStr(body)))
                .doOnError(e -> log.error("BDW user info request failed | req: {}", JSONUtil.toJsonStr(body), e));
    }

    private Mono<Map<String, Object>> handleResponse(ApiResponse<RemoteUserLevelInfo> response) {
        if (response.getCode() == 0) { // 根据业务文档确认0是否为错误码
            log.warn("BDW service returned business error | code: {}, msg: {}",
                    response.getErrorCode(), response.getMsg());
            return Mono.error(new BusinessException(
                    response.getErrorCode(),
                    response.getMsg()
            ));
        }
        RemoteUserLevelInfo remoteUserInfo = JacksonUtils.readValue(JSONObject.toJSONString(response.getData()), RemoteUserLevelInfo.class);
        Map<String, Object> userInfoMap = new HashMap<>();
        if (remoteUserInfo != null){
            userInfoMap.put(UserConstant.NICK_NAME, remoteUserInfo.getNickName());
            userInfoMap.put(UserConstant.TIME_VALUE_TOTAL, remoteUserInfo.getCurrentTimeValue());
            userInfoMap.put(UserConstant.TIME_VALUE_REMAIN, remoteUserInfo.getTimeValueBalance());
            userInfoMap.put(UserConstant.USER_LEVEL, UserConstant.USER_LEVEL_MAP.get(remoteUserInfo.getTimeValueLevel()));
            userInfoMap.put(UserConstant.USER_CREATE_TIME, remoteUserInfo.getCreateTime());
        }
        log.info("handleResponse BDW user info response | {}", remoteUserInfo);
        return Mono.just(userInfoMap)
                .switchIfEmpty(Mono.error(new BusinessException(response.getErrorCode(), response.getErrorDesc())));
    }
}
