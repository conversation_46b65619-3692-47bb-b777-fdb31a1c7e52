package com.wormhole.agent.workflow.util;

import com.wormhole.agent.workflow.model.InputValueTypeEnum;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.SchemaTypeEnum;
import com.wormhole.agent.workflow.model.inputs.ConditionInputs;

import java.util.ArrayList;
import java.util.List;

public class ConditionInputsBuilder {
    private ConditionInputs conditionInputs;
    private List<ConditionInputs.Branch> branches;

    public ConditionInputsBuilder() {
        this.conditionInputs = new ConditionInputs();
        this.branches = new ArrayList<>();
    }

    public static ConditionInputsBuilder create() {
        return new ConditionInputsBuilder();
    }

    public ConditionInputsBuilder addBranch(ConditionInputs.Branch branch) {
        this.branches.add(branch);
        return this;
    }

    public ConditionInputsBuilder withBranches(List<ConditionInputs.Branch> branches) {
        this.branches = branches;
        return this;
    }

    public ConditionInputs build() {
        conditionInputs.setBranches(branches);
        return conditionInputs;
    }

    public static class BranchBuilder {
        private ConditionInputs.Branch branch;
        private ConditionInputs.Condition condition;

        public BranchBuilder() {
            this.branch = new ConditionInputs.Branch();
        }

        public static BranchBuilder create() {
            return new BranchBuilder();
        }

        public BranchBuilder withCondition(ConditionInputs.Condition condition) {
            this.condition = condition;
            return this;
        }

        public ConditionInputs.Branch build() {
            branch.setCondition(condition);
            return branch;
        }
    }

    public static class ConditionBuilder {
        private ConditionInputs.Condition condition;
        private List<ConditionInputs.ConditionItem> conditions;

        public ConditionBuilder() {
            this.condition = new ConditionInputs.Condition();
            this.conditions = new ArrayList<>();
        }

        public static ConditionBuilder create() {
            return new ConditionBuilder();
        }

        public ConditionBuilder setLogic(int logic) {
            this.condition.setLogic(logic);
            return this;
        }

        public ConditionBuilder addConditionItem(ConditionInputs.ConditionItem item) {
            this.conditions.add(item);
            return this;
        }

        public ConditionInputs.Condition build() {
            condition.setConditions(conditions);
            return condition;
        }
    }

    public static class ConditionItemBuilder {
        private ConditionInputs.ConditionItem conditionItem;

        public ConditionItemBuilder() {
            this.conditionItem = new ConditionInputs.ConditionItem();
        }

        public static ConditionItemBuilder create() {
            return new ConditionItemBuilder();
        }

        public ConditionItemBuilder setLeft(String content, SchemaTypeEnum schemaType, InputValueTypeEnum inputValueType) {
            conditionItem.setLeft(createConditionSide(content, schemaType, inputValueType));
            return this;
        }

        public ConditionItemBuilder setRight(String content, SchemaTypeEnum schemaType, InputValueTypeEnum inputValueType) {
            conditionItem.setRight(createConditionSide(content, schemaType, inputValueType));
            return this;
        }

        public ConditionItemBuilder setOperator(String operator) {
            conditionItem.setOperator(operator);
            return this;
        }

        public ConditionInputs.ConditionItem build() {
            return conditionItem;
        }

        private ConditionInputs.ConditionSide createConditionSide(String content, SchemaTypeEnum schemaType, InputValueTypeEnum inputValueType) {
            ConditionInputs.ConditionSide side = new ConditionInputs.ConditionSide();
            side.setInput(Node.Input.builder()
                    .type(schemaType.getType())
                    .value(Node.Value.builder()
                            .type(inputValueType.getType())
                            .content(content)
                            .build())
                    .build());
            return side;
        }
    }
}

