package com.wormhole.agent.workflow.model.inputs;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.workflow.model.Node;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * WorkflowInputs
 *
 * <AUTHOR>
 * @version 2024/12/2
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@EqualsAndHashCode(callSuper = true)
public class WorkflowInputs extends Node.Inputs {
    /**
     * 工作流code
     */
    private String workflowCode;
    /**
     * 空间code
     */
    private String spaceCode;
    /**
     * 输入参数
     */
    private List<Node.Output> inputDefs;
    /**
     * type=0 单个输入的工作流，workflowAgent中的工作流
     * type=1 多个输入的工作流，LlmAgent中的工作流
     */
    private Integer type;

}
