package com.wormhole.agent.workflow.bot.deepseek;

import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.common.util.JacksonUtils;
import org.springframework.stereotype.Component;

/**
 * DeepseekR1DistillQwen32bBot
 *
 * <AUTHOR>
 * @version 2024/12/8
 */
@BotEnabled
@Component
public class DeepseekR1DistillQwen32bBot implements Bot {

    @Override
    public BotInfo createBot() {
        UnifiedModelEnum unifiedModelEnum = UnifiedModelEnum.ALIYUN_DEEPSEEK_R1_DISTILL_QWEN_32B;
        BotInfo botInfoDTO = BotInfo.builder()
                .botCode(unifiedModelEnum.getModel())
                .name(String.format("【通义千问】%s", unifiedModelEnum.getModel()))
                .description("DeepSeek-R1-Distill 系列模型是基于知识蒸馏技术，通过使用 DeepSeek-R1 生成的训练样本对 Qwen、Llama 等开源大模型进行微调训练后，所得到的增强型模型。")
                .botMode(BotModeEnum.LLM_AGENT.getValue())
                .modelInfo(ModelInfo.builder()
                        .model(unifiedModelEnum.getModel())
                        .modelProvider(unifiedModelEnum.getProvider())
                        .temperature(1.3)
                        .recentRound(3)
                        .build())
                .promptInfo(PromptInfo.builder()
                        .systemPrompt("你是一个有帮助的助手")
                        .build())
                .build();
        return botInfoDTO;
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(new DeepseekR1DistillQwen32bBot().createBot()));
    }
}
