package com.wormhole.agent.workflow.model.inputs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.workflow.model.Node;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * LlmInputs
 *
 * <AUTHOR>
 * @version 2024/10/24
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class LlmInputs extends Node.Inputs {

    private LlmParams llmParams;

    private boolean intentSwitch = false;

    private IntentLlmParams intentLlmParams;

    private FunctionCallParams functionCallParams;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class LlmParams {
        private String modelProvider;
        private String generationDiversity;
        @Builder.Default
        private boolean enableChatHistory = false;
        /**
         * text/markdown/json
         */
        private String responseFormat;
        private String model;
        private Double temperature;
        private Double frequencyPenalty;
        /**
         * maxCompletionTokens
         */
        private Integer maxCompletionTokens;
        /**
         * 系统提示词
         */
        private String systemPrompt;
        /**
         * 用户提示词
         */
        private String userPrompt;

        private String intentSystemPrompt;

        private String intentUserPrompt;

        /**
         * 会话轮数
         */
        private Integer chatHistoryRound;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IntentLlmParams extends LlmParams{
        private Integer maxRecursionDepth = 1;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class FunctionCallParams {

        private List<PluginFunctionCallParam> pluginList;

        private List<WorkflowFunctionCallParam> workflowList;

        private List<McpFunctionCallParam> mcpServerList;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class WorkflowFunctionCallParam {

        private String workflowCode;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PluginFunctionCallParam {

        private String pluginToolCode;

        private String pluginCode;

        private Integer version;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class McpFunctionCallParam {

        /**
         * MCP客户端名称
         */
        private String mcpServerName;

        /**
         * MCP工具名称
         */
        private List<String> toolNames;

    }
}