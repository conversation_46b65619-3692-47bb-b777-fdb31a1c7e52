package com.wormhole.agent.workflow.bot.deepseek;

import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.common.util.JacksonUtils;
import org.springframework.stereotype.Component;

/**
 * SiliconflowDeepseekR1Bot
 *
 * <AUTHOR>
 * @version 2024/12/8
 */
@BotEnabled
@Component
public class SiliconflowDeepseekR1Bot implements Bot {

    @Override
    public BotInfo createBot() {
        UnifiedModelEnum unifiedModelEnum = UnifiedModelEnum.SF_DEEPSEEK_R1;
        BotInfo botInfoDTO = BotInfo.builder()
                .botCode(unifiedModelEnum.getModel())
                .name(String.format("【硅基流动】%s", unifiedModelEnum.getModel()))
                .description("【华为云昇腾云服务 - 全尺寸 - 满血版】DeepSeek-R1 是一款强化学习（RL）驱动的推理模型，解决了模型中的重复性和可读性问题。在 RL 之前，DeepSeek-R1 引入了冷启动数据，进一步优化了推理性能。它在数学、代码和推理任务中与 OpenAI-o1 表现相当，并且通过精心设计的训练方法，提升了整体效果。")
                .botMode(BotModeEnum.LLM_AGENT.getValue())
                .modelInfo(ModelInfo.builder()
                        .model(unifiedModelEnum.getModel())
                        .modelProvider(unifiedModelEnum.getProvider())
                        .temperature(1.3)
                        .recentRound(3)
                        .build())
                .promptInfo(PromptInfo.builder()
                        .systemPrompt("你是一个有帮助的助手")
                        .build())
                .build();
        return botInfoDTO;
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(new SiliconflowDeepseekR1Bot().createBot()));
    }
}
