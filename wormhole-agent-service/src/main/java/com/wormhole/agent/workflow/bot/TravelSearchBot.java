package com.wormhole.agent.workflow.bot;

import com.google.common.collect.Lists;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import com.wormhole.agent.nacos.config.TemplateEnum;
import com.wormhole.agent.nacos.listener.TemplateService;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.agent.workflow.model.ContentFormatTypeEnum;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.SchemaTypeEnum;
import com.wormhole.agent.workflow.model.TerminatePlanEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.CodeInputs;
import com.wormhole.agent.workflow.model.inputs.EndInputs;
import com.wormhole.agent.workflow.util.LocalBotUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/1/14 14:57
 */
@BotEnabled
@Component
public class TravelSearchBot implements Bot {
    public static final String BOT_CODE = "travel_search_bot";

    @Resource
    private TemplateService templateService;

    @Override
    public BotInfo createBot() {
        return BotInfo.builder()
                .botCode(BOT_CODE)
                .name("周边推荐助手")
                .description("你好，我是周边推荐小助手")
                .botMode(BotModeEnum.LLM_AGENT.getValue())
                .modelInfo(ModelInfo.builder()
                        .model(UnifiedModelEnum.DOUBAO_LITE_32K.getModel())
                        .modelProvider(UnifiedModelEnum.DOUBAO_LITE_32K.getProvider())
                        .temperature(0.3d)
                        .recentRound(3)
                        .build())
                .promptInfo(PromptInfo.builder()
                        .systemPrompt(templateService.getTemplate(TemplateEnum.travel_search_system_prompt))
                        .userPrompt(templateService.getTemplate(TemplateEnum.travel_search_user_prompt))
                        .build())
                .workflowCodeList(Lists.newArrayList("travel_search"))
                .build();

    }

    @Override
    public Workflow createWorkflow() {
        Node startNode = Node.builder()
                .id("start")
                .type(NodeTypeEnum.START.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("开始").build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name(WorkflowConstant.USER_INPUT).type(SchemaTypeEnum.STRING.getType()).description(WorkflowConstant.BOT_USER_INPUT_DESC).required(true).build()
                        ))
                        .build())
                .build();

        Node codeNode = Node.builder()
                .id("code")
                .type(NodeTypeEnum.CODE.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("代码节点").build())
                        .inputs(CodeInputs.builder()
                                .code(templateService.getTemplate(TemplateEnum.travel_search_code))
                                .language(1)
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("query", startNode, WorkflowConstant.USER_INPUT)
                                ))
                                .build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output").type(SchemaTypeEnum.STRING.getType()).description("大模型输出").build()
                        ))
                        .build())
                .build();

        Node endNode = Node.builder()
                .id("end")
                .type(NodeTypeEnum.END.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("结束节点").build())
                        .inputs(EndInputs.builder()
                                .terminatePlan(TerminatePlanEnum.RETURN_VARIABLES.getPlan())
                                .templateEngineType(ContentFormatTypeEnum.JSON_PATH.getType())
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("output", codeNode, "output")
                                ))
                                .build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output").type(SchemaTypeEnum.STRING.getType()).description("大模型结果输出").build()
                        ))
                        .build())
                .build();

        List<Node> nodes = Lists.newArrayList(startNode, codeNode, endNode);
        List<Edge> edges = Lists.newArrayList(
                Edge.builder().sourceNodeId(startNode.getId()).targetNodeId(codeNode.getId()).build(),
                Edge.builder().sourceNodeId(codeNode.getId()).targetNodeId(endNode.getId()).build()
        );

        WorkflowDefinition workflowDefinition = WorkflowDefinition.builder()
                .workflowCode("travel_search")
                .workflowName("订单搜索机器人")
                .nodes(nodes)
                .edges(edges)
                .build();
        return new Workflow(workflowDefinition);
    }
}
