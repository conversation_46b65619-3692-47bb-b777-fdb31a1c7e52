package com.wormhole.agent.workflow.bot.hotel;

import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import com.wormhole.agent.nacos.config.TemplateEnum;
import com.wormhole.agent.nacos.listener.TemplateService;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/10 15:18
 */
@BotEnabled
@Component
public class PromptExpertBot implements Bot {

    public static final String BOT_CODE = "prompt_expert_bot";

    @Resource
    private TemplateService templateService;

    @Override
    public BotInfo createBot() {
        return BotInfo.builder()
                .botCode(BOT_CODE)
                .name("提示词专家")
                .description("你好，我是一位提示词专家，说出你想定义的角色名称,我将为你生成专业的Prompt！")
                .botMode(BotModeEnum.LLM_AGENT.getValue())
                .modelInfo(ModelInfo.builder()
                        .model(UnifiedModelEnum.GPT_4_O.getModel())
                        .modelProvider(UnifiedModelEnum.GPT_4_O.getProvider())
                        .temperature(0.5)
                        .recentRound(3)
                        .build())
                .promptInfo(PromptInfo.builder()
                        .systemPrompt(templateService.getTemplate(TemplateEnum.prompt_expert_system_prompt))
                        .userPrompt(templateService.getTemplate(TemplateEnum.prompt_expert_user_prompt))
                        .build())
                .build();
    }


}
