package com.wormhole.agent.workflow.model.inputs;

import com.wormhole.agent.workflow.model.Node;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * CodeInputs
 *
 * <AUTHOR>
 * @version 2024/11/28
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CodeInputs extends Node.Inputs {

    /**
     * 代码块
     */
    private String code;
    /**
     * 语言类型
     */
    private Integer language;

}