package com.wormhole.agent.workflow.bot.user.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

/**
 * @author: joker.liu
 * @date: 2025/2/20
 * @Description:    bdw相关的响应体结构,http调用时接收
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ApiResponse<T> {

    private int code;

    private String errorCode;

    private String msg;

    private String errorDesc;

    private String traceId;

    private T data;

}
