{"workflow_id": 10002, "workflow_code": "hotel_search", "workflow_name": "hotel_search_workflow", "nodes": [{"id": "100001", "type": "start", "meta": {"position": {"x": 0, "y": 0}}, "data": {"node_meta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start.png", "sub_title": "", "title": "开始"}, "outputs": [{"type": "string", "name": "USER_INPUT", "required": false, "description": "用户本轮对话输入内容"}]}}, {"id": "900001", "type": "end", "meta": {"position": {"x": 1000, "y": 0}}, "data": {"node_meta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End.png", "sub_title": "", "title": "结束"}, "inputs": {"terminate_plan": "useAnswerContent", "streaming_output": false, "input_parameters": [{"name": "output", "input": {"type": "list", "schema": {"type": "object", "schema": [{"type": "string", "name": "output"}]}, "value": {"type": "ref", "content": {"source": "block-output", "block_id": "126936", "name": "output_list"}}}}], "format_type": "json_path", "content": {"type": "string", "value": {"type": "literal", "content": "$.output"}}}}}, {"id": "126936", "type": "hotel_search", "meta": {"position": {"x": 490.5, "y": -26.5}}, "data": {"node_meta": {"title": "知识库", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Knowledge.png", "description": "在选定的知识中,根据输入变量召回最匹配的信息,并以列表形式返回", "sub_title": "知识库"}, "outputs": [{"type": "list", "name": "output_list", "schema": {"type": "object", "schema": [{"type": "string", "name": "output"}]}}], "inputs": {"input_parameters": [{"name": "Query", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "block_id": "100001", "name": "USER_INPUT"}}}}], "dataset_param": [{"name": "dataset_list", "input": {"type": "list", "schema": {"type": "string"}, "value": {"type": "literal", "content": []}}}, {"name": "top_k", "input": {"type": "integer", "value": {"type": "literal"}}}]}}}], "edges": [{"source_node_id": "100001", "target_node_id": "126936"}, {"source_node_id": "126936", "target_node_id": "900001"}]}