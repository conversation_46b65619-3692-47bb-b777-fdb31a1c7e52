{"nodes": [{"blocks": [], "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start-v2.jpg", "subTitle": "", "title": "开始"}, "outputs": [{"description": "用户本轮对话输入内容", "name": "USER_INPUT", "required": false, "type": "string"}, {"name": "name", "required": true, "type": "string"}, {"name": "number", "required": true, "type": "integer"}]}, "edges": null, "id": "100001", "meta": {"position": {"x": -475, "y": -261}}, "type": "1"}, {"blocks": [], "data": {"inputs": {"inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "151684", "name": "key0", "source": "block-output"}, "type": "ref"}}, "name": "output"}], "terminatePlan": "returnVariables"}, "nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End-v2.jpg", "subTitle": "", "title": "结束"}}, "edges": null, "id": "900001", "meta": {"position": {"x": 692, "y": -310}}, "type": "2"}, {"blocks": [], "data": {"inputs": {"code": "def str = \"hello \" + name;\n                log.info(str)\n                Map<String, Object> params = new HashMap<>();\n                params.put(\"key0\", str + \"!\"+ max(100, number));\n                return params;\n                \n                def max(int a, int b) {\n                    return Math.max(a, b)\n                }", "inputParameters": [{"input": {"type": "string", "value": {"content": {"blockID": "100001", "name": "name", "source": "block-output"}, "type": "ref"}}, "name": "name"}, {"input": {"type": "integer", "value": {"content": {"blockID": "100001", "name": "number", "source": "block-output"}, "type": "ref"}}, "name": "number"}], "language": 1, "settingOnError": {}}, "nodeMeta": {"description": "编写代码，处理输入变量来生成返回值", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Code-v2.jpg", "subTitle": "代码", "title": "代码"}, "outputs": [{"name": "key0", "type": "string"}]}, "edges": null, "id": "151684", "meta": {"position": {"x": 122.5, "y": -296.05}}, "type": "5"}], "edges": [{"sourceNodeID": "100001", "targetNodeID": "151684", "sourcePortID": ""}, {"sourceNodeID": "151684", "targetNodeID": "900001", "sourcePortID": ""}], "versions": {}}