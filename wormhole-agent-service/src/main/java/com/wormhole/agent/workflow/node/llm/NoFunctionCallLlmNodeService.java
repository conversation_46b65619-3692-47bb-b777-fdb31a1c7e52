package com.wormhole.agent.workflow.node.llm;

import com.google.common.collect.Lists;
import com.wormhole.agent.ai.core.service.ChatClientService;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.core.util.ChatMessageUtils;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.model.openai.OpenAiChatParams;
import com.wormhole.agent.util.DataProcessor;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.WorkflowRequestSourceEnum;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * NoFunctionLlmNodeService
 *
 * <AUTHOR>
 * @version 2025/2/25
 */
@Slf4j
@Service
public class NoFunctionCallLlmNodeService {

    @Resource
    private ChatClientService chatClientService;

    public Mono<WorkflowContext> execute(LlmInputs.LlmParams llmParams, Node node, NodeExecutionInfo<LlmInputs> executionInfo, WorkflowContext workflowContext) {
        Map<String, Object> initialInput = workflowContext.getInitialInput();
        Map<String, Object> inputMap = executionInfo.getInput();
        inputMap.putAll(initialInput);
        List<OpenAiChatMessage> chatMessageList = Lists.newArrayList();
        String systemPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(llmParams.getSystemPrompt(), inputMap);
        OpenAiChatMessage systemChatMessage = OpenAiChatMessage.builder().role(ChatRole.SYSTEM.getValue()).content(systemPrompt).build();
        chatMessageList.add(systemChatMessage);

        if (llmParams.isEnableChatHistory()) {
            // 是否智能体记忆
            int recentRound = llmParams.getChatHistoryRound() == null ? workflowContext.getRecentRound() : llmParams.getChatHistoryRound();
            chatMessageList.addAll(ChatMessageUtils.getRecentMessageList(workflowContext.getRecentMessageList(), recentRound));
        }

        String userPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(llmParams.getUserPrompt(), inputMap);
        OpenAiChatMessage userChatMessage = OpenAiChatMessage.builder().role(ChatRole.USER.getValue()).content(userPrompt).build();
        chatMessageList.add(userChatMessage);

        Integer maxCompletionTokens = llmParams.getMaxCompletionTokens();

        OpenAiChatParams openAiChatParams = OpenAiChatParams.builder()
                .stream(true)
                .model(llmParams.getModel())
                .modelProvider(llmParams.getModelProvider())
                .temperature(llmParams.getTemperature())
                .maxTokens(Objects.nonNull(maxCompletionTokens) && maxCompletionTokens > 0 ? maxCompletionTokens : null)
                .frequencyPenalty(llmParams.getFrequencyPenalty())
                .messages(chatMessageList)
                .build();
        log.info("NoFunctionCallLlmNodeService openAiChatParams:{}", JacksonUtils.writeValueAsString(openAiChatParams));
        ModelContext modelContext = ModelContext.builder()
                .openAiChatParams(openAiChatParams)
                .modelLogContext(Optional.ofNullable(workflowContext.getModelLogContext()).orElse(ModelContext.ModelLogContext.builder().build()))
                .build();
        return chatClientService.chatCompletions(modelContext)
                .flatMap(chatCompletions -> {
                    trySinkNext(chatCompletions, node, workflowContext);
                    return Mono.just(chatCompletions);
                })
                .collectList()
                .flatMap(list -> {
                    String content = list.stream()
                            .filter(Objects::nonNull)
                            .map(ChatCompletionUtils::getContent)
                            .filter(Objects::nonNull)  // 过滤掉 getContent 返回的 null 值
                            .collect(Collectors.joining());
                    executionInfo.setOutput(DataProcessor.convertToResultMap(content, node, llmParams));
                    return Mono.just(workflowContext);
                });
    }

    private void trySinkNext(ChatCompletions chatCompletions, Node node, WorkflowContext workflowContext) {
        if(WorkflowRequestSourceEnum.FUNCTION.equals(workflowContext.getWorkflowRequestSource())){
            return;
        }
        WorkflowUtils.getNextOutputNode(node, workflowContext)
                .ifPresentOrElse(
                        outputNode -> workflowContext.sinkNext(chatCompletions),
                        () -> log.info("未找到输出节点，跳过 sinkNext")
                );
    }


}