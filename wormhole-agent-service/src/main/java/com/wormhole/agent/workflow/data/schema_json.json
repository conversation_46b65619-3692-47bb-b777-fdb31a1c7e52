{"nodes": [{"id": "100001", "type": "start", "meta": {"position": {"x": 115.38095238095238, "y": -2.761904761904759}, "test_run": {}}, "data": {"node_meta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start.png", "sub_title": "", "title": "开始"}, "outputs": [{"type": "string", "name": "USER_INPUT", "required": false, "description": "用户本轮对话输入内容"}]}}, {"id": "900001", "type": "end", "meta": {"position": {"x": 1024, "y": 119}, "test_run": {}}, "data": {"node_meta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End.png", "sub_title": "", "title": "结束"}, "inputs": {"terminate_plan": "useAnswerContent", "streaming_output": false, "input_parameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "block_id": "100001", "name": "USER_INPUT"}}}}], "content": {"type": "string", "value": {"type": "literal", "content": "$!{output}"}}}}}, {"id": "137692", "type": "llm", "meta": {"position": {"x": 554.809523809524, "y": 150.39285714285717}, "test_run": {}}, "data": {"node_meta": {"title": "大模型", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM.png", "description": "调用大语言模型,使用变量和提示词生成回复", "sub_title": "大模型"}, "inputs": {"setting_on_error": {}, "input_parameters": [{"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "block_id": "100001", "name": "USER_INPUT"}}}}, {"name": "args1", "input": {"type": "string", "value": {"type": "literal", "content": "test"}}}], "llm_params": {"chat_model": "QWEN", "generation_diversity": "default_val", "enable_chat_recent": false, "response_format": "markdown", "model": "qwen-plus", "temperature": 0.3, "top_p": 0.9, "frequency_penalty": 0.0, "max_tokens": 500, "system_prompt": "你是一个有帮助的助手。", "user_prompt": "你好"}}, "outputs": [{"type": "string", "name": "output", "description": ""}]}, "version": "2"}], "edges": [{"source_node_id": "137692", "target_node_id": "900001"}, {"source_node_id": "100001", "target_node_id": "137692"}]}