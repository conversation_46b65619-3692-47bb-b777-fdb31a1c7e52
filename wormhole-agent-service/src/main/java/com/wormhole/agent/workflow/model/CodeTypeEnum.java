package com.wormhole.agent.workflow.model;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * CodeTypeEnum
 *
 * <AUTHOR>
 * @version 2024/11/28
 */
public enum CodeTypeEnum {

    Groovy(1),
    Python(2),
    JavaScript(3),
    ;


    private Integer type;

    CodeTypeEnum(Integer type) {
        this.type = type;
    }

    public static CodeTypeEnum getByType(Integer type) {
        return Optional.ofNullable(type)
                .flatMap(t -> Arrays.stream(CodeTypeEnum.values())
                        .filter(e -> Objects.equals(e.getType(), type))
                        .findFirst())
                .orElse(null);
    }

    public Integer getType() {
        return type;
    }
}
