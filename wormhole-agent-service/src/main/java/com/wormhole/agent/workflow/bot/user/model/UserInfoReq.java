package com.wormhole.agent.workflow.bot.user.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/2/18
 * @Description:
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserInfoReq implements Serializable {

    private String userId;

}
