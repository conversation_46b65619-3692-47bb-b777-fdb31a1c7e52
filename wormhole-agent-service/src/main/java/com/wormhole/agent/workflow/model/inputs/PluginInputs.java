package com.wormhole.agent.workflow.model.inputs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.common.util.JacksonUtils;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 * @date 2025-03-31 19:29:40
 * @Description:
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginInputs extends Node.Inputs{

    private PluginParams pluginParam;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class PluginParams {
        private String pluginCode;

        private Integer pluginVersion;

        private String pluginToolCode;

        private String pluginToolName;

    }

}
