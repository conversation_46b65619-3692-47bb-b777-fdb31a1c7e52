package com.wormhole.agent.workflow.bot.deepseek;

import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.common.util.JacksonUtils;
import org.springframework.stereotype.Component;

/**
 * DeepseekR1Bot
 *
 * <AUTHOR>
 * @version 2024/12/8
 */
@BotEnabled
@Component
public class DeepseekR1Bot implements Bot {

    @Override
    public BotInfo createBot() {
        UnifiedModelEnum unifiedModelEnum = UnifiedModelEnum.ALIYUN_DEEPSEEK_R1;
        BotInfo botInfoDTO = BotInfo.builder()
                .botCode(unifiedModelEnum.getModel())
                .name(String.format("【通义千问】%s", unifiedModelEnum.getModel()))
                .description("DeepSeek最新推出的推理模型DeepSeek-R1")
                .botMode(BotModeEnum.LLM_AGENT.getValue())
                .modelInfo(ModelInfo.builder()
                        .model(unifiedModelEnum.getModel())
                        .modelProvider(unifiedModelEnum.getProvider())
                        .temperature(1.3)
                        .recentRound(3)
                        .build())
                .promptInfo(PromptInfo.builder()
                        .systemPrompt("你是一个有帮助的助手")
                        .build())
                .build();
        return botInfoDTO;
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(new DeepseekR1Bot().createBot()));
    }
}
