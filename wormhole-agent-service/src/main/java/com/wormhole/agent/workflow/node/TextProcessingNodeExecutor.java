package com.wormhole.agent.workflow.node;

import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * TextProcessingNodeExecutor
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Component
public class TextProcessingNodeExecutor extends AbstractNodeExecutor<Node.Inputs> {

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.TEXT;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<Node.Inputs> executionInfo, WorkflowContext workflowContext) {
        Workflow workflow = workflowContext.getWorkflow();
        Node startNode = workflow.getStartNode();
        List<Node.Output> outputs = startNode.getData().getOutputs();

        NodeExecutionInfo nodeExecutionInfo = workflowContext.getNodeExecutionInfo(startNode.getId());
        String inputText = nodeExecutionInfo.getOutputAsString("USER_INPUT");
        List<Node.Output> startOutputList = startNode.getData().getOutputs();

        // Implement text processing logic
        String processedText = "Processed: " + inputText;
        Map<String, Object> output = new HashMap<>();
        output.put("output", processedText);
        return Mono.just(workflowContext);
    }

}