package com.wormhole.agent.workflow.model.inputs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.workflow.model.Node;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * @author: joker.liu
 * @date: 2025/7/21
 * @Description:
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class McpToolInputs extends Node.Inputs{

    /**
     * mcp服务名称
     */
    private String mcpServerName;

    /**
     * mcp工具参数
     */
    private McpToolParams mcpToolParams;


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class McpToolParams {
        /**
         * 工具名称
         */
        private String toolName;

    }

}
