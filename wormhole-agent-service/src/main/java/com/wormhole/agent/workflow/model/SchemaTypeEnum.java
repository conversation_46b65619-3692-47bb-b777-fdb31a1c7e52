package com.wormhole.agent.workflow.model;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * InputSchemaTypeEnum
 *
 * <AUTHOR>
 * @version 2024/12/9
 */
public enum SchemaTypeEnum {

    STRING("string"),
    OBJECT("object"),
    NUMBER("number"),
    LIST("list"),
    LITERAL("literal");

    private final String type;

    SchemaTypeEnum(String type) {
        this.type = type;
    }

    public static SchemaTypeEnum from(String type) {
        return Optional.ofNullable(type)
                .filter(StringUtils::isNotBlank)
                .flatMap(t -> Arrays.stream(values()).filter(item -> item.type.equalsIgnoreCase(type)).findFirst())
                .orElse(null);
    }

    public String getType() {
        return type;
    }

}