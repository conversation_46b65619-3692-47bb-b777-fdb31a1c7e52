package com.wormhole.agent.workflow.bot.test;

import com.google.common.collect.Lists;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.agent.workflow.model.ContentFormatTypeEnum;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.InputValueTypeEnum;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.SchemaTypeEnum;
import com.wormhole.agent.workflow.model.TerminatePlanEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.EndInputs;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import com.wormhole.agent.workflow.util.LocalBotUtils;
import com.wormhole.common.util.JacksonUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * WorkflowAgentLlmTestBot
 *
 * <AUTHOR>
 * @version 2024/12/9
 */
@BotEnabled
@Component
public class WorkflowAgentLlmTestBot implements Bot {

    public static final BotInfo BOT_INFO;

    public static final Workflow WORKFLOW;

    static {
        String botCode = "workflow-agent-llm-test-bot";
        Node startNode = Node.builder()
                .id("start")
                .type(NodeTypeEnum.START.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("开始节点").build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name(WorkflowConstant.USER_INPUT).type(SchemaTypeEnum.STRING.getType()).description(WorkflowConstant.BOT_USER_INPUT_DESC).required(true).build()
                        ))
                        .build())
                .build();

        Node llmNode = Node.builder()
                .id("llm")
                .type(NodeTypeEnum.LLM.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("llm").build())
                        .inputs(LlmInputs.builder()
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("question", startNode, WorkflowConstant.USER_INPUT)
                                ))
                                .llmParams(LlmInputs.LlmParams.builder()
                                        .enableChatHistory(false)
                                        .systemPrompt("""
                                                你是一个有帮助的助手。
                                                """)
                                        .userPrompt("""
                                                用户问题：${question}
                                                """)
                                        .model(UnifiedModelEnum.GPT_4_O_MINI.getModel())
                                        .modelProvider(UnifiedModelEnum.GPT_4_O_MINI.getProvider())
                                        .temperature(0.3d)
                                        .build())
                                .build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output").type(SchemaTypeEnum.STRING.getType()).description("大模型结果输出").build()
                        ))
                        .build())
                .build();

        Node endNode = Node.builder()
                .id("end")
                .type(NodeTypeEnum.END.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("结束节点").build())
                        .inputs(EndInputs.builder()
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("output", llmNode, "output")
                                ))
                                .terminatePlan(TerminatePlanEnum.USE_ANSWER_CONTENT.getPlan())
                                .templateEngineType(ContentFormatTypeEnum.JSON_PATH.getType())
                                .content(
                                        Node.Input.builder()
                                                .type(SchemaTypeEnum.STRING.getType())
                                                .value(Node.Value.builder().type(InputValueTypeEnum.literal.getType()).content("$.output").build())
                                                .build()
                                )
                                .build())
                        .build())
                .build();

        List<Node> nodes = Lists.newArrayList(startNode, llmNode, endNode);
        List<Edge> edges = Lists.newArrayList(
                Edge.builder().sourceNodeId(startNode.getId()).targetNodeId(llmNode.getId()).build(),
                Edge.builder().sourceNodeId(llmNode.getId()).targetNodeId(endNode.getId()).build()
        );
        WorkflowDefinition workflowDefinition = WorkflowDefinition.builder()
                .workflowCode(botCode)
                .workflowName("测试")
                .nodes(nodes)
                .edges(edges)
                .build();
        WORKFLOW = new Workflow(workflowDefinition);

        BOT_INFO = BotInfo.builder()
                .botCode(botCode)
                .botMode(BotModeEnum.WORKFLOW_AGENT.getValue())
                .workflowCodeList(Lists.newArrayList(botCode))
                .build();
    }

    @Override
    public BotInfo createBot() {
        return BOT_INFO;
    }

    @Override
    public Workflow createWorkflow() {
        return WORKFLOW;
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(BOT_INFO));
        System.out.println(JacksonUtils.writeValuePretty(WORKFLOW));
    }
}
