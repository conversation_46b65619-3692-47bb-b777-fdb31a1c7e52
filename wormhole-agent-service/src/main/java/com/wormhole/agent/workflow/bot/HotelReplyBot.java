package com.wormhole.agent.workflow.bot;

import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import com.wormhole.agent.nacos.config.TemplateEnum;
import com.wormhole.agent.nacos.listener.TemplateService;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * HotelReplyBot
 *
 * <AUTHOR>
 * @version 2024/12/8
 */
@BotEnabled
@Component
public class HotelReplyBot implements Bot {

    @Resource
    private TemplateService templateService;

    @Override
    public BotInfo createBot() {
        BotInfo botInfoDTO = BotInfo.builder()
                .botCode("hotel_reply_bot")
                .name("好评生成师")
                .description("你好，我是酒店评论回复管家，专注于提升酒店服务。可以帮你学习高情商回复客人评论、处理入住前后沟通及热点事件等问题。快来向我提问吧！\uD83D\uDE04")
                .botMode(BotModeEnum.LLM_AGENT.getValue())
                .modelInfo(ModelInfo.builder()
                        .model(UnifiedModelEnum.GPT_4_O.getModel())
                        .modelProvider(UnifiedModelEnum.GPT_4_O.getProvider())
                        .temperature(0.5)
                        .recentRound(3)
                        .build())
                .promptInfo(PromptInfo.builder()
                        .systemPrompt(templateService.getTemplate(TemplateEnum.hotel_reply_system_prompt))
                        .userPrompt(templateService.getTemplate(TemplateEnum.hotel_reply_user_prompt))
                        .build())
                .build();
        return botInfoDTO;
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(new HotelReplyBot().createBot()));
    }
}
