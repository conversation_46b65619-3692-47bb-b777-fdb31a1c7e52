package com.wormhole.agent.workflow;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
/**
 * 模型请求信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ModelRequestInfo {
    private String name;
    private String modelName;
    private String modelProvider;
    private String input;
    private String output;
    private long startTime;
    private long endTime;
    private long elapsedMs = -1L;
}