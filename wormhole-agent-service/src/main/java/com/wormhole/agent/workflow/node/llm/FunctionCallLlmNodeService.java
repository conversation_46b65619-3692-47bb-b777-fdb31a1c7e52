package com.wormhole.agent.workflow.node.llm;

import com.google.common.collect.Lists;
import com.wormhole.agent.ai.core.service.ChatClientService;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.core.util.ChatMessageUtils;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.log.metrics.PerformanceMonitorUtils;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.model.openai.OpenAiChatParams;
import com.wormhole.agent.tool.ToolChainDriver;
import com.wormhole.agent.tool.ToolDiscoveryProvider;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.util.DataProcessor;
import com.wormhole.agent.workflow.ModelRequestInfo;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * FunctionCallLlmNodeService
 *
 * <AUTHOR>
 * @version 2025/2/25
 */
@Service
@Slf4j
public class FunctionCallLlmNodeService {

    @Resource
    private ChatClientService chatClientService;

    @Resource
    private ToolChainDriver toolEngine;
    @Resource
    private ToolDiscoveryProvider toolDiscoveryProvider;

    /**
     * 执行工作流
     */
    public Mono<WorkflowContext> execute(LlmInputs.LlmParams llmParams,LlmInputs.IntentLlmParams intentLlmParams , Node node, NodeExecutionInfo<LlmInputs> executionInfo, WorkflowContext workflowContext) {
        LlmInputs inputs = executionInfo.getNodeDataInputs();
        Map<String, Object> initialInput = workflowContext.getInitialInput();
        Map<String, Object> inputMap = executionInfo.getInput();
        inputMap.putAll(initialInput);
        log.info("FunctionCallLlmNodeService input: {}", JacksonUtils.writeValueAsString(inputMap));
        // 初始化工具上下文
        ToolChainContext toolChainContext = Optional.ofNullable(workflowContext.getToolChainContext())
                .orElseGet(() -> {
                    ToolChainContext ctx = new ToolChainContext();
                    ctx.setNodeId(node.getId());
                    ctx.setRecentRound(workflowContext.getRecentRound());
                    ctx.setRecentMessageList(workflowContext.getRecentMessageList());
                    ctx.setSinks(workflowContext.getSinks());
                    workflowContext.setToolChainContext(ctx);
                    return ctx;
                });
        toolChainContext.reset();
        // 开始递归执行 function call 过程，使用异步版本避免响应式线程阻塞
        return toolDiscoveryProvider.getToolsAsync(inputs.getFunctionCallParams(), inputMap, toolChainContext)
                .flatMap(tools -> {
                    return toolEngine.executeToolChain(
                            intentLlmParams,
                            tools,
                            inputMap,
                            toolChainContext
                    );
                })
                .flatMap(finalResults -> {
            // 将最终结果添加到输入中，用于 LLM 总结
//            inputMap.put(WorkflowConstant.INNER_OUTPUT, JacksonUtils.writeValueAsString(finalResults));
//            return llmSummary(llmParams, node, executionInfo, workflowContext, inputMap);
            String content = ChatCompletionUtils.getContent(toolChainContext.getLastDirectResponse());
            trySinkNext(ChatCompletionUtils.buildChatCompletionsDelta(content), node, workflowContext);
            executionInfo.setOutput(DataProcessor.convertToResultMap(content, node, llmParams));
            return Mono.just(workflowContext);

        });
    }


    /**
     * 使用 LLM 对结果进行总结
     */
    private Mono<WorkflowContext> llmSummary(
            LlmInputs.LlmParams llmParams,
            Node node,
            NodeExecutionInfo<LlmInputs> executionInfo,
            WorkflowContext workflowContext,
            Map<String, Object> inputMap) {

        ModelContext modelContext = buildModelContext(llmParams, workflowContext, inputMap);
        Map<String, ModelRequestInfo> llSummaryInfoMap = workflowContext.getModelRequestInfoMap();

        long startTime = System.currentTimeMillis();

        return chatClientService.chatCompletions(modelContext)
                .flatMap(chatCompletions -> {
                    trySinkNext(chatCompletions, node, workflowContext);
                    return Mono.just(chatCompletions);
                })
                .collectList()
                .flatMap(list-> {
                    long end = System.currentTimeMillis();
                    ModelRequestInfo modelRequestInfo = ModelRequestInfo.builder()
                            .name("summary")
                            .modelName(modelContext.getOpenAiChatParams().getModel())
                            .modelProvider(modelContext.getOpenAiChatParams().getModelProvider())
                            .input(JacksonUtils.writeValueAsString(modelContext.getOpenAiChatParams()))
                            .output(modelContext.getAnswer())
                            .startTime(startTime)
                            .endTime(end)
                            .elapsedMs(end - startTime)  // 设置耗时
                            .build();
                    PerformanceMonitorUtils.log(modelRequestInfo);
                    llSummaryInfoMap.put(node.getId(), modelRequestInfo);
                    executionInfo.setOutput(DataProcessor.convertToResultMap(modelContext.getAnswer(), node, llmParams));
                    return Mono.just(workflowContext);
                });
    }


    /**
     * 尝试推送结果到下一个节点
     */
    private void trySinkNext(ChatCompletions chatCompletions, Node node, WorkflowContext workflowContext) {
        WorkflowUtils.getNextOutputNode(node, workflowContext)
                .ifPresentOrElse(
                        outputNode -> workflowContext.sinkNext(chatCompletions),
                        () -> log.info("未找到输出节点，跳过 sinkNext")
                );
    }


    /**
     * 构建模型上下文（用于总结）
     */
    private ModelContext buildModelContext(
            LlmInputs.LlmParams llmParams,
            WorkflowContext workflowContext,
            Map<String, Object> inputMap) {

        List<OpenAiChatMessage> chatMessageList = Lists.newArrayList();

        // 系统提示
        String systemPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(
                llmParams.getSystemPrompt(), inputMap);
        OpenAiChatMessage systemChatMessage = OpenAiChatMessage.builder()
                .role(ChatRole.SYSTEM.getValue())
                .content(systemPrompt)
                .build();
        chatMessageList.add(systemChatMessage);

        // 添加历史消息
        if (llmParams.isEnableChatHistory()) {
            int recentRound = llmParams.getChatHistoryRound() == null ?
                    workflowContext.getRecentRound() : llmParams.getChatHistoryRound();
            chatMessageList.addAll(ChatMessageUtils.getRecentMessageList(
                    workflowContext.getRecentMessageList(), recentRound));
        }

        // 用户提示
        String userPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(
                llmParams.getUserPrompt(), inputMap);
        OpenAiChatMessage userChatMessage = OpenAiChatMessage.builder()
                .role(ChatRole.USER.getValue())
                .content(userPrompt)
                .build();
        chatMessageList.add(userChatMessage);


        // 添加工具对话历史
        if (workflowContext.getToolChainContext() != null &&
                CollectionUtils.isNotEmpty(workflowContext.getToolChainContext().getConversationHistory())) {
            chatMessageList.addAll(workflowContext.getToolChainContext().getConversationHistory());
        }

        // 构建 OpenAI 聊天参数
        Integer maxCompletionTokens = Objects.nonNull(llmParams.getMaxCompletionTokens()) && llmParams.getMaxCompletionTokens() > 0 ? llmParams.getMaxCompletionTokens() : null;
        OpenAiChatParams openAiChatParams = OpenAiChatParams.builder()
                .stream(true)
                .model(llmParams.getModel())
                .modelProvider(llmParams.getModelProvider())
                .temperature(llmParams.getTemperature())
                .maxTokens(maxCompletionTokens)
                .maxCompletionTokens(maxCompletionTokens)
                .frequencyPenalty(llmParams.getFrequencyPenalty())
                .messages(chatMessageList)
                .build();

        return ModelContext.builder()
                .openAiChatParams(openAiChatParams)
                .modelLogContext(Optional.ofNullable(workflowContext.getModelLogContext())
                        .orElse(ModelContext.ModelLogContext.builder().build()))
                .build();
    }

}