package com.wormhole.agent.workflow.bot.user.model;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class RemoteUserLevelInfo {

    private String userId;

    private String nickName;

    private String mobile;

    private String areaCode;

    private Integer status;

    private String createTime;

    private Integer timeValueLevel; // 百达星系等级值

    private String levelUpTime; // 升级时间

    private String userNo;

    private Long currentTimeValue;

    private Long timeValueBalance;

    private String channel;

    private List<BdwTimeValueEntity> levels; // 所有时光值的等级

    @Data
    public static class BdwTimeValueEntity{
        private Integer level; // 等级

        private String name;// 名称

        private Long minTimeValue; // 当前等级所需最小时光值
    }
}
