package com.wormhole.agent.workflow;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.bot.FlowMode;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.Node;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * WorkflowDefinition
 *
 * <AUTHOR>
 * @version 2024/11/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WorkflowDefinition {
    /**
     * workflowCode
     */
    private String workflowCode;
    /**
     * workflowName
     */
    private String workflowName;
    /**
     * workflowDesc
     */
    private String workflowDesc;
    /**
     * workflow/chatflow
     */
    @Builder.Default
    private String flowMode = FlowMode.CHATFLOW.getValue();
    /**
     * 工作流开始节点的tool定义
     */
    private OpenAiTool tool;
    /**
     * 节点
     */
    private List<Node> nodes;
    /**
     * 边
     */
    private List<Edge> edges;
}
