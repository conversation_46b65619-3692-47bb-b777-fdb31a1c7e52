package com.wormhole.agent.workflow.node;

import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import reactor.core.publisher.Mono;

/**
 * NodeExecutor
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
public interface NodeExecutor<T extends Node.Inputs> {

    Class<T> getInputClass();

    NodeTypeEnum getType();

    /**
     * 节点执行前的处理
     */
    Mono<NodeExecutionInfo<T>> before(Node node, NodeExecutionInfo<T> executionInfo, WorkflowContext workflowContext);

    /**
     * 执行节点逻辑
     */
    Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<T> executionInfo, WorkflowContext workflowContext);

    /**
     * 节点执行后的处理
     */
    default Mono<WorkflowContext> after(Node node, NodeExecutionInfo<T> executionInfo, WorkflowContext workflowContext) {
        return Mono.just(workflowContext);
    }

    /**
     * 完整的执行流程
     */
    Mono<WorkflowContext> process(Node node, NodeExecutionInfo<T> executionInfo, WorkflowContext workflowContext);
}