package com.wormhole.agent.workflow.model;

/**
 * NodeExecutionStatus
 *
 * <AUTHOR>
 * @version 2024/12/13
 */
public enum NodeExecutionStatus {
    /**
     * 初始状态
     */
    PENDING("PENDING"),
    /**
     * 节点开始执行
     */
    STARTED("STARTED"),
    /**
     * 执行完成
     */
    COMPLETED("COMPLETED"),
    /**
     * 执行失败
     */
    FAILED("FAILED"),
    /**
     * 被跳过（未激活的分支）
     */
    SKIPPED("SKIPPED"),
    ;

    /**
     * 状态
     */
    private final String status;

    NodeExecutionStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }
}
