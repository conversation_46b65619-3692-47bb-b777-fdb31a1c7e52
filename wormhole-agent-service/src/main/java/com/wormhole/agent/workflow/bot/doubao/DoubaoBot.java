package com.wormhole.agent.workflow.bot.doubao;

import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.common.util.JacksonUtils;
import org.springframework.stereotype.Component;

/**
 * DeepseekR1Bot
 *
 * <AUTHOR>
 * @version 2024/12/8
 */
@BotEnabled
@Component
public class DoubaoBot implements Bot {

    @Override
    public BotInfo createBot() {
        UnifiedModelEnum unifiedModelEnum = UnifiedModelEnum.DOUBAO_PRO_256K;
        BotInfo botInfoDTO = BotInfo.builder()
                .botCode("doubao_pro_256k")
                .name("【豆包】doubao_pro_256k")
                .description("【豆包】Doubao-pro-256k是豆包推出行业领先的专业版大模型。模型在参考问答、摘要总结、创作等广泛的应用场景上能提供优质的回答，是同时具备高质量与低成本的极具性价比模型。")
                .botMode(BotModeEnum.LLM_AGENT.getValue())
                .modelInfo(ModelInfo.builder()
                        .model(unifiedModelEnum.getModel())
                        .modelProvider(unifiedModelEnum.getProvider())
                        .temperature(1.0)
                        .recentRound(3)
                        .build())
                .promptInfo(PromptInfo.builder()
                        .systemPrompt("你是一个有帮助的助手")
                        .build())
                .build();
        return botInfoDTO;
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(new DoubaoBot().createBot()));
    }
}
