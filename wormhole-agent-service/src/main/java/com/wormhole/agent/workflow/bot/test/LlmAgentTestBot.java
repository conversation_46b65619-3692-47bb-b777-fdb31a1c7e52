package com.wormhole.agent.workflow.bot.test;

import com.google.common.collect.Lists;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.agent.workflow.model.ContentFormatTypeEnum;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.SchemaTypeEnum;
import com.wormhole.agent.workflow.model.TerminatePlanEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.EndInputs;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import com.wormhole.agent.workflow.util.LocalBotUtils;
import com.wormhole.common.util.JacksonUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * LlmAgentTestBot
 *
 * <AUTHOR>
 * @version 2024/12/9
 */
@BotEnabled
@Component
public class LlmAgentTestBot implements Bot {

    public static final BotInfo BOT_INFO;

    public static final Workflow WORKFLOW;

    static {
        String botCode = "llm_agent_test_bot";
        Node startNode = Node.builder()
                .id("start")
                .type(NodeTypeEnum.START.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("开始节点").build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name(WorkflowConstant.USER_INPUT).type(SchemaTypeEnum.STRING.getType()).description(WorkflowConstant.BOT_USER_INPUT_DESC).required(true).build()
                        ))
                        .build())
                .build();

        Node llmNode = Node.builder()
                .id("llm")
                .type(NodeTypeEnum.LLM.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("llm").build())
                        .inputs(LlmInputs.builder()
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("text", startNode, WorkflowConstant.USER_INPUT)
                                ))
                                .llmParams(LlmInputs.LlmParams.builder()
                                        .enableChatHistory(false)
                                        .systemPrompt("""
                                                你是一个有帮助的助手。提取酒店名称和地址信息。
                                                """)
                                        .userPrompt("""
                                                基本信息:
                                                酒店中文名称: 绍兴鲁迅故里颐居酒店
                                                酒店英文名称: 富阳开元颐居酒店
                                                门店评分: 0.0
                                                评价数量: 0
                                                品牌名: 颐居
                                                酒店中文地址: 浙江省绍兴市越城区塔山街道解放南路287号4-6层
                                                酒店英文地址: 杭州市富阳区富春街道桂花路22号
                                                酒店经度: 120.58
                                                酒店纬度: 29.992
                                                酒店中文简介: 绍兴鲁迅故里颐居酒店是德胧集团旗下中端商务酒店，位于绍兴市中心地段，毗邻绍兴鲁迅故里景区，紧邻地铁站，独立建筑，灰瓦白墙，衬托绍兴水乡之淳朴。周边一公里内包含多个著名文人景点，均能体现出丰富的历史底蕴；以及一公里内聚集多个商城，例如银泰、王子商业街、城市广场、国商大厦、东街购物区等等，周边医疗配套齐全； 酒店总建筑面积约2870平方米，整栋会稽山大厦，周边配套餐饮、商业、旅游景点一体。酒店实际拥有客房60间，其装修风格简约清爽，以原木色调为主，客房具备智能控制系统，用你动听的声音便能唤醒美好的一天，70英寸超大屏电视，尽情享受惬意和放松的假期生活。酒店二楼设有8个棋牌室包厢和健身房，在“汗栈”健身房内释放您工作的压力；酒店负一楼配有丰味餐厅餐位100余个、多功能小型会议室；另外酒店还提供24小时洗熨烘干自助洗衣房；精致而贴心的服务，为您提供一个轻松自在的旅居空间，同时也满足您体验绍兴本土风情和文化；酒店配套小型停车场以及塔山文化广场停车场供住店客人使用；
                                                酒店英文简介:\s
                                                预订须知: 1、请于14:00后办理入住，如提前到店，视酒店客房情况确定； 2、视客情可延迟至14:00退房； 3、担保预订：为有效保证您的预订，建议您使用在线支付，我们将为您的预订保留至退房之日的中午12:00；
                                                钟点房预订须知:\s
                                                温馨提示: 1、请于14:00后办理入住，如提前到店，视酒店客房情况确定； 2、视客情可延迟至14:00退房； 3、担保预订：为有效保证您的预订，建议您使用在线支付，我们将为您的预订保留至退房之日的中午12:00。
                                                是否支持电子门锁: 支持
                                                所在省、市: 浙江省
                                                所在市: 绍兴市
                                                所在区: 越城区
                                                特色文案: 绍兴鲁迅故里颐居酒店是德胧集团旗下中端商务酒店，位于绍兴市中心地段，毗邻绍兴
                                                搜索词:\s
                                                    绍兴，鲁迅故里，颐居
                                                门店标签:\s
                                                    火车东站
                                                    轻工业风格
                                                    商务宴会
                                                酒店类型: 4
                                                酒店特殊点: 350酒店
                                                """)
                                        .model(UnifiedModelEnum.GPT_4_O_MINI.getModel())
                                        .modelProvider(UnifiedModelEnum.GPT_4_O_MINI.getProvider())
                                        .temperature(0.3d)
                                        .responseFormat("text")
                                        .build())
                                .build())
                        .outputs(Lists.newArrayList(
                                // 直接输出大模型结果
                                Node.Output.builder().name("output").type(SchemaTypeEnum.STRING.getType()).description("大模型结果输出").build()
                        ))
                        .build())
                .build();

        Node endNode = Node.builder()
                .id("end")
                .type(NodeTypeEnum.END.getType())
                .data(Node.NodeData.builder()
                        .nodeMeta(Node.NodeMeta.builder().title("结束节点").build())
                        .inputs(EndInputs.builder()
                                .inputParameters(Lists.newArrayList(
                                        LocalBotUtils.getRefInputParameter("output", llmNode, "output")
                                ))
                                .terminatePlan(TerminatePlanEnum.RETURN_VARIABLES.getPlan())
                                .templateEngineType(ContentFormatTypeEnum.JSON_PATH.getType())
//                                .content(
//                                        Node.Input.builder()
//                                                .type(SchemaTypeEnum.STRING.getType())
//                                                .value(Node.Value.builder().type(InputValueTypeEnum.literal.getType()).content("$.output").build())
//                                                .build()
//                                )
                                .build())
                        .outputs(Lists.newArrayList(
                                Node.Output.builder().name("output").type(SchemaTypeEnum.STRING.getType()).description("大模型结果输出").build()
                        ))
                        .build())
                .build();

        List<Node> nodes = Lists.newArrayList(startNode, llmNode, endNode);
        List<Edge> edges = Lists.newArrayList(
                Edge.builder().sourceNodeId(startNode.getId()).targetNodeId(llmNode.getId()).build(),
                Edge.builder().sourceNodeId(llmNode.getId()).targetNodeId(endNode.getId()).build()
        );

        WorkflowDefinition workflowDefinition = WorkflowDefinition.builder()
                .workflowCode(botCode)
                .workflowName("天气信息实时获取")
                .nodes(nodes)
                .edges(edges)
                .build();
        WORKFLOW = new Workflow(workflowDefinition);

        BOT_INFO = BotInfo.builder()
                .botCode(botCode)
                .botMode(BotModeEnum.LLM_AGENT.getValue())
                .modelInfo(ModelInfo.builder()
                        .model(UnifiedModelEnum.GPT_4_O_MINI.getModel())
                        .modelProvider(UnifiedModelEnum.GPT_4_O_MINI.getProvider())
                        .temperature(0.3d)
                        .recentRound(3)
                        .build())
                .promptInfo(PromptInfo.builder()
                        .systemPrompt("""
                                你是一个天气信息查询工具。
                                """)
                        .userPrompt("""
                                用户问题：${question}
                                参考信息：${wf_output_json}
                                """)
                        .build())
                .workflowCodeList(Lists.newArrayList(botCode))
                .build();
    }

    @Override
    public BotInfo createBot() {
        return BOT_INFO;
    }

    @Override
    public Workflow createWorkflow() {
        return WORKFLOW;
    }

    public static void main(String[] args) {
        System.out.println(JacksonUtils.writeValuePretty(BOT_INFO));
        System.out.println(JacksonUtils.writeValuePretty(WORKFLOW));
    }
}
