package com.wormhole.agent.workflow.util;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.common.enums.SystemVariableEnum;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-04-14 15:43:48
 * @Description:
 */
public class SystemVariableUtils {

    /**
     * 从chatContext 拿到系统变量
     */
    public static Map<String, Object> getSystemVariable(ChatContext chatContext, Map<String, Object> initMap) {
        Map<String, Object> inputMap = new HashMap<>();

        inputMap.put(SystemVariableEnum.SYS_USER_NAME.getName(),
                StrUtil.isNotBlank(chatContext.getUsername())? chatContext.getUsername(): initMap.get(WorkflowConstant.BDW_USER_NAME));

        inputMap.put(SystemVariableEnum.SYS_USER_ID.getName(),
                StrUtil.isNotBlank(chatContext.getUserId())? chatContext.getUserId(): initMap.get(WorkflowConstant.USER_ID));

        inputMap.put(SystemVariableEnum.SYS_HOTEL_CODE.getName(),
                StrUtil.isNotBlank(chatContext.getHotelCode())? chatContext.getHotelCode(): initMap.get(WorkflowConstant.HOTEL_CODE));

        inputMap.put(SystemVariableEnum.SYS_DEVICE_ID.getName(),
                StrUtil.isNotBlank(chatContext.getDeviceId())? chatContext.getDeviceId(): initMap.get(WorkflowConstant.DEVICE_ID));

        inputMap.put(SystemVariableEnum.SYS_ROOM_NO.getName(),
                StrUtil.isNotBlank(chatContext.getRoomNo())? chatContext.getRoomNo(): initMap.get(WorkflowConstant.ROOM_NO));

        inputMap.put(SystemVariableEnum.SYS_POSITION_CODE.getName(),
                StrUtil.isNotBlank(chatContext.getPositionCode())? chatContext.getPositionCode(): initMap.get(WorkflowConstant.POSITION_CODE));

        inputMap.put(SystemVariableEnum.SYS_POSITION_NAME.getName(),
                StrUtil.isNotBlank(chatContext.getPositionName())? chatContext.getPositionName(): initMap.get(WorkflowConstant.POSITION_NAME));

        inputMap.put(SystemVariableEnum.SYS_CLIENT_REQ_ID.getName(),
                StrUtil.isNotBlank(chatContext.getClientReqId())? chatContext.getClientReqId(): initMap.get(WorkflowConstant.CLIENT_REQ_ID));

        inputMap.put(SystemVariableEnum.SYS_DATE.getName(), LocalDate.now().format(DatePattern.NORM_DATE_FORMATTER));

        inputMap.put(SystemVariableEnum.SYS_CLIENT_TYPE.getName(),
                StrUtil.isNotBlank(chatContext.getClientType())? chatContext.getClientType(): initMap.get(WorkflowConstant.CLIENT_TYPE));

        return inputMap;
    }


}
