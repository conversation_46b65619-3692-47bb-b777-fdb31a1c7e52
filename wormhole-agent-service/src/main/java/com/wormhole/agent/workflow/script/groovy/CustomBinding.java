package com.wormhole.agent.workflow.script.groovy;

import groovy.lang.Binding;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * CustomBinding
 *
 * <AUTHOR>
 * @version 2024/10/10
 */
public class CustomBinding extends Binding {

    // 全局变量
    private final Map<String, Object> globalVariables;

    /**
     * 构造函数
     *
     * @param globalVariables 包含全局变量的Map
     * @param variables       包含普通变量的Map
     */
    public CustomBinding(Map<String, Object> globalVariables, Map<String, Object> variables) {
        super(variables);
        this.globalVariables = Objects.nonNull(globalVariables) ? globalVariables : new HashMap<>();
    }

    /**
     * 重写getVariable方法，优先从全局变量获取
     *
     * @param name 变量名
     * @return 变量值
     */
    @Override
    public Object getVariable(String name) {
        if (globalVariables.containsKey(name)) {
            return globalVariables.get(name);
        }
        return super.getVariable(name);
    }

    /**
     * 重写hasVariable方法，检查是否存在该变量
     *
     * @param name 变量名
     * @return 是否存在该变量
     */
    @Override
    public boolean hasVariable(String name) {
        if (globalVariables.containsKey(name)) {
            return true;
        }
        return super.hasVariable(name);
    }
}