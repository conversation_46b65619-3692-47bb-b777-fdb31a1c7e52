{"nodes": [{"id": "100001", "type": "start", "meta": {"position": {"x": 115.38095238095238, "y": -2.761904761904759}, "testRun": {}}, "data": {"nodeMeta": {"description": "工作流的起始节点，用于设定启动工作流需要的信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-Start.png", "subTitle": "", "title": "开始"}, "outputs": [{"type": "string", "name": "USER_INPUT", "required": false, "description": "用户本轮对话输入内容"}]}}, {"id": "900001", "type": "end", "meta": {"position": {"x": 1024, "y": 119}, "testRun": {}}, "data": {"nodeMeta": {"description": "工作流的最终节点，用于返回工作流运行后的结果信息", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-End.png", "subTitle": "", "title": "结束"}, "inputs": {"terminatePlan": "useAnswerContent", "streamingOutput": false, "inputParameters": [{"name": "output", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockId": "100001", "name": "USER_INPUT"}}}}], "content": {"type": "string", "value": {"type": "literal", "content": "$!{output}"}}}}}, {"id": "137692", "type": "llm", "meta": {"position": {"x": 554.809523809524, "y": 150.39285714285717}, "testRun": {}}, "data": {"nodeMeta": {"title": "大模型", "icon": "https://lf3-static.bytednsdoc.com/obj/eden-cn/dvsmryvd_avi_dvsm/ljhwZthlaukjlkulzlp/icon/icon-LLM.png", "description": "调用大语言模型,使用变量和提示词生成回复", "subTitle": "大模型"}, "inputs": {"settingOnError": {}, "inputParameters": [{"name": "input", "input": {"type": "string", "value": {"type": "ref", "content": {"source": "block-output", "blockId": "100001", "name": "USER_INPUT"}}}}, {"name": "args1", "input": {"type": "string", "value": {"type": "literal", "content": "test"}}}], "llmParams": {"chatModel": "QWEN", "generationDiversity": "default_val", "enableChatHistory": false, "responseFormat": "markdown", "model": "qwen-plus", "temperature": 0.3, "topP": 0.9, "frequencyPenalty": 0.0, "maxTokens": 500, "systemPrompt": "你是一个有帮助的助手。", "userPrompt": "你好"}}, "outputs": [{"type": "string", "name": "output", "description": ""}]}, "version": "2"}], "edges": [{"sourceNodeID": "137692", "targetNodeID": "900001"}, {"sourceNodeID": "100001", "targetNodeID": "137692"}]}