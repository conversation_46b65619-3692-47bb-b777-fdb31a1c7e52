package com.wormhole.agent.workflow.model;

import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.workflow.Workflow;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * WorkflowResult
 *
 * <AUTHOR>
 * @version 2024/11/21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowResult {

    private ChatToolCall chatToolCall;

    private Workflow workflow;

    private Map<String, Object> initialInput;
    /**
     * 智能体回复方式
     */
    private TerminatePlanEnum terminatePlan;
    /**
     * 工作流返回值
     */
    private Map<String, Object> output;

    private Map<String, NodeExecutionInfo<Node.Inputs>> nodeExecutionInfoMap;

    @Builder.Default
    private boolean nodeOutPutFlag = false;
}
