package com.wormhole.agent.workflow;

import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.node.NodeExecutor;
import com.wormhole.agent.workflow.node.NodeExecutorFactory;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.*;

@Slf4j
@Component
public class WorkflowEngine {

    @Lazy
    @Resource
    private NodeExecutorFactory nodeExecutorFactory;

    public Mono<WorkflowContext> executeWorkflow(WorkflowContext context) {
        context.initializeNodeExecutions();
        return Mono.defer(() -> executeNode(context.getWorkflow().getStartNode(), context));
    }

    public Mono<WorkflowContext> executeWorkflow(WorkflowContext context,String nodeId) {
        context.initializeNodeExecutions();
        Node node = context.getWorkflow().getNodeById(nodeId);

        NodeExecutor executor = nodeExecutorFactory.getExecutor(node.getType());
        NodeExecutionInfo<Node.Inputs> inputsNodeExecutionInfo = context.getNodeExecutionInfoMap().get(nodeId);
        inputsNodeExecutionInfo.setInput(context.getInitialInput());
        return doExecuteNode(node, context, executor);
    }

    private Mono<WorkflowContext> executeNode(Node node, WorkflowContext context) {
        Mono<WorkflowContext> execution = context.getNodeExecution(node.getId());
        if (Objects.nonNull(execution)) {
            return execution;
        }

        NodeExecutionInfo nodeInfo = context.getNodeExecutionInfo(node.getId());
        if (!nodeInfo.isActive()) {
            return Mono.just(context);
        }

        NodeExecutor executor = nodeExecutorFactory.getExecutor(node.getType());
        Workflow workflow = context.getWorkflow();

        Mono<WorkflowContext> nodeExecutionMono;
        if (node.equals(workflow.getStartNode())) {
            nodeExecutionMono = doExecuteNode(node, context, executor);
        } else {
            nodeExecutionMono = checkUpstreamNodesAndExecute(node, context, executor);
        }

        nodeExecutionMono = nodeExecutionMono.cache();
        context.setNodeExecution(node.getId(), nodeExecutionMono);
        return nodeExecutionMono.flatMap(workflowContext -> triggerNextNodes(node, context));
    }

    private Mono<WorkflowContext> checkUpstreamNodesAndExecute(Node node, WorkflowContext context, NodeExecutor executor) {
        List<Edge> incomingEdges = context.getWorkflow().getIncomingEdges(node.getId());

        // 对所有上游节点进行检查，根据条件分支决定是否产生输入信号
        return Flux.fromIterable(incomingEdges)
                .flatMap(edge -> {
                    Node sourceNode = context.getWorkflow().getNodeById(edge.getSourceNodeId());
                    NodeExecutionInfo sourceInfo = context.getNodeExecutionInfo(sourceNode.getId());

                    // 如果上游是条件分支节点，检查当前节点是否在该分支中激活
                    if (NodeTypeEnum.CONDITIONAL_BRANCH.equals(NodeTypeEnum.getByType(sourceNode.getType()))
                            || NodeTypeEnum.INTENT_RECOGNITION.equals(NodeTypeEnum.getByType(sourceNode.getType()))
                    ) {
                        Map<String, Object> branchOutput = sourceInfo.getOutput();
                        String branchKey = WorkflowUtils.getBranchKey(edge);
                        Boolean isActive = MapUtils.getBoolean(branchOutput, branchKey);
                        if (!Boolean.TRUE.equals(isActive)) {
                            // 此路径不激活当前节点，返回空信号
                            return Mono.empty();
                        }
                    }
                    // 上游激活本节点，返回上游的执行结果
                    return Optional.ofNullable(context.getNodeExecution(edge.getSourceNodeId())).orElse(Mono.empty());
                })
                .collectList()
                .flatMap(inputs -> {
                    NodeExecutionInfo nodeInfo = context.getNodeExecutionInfo(node.getId());
                    if (inputs.isEmpty()) {
                        // 没有任何上游为该节点提供执行条件，将节点标记为不活跃并跳过执行
                        nodeInfo.setActive(false);
                        return Mono.just(context);
                    }

                    // 有至少一个上游激活该节点，正常执行
                    return (Mono<WorkflowContext>) doExecuteNode(node, context, executor);
                });
    }

    private Mono<WorkflowContext> doExecuteNode(Node node, WorkflowContext context, NodeExecutor<Node.Inputs> executor) {
        if(!context.isRun()){
            return Mono.just(context);
        }
        NodeExecutionInfo<Node.Inputs> executionInfo = context.getNodeExecutionInfo(node.getId());
        executionInfo.markStarted();
        return executor.process(node, executionInfo, context)
                .doOnNext(workflowContext -> executionInfo.complete())
                .onErrorResume(error -> handleExecutionError(node, executionInfo, context, error));

    }

    private Mono<WorkflowContext> triggerNextNodes(Node currentNode, WorkflowContext context) {
        List<Edge> outgoingEdges = context.getWorkflow().getOutgoingEdges(currentNode.getId());

        if (CollectionUtils.isEmpty(outgoingEdges)) {
            // 无下游，工作流即将结束
            context.markCompleted();
            // 将未执行的节点设为 SKIPPED
            context.skipUnexecutedNodes();
            return Mono.just(context);
        }

        if (Objects.equals(NodeTypeEnum.CONDITIONAL_BRANCH, NodeTypeEnum.getByType(currentNode.getType()))
                || Objects.equals(NodeTypeEnum.INTENT_RECOGNITION, NodeTypeEnum.getByType(currentNode.getType()))) {
            Map<String, Object> branchOutput = context.getNodeExecutionInfo(currentNode.getId()).getOutput();

            return Flux.fromIterable(outgoingEdges)
                    .filter(edge -> Boolean.TRUE.equals(MapUtils.getBoolean(branchOutput, WorkflowUtils.getBranchKey(edge))))
                    .flatMap(edge -> {
                        Node nextNode = context.getWorkflow().getNodeById(edge.getTargetNodeId());
                        return executeNode(nextNode, context);
                    })
                    .then(Mono.just(context));
        }

        return Flux.fromIterable(outgoingEdges)
                .flatMap(edge -> {
                    Node nextNode = context.getWorkflow().getNodeById(edge.getTargetNodeId());
                    return executeNode(nextNode, context);
                }, outgoingEdges.size())
                .then(Mono.just(context));
    }

    /**
     * 处理执行过程中的错误
     *
     * @param node 当前节点
     * @param executionInfo 执行信息
     * @param context 工作流上下文
     * @param error 发生的错误
     * @return 处理后的Mono<WorkflowContext>
     */
    private Mono<WorkflowContext> handleExecutionError(Node node, NodeExecutionInfo<Node.Inputs> executionInfo,
                                                       WorkflowContext context, Throwable error) {
        // 设置结束时间
        context.setEndTime(System.currentTimeMillis());
        log.error("Error in doExecuteNode: {}", error.getMessage(), error);

        // 获取错误处理设置
        Node.SettingOnError settingOnError = executionInfo.getNodeDataInputs().getSettingOnError();
        if(settingOnError == null){
            return handleDefaultError(executionInfo, context, error);
        }
        if (settingOnError.getEnabled()) {
            return handleCustomError(executionInfo, context, error, settingOnError);
        } else {
            return handleDefaultError(executionInfo, context, error);
        }
    }

    /**
     * 处理自定义错误配置
     */
    private Mono<WorkflowContext> handleCustomError(NodeExecutionInfo<Node.Inputs> executionInfo, WorkflowContext context,
                                                    Throwable error, Node.SettingOnError settingOnError) {
        try {
            Object dataOnErr = settingOnError.getDataOnErr();
            log.debug("Error handling - dataOnErr type: {}, value: {}",
                    dataOnErr != null ? dataOnErr.getClass().getName() : "null",
                    dataOnErr);

            Map<String, Object> dataOnErrMap = extractErrorDataMap(dataOnErr);
            String output = extractOutputMessage(dataOnErrMap);

            // 根据output是否有值决定使用哪个方法生成ChatCompletions
            ChatCompletions chatCompletions;
            if (output != null && !output.trim().isEmpty()) {
                chatCompletions = ChatCompletionUtils.buildChatCompletionsDelta(output);
            } else {
                chatCompletions = ChatCompletionUtils.buildChatCompletionsDeltaLast("系统繁忙，请稍后重试");
            }

            executionInfo.setOutput(dataOnErrMap);
            context.sinkNext(chatCompletions);

            return Mono.just(context);
        } catch (Exception e) {
            log.error("Error in custom error handling: {}", e.getMessage(), e);
            // 降级到默认错误处理
            return handleDefaultError(executionInfo, context, error);
        }
    }

    /**
     * 从错误数据中提取Map结构
     */
    private Map<String, Object> extractErrorDataMap(Object dataOnErr) {
        // 根据dataOnErr的类型进行不同处理
        if (dataOnErr instanceof Map) {
            // 已经是Map类型
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) dataOnErr;
            return map;
        }

        // 尝试作为字符串处理
        String dataStr = dataOnErr != null ? dataOnErr.toString() : "";
        Map<String, Object> dataMap = new HashMap<>();

        // 检查是否是有效的JSON格式
        if (dataStr.trim().startsWith("{")) {
            try {
                return JacksonUtils.readValue(dataStr);
            } catch (Exception e) {
                log.warn("Failed to parse JSON: {}", e.getMessage());
                dataMap.put("output", "系统繁忙，请稍后重试");
                return dataMap;
            }
        } else {
            // 不是JSON格式，直接使用作为输出或使用默认消息
            dataMap.put("output", dataStr.isEmpty() ? "系统繁忙，请稍后重试" : dataStr);
            return dataMap;
        }
    }

    /**
     * 从错误数据Map中提取输出消息
     */
    private String extractOutputMessage(Map<String, Object> dataMap) {
        return dataMap.containsKey("output") ?
                String.valueOf(dataMap.get("output")) :
                null;  // 返回null表示没有output值
    }

    /**
     * 处理默认错误情况
     */
    private Mono<WorkflowContext> handleDefaultError(NodeExecutionInfo<Node.Inputs> executionInfo, WorkflowContext context, Throwable error) {
        ChatCompletions chatCompletions = ChatCompletionUtils.buildChatCompletionsDeltaLast("系统繁忙，请稍后重试");
        context.sinkNext(chatCompletions);
        context.setRun(false);
        executionInfo.fail(error.getMessage());
        return Mono.just(context);
    }


}
