package com.wormhole.agent.workflow.model;

import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Optional;

/**
 * ContentFormatTypeEnum
 *
 * <AUTHOR>
 * @version 2024/12/10
 */
public enum ContentFormatTypeEnum {

    /**
     * JSON path format
     */
    JSON_PATH("json_path"),

    /**
     * Freemarker template format
     */
    FREEMARKER("freemarker"),

    /**
     * Raw/original format output
     */
    RAW_FORMAT("raw_format");

    private final String type;

    ContentFormatTypeEnum(String type) {
        this.type = type;
    }

    public static ContentFormatTypeEnum from(String type) {
        return Optional.ofNullable(type)
                .filter(StringUtils::isNotBlank)
                .flatMap(t -> Arrays.stream(values()).filter(item -> item.getType().equalsIgnoreCase(type)).findFirst())
                .orElse(null);
    }

    public String getType() {
        return type;
    }

}
