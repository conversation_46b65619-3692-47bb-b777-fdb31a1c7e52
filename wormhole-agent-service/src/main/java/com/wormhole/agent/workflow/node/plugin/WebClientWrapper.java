package com.wormhole.agent.workflow.node.plugin;

import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-04-02 14:18:45
 * @Description:
 */
public class WebClientWrapper {

    private final WebClient webClient;

    private WebClientWrapper(WebClient webClient) {
        this.webClient = webClient;
    }


    public static class Builder {
        private Map<String,String> queryParamMap;

        private Map<String,String> headerMap;


    }


}
