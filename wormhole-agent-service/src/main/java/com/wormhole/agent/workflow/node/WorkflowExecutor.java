package com.wormhole.agent.workflow.node;

import com.google.common.collect.Maps;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.TerminatePlanEnum;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.WorkflowRequestSourceEnum;
import com.wormhole.agent.workflow.model.inputs.WorkflowInputs;
import com.wormhole.agent.workflow.service.WorkflowService;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;

/**
 * 工作流节点
 *
 * <AUTHOR>
 * @version 2024/12/2
 */
@Slf4j
@Component
public class WorkflowExecutor extends AbstractNodeExecutor<WorkflowInputs> {

    @Lazy
    @Resource
    private WorkflowService workflowService;

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.WORKFLOW;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<WorkflowInputs> executionInfo, WorkflowContext workflowContext) {
        WorkflowInputs inputs = executionInfo.getNodeDataInputs();
        String workflowCode = inputs.getWorkflowCode();
        Map<String, Object> inputMap = executionInfo.getInput();

        Map<String, Object> initialInput = workflowContext.getInitialInput();

        inputMap.putAll(initialInput);

        WorkflowContext subWorkflowContext = WorkflowContext.builder()
                .workflowCode(workflowCode)
                .initialInput(inputMap)
                .recentMessageList(workflowContext.getRecentMessageList())
                .workflowRequestSource(WorkflowRequestSourceEnum.SUB)
                .sinks(workflowContext.getSinks())
                .build();
        return workflowService.executeWorkflow(subWorkflowContext)
                .flatMap(sub -> {
                    // 读取子工作流的返回值
                    Map<String, Object> output = WorkflowUtils.getWorkflowOutput(subWorkflowContext);
                    // 定义当前工作流的返回值
                    Map<String, Object> resultMap = Maps.newHashMap();
                    TerminatePlanEnum terminatePlan = subWorkflowContext.getTerminatePlan();
                    if (TerminatePlanEnum.RETURN_VARIABLES.equals(terminatePlan)) {
                        resultMap = output;
                    } else {
                        // 使用设定的内容直接回复，回复的值在_output中
                        Object value = MapUtils.getObject(output, WorkflowConstant.INNER_OUTPUT);
                        resultMap.put(WorkflowConstant.OUTPUT, JacksonUtils.writeValueAsString(value));
                    }
                    log.info("WorkflowExecutor workflow {} executeTimeout result: {}", workflowCode, resultMap);
                    executionInfo.setOutput(resultMap);
                    return Mono.just(workflowContext);
                });
    }

}