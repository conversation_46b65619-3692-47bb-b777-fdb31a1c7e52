package com.wormhole.agent.workflow.node.llm;

import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import com.wormhole.agent.workflow.node.AbstractNodeExecutor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Objects;

/**
 * LlmNodeExecutor
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Slf4j
@Component
public class LlmNodeExecutor extends AbstractNodeExecutor<LlmInputs> {

    @Resource
    private FunctionCallLlmNodeService functionCallLlmNodeService;
    @Resource
    private NoFunctionCallLlmNodeService noFunctionCallLlmNodeService;

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.LLM;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<LlmInputs> executionInfo, WorkflowContext workflowContext) {
        LlmInputs inputs = executionInfo.getNodeDataInputs();
        LlmInputs.LlmParams llmParams = inputs.getLlmParams();
        LlmInputs.IntentLlmParams intentLlmParams = buildIntentLlmParams(inputs);
        LlmInputs.FunctionCallParams functionCallParams = inputs.getFunctionCallParams();
        if (Objects.nonNull(functionCallParams) && inputs.isIntentSwitch() &&
                (CollectionUtils.isNotEmpty(functionCallParams.getWorkflowList()) ||
                        CollectionUtils.isNotEmpty(functionCallParams.getPluginList()) ||
                        CollectionUtils.isNotEmpty(functionCallParams.getMcpServerList())
                )) {
            return functionCallLlmNodeService.execute(llmParams, intentLlmParams, node, executionInfo, workflowContext);
        } else {
            // step1: llm-summary
            return noFunctionCallLlmNodeService.execute(llmParams, node, executionInfo, workflowContext);
        }
    }

    /**
     * 兜底逻辑
     * 构建IntentLlmParams
     * @param inputs
     * @return
     */
    private LlmInputs.IntentLlmParams buildIntentLlmParams(LlmInputs inputs) {
        LlmInputs.IntentLlmParams intentLlmParams = inputs.getIntentLlmParams();
        if (intentLlmParams == null) {
            intentLlmParams = new LlmInputs.IntentLlmParams();
            BeanUtils.copyProperties(inputs.getLlmParams(), intentLlmParams);
            intentLlmParams.setSystemPrompt(inputs.getLlmParams().getSystemPrompt());
            intentLlmParams.setUserPrompt(inputs.getLlmParams().getUserPrompt());
            intentLlmParams.setModel(UnifiedModelEnum.GPT_4_O_MINI.getModel());
            intentLlmParams.setModelProvider(UnifiedModelEnum.GPT_4_O_MINI.getProvider());
            intentLlmParams.setTemperature(0.1d);
            intentLlmParams.setMaxRecursionDepth(2);
            intentLlmParams.setEnableChatHistory(true);
            intentLlmParams.setChatHistoryRound(6);
        }
        return intentLlmParams;
    }

}