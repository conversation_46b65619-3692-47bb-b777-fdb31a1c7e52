package com.wormhole.agent.workflow.node.http;

import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.util.HttpHeadersUtils;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.inputs.HttpNodeInputs;
import com.wormhole.agent.workflow.node.AbstractNodeExecutor;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.enums.HttpRequestParamLocationEnum;
import com.wormhole.common.util.JacksonUtils;
import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;
import reactor.netty.http.client.HttpClient;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-03 13:58:32
 * @Description:
 */
@Component
@Slf4j
public class HttpNodeExecutor extends AbstractNodeExecutor<HttpNodeInputs> {

    private static final Map<HttpNodeInputs.BodyType, MediaType> MEDIA_TYPE_MAP;

    private static final String BEARER_PREFIX = "Bearer ";

    static {
        MEDIA_TYPE_MAP = Map.of(
                HttpNodeInputs.BodyType.JSON, MediaType.APPLICATION_JSON,
                HttpNodeInputs.BodyType.FORM_DATA, MediaType.MULTIPART_FORM_DATA
        );
    }


    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.HTTP;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<HttpNodeInputs> executionInfo, WorkflowContext workflowContext) {
        HttpNodeInputs nodeInputs = executionInfo.getNodeDataInputs();
        HttpNodeInputs.ApiInfo apiInfo = nodeInputs.getApiInfo();
        String url = apiInfo.getUrl();
        String method = apiInfo.getMethod();
        HttpMethod httpMethod = HttpMethod.valueOf(method);
        // 请求头
        Map<String, Object> headersMap = WorkflowUtils.getInputParametersValue(nodeInputs.getHeaders(), workflowContext);
        // param
        Map<String, Object> paramsMap = WorkflowUtils.getInputParametersValue(nodeInputs.getParams(), workflowContext);
        // auth
        handleAuthType(nodeInputs.getAuth(), headersMap, paramsMap, workflowContext);

        String fullUrl = buildFullUrl(url, paramsMap);

        HttpClient httpClient = createHttpClient(nodeInputs);


        WebClient.RequestBodySpec requestBodySpec = WebClient
                .builder()
                .clientConnector(new ReactorClientHttpConnector(httpClient))
                .build()
                .method(httpMethod)
                .uri(fullUrl)
                .headers(httpHeaders -> headersMap.forEach((k, v) -> httpHeaders.add(k, v.toString())));

        // body
        WebClient.RequestHeadersSpec<?> requestHeadersSpec = applyRequestBody(requestBodySpec, nodeInputs.getBody(), workflowContext);


        return requestHeadersSpec.retrieve()
                .toEntity(String.class)
                .map(responseEntity -> {
                    HttpStatus statusCode = (HttpStatus) responseEntity.getStatusCode();
                    HttpHeaders headers = responseEntity.getHeaders();
                    Map<String, String> httpHeadersMap = HttpHeadersUtils.getHttpHeadersMap(headers);
                    Map<String, Object> resultMap = buildResponseMap(statusCode.value(), responseEntity.getBody(), httpHeadersMap);
                    executionInfo.setOutput(resultMap);
                    return workflowContext;
                })
                .onErrorResume(error -> {
                    log.error("HTTP request execution failed: {}", error.getMessage(), error);
                    Node.SettingOnError settingOnError = executionInfo.getNodeDataInputs().getSettingOnError();
                    Map<String, Object> resultMap;
                    if (!Boolean.FALSE.equals(settingOnError.getEnabled())) {
                        resultMap = WorkflowUtils.getDataOnErr(settingOnError);
                    } else {
                        resultMap = buildResponseMap(500, error.getMessage(), new HashMap<>());
                    }
                    executionInfo.setOutput(resultMap);
                    return Mono.just(workflowContext);
                });
    }

    private Map<String, Object> buildResponseMap(int code, String body, Map<String, String> headers) {
        return Map.of(
                "statusCode", code,
                "body", Optional.ofNullable(body).orElse(StrUtil.EMPTY),
                "headers", Optional.ofNullable(headers).map(JacksonUtils::writeValueAsString)
                        .orElse(StrUtil.EMPTY_JSON)
        );
    }

    private HttpClient createHttpClient(HttpNodeInputs nodeInputs) {
        HttpNodeInputs.Setting setting = nodeInputs.getSetting();
        // 设置超时
        return HttpClient.create()
                // 连接超时 5 秒
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, setting.getTimeout())
                // 响应超时 10 秒
                .responseTimeout(Duration.ofSeconds(setting.getTimeout()))
                // 读取超时 10 秒
                .doOnConnected(conn -> conn
                        .addHandlerLast(new ReadTimeoutHandler(setting.getTimeout())));
    }

    private String buildFullUrl(String baseUrl, Map<String, Object> queryParams) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(baseUrl);
        queryParams.forEach(builder::queryParam);
        return builder.toUriString();
    }

    /**
     * Handle authentication based on auth type
     */
    private void handleAuthType(HttpNodeInputs.Auth auth, Map<String, Object> headersMap, Map<String, Object> paramsMap, WorkflowContext workflowContext) {
        if (auth == null || !Boolean.TRUE.equals(auth.getAuthOpen())) {
            return;
        }

        // Apply auth based on type
        HttpNodeInputs.AuthType authType = auth.getAuthType();

        switch (authType) {
            case BEARER_AUTH -> {
                applyBearerAuth(auth, headersMap, workflowContext);
            }
            case CUSTOM_AUTH -> {
                String addTo = auth.getAuthData().getCustomData().getAddTo();
                boolean isQueryParam = HttpRequestParamLocationEnum.QUERY.name().equalsIgnoreCase(addTo);
                applyCustomAuth(auth, isQueryParam ? paramsMap : headersMap, workflowContext);
            }
        }
    }

    /**
     * Apply Bearer token authentication
     */
    private void applyBearerAuth(HttpNodeInputs.Auth auth, Map<String, Object> targetMap, WorkflowContext workflowContext) {
        Map<String, Object> tokenData = WorkflowUtils.getInputParametersValue(auth.getAuthData().getBearerTokenData(), workflowContext);
        String token = Optional.ofNullable(tokenData.get("token"))
                .map(Object::toString)
                .orElse(StrUtil.EMPTY);

        targetMap.put(HttpHeaders.AUTHORIZATION, BEARER_PREFIX + token);
    }

    /**
     * Apply custom authentication
     */
    private void applyCustomAuth(HttpNodeInputs.Auth auth, Map<String, Object> targetMap, WorkflowContext workflowContext) {
        Map<String, Object> customAuthData = WorkflowUtils.getInputParametersValue(
                auth.getAuthData().getCustomData().getData(),
                workflowContext
        );
        Object key = customAuthData.get("key");
        if (Objects.isNull(key) || StrUtil.isBlank(key.toString())) {
            return;
        }
        targetMap.put(key.toString(), customAuthData.get("value"));
    }


    /**
     * Apply the appropriate request body based on body type
     */
    private WebClient.RequestHeadersSpec<?> applyRequestBody(
            WebClient.RequestBodySpec requestBodySpec,
            HttpNodeInputs.Body body,
            WorkflowContext workflowContext) {

        if (body == null) {
            return requestBodySpec;
        }

        HttpNodeInputs.BodyType bodyType = HttpNodeInputs.BodyType.of(body.getBodyType());
        MediaType mediaType = MEDIA_TYPE_MAP.getOrDefault(bodyType, MediaType.APPLICATION_JSON);
        requestBodySpec.contentType(mediaType);

        return switch (bodyType) {
            case JSON -> applyJsonBody(requestBodySpec, body);
            case FORM_DATA -> applyFormDataBody(requestBodySpec, body, workflowContext);
        };
    }

    /**
     * Apply JSON request body
     */
    private WebClient.RequestHeadersSpec<?> applyJsonBody(WebClient.RequestBodySpec requestBodySpec, HttpNodeInputs.Body body) {
        return requestBodySpec.bodyValue(body.getBodyData().getJson());
    }

    /**
     * Apply form data request body
     */
    private WebClient.RequestHeadersSpec<?> applyFormDataBody(
            WebClient.RequestBodySpec requestBodySpec,
            HttpNodeInputs.Body body,
            WorkflowContext workflowContext) {

        HttpNodeInputs.FormData formData = body.getBodyData().getFormData();
        List<Node.InputParameter> data = formData.getData();
        Map<String, Object> formDataMap = WorkflowUtils.getInputParametersValue(data, workflowContext);

        // Convert to multipart format
        Map<String, List<String>> multipartMap = formDataMap.entrySet().stream()
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.mapping(
                                e -> Optional.ofNullable(e.getValue())
                                        .map(Object::toString)
                                        .orElse(StrUtil.EMPTY),
                                Collectors.toList()
                        )
                ));

        return requestBodySpec.body(BodyInserters.fromFormData(new LinkedMultiValueMap<>(multipartMap)));
    }


}
