package com.wormhole.agent.workflow.node;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Edge;
import com.wormhole.agent.workflow.model.LogicEnum;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.inputs.ConditionInputs;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.tarot.AndCond;
import com.wormhole.tarot.ConditionDesc;
import com.wormhole.tarot.OrCond;
import com.wormhole.tarot.api.Cond;
import com.wormhole.tarot.core.CondParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 条件分支节点
 *
 * <AUTHOR>
 * @version 2024/9/23
 */
@Slf4j
@Component
public class ConditionalBranchNodeExecutor extends AbstractNodeExecutor<ConditionInputs> {

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.CONDITIONAL_BRANCH;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<ConditionInputs> executionInfo, WorkflowContext workflowContext) {
        Workflow workflow = workflowContext.getWorkflow();
        ConditionInputs inputs = executionInfo.getNodeDataInputs();

        Map<Integer, String> indexToTargetNodeIdMap = Maps.newHashMap();
        String lastBranchKey = StringUtils.EMPTY;

        List<Edge> outgoingEdges = workflow.getOutgoingEdges(node.getId());
        for (Edge edge : outgoingEdges) {
            if (edge.getSourcePortId().contains("_")) {
                String index = edge.getSourcePortId().split("_")[1];
                indexToTargetNodeIdMap.put(Integer.valueOf(index), edge.getTargetNodeId());
            } else {
                lastBranchKey = WorkflowUtils.getBranchKey(edge);
            }
        }

        Map<String, Object> checkedResultMap = Maps.newHashMap();
        AtomicInteger indexCounter = new AtomicInteger(0);
        AtomicBoolean lastNodeChecked = new AtomicBoolean(true);

        for (ConditionInputs.Branch branch : inputs.getBranches()) {
            ConditionInputs.Condition condition = branch.getCondition();

            // 单个分支的表达式列表
            List<ConditionDesc> conditionDescList = Lists.newArrayList();

            // 单个分支左边量的值
            Map<String, Object> contextMap = Maps.newHashMap();
            condition.getConditions().forEach(conditionItem -> {
                Node.Input leftInput = conditionItem.getLeft().getInput();

                // 表达式
                ConditionDesc conditionDesc = new ConditionDesc();
                // 从左边量读取
                conditionDesc.setKey(WorkflowUtils.getRefJsonPath(leftInput));
                conditionDesc.setType(leftInput.getType());
                // 操作符
                conditionDesc.setOperator(conditionItem.getOperator());
                // 从右变量读取。右变量可能是ref或者输入
                ConditionInputs.ConditionSide right = conditionItem.getRight();
                conditionDesc.setValue(WorkflowUtils.getRightValue(right.getInput(), contextMap));
                conditionDescList.add(conditionDesc);

                // 变量值
                String refKey = WorkflowUtils.getRefJsonKey(leftInput);
                Object contentValue = WorkflowUtils.getContentValue(leftInput, workflowContext);
                contextMap.put(refKey, contentValue);
            });

            Integer logic = condition.getLogic();
            List<Cond> conds = CondParser.parseCondList(conditionDescList);
            Cond cond = Objects.equals(LogicEnum.AND.getType(), logic) ? new AndCond(conds) : new OrCond(conds);

            int index = indexCounter.getAndIncrement();
            boolean checkResult = cond.check(contextMap);

            String nextNodeId = MapUtils.getString(indexToTargetNodeIdMap, index);

            String branchKey = WorkflowUtils.getBranchKey(node.getId(), nextNodeId);
            checkedResultMap.put(branchKey, checkResult);
            if (checkResult) {
                lastNodeChecked.set(false);
            }
            log.info("branch-{}-checkResult={}", index, checkResult);
        }

        // 默认分支
        checkedResultMap.put(lastBranchKey, lastNodeChecked.get());
        executionInfo.setOutput(checkedResultMap);
        return Mono.just(workflowContext);
    }

}