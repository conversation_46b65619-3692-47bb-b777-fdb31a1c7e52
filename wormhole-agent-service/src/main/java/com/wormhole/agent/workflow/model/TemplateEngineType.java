package com.wormhole.agent.workflow.model;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * Template Engine Type
 *
 * <AUTHOR>
 * @version 2025/2/23
 */
@Getter
public enum TemplateEngineType {

    /**
     * json_path
     */
    JSON_PATH("json_path"),
    /**
     * freemarker
     */
    FREEMARKER("freemarker"),
    /**
     * jinja
     */
    JINJA("jinja"),

    ;

    private final String value;

    TemplateEngineType(String value) {
        this.value = value;
    }

    public static TemplateEngineType from(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return Arrays.stream(values())
                .filter(item -> item.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElse(null);
    }

}
