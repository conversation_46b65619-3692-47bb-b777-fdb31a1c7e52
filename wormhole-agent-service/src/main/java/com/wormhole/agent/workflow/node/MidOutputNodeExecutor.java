package com.wormhole.agent.workflow.node;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.*;
import com.wormhole.agent.workflow.model.inputs.MidOutputInputs;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.JsonPathUtils;
import dev.langchain4j.model.input.PromptTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 支持中间结果的消息输出
 *
 * <AUTHOR>
 * @version 2025/2/16
 */
@Slf4j
@Component
public class MidOutputNodeExecutor extends AbstractNodeExecutor<MidOutputInputs> {

    @Override
    public NodeTypeEnum getType() {
        return NodeTypeEnum.MID_OUTPUT;
    }

    @Override
    public Mono<WorkflowContext> execute(Node node, NodeExecutionInfo<MidOutputInputs> executionInfo, WorkflowContext workflowContext) {
        MidOutputInputs inputs = executionInfo.getNodeDataInputs();
        // 输出变量
        Map<String, Object> varMap = WorkflowUtils.getInputParametersValue(inputs, workflowContext);
        // 输出内容
        Node.Input inputsContent = inputs.getContent();
        String template = (String) inputsContent.getValue().getContent();
        Object content;
        if (StringUtils.isBlank(template)) {
            // 直接输出变量
            content = varMap;
        } else {
            // 变量格式化输出
            String formatType = inputs.getTemplateEngineType();
            TemplateEngineType templateEngineType = TemplateEngineType.from(formatType);
            Preconditions.checkNotNull(templateEngineType, "template_engine_type must not be null");
            switch (templateEngineType) {
                // string
                case FREEMARKER -> content = FreeMarkerTemplateCacheUtils.processTemplateIntoString(template, varMap);
                // string
                case JINJA -> content = PromptTemplate.from(template).apply(varMap).text();
                // object
                case JSON_PATH -> content = JsonPathUtils.read(varMap, template);
                default -> throw new IllegalStateException("Unexpected value: " + templateEngineType);
            }
            trySinkNext(workflowContext, content);
        }
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put(WorkflowConstant.INNER_OUTPUT, content);
        executionInfo.setOutput(resultMap);
        return Mono.just(workflowContext);
    }

    public void trySinkNext(WorkflowContext workflowContext, Object output) {
        if (Objects.nonNull(output)) {
            workflowContext.getSinks().tryEmitNext(ChatCompletionUtils.buildChatCompletionsDelta(output.toString()));
        }
    }

}