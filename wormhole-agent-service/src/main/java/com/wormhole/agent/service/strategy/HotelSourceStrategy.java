package com.wormhole.agent.service.strategy;

import com.google.common.base.Preconditions;
import com.wormhole.agent.client.chat.params.UserConversationVO;
import com.wormhole.agent.dto.SaveConversationDTO;
import com.wormhole.agent.service.UserConversationService;
import com.wormhole.common.enums.SourcePlatform;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/6
 * @Description:
 */
@Component
public class HotelSourceStrategy implements SourceStrategy{

    @Resource
    private UserConversationService userConversationService;

    @Override
    public String getSource() {
        return SourcePlatform.HOTEL.getCode();
    }

    @Override
    public Mono<UserConversationVO> createConversation(SaveConversationDTO dto) {
        // Preconditions.checkArgument(StringUtils.isNotBlank(req.getDeviceId()), "deviceId不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(dto.getHotelCode()), "hotelCode不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(dto.getPositionCode()), "positionCode不能为空");

        if (dto.isCreate()) {
            return userConversationService.saveEntity(dto).map(userConversationService::toVO);
        }
        return userConversationService.queryExistingConversation(dto)
                .flatMap(Mono::just) // 确保查询结果被消费
                .switchIfEmpty(Mono.defer(() -> userConversationService.saveEntity(dto)))
                .map(userConversationService::toVO);


    }

}
