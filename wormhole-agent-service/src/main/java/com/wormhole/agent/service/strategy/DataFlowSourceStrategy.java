package com.wormhole.agent.service.strategy;

import com.wormhole.agent.client.chat.params.UserConversationVO;
import com.wormhole.agent.dto.SaveConversationDTO;
import com.wormhole.agent.service.UserConversationService;
import com.wormhole.common.enums.SourcePlatform;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/6
 * @Description:
 */
@Component
public class DataFlowSourceStrategy implements SourceStrategy{

    @Resource
    private UserConversationService userConversationService;
    @Override
    public String getSource() {
        return SourcePlatform.DATAFLOW.getCode();
    }

    @Override
    public Mono<UserConversationVO> createConversation(SaveConversationDTO dto) {
        return userConversationService.saveEntity(dto).map(userConversationService::toVO);
    }

}
