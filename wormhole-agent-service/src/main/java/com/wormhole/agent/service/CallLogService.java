package com.wormhole.agent.service;

import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.model.entity.UserConversationEntity;
import com.wormhole.agent.repository.UserConversationRepository;
import com.wormhole.agent.util.DeviceInitRtcUtil;
import com.wormhole.common.constant.HeaderConstant;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.core.enums.CallTypeEnum;
import com.wormhole.hotelds.core.model.entity.HdsCallLogEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/14 17:43
 */

@Slf4j
@Service
public class CallLogService {
    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private UserConversationRepository userConversationRepository;

    public Mono<HdsCallLogEntity> saveCallLog(ChatContext chatContext, String messageType) {
        return buildHdsCallLogEntity(chatContext, messageType)
                .flatMap(entity -> {
                    log.info("saveCallLog entity:{}", JacksonUtils.writeValueAsString(entity));
                    return r2dbcEntityTemplate.insert(HdsCallLogEntity.class).using(entity);
                })
                .doOnSuccess(entity -> log.info("CallLog saved successfully | type:{} | userId:{} | conversationId:{}", messageType, entity.getCreatedBy(), chatContext.getConversationId()))
                .onErrorResume(e -> {
                    log.error("CallLog save failed | type:{} | userId:{} | conversationId:{} | error:{}", messageType, chatContext.getUserId(), chatContext.getConversationId(), e.getMessage());
                    return Mono.empty();
                });
    }

    private static String getPositionCodeFromRtcRoomId(String roomId) {
        if (roomId == null ) return null;
        String[] split = roomId.split("_");
        if (split.length < 2) return null;
        return StringUtils.equalsAny(split[1],"null") ? null : split[1];
    }

    private Mono<HdsCallLogEntity> buildHdsCallLogEntity(ChatContext chatContext, String messageType) {
        String userId = Optional.ofNullable(chatContext.getUserId())
                .orElseGet(() -> chatContext.getHttpHeaders().getFirst(HeaderConstant.USER_ID));
        String userName = Optional.ofNullable(chatContext.getUsername())
                .orElseGet(() -> chatContext.getHttpHeaders().getFirst(HeaderConstant.USERNAME));

        HdsCallLogEntity entity = new HdsCallLogEntity();
        entity.setConversationId(chatContext.getConversationId());
        entity.setRtcRoomId(chatContext.getRtcRoomId());
        entity.setDeviceId(chatContext.getDeviceId());
        entity.setClientReqId(chatContext.getClientReqId());
        entity.setBotCode(chatContext.getBotCode());
        entity.setHotelCode(chatContext.getHotelCode());
        entity.setPositionCode(getPositionCodeFromRtcRoomId(chatContext.getRtcRoomId()));
        entity.setMessageId(chatContext.getMessageId());
        entity.setMessageType(messageType);
        entity.setCallType(CallTypeEnum.AGENT_CALL.getType());

        String content = ChatRole.USER.getValue().equals(messageType) ?
                chatContext.getQuestion() : chatContext.getAnswer();
        entity.setContent(content);

        LocalDateTime now = LocalDateTime.now();

        String finalUserId = DeviceInitRtcUtil.getDeviceIdByRtcUserId(userId);
        entity.setCreatedBy(finalUserId);
        entity.setCreatedByName(userName);
        entity.setCreatedAt(now);
        entity.setUpdatedAt(now);
        entity.setUpdatedBy(finalUserId);
        entity.setUpdatedByName(userName);
        entity.setRowStatus(RowStatusEnum.VALID.getId());

        // 如果会话信息中缺少必要字段，则尝试查库补全
        if (StringUtils.isAnyBlank(entity.getBotCode(), entity.getHotelCode(), entity.getDeviceId(), entity.getPositionCode())) {
            return userConversationRepository.findByConversationId(chatContext.getConversationId())
                    .map(conversation -> {
                        if (StringUtils.isBlank(entity.getBotCode())) entity.setBotCode(conversation.getBotCode());
                        if (StringUtils.isBlank(entity.getHotelCode())) entity.setHotelCode(conversation.getHotelCode());
                        if (StringUtils.isBlank(entity.getDeviceId())) entity.setDeviceId(conversation.getDeviceId());
                        if (StringUtils.isBlank(entity.getPositionCode())) entity.setPositionCode(conversation.getPositionCode());
                        return entity;
                    })
                    .switchIfEmpty(Mono.just(entity)); // 查不到也返回原始 entity
        } else {
            return Mono.just(entity);
        }
    }


}
