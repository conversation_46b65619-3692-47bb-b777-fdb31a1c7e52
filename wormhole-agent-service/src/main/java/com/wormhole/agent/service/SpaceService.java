package com.wormhole.agent.service;

import com.wormhole.agent.entity.SpaceEntity;
import com.wormhole.agent.entity.SpaceFieldEnum;
import com.wormhole.agent.entity.SpaceUserRoleEntity;
import com.wormhole.agent.entity.SpaceUserRoleFieldEnum;
import com.wormhole.agent.repository.SpaceRepository;
import com.wormhole.agent.repository.SpaceUserRoleRepository;
import jakarta.annotation.Resource;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: joker.liu
 * @date: 2025/4/17
 * @Description:
 */
@Service
public class SpaceService {

    @Resource
    private SpaceRepository spaceRepository;

    @Resource
    private SpaceUserRoleRepository spaceUserRoleRepository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    public Mono<List<String>> getInSpaceUserList(String userId) {

        return getSpaceList("PUBLIC")
                .flatMap(spaceEntities -> findSpaceRoleByUserId(userId)
                        .flatMap(spaceUserRoleEntities -> {

                            Set<String> spaceCodes = spaceEntities.stream().map(SpaceEntity::getCode).collect(Collectors.toSet());
                            spaceUserRoleEntities.forEach(spaceUserRoleEntity -> spaceCodes.add(spaceUserRoleEntity.getSpaceCode()));

                            return Mono.just(spaceCodes.stream().toList());
                        }));
    }

    public Mono<List<SpaceEntity>> getSpaceList(String spaceType) {
        Criteria criteria = Criteria.where(SpaceFieldEnum.space_type.name()).is(spaceType);
        return r2dbcEntityTemplate.select(Query.query(criteria), SpaceEntity.class).collectList();
    }

    public Mono<List<SpaceUserRoleEntity>> findSpaceRoleByUserId(String userId) {
        Criteria criteria = Criteria.where(SpaceUserRoleFieldEnum.user_id.name()).is(userId);
        return r2dbcEntityTemplate.select(Query.query(criteria), SpaceUserRoleEntity.class).collectList();
    }

}
