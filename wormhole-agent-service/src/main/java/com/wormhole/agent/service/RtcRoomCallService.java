package com.wormhole.agent.service;

import com.google.common.collect.Maps;
import com.wormhole.agent.client.chat.params.UserConversationVO;
import com.wormhole.agent.dto.CallStateChange;
import com.wormhole.agent.dto.RtcRoomCallDTO;
import com.wormhole.agent.dto.SaveConversationDTO;
import com.wormhole.agent.entity.RtcRoomCallEntity;
import com.wormhole.agent.enums.RtcCallType;
import com.wormhole.agent.repository.RtcRoomCallRepository;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.config.RtcVoiceChatProperties;
import com.wormhole.agent.rtc.consts.RoomUserHeaderConstant;
import com.wormhole.agent.util.DeviceInitRtcUtil;
import com.wormhole.agent.util.RtcRoomParseUtil;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.service.WorkflowService;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import com.wormhole.channel.consts.enums.VoiceChatUpdateCommandEnums;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.channel.consts.message.CallInfoMessage;
import com.wormhole.channel.consts.request.RtcApiRequest;
import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.LocalDateTimeUtils;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.mq.producer.ReactiveMessageSender;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: joker.liu
 * @date: 2025/3/31
 * @Description:    语聊房通话记录处理
 */
@Component
@RequiredArgsConstructor
public class RtcRoomCallService {

    private static final Logger log = LoggerFactory.getLogger(RtcRoomCallService.class);

    private final RtcRoomCallRepository rtcRoomCallRepository;

    private final ConnectionService connectionService;

    private final UserConversationService userConversationService;

    private final RtcVoiceChatProperties rtcVoiceChatProperties;

    private final RtcHelper rtcHelper;

    private final ReactiveMessageSender reactiveMessageSender;

    private final WorkflowService workflowService;

    @Value("${rocket.topic.call-event-info:call_event_info_topic}")
    public String CALL_EVENT_INFO_TOPIC;


    /**
     * 创建语聊房通话记录
     * @return  通话记录
     */
    public Mono<RtcRoomCallEntity> create(RtcRoomCallDTO rtcRoomCallDTO) {
        String deviceId = RtcRoomParseUtil.getDeviceId(rtcRoomCallDTO.getRtcRoomId());
        if (StringUtils.isBlank(deviceId)) {
            return Mono.error(new RuntimeException("deviceId is empty"));
        }
        return getRoomCallInfo(rtcRoomCallDTO.getRtcRoomId())
                .switchIfEmpty(Mono.defer(() ->
                        createNewRoomCall(rtcRoomCallDTO, deviceId))
                );
    }

    private Mono<RtcRoomCallEntity> createNewRoomCall(RtcRoomCallDTO rtcRoomCallDTO, String deviceId) {
        return connectionService.getDeviceInfo(deviceId, null)
                .flatMap(deviceInfo -> {
                    SaveConversationDTO saveConversationDTO = buildConversationDTO(deviceInfo, rtcRoomCallDTO.getLanguage());
                    boolean isFront = deviceInfo.getDeviceType().startsWith(DeviceTypeEnum.FRONT.getCode());

                    return userConversationService.createConversation(saveConversationDTO)
                            .map(userConversation -> buildRtcRoomCallEntity(rtcRoomCallDTO, deviceInfo, userConversation, isFront))
                            .flatMap(rtcRoomCallRepository::save);
                });
    }

    private SaveConversationDTO buildConversationDTO(DeviceInfoResp deviceInfo, String language) {
        return SaveConversationDTO.builder()
                .userId(deviceInfo.getRtcUserId())
                .username(deviceInfo.getPositionCode())
                .source(SourcePlatform.HOTEL.getCode())
                .botCode(rtcVoiceChatProperties.getChatBot(language, deviceInfo.getDeviceId()))
                .isDebug(false)
                .hotelCode(deviceInfo.getHotelCode())
                .deviceId(deviceInfo.getDeviceId())
                .title(deviceInfo.getHotelCode())
                .positionCode(deviceInfo.getPositionCode())
                .build();
    }

    private RtcRoomCallEntity buildRtcRoomCallEntity(RtcRoomCallDTO rtcRoomCallDTO, DeviceInfoResp deviceInfo, UserConversationVO userConversation, boolean isFront) {
        RtcRoomCallEntity roomCall = new RtcRoomCallEntity();
        roomCall.setRtcRoomId(rtcRoomCallDTO.getRtcRoomId())
                .setPositionCode(deviceInfo.getPositionCode())
                .setConversationId(userConversation.getConversationId())
                .setCallDirection(isFront ? 1 : 2)
                .setCallStatus(1)
                .setInitiatorId(deviceInfo.getRtcUserId())
                .setInitiatorType(isFront ? 1 : 2)
                .setCallStartTime(LocalDateTimeUtils.toLocalDateTime(rtcRoomCallDTO.getTimestamp()))
                .setInitiatorClientType(rtcRoomCallDTO.getClientType());

        roomCall.setStateChanges(JacksonUtils.writeValueAsString(List.of(CallStateChange.instance("call_start", null, deviceInfo.getRtcUserId()))));

        roomCall.setCreatedAt(LocalDateTime.now());

        roomCall.setHasAiParticipant(rtcRoomCallDTO.getHasAiParticipant());

        roomCall.setCreatedBy(deviceInfo.getAccount());
        roomCall.setCreatedByName(deviceInfo.getUsername());
        roomCall.setInitiatorName(deviceInfo.getUsername());
        roomCall.setUpdatedBy(deviceInfo.getAccount());
        roomCall.setUpdatedByName(deviceInfo.getUsername());

        return roomCall;
    }


    /**
     * 根据房间id获取正在通话的记录
     * @param roomId    房间id
     * @return  通话记录
     */
    public Mono<RtcRoomCallEntity> getOnlineRoomCallInfo(String roomId) {
        return rtcRoomCallRepository.findByRtcRoomIdAndRowStatus(roomId, 1);
    }

    public Mono<RtcRoomCallEntity> getRoomCallInfo(String roomId) {
        return rtcRoomCallRepository.findByRtcRoomId(roomId);
    }

    /**
     * 呼叫智能体
     * @param rtcRoomCallDTO  通话相关字段
     */
    public Mono<Boolean> callAgent(RtcRoomCallDTO rtcRoomCallDTO) {
        return this.getOnlineRoomCallInfo(rtcRoomCallDTO.getRtcRoomId())
                .flatMap(call -> {
                    call.setCallStatus(RtcCallStatusEnum.CALLING.getCode());
                    call.setReceiverId(rtcRoomCallDTO.getAgentId());
                    call.setReceiverName("agent");
                    call.setReceiverType(RtcCallType.AGENT.getCode());
                    call.setHasAiParticipant(1);
                    call.setAiAgentId(rtcRoomCallDTO.getAgentId());
                    call.setInitiatorClientType(rtcRoomCallDTO.getClientType());
                    call.setReceiverClientType(0);
                    call.setCallAgentAnswerTime(LocalDateTime.now());

                    call.setCurrentParticipantId(rtcRoomCallDTO.getAgentId());
                    call.setCurrentParticipantName("agent");
                    call.setCurrentParticipantType(RtcCallType.AGENT.getCode());
                    call.setCurrentParticipantClientType(0);

                    List<CallStateChange> callStateChanges = new ArrayList<>();
                    if (StringUtils.isNotBlank(call.getStateChanges())) {
                        callStateChanges = JacksonUtils.readValues(call.getStateChanges(), CallStateChange.class);
                    }
                    CallStateChange callStateChange = CallStateChange.instance("call_agent", null, rtcRoomCallDTO.getRtcUserId(), rtcRoomCallDTO.getAgentId());
                    callStateChanges.add(callStateChange);
                    call.setStateChanges(JacksonUtils.writeValueAsString(callStateChanges));
                    return update(call.getId(), call)
                            .doOnSuccess(result -> {
                                log.info("callAgent over: {}", JacksonUtils.writeValueAsString(result));
                            }).thenReturn(Boolean.TRUE);
                });
    }

    /**
     * 智能体接听的时间
     * @param rtcRoomCallDTO    通话相关字段
     */
    public Mono<Boolean> calledAgentAccept(RtcRoomCallDTO rtcRoomCallDTO) {
        return this.getOnlineRoomCallInfo(rtcRoomCallDTO.getRtcRoomId())
                .flatMap(call -> {
                    call.setCallStatus(RtcCallStatusEnum.ANSWERED.getCode());
                    call.setCallAgentAnswerTime(LocalDateTimeUtils.toLocalDateTime(rtcRoomCallDTO.getTimestamp()));
                    List<CallStateChange> callStateChanges = new ArrayList<>();
                    if (StringUtils.isNotBlank(call.getStateChanges())) {
                        callStateChanges = JacksonUtils.readValues(call.getStateChanges(), CallStateChange.class);
                    }
                    CallStateChange callStateChange = CallStateChange.instance("call_agent_accept", null, rtcRoomCallDTO.getAgentId());
                    callStateChanges.add(callStateChange);
                    call.setStateChanges(JacksonUtils.writeValueAsString(callStateChanges));
                    return update(call.getId(), call)
                            .doOnSuccess(result -> {
                                log.info("calledAgentAccept over: {}", JacksonUtils.writeValueAsString(result));
                            }).thenReturn(Boolean.TRUE);
                });
    }

    /**
     * 转接
     * @param roomId    发生的房间
     * @param userId    谁转接的（一般来说是智能体，如果是客户端直接调用，则是用户）
     */
    public Mono<Boolean> transfer(String roomId, String userId, String username, String receiverId, String receiverName, Integer type) {
        return this.getOnlineRoomCallInfo(roomId)
                .flatMap(call -> {
                    call.setCallStatus(RtcCallStatusEnum.CALLING.getCode());
                    call.setHasTransfer(call.getHasAiParticipant());
                    call.setTransferTime(LocalDateTime.now());
                    String stateChanges = call.getStateChanges();
                    List<CallStateChange> callStateChanges = new ArrayList<>();
                    if (StringUtils.isNotBlank(stateChanges)) {
                        callStateChanges = JacksonUtils.readValues(stateChanges, CallStateChange.class);
                    }
                    CallStateChange callStateChange = CallStateChange.instance("call_transfer", null, userId);
                    callStateChanges.add(callStateChange);
                    call.setStateChanges(JacksonUtils.writeValueAsString(callStateChanges));

                    if (StringUtils.isNotBlank(receiverId)) {
                        call.setReceiverId(receiverId);
                    }

                    if (StringUtils.isNotBlank(receiverName)) {
                        call.setReceiverName(receiverName);
                    }

                    if (Objects.nonNull(type)) {
                        call.setReceiverType(type);
                    }

                    call.setUpdatedBy(userId);
                    call.setUpdatedByName(username);

                    return update(call.getId(), call)
                            .doOnSuccess(result -> {
                                log.info("transfer over: {}", JacksonUtils.writeValueAsString(result));
                            })
                            // .then(closeAgent(call))
                            .thenReturn(Boolean.TRUE);
                });
    }

    public Mono<Boolean> cleanupVoiceChat(String roomId) {
        return this.getOnlineRoomCallInfo(roomId)
                .flatMap(call -> closeAgent(call).then(closeReplayStream(call)));
    }

    private Mono<Boolean> closeReplayStream(RtcRoomCallEntity call) {
        if (call.getRtcRoomId().startsWith(RoomUserHeaderConstant.CHECK.getHeader())) {
            return rtcHelper.stopRelayStreamAsync(call.getRtcRoomId(), call.getRtcRoomId())
                    .thenReturn(Boolean.TRUE);
        }
        return Mono.just(Boolean.TRUE);
    }

    public Mono<Boolean> closeAgent(RtcRoomCallEntity call) {
        if (call.getHasAiParticipant() == 0) {
            return Mono.just(Boolean.TRUE);
        }
        // 构建语音聊天中断和停止请求
        RtcApiRequest interruptRequest = RtcApiRequest.createUpdateVoiceChatRequest(
                call.getRtcRoomId(),
                call.getRtcRoomId(),
                VoiceChatUpdateCommandEnums.INTERRUPT.getCommand()
        );

        RtcApiRequest stopRequest = RtcApiRequest.createStopVoiceChatRequest(
                call.getRtcRoomId(),
                call.getRtcRoomId()
        );
        return Mono.when(
                rtcHelper.updateVoiceChatAsync(interruptRequest),
                rtcHelper.stopVoiceChatAsync(stopRequest)
        ).thenReturn(Boolean.TRUE);
    }

    /**
     * 接听
     * @param rtcRoomCallDTO    通过相关字段
     */
    public Mono<Boolean> accept(RtcRoomCallDTO rtcRoomCallDTO) {
        return this.getOnlineRoomCallInfo(rtcRoomCallDTO.getRtcRoomId())
                .flatMap(call -> {
                    if (Objects.equals(call.getInitiatorId(), rtcRoomCallDTO.getRtcUserId())) {
                        return Mono.just(Boolean.TRUE);
                    }
                    if (StringUtils.isNotBlank(call.getReceiverId())) {
                        call.setCurrentParticipantId(rtcRoomCallDTO.getRtcUserId());
                        call.setCurrentParticipantType(rtcRoomCallDTO.getRtcUserId().startsWith(DeviceTypeEnum.FRONT.getCode()) ? 1 : 2);
                        call.setCurrentParticipantName(rtcRoomCallDTO.getUsername());
                        call.setCurrentParticipantClientType(rtcRoomCallDTO.getClientType());
                    } else {
                        call.setReceiverId(rtcRoomCallDTO.getRtcUserId());
                        call.setReceiverType(rtcRoomCallDTO.getRtcUserId().startsWith(DeviceTypeEnum.FRONT.getCode()) ? 1 : 2);
                        call.setReceiverName(rtcRoomCallDTO.getUsername());
                        call.setReceiverClientType(rtcRoomCallDTO.getClientType());
                    }

                    call.setCallStatus(RtcCallStatusEnum.ANSWERED.getCode());
                    call.setCallHumanAnswerTime(LocalDateTimeUtils.toLocalDateTime(rtcRoomCallDTO.getTimestamp()));
                    String stateChanges = call.getStateChanges();
                    List<CallStateChange> callStateChanges = new ArrayList<>();
                    if (StringUtils.isNotBlank(stateChanges)) {
                        callStateChanges = JacksonUtils.readValues(stateChanges, CallStateChange.class);
                    }
                    CallStateChange callStateChange = CallStateChange.instance("call_accept", null, rtcRoomCallDTO.getRtcUserId());
                    callStateChanges.add(callStateChange);
                    call.setStateChanges(JacksonUtils.writeValueAsString(callStateChanges));

                    call.setUpdatedBy(rtcRoomCallDTO.getRtcUserId());
                    call.setUpdatedByName(rtcRoomCallDTO.getUsername());
                    return update(call.getId(), call)
                            .doOnSuccess(result -> {
                                log.info("accept over: {}", JacksonUtils.writeValueAsString(result));
                            }).thenReturn(Boolean.TRUE);
                });
    }


    /**
     * 结束当前通话记录，挂断
     * @param rtcRoomCallDTO    通话相关记录
     */
    public Mono<Boolean> calledNormalOver(RtcRoomCallDTO rtcRoomCallDTO) {
        return this.getOnlineRoomCallInfo(rtcRoomCallDTO.getRtcRoomId())
                .flatMap(call -> {
                    if (call.getCallEndTime() != null) {
                        return Mono.just(Boolean.TRUE);
                    }
                    call.setCallEndTime(LocalDateTime.now());
                    call.setCallDuration((int) Duration.between(call.getCallStartTime(), call.getCallEndTime()).toSeconds());
                    if (call.getHasAiParticipant() == 1) {
                        call.setCallAgentDuration(call.getHasTransfer() == 1 ?
                                (int) Duration.between(call.getCallAgentAnswerTime(), call.getTransferTime()).toSeconds() :
                                (int) Duration.between(call.getCallAgentAnswerTime(), call.getCallEndTime()).toSeconds());
                    }
                    if (call.getHasTransfer() == 1 && Objects.nonNull(call.getCallHumanAnswerTime())) {
                        call.setCallHumanDuration((int) Duration.between(call.getCallHumanAnswerTime(), call.getCallEndTime()).toSeconds());
                    }
                    call.setCallStatus(RtcCallStatusEnum.FINISHED.getCode());
                    call.setRowStatus(0);
                    String stateChanges = call.getStateChanges();
                    List<CallStateChange> callStateChanges = new ArrayList<>();
                    if (StringUtils.isNotBlank(stateChanges)) {
                        callStateChanges = JacksonUtils.readValues(stateChanges, CallStateChange.class);
                    }
                    CallStateChange callStateChange = CallStateChange.instance("call_ended", rtcRoomCallDTO.getReason(), rtcRoomCallDTO.getRtcUserId());
                    callStateChanges.add(callStateChange);
                    call.setStateChanges(JacksonUtils.writeValueAsString(callStateChanges));

                    call.setUpdatedBy(rtcRoomCallDTO.getRtcUserId());
                    call.setUpdatedByName(rtcRoomCallDTO.getUsername());
                    return update(call.getId(), call)
                            .doOnSuccess(result -> {
                                log.info("called over over: {}", JacksonUtils.writeValueAsString(result));
                            })
                            .then(closeAgent(call))
                            .then(closeReplayStream(call))
                            .then(closeSubtitle(call.getRtcRoomId(), call.getRtcRoomId()))
                            .thenReturn(Boolean.TRUE);
                });
    }

    /**
     * 取消
     * @param rtcRoomCallDTO    语音通话字段
     */
    public Mono<Boolean> cancel(RtcRoomCallDTO rtcRoomCallDTO) {
        return this.getOnlineRoomCallInfo(rtcRoomCallDTO.getRtcRoomId())
                .flatMap(call -> {
                    call.setCallStatus(RtcCallStatusEnum.CANCELLED.getCode());
                    call.setRowStatus(0);
                    String stateChanges = call.getStateChanges();
                    List<CallStateChange> callStateChanges = new ArrayList<>();
                    if (StringUtils.isNotBlank(stateChanges)) {
                        callStateChanges = JacksonUtils.readValues(stateChanges, CallStateChange.class);
                    }
                    CallStateChange callStateChange = CallStateChange.instance("call_cancel", rtcRoomCallDTO.getReason(), rtcRoomCallDTO.getRtcUserId());
                    callStateChanges.add(callStateChange);
                    call.setStateChanges(JacksonUtils.writeValueAsString(callStateChanges));

                    call.setCallEndTime(LocalDateTime.now());

                    call.setUpdatedBy(rtcRoomCallDTO.getRtcUserId());
                    call.setUpdatedByName(rtcRoomCallDTO.getUsername());
                    return update(call.getId(), call)
                            .doOnSuccess(result -> {
                                log.info("cancel over: {}", JacksonUtils.writeValueAsString(result));
                            })
                            .then(closeSubtitle(call.getRtcRoomId(), call.getRtcRoomId()))
                            .thenReturn(Boolean.TRUE);
                });
    }

    /**
     * 拒绝
     * @param rtcRoomCallDTO    通话相关字段
     */
    public Mono<Boolean> reject(RtcRoomCallDTO rtcRoomCallDTO) {
        return this.getOnlineRoomCallInfo(rtcRoomCallDTO.getRtcRoomId())
                .flatMap(call -> {
                    call.setCallStatus(rtcRoomCallDTO.getStatus().getCode());
                    call.setRowStatus(rtcRoomCallDTO.getStatus() == RtcCallStatusEnum.REJECTED ? 0 : 1);
                    String stateChanges = call.getStateChanges();
                    if (rtcRoomCallDTO.getStatus() == RtcCallStatusEnum.REJECTED) {
                        call.setCallEndTime(LocalDateTime.now());
                    }
                    List<CallStateChange> callStateChanges = new ArrayList<>();
                    if (StringUtils.isNotBlank(stateChanges)) {
                        callStateChanges = JacksonUtils.readValues(stateChanges, CallStateChange.class);
                    }
                    CallStateChange callStateChange = CallStateChange.instance("call_reject", null, rtcRoomCallDTO.getRtcUserId());
                    callStateChanges.add(callStateChange);
                    call.setStateChanges(JacksonUtils.writeValueAsString(callStateChanges));

                    call.setUpdatedBy(rtcRoomCallDTO.getRtcUserId());
                    call.setUpdatedByName(rtcRoomCallDTO.getUsername());
                    return update(call.getId(), call)
                            .doOnSuccess(result -> {
                                log.info("reject over: {}", JacksonUtils.writeValueAsString(result));
                            })
                            .then(closeSubtitle(call.getRtcRoomId(), call.getRtcRoomId()))
                            .thenReturn(Boolean.TRUE);
                });
    }

    public Mono<Void> openSubtitle(String roomId, String taskId) {
        return rtcHelper.startSubtitleAsync(roomId, taskId)
                .doOnSuccess(result -> log.debug("start subtitle success: roomId={}, taskId={}",  roomId, taskId))
                .doOnError(error -> log.error("start subtitle error: roomId={}, taskId={}, error={}", roomId, taskId, error.getMessage(), error))
                .then();
    }


    /**
     * 关闭字幕
     * @param roomId    房间id
     * @param taskId    任务id
     */
    private Mono<Void> closeSubtitle(String roomId, String taskId) {
        return rtcHelper.stopSubtitleAsync(roomId, taskId)
                .doOnSuccess(result -> log.debug("stop subtitle success: roomId={}, taskId={}",  roomId, taskId))
                .doOnError(error -> log.error("stop subtitle error: roomId={}, taskId={}, error={}", roomId, taskId, error.getMessage(), error))
                .then();
    }

    /**
     * 更新通话记录
     * @param id
     * @param rtcRoomCallEntity
     * @return
     */
    public Mono<RtcRoomCallEntity> update(Long id, RtcRoomCallEntity rtcRoomCallEntity) {
        return rtcRoomCallRepository.findById(id)
                .doOnNext(existingCall -> log.info("update call: {} rtcRoomCallEntity:{}", JacksonUtils.writeValueAsString(existingCall), JacksonUtils.writeValueAsString(rtcRoomCallEntity)))
                .flatMap(call -> {
                    call.setCallStatus(rtcRoomCallEntity.getCallStatus());

                    call.setInitiatorClientType(rtcRoomCallEntity.getInitiatorClientType());

                    call.setReceiverId(rtcRoomCallEntity.getReceiverId());
                    call.setReceiverType(rtcRoomCallEntity.getReceiverType());
                    call.setReceiverClientType(rtcRoomCallEntity.getReceiverClientType());

                    call.setCurrentParticipantId(rtcRoomCallEntity.getCurrentParticipantId());
                    call.setCurrentParticipantType(rtcRoomCallEntity.getCurrentParticipantType());
                    call.setCurrentParticipantClientType(rtcRoomCallEntity.getCurrentParticipantClientType());

                    call.setHasAiParticipant(rtcRoomCallEntity.getHasAiParticipant());
                    call.setAiAgentId(rtcRoomCallEntity.getAiAgentId());

                    call.setHasTransfer(rtcRoomCallEntity.getHasTransfer());
                    call.setTransferTime(rtcRoomCallEntity.getTransferTime());

                    call.setCallStartTime(rtcRoomCallEntity.getCallStartTime());
                    call.setCallAgentAnswerTime(rtcRoomCallEntity.getCallAgentAnswerTime());
                    call.setCallHumanAnswerTime(rtcRoomCallEntity.getCallHumanAnswerTime());
                    call.setCallEndTime(rtcRoomCallEntity.getCallEndTime());
                    call.setCallDuration(rtcRoomCallEntity.getCallDuration());
                    call.setCallAgentDuration(rtcRoomCallEntity.getCallAgentDuration());
                    call.setCallHumanDuration(rtcRoomCallEntity.getCallHumanDuration());

                    call.setStateChanges(rtcRoomCallEntity.getStateChanges());

                    call.setRowStatus(rtcRoomCallEntity.getRowStatus());
                    call.setUpdatedBy(rtcRoomCallEntity.getUpdatedBy());
                    call.setUpdatedByName(rtcRoomCallEntity.getUpdatedByName() == null? call.getUpdatedByName() : rtcRoomCallEntity.getUpdatedByName());

                    return Mono.zip(rtcRoomCallRepository.save(call),HeaderUtils.getHeaderInfo())
                            .flatMap(tuple -> {
                                RtcRoomCallEntity result = tuple.getT1();
                                Mono<Void> workflowExecution = Mono.empty(); // 默认为空Mono
                                String deviceId = RtcRoomParseUtil.getDeviceId(rtcRoomCallEntity.getRtcRoomId());
                                log.info("device_white_list ={},deviceId={}",rtcVoiceChatProperties.getDeviceWhiteList(),deviceId);
                                if (Objects.equals(result.getCallStatus(), RtcCallStatusEnum.FINISHED.getCode())) {
                                    Map<String, Object> initialInput = Maps.newHashMap();
                                    initialInput.put("USER_INPUT", "根据通话记录和历史工单总结，并生成对应的工单");
                                    initialInput.put("conversation_id", rtcRoomCallEntity.getConversationId());
                                    initialInput.put("rtc_room_id", rtcRoomCallEntity.getRtcRoomId());
                                    initialInput.put("hotel_code", RtcRoomParseUtil.getHotelCode(rtcRoomCallEntity.getRtcRoomId()));
                                    initialInput.put("device_id", deviceId);
                                    initialInput.put("position_code", rtcRoomCallEntity.getPositionCode());
                                    initialInput.put("client_type", rtcRoomCallEntity.getInitiatorClientType());
                                    initialInput.put("sys_user_name", rtcRoomCallEntity.getInitiatorName());
                                    initialInput.put("sys_user_id", DeviceInitRtcUtil.getDeviceIdByRtcUserId(rtcRoomCallEntity.getInitiatorId()));

                                    // 构建工作流上下文
                                    WorkflowContext workflowContext = WorkflowContext.builder()
                                            .workflowCode(rtcVoiceChatProperties.getWorkFlowCodeAfterHangUp(tuple.getT2().getLanguage()))
                                            .initialInput(initialInput)
                                            .isDebug(false)
                                            .sinks(Sinks.many().multicast().onBackpressureBuffer())
                                            .build();

                                    // 执行工作流，但不等待其结果，而是使用onErrorResume处理任何错误
                                    workflowExecution = workflowService.executeWorkflow(workflowContext)
                                            .then()
                                            .onErrorResume(e -> {
                                                log.error("执行挂断工作流失败", e);
                                                return Mono.empty();
                                            });


                                }

                                // 无论设备是否在白名单中，都继续执行sendEndCallInfo
                                return workflowExecution.then(sendEndCallInfo(result)).thenReturn(result);
                            });

                });
    }

    /**
     * 通话埋点
     */
    private Mono<Void> sendEndCallInfo(RtcRoomCallEntity rtcRoomCallEntity) {

        /*if (Objects.isNull(rtcRoomCallEntity) || Objects.isNull(rtcRoomCallEntity.getCallEndTime())) {
            return Mono.empty();
        }*/

        MessageBody messageBody = new MessageBody();
        messageBody.setAction("call_info");
        messageBody.setTimestamp(System.currentTimeMillis() + "");

        CallInfoMessage callInfoMessage = new CallInfoMessage();
        callInfoMessage.setRtcRoomId(rtcRoomCallEntity.getRtcRoomId())
                .setInitiatorType(rtcRoomCallEntity.getInitiatorType())
                .setInitiatorUserId(rtcRoomCallEntity.getInitiatorId())
                .setInitiatorClientType(rtcRoomCallEntity.getInitiatorClientType())
                .setReceiverType(rtcRoomCallEntity.getReceiverType())
                .setReceiverUserId(rtcRoomCallEntity.getReceiverId())
                .setReceiverClientType(rtcRoomCallEntity.getReceiverClientType())
                .setCurrentParticipantId(rtcRoomCallEntity.getCurrentParticipantId())
                .setCurrentParticipantType(rtcRoomCallEntity.getCurrentParticipantType())
                .setCurrentParticipantClientType(rtcRoomCallEntity.getCurrentParticipantClientType())
                .setCallStartTime(rtcRoomCallEntity.getCallStartTime())
                .setCallEndTime(rtcRoomCallEntity.getCallEndTime())
                .setPositionCode(rtcRoomCallEntity.getPositionCode())
                .setCallStatus(rtcRoomCallEntity.getCallStatus())
                .setHasAiParticipant(rtcRoomCallEntity.getHasAiParticipant() == 1)
                .setHasTransfer(rtcRoomCallEntity.getHasTransfer() == 1)
                .setCallHumanDuration(rtcRoomCallEntity.getCallHumanDuration())
                .setCallAgentDuration(rtcRoomCallEntity.getCallAgentDuration())
                .setCallDuration(rtcRoomCallEntity.getCallDuration());

        messageBody.setData(callInfoMessage);

        return reactiveMessageSender.sendMessage(CALL_EVENT_INFO_TOPIC, messageBody)
                .then();

    }

}
