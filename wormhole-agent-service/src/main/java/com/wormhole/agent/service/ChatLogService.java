package com.wormhole.agent.service;

import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import com.wormhole.agent.chat.memory.service.MemoryService;
import com.wormhole.agent.chat.model.ChatConstant;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.log.service.ChatAccessLogService;
import com.wormhole.agent.rtc.config.RtcVoiceChatProperties;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.service.WorkflowService;
import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.core.model.entity.HdsCallLogEntity;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class ChatLogService {
    @Resource
    private UserChatMessageService userChatMessageService;
    @Resource
    private ChatAccessLogService chatAccessLogService;
    @Resource
    protected MemoryService memoryService;
    @Resource
    private CallLogService callLogService;
    @Autowired
    private RtcVoiceChatProperties rtcVoiceChatProperties;
    @Autowired
    private WorkflowService workflowService;

    @Resource
    protected ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    public Mono<Void> asyncSaveChatLogs(ChatContext chatContext) {
        return Mono.defer(() -> {
                    // 1. 先执行 storeMemory
                    return memoryService.storeMemory(chatContext)
                            .flatMap(storedContext -> {
                                // 2. storeMemory 完成后，继续执行 MySQL 和 ES 保存
                                Mono<Long> mysqlSave = userChatMessageService
                                        .saveUserChatMessage(storedContext, ChatRole.ASSISTANT.getValue(), storedContext.getFirstContextValueForString(ChatConstant.CHAT_EXCEPTION_MSG))
                                        .doOnNext(savedId -> {
                                            // 将主键设置到 chatContext
                                            chatContext.setMessageId(savedId == null ? "" : savedId.toString());
                                        })
                                        .onErrorResume(e -> {
                                            log.error("[MySQL]保存异常", e);
                                            return Mono.empty(); // 返回空的Mono，继续执行
                                        });

                                Mono<Void> esSave = chatAccessLogService
                                        .saveChatAccessLog(storedContext)
                                        .onErrorResume(e -> {
                                            log.error("[ES]保存异常", e);
                                            return Mono.empty(); // 返回空的Mono，继续执行
                                        });

                                // 3. 并行执行 MySQL 和 ES 保存
                                return Mono.when(mysqlSave, esSave).thenReturn(chatContext);
                            })
                            .flatMap(v -> {
                                // 4. 最后执行 callLogService
                                if (StringUtils.isBlank(chatContext.getRtcRoomId()) &&
                                        !Objects.equals(SourcePlatform.HOTEL.getCode(), chatContext.getSource())) {
                                    log.info("不执行 callLogService, rtcRoomId: {}, source: {}", chatContext.getRtcRoomId(), chatContext.getSource());
                                    return Mono.empty(); // 不执行 saveCallLog
                                }
                                String textToTicketsKey = String.format("textToTicketsWorkflow:%s:%s",chatContext.getConversationId(),chatContext.getDeviceId());
                                return Mono.zip(callLogService.saveCallLog(chatContext, ChatRole.ASSISTANT.getValue()),
                                                reactiveStringRedisTemplate.opsForValue().setIfAbsent(textToTicketsKey,chatContext.getTraceId(),Duration.ofSeconds(rtcVoiceChatProperties.getTxtInterval()))
                                                        .defaultIfEmpty(Boolean.FALSE))
                                        .flatMap(tuple -> {
                                            HdsCallLogEntity entity = tuple.getT1();
                                            Boolean lock = tuple.getT2();
                                            log.info("get textToTicketsKey: {} lock {} callLog.id {} callLog.content {}",  textToTicketsKey,lock,entity.getId(),entity.getContent());
                                            if (StringUtils.equalsAny( entity.getBotCode(),rtcVoiceChatProperties.getBotCode(),rtcVoiceChatProperties.getBotCode(),rtcVoiceChatProperties.getI18nBot())
                                                    && StringUtils.isBlank(entity.getRtcRoomId()) && ObjectUtil.equal(lock,Boolean.TRUE)) {
                                                return  HeaderUtils.getHeaderInfo().map(headerInfo -> getWorkflowContext(chatContext,entity, headerInfo.getLanguage()))
                                                        .flatMap(res -> workflowService.executeWorkflow(res))
                                                        .doOnNext(workflowContext1 ->  log.info("执行工作流, workflowContext: {}", JacksonUtils.writeValueAsString(workflowContext1)))
                                                        .onErrorResume(e -> {
                                                            log.error("执行挂断工作流失败", e);
                                                            return Mono.empty();
                                                        });
                                            }
                                            return Mono.empty();

                                        })
                                        .onErrorResume(e -> {
                                            log.error("[CallLog]保存异常", e);
                                            return Mono.empty();
                                        });
                            })
                            .then(); // 4. 确保整个流程完成后返回 Mono<Void>
                })
                .subscribeOn(Schedulers.boundedElastic()); // 5. 指定执行线程
    }

    private WorkflowContext getWorkflowContext(ChatContext chatContext, HdsCallLogEntity entity,String language) {
        WorkflowContext workflowContext = WorkflowContext.builder()
                .workflowCode(rtcVoiceChatProperties.getWorkFlowCodeAfterHangUp(language))
                .initialInput(buildInitialInput(entity, chatContext))
                .isDebug(false)
                .sinks(Sinks.many().multicast().onBackpressureBuffer())
                .build();
        return workflowContext;
    }

    @NotNull
    private static Map<String, Object> buildInitialInput(HdsCallLogEntity entity,ChatContext chatContext) {
        Map<String, Object> initialInput = Maps.newHashMap();
        initialInput.put("USER_INPUT", "根据通话记录和历史工单总结，并生成对应的工单");
        initialInput.put("conversation_id", entity.getConversationId());
        initialInput.put("rtc_room_id", entity.getRtcRoomId());
        initialInput.put("hotel_code", entity.getHotelCode());
        initialInput.put("device_id", entity.getDeviceId());
        initialInput.put("position_code", entity.getPositionCode());
        initialInput.put("client_type",chatContext.getClientType());
        initialInput.put("sys_user_name", chatContext.getUsername()!= null? chatContext.getUsername() : "delonix");
        initialInput.put("sys_user_id", chatContext.getUserId()!= null? chatContext.getUserId() : "99999");
        return initialInput;
    }


}
