package com.wormhole.agent.service;

import com.google.common.collect.Lists;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.nacos.config.ConfigItem;
import com.wormhole.agent.nacos.listener.BotInfoListener;
import com.wormhole.agent.nacos.listener.WorkflowListener;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.bot.core.Bot;
import com.wormhole.agent.workflow.bot.core.BotEnabled;
import com.wormhole.common.constant.Constants;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@Slf4j
@Service
public class BotService implements CommandLineRunner {

    @Autowired
    private List<Bot> botList;
    @Lazy
    @Resource
    private WorkflowListener workflowListener;
    @Lazy
    @Resource
    private BotInfoListener botInfoListener;
    public Mono<BotInfo> getBotInfo(String botCode, boolean debug) {
        return Mono.justOrEmpty(getBotInfoByCode(botCode, debug));
    }

    public BotInfo getBotInfoByCode(String botCode, boolean debug) {
        if (StringUtils.isBlank(botCode)) {
            return null;
        }
        String newCode = debug ? botCode + Constants.DEBUG_SUFFIX : botCode;
        return botInfoListener.getContent(newCode);
    }


    public List<Workflow> getWorkflow(BotInfo botInfo) {
        if (Objects.isNull(botInfo)) {
            return Lists.newArrayList();
        }
        return workflowListener.getWorkflowByCodeList(botInfo.getWorkflowCodeList());
    }

    @Override
    public void run(String... args) {
        botList.forEach(bot -> {
            BotEnabled annotation = bot.getClass().getAnnotation(BotEnabled.class);
            if (annotation != null && annotation.value()) {
                BotInfo botInfoDTO = bot.createBot();
                Workflow workflow = bot.createWorkflow();
//                log.info("load bot: {}", botInfoDTO.getBotCode());
                botInfoListener.addLocalConfig(ConfigItem.<BotInfo>builder().content(botInfoDTO).build());
                if (Objects.nonNull(workflow)) {
                    workflowListener.addLocalConfig(ConfigItem.<Workflow>builder().content(workflow).build());
                }
            }
        });
    }

    public Mono<List<BotInfo>> getBotInfoList() {
        return Mono.just(botInfoListener.getContents());
    }

    public BotInfo getDefaultBotInfo(String botCode) {
        return botInfoListener.getContents()
                .stream()
                .filter(botInfoDTO -> Objects.equals(botCode, botInfoDTO.getBotCode()))
                .findFirst()
                .orElse(new BotInfo());
    }
}
