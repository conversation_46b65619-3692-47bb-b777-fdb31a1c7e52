package com.wormhole.agent.service;

import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.resp.DeviceRtcInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.Objects;

/**
 * @author: joker.liu
 * @date: 2025/3/10
 * @Description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConnectionService {

    private final ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    private final HotelDsApiClient hotelDsApiClient;

    public Mono<DeviceInfoResp> deviceInitInfo(String deviceId, Integer clientType) {

        return hotelDsApiClient.getRtcDeviceInfo(deviceId, Objects.nonNull(clientType) ? clientType.toString() : null)
                .flatMap(deviceInfoEntity -> {
                    DeviceInfoResp deviceInfoResp = buildDeviceInitInfoResp(deviceInfoEntity);
                    return Mono.when(
                            reactiveStringRedisTemplate.opsForValue()
                                    .set(String.format(RedisKeyConstant.DEVICE_INFO_DEVICE_KEY, deviceId), JacksonUtils.writeValueAsString(deviceInfoResp), Duration.ofMinutes(5)),
                                    reactiveStringRedisTemplate.opsForValue()
                                            .set(String.format(RedisKeyConstant.DEVICE_INFO_USER_KEY, deviceInfoEntity.getRtcUserId()), JacksonUtils.writeValueAsString(deviceInfoResp), Duration.ofMinutes(10))
                    ).thenReturn(deviceInfoResp);
                })
                .onErrorResume(throwable -> {
                    log.error("deviceInitInfo error", throwable);
                    return Mono.empty();
                });
                // .switchIfEmpty(Mono.error(new BusinessException("DEVICE-NOT-REGISTER", "device not register or active")));
    }

    private DeviceInfoResp buildDeviceInitInfoResp(DeviceRtcInfoResp deviceRtcInfoResp) {

        DeviceInfoResp deviceInfoResp = new DeviceInfoResp();

        deviceInfoResp
                .setHotelCode(deviceRtcInfoResp.getHotelCode())
                .setRtcUserId(deviceRtcInfoResp.getRtcUserId())
                .setDeviceId(deviceRtcInfoResp.getDeviceId())
                .setPositionCode(deviceRtcInfoResp.getPositionCode())
                .setPositionFullName(deviceRtcInfoResp.getPositionFullName())
                .setDeviceType(deviceRtcInfoResp.getDeviceType())
                .setAccount(deviceRtcInfoResp.getAccount())
                .setUsername(deviceRtcInfoResp.getUsername());
        if (deviceRtcInfoResp.getDeviceType().equals(DeviceTypeEnum.ROOM.getCode())) {
            deviceInfoResp.setAccount(deviceRtcInfoResp.getPositionCode());
            deviceInfoResp.setUsername(deviceRtcInfoResp.getPositionFullName());
        }
        return deviceInfoResp;

    }

    public Mono<DeviceInfoResp> getDeviceInfo(String deviceId, Integer clientType) {
        return reactiveStringRedisTemplate.opsForValue().get(String.format(RedisKeyConstant.DEVICE_INFO_DEVICE_KEY, deviceId))
                .flatMap(value ->
                        Mono.just(JacksonUtils.readValue(value, DeviceInfoResp.class))
                )
                .switchIfEmpty(Mono.defer(() -> deviceInitInfo(deviceId, clientType)))
                .doOnNext(deviceInfoResp -> log.info("设备信息: {}", deviceInfoResp));
    }

    public Mono<DeviceInfoResp> getDeviceInfoByUserId(String userId) {
        return reactiveStringRedisTemplate.opsForValue().get(String.format(RedisKeyConstant.DEVICE_INFO_USER_KEY, userId))
                .flatMap(value ->
                        Mono.just(JacksonUtils.readValue(value, DeviceInfoResp.class))
                );
    }

}
