package com.wormhole.agent.service;

import cn.hutool.core.util.StrUtil;
import com.wormhole.agent.dto.RoomChatSaveDTO;
import com.wormhole.agent.entity.RoomChatEntity;
import com.wormhole.agent.repository.RoomChatRepository;
import com.wormhole.channel.consts.enums.VoiceChatUpdateCommandEnums;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.channel.consts.request.RtcApiRequest;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@RequiredArgsConstructor
public class RoomChatService {

    private final RoomChatRepository roomChatRepository;
    @Resource
    protected RtcHelper rtcHelper;


    public Mono<RoomChatEntity> create(RoomChatSaveDTO roomChatSaveDTO ) {
        RoomChatEntity roomChatEntity = new RoomChatEntity();
        roomChatEntity.setRoomId(roomChatSaveDTO.getRoomId());
        roomChatEntity.setTaskId(roomChatSaveDTO.getTaskId());
        roomChatEntity.setChatUserId(roomChatSaveDTO.getChatUserId());
        roomChatEntity.setConversationId(roomChatSaveDTO.getConversationId());
        return roomChatRepository.save(roomChatEntity);
    }

    public Mono<RoomChatEntity> update(Long id, RoomChatEntity roomChatEntity) {
        return roomChatRepository.findById(id)
                .flatMap(existingChat -> {
                    existingChat.setRoomId(roomChatEntity.getRoomId());
                    existingChat.setTaskId(roomChatEntity.getTaskId());
                    existingChat.setRowStatus(roomChatEntity.getRowStatus());
                    existingChat.setUpdatedBy(roomChatEntity.getUpdatedBy());
                    existingChat.setUpdatedByName(roomChatEntity.getUpdatedByName());
                    return roomChatRepository.save(existingChat);
                });
    }



    public Mono<Void> delete(Long id) {
        return roomChatRepository.deleteById(id);
    }

    public Mono<RoomChatEntity> findById(Long id) {
        return roomChatRepository.findById(id);
    }
    public Mono<RoomChatEntity> findByRoomId(String roomId) {
        return roomChatRepository.findByRoomIdAndRowStatus(roomId,1);
    }

    public Flux<RoomChatEntity> findAll() {
        return roomChatRepository.findAll();
    }

    /**
     * 清理语音聊天相关资源
     * @param roomId 房间ID
     * @return 清理操作是否成功
     */
    public Mono<Boolean> cleanupVoiceChat(String roomId) {
        if (StrUtil.isBlank(roomId)) {
            log.warn("清理语音聊天失败: roomId为空");
            return Mono.just(false);
        }

        log.info("开始清理房间语音聊天资源: roomId={}", roomId);

        return this.findByRoomId(roomId)
                .flatMap(roomChat -> processRoomCleanup(roomId, roomChat))
                .switchIfEmpty(Mono.defer(() -> {
                    log.info("未找到房间聊天记录，无需清理: roomId={}", roomId);
                    return Mono.just(true);
                }))
                .doOnSuccess(result -> log.info("清理房间语音聊天资源完成: roomId={}, result={}", roomId, result))
                .onErrorResume(e -> {
                    log.error("清理房间语音聊天资源异常: roomId={}", roomId, e);
                    return Mono.just(false);
                });
    }

    /**
     * 处理房间资源清理
     * @param roomId 房间ID
     * @param roomChat 房间聊天实体
     * @return 处理结果
     */
    private Mono<Boolean> processRoomCleanup(String roomId, RoomChatEntity roomChat) {
        log.info("处理房间资源清理: roomId={}, taskId={}", roomId, roomChat.getTaskId());

        // 设置房间状态为无效
        roomChat.setRowStatus(0);

        // 构建语音聊天中断和停止请求
        RtcApiRequest interruptRequest = RtcApiRequest.createUpdateVoiceChatRequest(
                roomId,
                roomChat.getTaskId(),
                VoiceChatUpdateCommandEnums.INTERRUPT.getCommand()
        );

        RtcApiRequest stopRequest = RtcApiRequest.createStopVoiceChatRequest(
                roomId,
                roomChat.getTaskId()
        );

        // 按顺序执行: 中断 -> 更新房间状态 -> 停止
        return updateRoomStatus(roomChat)
                .then(interruptVoiceChat(interruptRequest))
                .then(stopVoiceChat(stopRequest))
                .thenReturn(true);
    }

    public Mono<Boolean> updateRtcVoiceChatSendPrompt(String roomId, RoomChatEntity roomChat, String message) {
        // 文本推送 tts播放
        RtcApiRequest rtcApiRequest = RtcApiRequest.createUpdateVoiceChatRequest(
                roomId,
                roomChat.getTaskId(),
                VoiceChatUpdateCommandEnums.EXTERNAL_TEXT_TO_SPEECH.getCommand(),
                message
        );
        return rtcHelper.updateVoiceChatAsync(rtcApiRequest)
                .doOnSuccess(result -> log.info("send tts prompt success: roomId={}, taskId={}",
                        rtcApiRequest.getRoomId(), rtcApiRequest.getTaskId()))
                .doOnError(e -> log.error("send tts prompt error: roomId={}, taskId={}",
                        rtcApiRequest.getRoomId(), rtcApiRequest.getTaskId(), e))
                .thenReturn(Boolean.TRUE);
    }

    /**
     * 更新房间状态
     * @param roomChat 房间聊天实体
     * @return 更新结果
     */
    private Mono<Void> updateRoomStatus(RoomChatEntity roomChat) {
        return this.update(roomChat.getId(), roomChat)
                .doOnSuccess(result -> log.info("更新房间状态成功: id={}", roomChat.getId()))
                .doOnError(e -> log.error("更新房间状态失败: id={}", roomChat.getId(), e))
                .then();
    }

    /**
     * 中断语音聊天
     * @param interruptRequest 中断请求
     * @return 中断结果
     */
    private Mono<Void> interruptVoiceChat(RtcApiRequest interruptRequest) {
        return rtcHelper.updateVoiceChatAsync(interruptRequest)
                .doOnSuccess(result -> log.info("中断语音聊天成功: roomId={}, taskId={}",
                        interruptRequest.getRoomId(), interruptRequest.getTaskId()))
                .doOnError(e -> log.error("中断语音聊天失败: roomId={}, taskId={}",
                        interruptRequest.getRoomId(), interruptRequest.getTaskId(), e))
                .then();
    }

    /**
     * 停止语音聊天
     * @param stopRequest 停止请求
     * @return 停止结果
     */
    private Mono<Void> stopVoiceChat(RtcApiRequest stopRequest) {
        return rtcHelper.stopVoiceChatAsync(stopRequest)
                .doOnSuccess(result -> log.info("停止语音聊天成功: roomId={}, taskId={}",
                        stopRequest.getRoomId(), stopRequest.getTaskId()))
                .doOnError(e -> log.error("停止语音聊天失败: roomId={}, taskId={}",
                        stopRequest.getRoomId(), stopRequest.getTaskId(), e))
                .then();
    }
}
