package com.wormhole.agent.service;

import com.wormhole.agent.chat.context.ChatContextPreProcessor;
import com.wormhole.agent.chat.model.ChatConstant;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.chat.processor.ChatProcessor;
import com.wormhole.agent.client.chat.params.ChatParams;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@Slf4j
@Service
public class ChatService {

    @Autowired
    private List<ChatContextPreProcessor<ChatParams>> chatContextPreProcessorList;
    @Autowired
    private List<ChatProcessor> chatProcessorList;
    @Resource
    private UserChatMessageService userChatMessageService;
    @Resource
    private ChatLogService chatLogService;
    @Resource
    private CallLogService callLogService;

    public Flux<ChatCompletions> chatCompletions(ChatContext chatContext) {
        return chatContextPreProcess(chatContext)
                .flatMap(ctx ->
                    userChatMessageService.retryMessageUpdate(getValueFromPayLoad(ctx.getChatParams().getPayload(),ChatConstant.PAYLOAD_KEY_RETRY_MESSAGE_ID),ctx.getChatParams().getConversationId())
                            .subscribeOn(Schedulers.boundedElastic())
                            .thenReturn(ctx)
                )
                .flatMap(ctx -> {
                    if (StringUtils.isNotBlank(getValueFromPayLoad(ctx.getChatParams().getPayload(),ChatConstant.PAYLOAD_KEY_RETRY_MESSAGE_ID))) {
                        // 如果是重试消息，则不保存用户输入
                        return Mono.empty().thenReturn(ctx);
                    }
                    return userChatMessageService.saveUserChatMessage(ctx, ChatRole.USER.getValue(), null)
                            .subscribeOn(Schedulers.boundedElastic())
                            .doOnNext(id -> ctx.setMessageId(id.toString()))
                            .thenReturn(ctx);
                })
                .flatMap(ctx -> {
                    if (StringUtils.isNotBlank(getValueFromPayLoad(ctx.getChatParams().getPayload(),ChatConstant.PAYLOAD_KEY_RETRY_MESSAGE_ID))) {
                        // 如果是重试消息，则不保存用户输入
                        return Mono.empty().thenReturn(ctx);
                    }
                    if (StringUtils.isBlank(chatContext.getRtcRoomId()) &&
                            !Objects.equals(SourcePlatform.HOTEL.getCode(), chatContext.getSource())) {
                        return Mono.empty().thenReturn(ctx); // 不执行 saveCallLog
                    }
                    return callLogService.saveCallLog(chatContext, ChatRole.USER.getValue())
                            .subscribeOn(Schedulers.boundedElastic())
                            .thenReturn(ctx);
                })
                .flatMapMany(this::processChat)
                .onErrorResume(e -> handleChatError(chatContext, e)); // 类型修正关键点
    }

    public String getValueFromPayLoad(Map<String, Object> payload, String key) {
        Object content = MapUtils.getObject(payload, key);
        String value = StringUtils.EMPTY;
        if (Objects.nonNull(content)) {
            if (content instanceof String){
                value = (String) content;
            }
            if (content instanceof Number){
                value = content.toString();
            }
        }
        return value;
    }

    private Mono<ChatContext> chatContextPreProcess(ChatContext chatContext) {
        return chatContextPreProcessorList.stream()
                .filter(chatContextPreProcessor -> chatContextPreProcessor.support(chatContext))
                .findFirst()
                .orElseThrow(() -> new BusinessException(ResultCode.INVALID_PARAMETER, "Unsupported chatType:" + chatContext.getChatType()))
                .preProcess(chatContext)
                .doOnNext(ctx -> {
                    // 输出处理后的 ctx 的重要字段以进行调试
                    log.info("After preProcess: clientReqId={}, conversationId={}, question={}, userId={}",
                            ctx.getClientReqId(), ctx.getConversationId(), ctx.getQuestion(), ctx.getUserId());
                });
    }

    private Publisher<ChatCompletions> processChat(ChatContext chatContext) {
        ChatProcessor chatProcessor = chatProcessorList.stream()
                .filter(processor -> processor.support(chatContext))
                .findFirst()
                .orElseThrow(() -> {
                    String errMsg = "Unsupported chatType process";
                    BusinessException exception = new BusinessException(ResultCode.INVALID_PARAMETER, errMsg);
                    chatContext.getExecutionStatManager().fail(ExecutionStatType.chat_access, exception);
                    userChatMessageService.saveUserChatMessage(chatContext,ChatRole.SYSTEM.getValue(), errMsg);
                    return exception;
                });
        return Flux.from(chatProcessor.processChat(chatContext))
                .onErrorResume(e -> Flux.just(ChatCompletionUtils.buildChatError(e)));
    }



    // 修正后的错误处理方法（返回Flux类型）
    private Flux<ChatCompletions> handleChatError(ChatContext context, Throwable e) {
        return Mono.fromRunnable(() -> {
                    // 错误日志记录
                    log.error("Chat error", e);
                    // 错误上下文记录
                    context.getExecutionStatManager().fail(ExecutionStatType.chat_access, e);
                    context.addContextValue(ChatConstant.CHAT_EXCEPTION_TRACE, ExceptionUtils.getRootCause(e));
                    context.addContextValue(ChatConstant.CHAT_EXCEPTION_MSG, e.getMessage());

                    // 触发错误日志保存
                    chatLogService.asyncSaveChatLogs(context)
                            .onErrorResume(err -> {
                                log.error("Error log save failed", err);
                                return Mono.empty();
                            })
                            .subscribe();
                })
                .thenMany(Flux.just(ChatCompletionUtils.buildChatError(e))); // 关键类型转换
    }



}