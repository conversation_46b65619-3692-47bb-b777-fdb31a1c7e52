package com.wormhole.agent.service.strategy;


import com.wormhole.agent.client.chat.params.UserConversationVO;
import com.wormhole.agent.dto.SaveConversationDTO;
import com.wormhole.agent.service.UserConversationService;
import com.wormhole.common.enums.SourcePlatform;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * 平台来源实现具体策略
 * <AUTHOR>
 * @date 2025/2/28 11:34
 **/
@Component
public class ChatSourceStrategy implements SourceStrategy {
    @Resource
    private UserConversationService userConversationService;
    @Override
    public String getSource() {
        return SourcePlatform.CHAT.getCode();
    }

    @Override
    public Mono<UserConversationVO> createConversation(SaveConversationDTO dto) {
        return userConversationService.queryExistingConversation(dto)
                .flatMap(Mono::just) // 确保查询结果被消费
                .switchIfEmpty(Mono.defer(() -> userConversationService.saveEntity(dto)))
                .map(userConversationService::toVO);
    }


}