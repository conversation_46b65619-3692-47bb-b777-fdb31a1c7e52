package com.wormhole.agent.service;

import com.google.common.base.Preconditions;
import com.wormhole.agent.chat.memory.params.ReadMemoryParams;
import com.wormhole.agent.chat.memory.service.RecentMemoryService;
import com.wormhole.agent.client.chat.params.query.BotHistoryQuery;
import com.wormhole.agent.client.chat.params.query.BotInfoQuery;
import com.wormhole.agent.client.chat.response.BotInfoResponse;
import com.wormhole.agent.client.chat.response.WorkflowInfoResponse;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.entity.BotEntity;
import com.wormhole.agent.entity.BotFieldEnum;
import com.wormhole.agent.enums.BotCategoryEnum;
import com.wormhole.agent.enums.PublishStatusEnum;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.nacos.listener.WorkflowListener;
import com.wormhole.agent.query.BotQueryCondition;
import com.wormhole.agent.util.SqlUtils;
import com.wormhole.agent.vo.BotCategoryVO;
import com.wormhole.agent.vo.BotSimpleInfoVO;
import com.wormhole.agent.vo.OnlineBotListVO;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.model.UserDetailInfo;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BotApiService {
    @Resource
    private BotService botService;
    @Resource
    private WorkflowListener workflowListener;
    @Resource
    private RecentMemoryService recentMemoryService;
    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Resource
    private UserConversationService userConversationService;
    @Resource
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;
    @Resource
    private SpaceService spaceService;
    private static final String USER_DETAIL_INFO_KEY = "wormhole:user:info:%s";

    public Mono<List<BotCategoryVO>> getBotCategoryList() {
        return Mono.just(Arrays.stream(BotCategoryEnum.values())
                .filter(categoryEnum -> !categoryEnum.getCategory().equals(BotCategoryEnum.DEFAULT.getCategory()))
                .map(botCategoryEnum -> {
                    BotCategoryVO botCategory = new BotCategoryVO();
                    botCategory.setCategory(botCategoryEnum.getCategory());
                    botCategory.setCategoryName(botCategoryEnum.getCategoryName());
                    return botCategory;
                }).collect(Collectors.toList()));
    }


    public Mono<List<OnlineBotListVO>> getOnlineBotList(BotQueryCondition queryCondition) {
        // 验证请求条件
        Preconditions.checkNotNull(queryCondition, "请求对象不能为空");
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    String redisKey = String.format(USER_DETAIL_INFO_KEY, headerInfo.getUserId());
                    return reactiveStringRedisTemplate.opsForValue().get(redisKey)
                            .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "权限不足，请联系管理员")))
                            .flatMap(str -> spaceService.getInSpaceUserList(headerInfo.getUserId())
                                    .flatMap(spaceCodes -> {
                                        // UserDetailInfo userDetailInfo = JacksonUtils.readValue(str, UserDetailInfo.class);
                                        // 构建查询条件
                                        Criteria criteria = buildCriteria(queryCondition)
                                                .and(BotFieldEnum.status.name()).is(PublishStatusEnum.online.getStatus())
                                                .and(BotFieldEnum.visibility.name()).is(true)
                                                .and(BotFieldEnum.space_code.name()).in(spaceCodes);
                                        Sort sort = Sort.by(Sort.Direction.DESC, BotFieldEnum.updated_at.name());
                                        Query query = Query.query(criteria).sort(sort);
                                        return r2dbcEntityTemplate.select(query, BotEntity.class)
                                                .collectList()
                                                .flatMap(this::processEntities);
                                    }));
                });

    }

    private Mono<List<OnlineBotListVO>> processEntities(List<BotEntity> entities) {
        // 用流式编程处理实体并生成分类映射
        Map<String, List<BotSimpleInfoVO>> categoryMap = entities.stream()
                .filter(entity -> !entity.getCategory().equals(BotCategoryEnum.DEFAULT.getCategory()))
                .map(this::createBotSimpleInfoVO)
                .collect(Collectors.groupingBy(BotSimpleInfoVO::getCategory));

        // 处理热门分类
        List<BotSimpleInfoVO> hotBots = categoryMap.values().stream()
                .flatMap(List::stream)
                .filter(bot -> BotCategoryEnum.HOT.getCategory().equals(bot.getCategory()))
                .collect(Collectors.toList());

        if (!hotBots.isEmpty()) {
            categoryMap.put(BotCategoryEnum.HOT.getCategory(), hotBots);
        }

        return Mono.just(transformToOnlineBotListVO(categoryMap));
    }

    private BotSimpleInfoVO createBotSimpleInfoVO(BotEntity entity) {
        BotSimpleInfoVO vo = BotSimpleInfoVO.toSimpleVO(entity)
                .setCategory(entity.getCategory())
                .setCategoryName(BotCategoryEnum.fromCategoryName(entity.getCategory()).getCategoryName());

        // 如果是热门 Bot，更新分类
        if (Boolean.TRUE.equals(entity.getIsHot())) {
            vo.setCategory(BotCategoryEnum.HOT.getCategory())
                    .setCategoryName(BotCategoryEnum.HOT.getCategoryName());
        }

        return vo;
    }

    private List<OnlineBotListVO> transformToOnlineBotListVO(Map<String, List<BotSimpleInfoVO>> categoryMap) {
        return categoryMap.entrySet().stream()
                .map(entry -> new OnlineBotListVO()
                        .setCategory(entry.getKey())
                        .setCategoryName(entry.getValue().get(0).getCategoryName())
                        .setBots(entry.getValue()))
                .sorted(Comparator.comparing((OnlineBotListVO o) ->
                                o.getCategory().equals(BotCategoryEnum.HOT.getCategory()) ? 0 : 1)
                        .thenComparing(OnlineBotListVO::getCategoryName))
                .collect(Collectors.toList());
    }


    public Mono<BotInfoResponse> getBotInfo(BotInfoQuery botInfoQuery) {
        return botService.getBotInfo(botInfoQuery.getBotCode(), false)
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "bot 信息不存在")))
                .map(botInfo -> {
                    BotInfoResponse botInfoResponse = convertToBotInfoForPy(botInfo, botInfoQuery);
                    if (CollectionUtils.isEmpty(botInfo.getWorkflowCodeList())) {
                        return botInfoResponse;
                    }
                    List<Workflow> workflowList = workflowListener.getWorkflowByCodeList(botInfo.getWorkflowCodeList());
                    List<WorkflowInfoResponse> workflowInfoList = workflowList.stream()
                            .map(this::convertToWorkflowInfoForPy)
                            .collect(Collectors.toList());
                    botInfoResponse.setWorkflowList(workflowInfoList);
                    return botInfoResponse;
                });
    }


    public Mono<List<OpenAiChatMessage>> getHistoryMessage(BotHistoryQuery botInfoQuery) {
        Preconditions.checkNotNull(botInfoQuery, "查询对象不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(botInfoQuery.getConversationId()), "botCode 不能为空");
        return userConversationService.findByConversationId(botInfoQuery.getConversationId())
                .flatMap(entity -> botService.getBotInfo(entity.getBotCode(), false)
                        .map(botInfo -> {
                            ModelInfo modelInfo = botInfo.getModelInfo();
                            return (modelInfo != null && modelInfo.getRecentRound() != null) ? modelInfo.getRecentRound() : 3;
                        })
                        .defaultIfEmpty(3)
                        .map(recentRound -> ReadMemoryParams.builder()
                                .conversationId(botInfoQuery.getConversationId())
                                .maxSize(recentRound)
                                .build()
                        )
                        .flatMap(recentMemoryService::getRecentMemory)
                        .onErrorResume(e -> {
                            log.error("Error occurred while fetching history message", e);
                            return Mono.just(Collections.emptyList());
                        }));
    }


    // 转换 BotInfo 和 BotParamForPy -> BotInfoForPy
    private BotInfoResponse convertToBotInfoForPy(BotInfo botInfoDTO, BotInfoQuery botInfoQuery) {
        BotInfoResponse botInfoResponse = new BotInfoResponse();
        // 从 BotParamForPy 填充用户相关信息
        botInfoResponse.setUserId(botInfoQuery.getUserId());
        botInfoResponse.setConversationId(botInfoQuery.getConversationId());
        botInfoResponse.setClientReqId(botInfoQuery.getClientReqId());

        // 从 BotInfo 填充机器人相关信息
        botInfoResponse.setBotCode(botInfoDTO.getBotCode());
        botInfoResponse.setName(botInfoDTO.getName());
        botInfoResponse.setModelInfo(botInfoDTO.getModelInfo());
        botInfoResponse.setPromptInfo(botInfoDTO.getPromptInfo());
        return botInfoResponse;
    }

    private WorkflowInfoResponse convertToWorkflowInfoForPy(Workflow workflow) {
        WorkflowInfoResponse workflowInfoResponse = new WorkflowInfoResponse();
        WorkflowDefinition definition = workflow.getWorkflowDefinition();

        workflowInfoResponse.setWorkflowCode(definition.getWorkflowCode());
        workflowInfoResponse.setWorkflowName(definition.getWorkflowName());
        workflowInfoResponse.setTool(definition.getTool());
        return workflowInfoResponse;
    }

    public static Criteria buildCriteria(BotQueryCondition queryCondition) {
        Criteria criteria = Criteria.empty();

        if (org.apache.commons.lang3.StringUtils.isNotBlank(queryCondition.getBotName())) {
            criteria = criteria.and(BotFieldEnum.name.name()).like(SqlUtils.like(queryCondition.getBotName()));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(queryCondition.getBotCode())) {
            criteria = criteria.and(BotFieldEnum.code.name()).is(queryCondition.getBotCode());
        }
        if (queryCondition.getIsPublished() != null) {
            criteria = criteria.and(BotFieldEnum.is_published.name()).is(queryCondition.getIsPublished());
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(queryCondition.getSpaceCode())) {
            criteria = criteria.and(BotFieldEnum.space_code.name()).is(queryCondition.getSpaceCode());
        }
        criteria = criteria.and(BotFieldEnum.status.name()).not(PublishStatusEnum.deleted.getStatus());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(queryCondition.getCategory())) {
            if (BotCategoryEnum.HOT.getCategory().equals(queryCondition.getCategory())) {
                criteria = criteria.and(BotFieldEnum.is_hot.name()).is(1);
            } else {
                criteria = criteria.and(BotFieldEnum.category.name()).is(queryCondition.getCategory());
            }
        }
        return criteria;
    }

}
