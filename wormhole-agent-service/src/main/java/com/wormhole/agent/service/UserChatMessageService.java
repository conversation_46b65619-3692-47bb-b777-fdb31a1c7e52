package com.wormhole.agent.service;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Preconditions;
import com.wormhole.agent.chat.memory.service.RecentMemoryService;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.LlmChatParams;
import com.wormhole.agent.client.chat.params.UserConversationVO;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.core.model.entity.UserChatMessageEntity;
import com.wormhole.agent.core.model.entity.UserChatMessageFieldEnum;
import com.wormhole.agent.dto.SaveConversationDTO;
import com.wormhole.agent.dto.UserChatMessageDTO;
import com.wormhole.agent.query.*;
import com.wormhole.agent.repository.UserChatMessageRepository;
import com.wormhole.agent.util.DeviceInitRtcUtil;
import com.wormhole.agent.vo.UserChatMessageHistoryVO;
import com.wormhole.agent.vo.UserChatMessageVO;
import com.wormhole.common.constant.HeaderConstant;
import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Nullable;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户对话消息服务
 *
 * <AUTHOR>
 * @version 2024/10/21
 */
@Slf4j
@Service
public class UserChatMessageService {

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;
    @Resource
    private UserChatMessageRepository repository;
    @Resource
    private UserConversationService userConversationService;
    @Resource
    private RecentMemoryService recentMemoryService;
    public static final String[] COPY_IGNORE_FIELDS =
            {UserChatMessageFieldEnum.id.name(), UserChatMessageFieldEnum.created_at.name(), UserChatMessageFieldEnum.updated_at.name(), UserChatMessageFieldEnum.parent_message_id.name()};


    public Mono<Long> saveUserChatMessage(ChatContext chatContext, String messageType, String msg) {
        UserChatMessageDTO messageDTO = buildUserChatMessageDTO(chatContext, messageType, msg);
        return buildMessageEntity(messageDTO)
                .flatMap(entity -> {
                        log.info("saveUserChatMessage entity:{}", JacksonUtils.writeValueAsString(entity));
                        return repository.save(entity)
                        .flatMap(savedEntity -> updateUserConversation(chatContext, messageType)
                                .thenReturn(savedEntity));
                })
                .doOnSuccess(entity -> log.debug("Message saved successfully | type:{} | userId:{} | conversationId:{}", messageType, entity.getCreatedBy(),chatContext.getConversationId()))
                .onErrorResume(e -> {
                    log.error("Message save failed | type:{} | userId:{} | conversationId:{} | error:{}", messageType, chatContext.getUserId(),chatContext.getConversationId(), e.getMessage());
                    return Mono.empty(); // 捕获异常并返回一个空的 Mono，确保流程继续
                })
                .map(UserChatMessageEntity::getId);
    }



    private Mono<UserConversationVO> findUserConversation(ChatContext chatContext) {
        UserConversationQuery query = new UserConversationQuery();
        query.setBotCode(chatContext.getBotCode());
        query.setConversationId(chatContext.getConversationId());
        query.setUserId(chatContext.getUserId());
        query.setSource(chatContext.getSource());
        return userConversationService.listConversations(query)
                .filter(result -> result.getTotal() > 0)
                .map(result -> result.getDataList().get(0));
    }

    private Mono<Void> updateUserConversation(ChatContext chatContext, String messageType) {
        if (ChatRole.USER.getValue().equals(messageType)) {
            // 用户消息：查找会话，不存在则创建新会话
            return findUserConversation(chatContext)
                    .switchIfEmpty(createNewConversation(chatContext).then(Mono.empty()))
                    .then();
        } else {
            return findUserConversation(chatContext)
                    .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "Conversation not found")))
                    .flatMap(conversation -> {
                        UserConversationUpdateReq req = buildUserConversationUpdateReq(chatContext, conversation);
                        return userConversationService.updateConversation(req).then();
                    });
        }
    }

    private Mono<Void> createNewConversation(ChatContext chatContext) {
        SaveConversationDTO dto = SaveConversationDTO.builder()
                .userId(chatContext.getUserId())
                .username(chatContext.getUsername())
                .source(chatContext.getSource())
                .botCode(chatContext.getBotCode())
                .conversationId(chatContext.getConversationId())
                .isDebug(chatContext.isDebug())
                .build();

        return userConversationService.saveEntity(dto).then();
    }


    private UserConversationUpdateReq buildUserConversationUpdateReq(ChatContext chatContext, UserConversationVO userConversationVO) {
        return new UserConversationUpdateReq()
                .setUserId(chatContext.getUserId())
                .setUsername(chatContext.getUsername())
                .setConversationId(userConversationVO.getConversationId())
                .setBotCode(chatContext.getBotCode())
                .setSource(userConversationVO.getSource())
                .setContent(chatContext.getQuestion());
    }

    // 查询父消息的独立方法
    private Mono<UserChatMessageEntity> findParentMessage(UserChatMessageDTO dto) {
        Criteria criteria = Criteria.where(UserChatMessageFieldEnum.created_by.name()).is(dto.getUserId())
                .and(UserChatMessageFieldEnum.conversation_id.name()).is(dto.getConversationId());

        return r2dbcEntityTemplate.selectOne(
                Query.query(criteria)
                        .limit(1)
                        .sort(Sort.by(Sort.Direction.DESC, UserChatMessageFieldEnum.created_at.name())),
                UserChatMessageEntity.class
        );
    }

    // 合并实体构建逻辑
    private Mono<UserChatMessageEntity> buildMessageEntity(UserChatMessageDTO dto) {
        return findParentMessage(dto)
                .map(parent -> createMessageEntity(dto, parent))
                .defaultIfEmpty(createMessageEntity(dto, null));
    }

    // 增强版实体创建方法
    private UserChatMessageEntity createMessageEntity(UserChatMessageDTO dto, @Nullable UserChatMessageEntity parent) {
        UserChatMessageEntity entity = new UserChatMessageEntity();

        // 安全属性拷贝（使用明确的字段排除列表）
        BeanUtils.copyProperties(dto, entity, COPY_IGNORE_FIELDS);
        entity.setElapsedMs((int)dto.getElapsedMs());
        entity.setErrorMsg(dto.getErrorMsg());
        entity.setRtcRoomId(dto.getRtcRoomId());
        if (parent != null) {
            entity.setParentMessageId(parent.getId());
            if (dto.getConversationId() == null) {
                entity.setConversationId(parent.getConversationId());
            }
        }


        return entity;
    }


    // 新增DTO构建专用方法，解耦数据准备逻辑
    private UserChatMessageDTO buildUserChatMessageDTO(ChatContext chatContext, String messageType, String errMsg) {
        UserChatMessageDTO dto = new UserChatMessageDTO();
        String userId = Optional.ofNullable(chatContext.getUserId())
                .orElseGet(()-> chatContext.getHttpHeaders().getFirst(HeaderConstant.USER_ID));
        String userName = Optional.ofNullable(chatContext.getUsername())
                .orElseGet(()-> chatContext.getHttpHeaders().getFirst(HeaderConstant.USERNAME));
        // 基础字段
        dto.setUserId(userId);
        dto.setRtcRoomId(chatContext.getRtcRoomId()==null?"":chatContext.getRtcRoomId());
        dto.setConversationId(chatContext.getConversationId());
        dto.setTraceId(chatContext.getTraceId());
        dto.setClientReqId(chatContext.getClientReqId());
        dto.setMessageType(messageType);
        dto.setElapsedMs(chatContext.getExecutionStatManager().getGlobalElapsedTime());
        dto.setChatCompletionId(chatContext.getChatCompletionId());

        // 内容字段
        String content = ChatRole.USER.getValue().equals(messageType) ?
                chatContext.getQuestion() : chatContext.getAnswer();
        dto.setContent(content);

        // 模型信息处理
        resolveModelInfo(chatContext, dto);

        // 错误状态处理
        if (StringUtils.isNotBlank(errMsg)) {
            dto.setReqStatus(2);
            dto.setErrorMsg(errMsg);
        }
        //审计字段
        LocalDateTime now = LocalDateTime.now();
        String finalUserId = DeviceInitRtcUtil.getDeviceIdByRtcUserId(userId);

        dto.setCreatedAt(now);
        dto.setUpdatedAt(now);
        dto.setCreatedBy(finalUserId);
        dto.setUpdatedBy(finalUserId);
        dto.setCreatedByName(userName);  // 注意：可能需要使用实际用户名
        dto.setUpdatedByName(userName);
        return dto;
    }

    // 模型信息解析专用方法
    private void resolveModelInfo(ChatContext context, UserChatMessageDTO dto) {
        if (ChatType.LLM.equals(context.getChatType())) {
            LlmChatParams params = (LlmChatParams) context.getChatParams();
            dto.setModel(params.getModel());
            dto.setModelProvider(params.getModelProvider());
        } else {
            ModelInfo modelInfo = Optional.ofNullable(context.getBotInfo())
                    .map(BotInfo::getModelInfo)
                    .orElse(null);
            dto.setModel(modelInfo != null ? modelInfo.getModel() : null);
            dto.setModelProvider(modelInfo != null ? modelInfo.getModelProvider() : null);
        }
        dto.setBotCode(Optional.ofNullable(context.getBotCode()).orElse("llm"));
    }


    /**
     * 根据ID获取消息详情
     *
     * @param id 消息主键
     * @return 消息实体（不存在时返回404）
     */
    public Mono<UserChatMessageVO> getById(String id) {
        return repository.findById(NumberUtils.toLong(id))
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND)))
                .map(UserChatMessageVO::toVO);
    }

    /**
     * 删除指定消息
     *
     * @param delReq 用户删除请求
     * @return 空结果（表示删除成功）
     */
    public Mono<Boolean> deleteMessage(UserChatMessageDelReq delReq) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    recentMemoryService.deleteMemory(delReq.getConversationId());
                    return repository.deleteByCreatedByAndConversationId(headerInfo.getUserId(), delReq.getConversationId())
                            .then(Mono.just(true));
                })
                .then(Mono.just(true));
    }

    /**
     * 获取用户+bot的会话列表历史（按时间倒序）
     */
    public Mono<List<UserChatMessageVO>> getConversationList(ChatConversationQuery chatConversationQuery) {
        Preconditions.checkNotNull(chatConversationQuery, "查询对象不能为空");
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    Criteria criteria = Criteria.where(UserChatMessageFieldEnum.created_by.name()).is(headerInfo.getUserId());
                    if (StringUtils.isNotBlank(chatConversationQuery.getBotCode())) {
                        criteria.and(UserChatMessageFieldEnum.bot_code.name()).is(chatConversationQuery.getBotCode());
                    }
                    Sort sort = Sort.by(Sort.Direction.DESC, UserChatMessageFieldEnum.created_at.name());
                    Query query = Query.query(criteria).sort(sort);
                    Flux<UserChatMessageEntity> flux = r2dbcEntityTemplate.select(query, UserChatMessageEntity.class);
                    return flux.collectList().map(dataList -> {
                        // 按 conversationId 分组
                        Map<String, List<UserChatMessageEntity>> groupedMap = dataList.stream()
                                .collect(Collectors.groupingBy(UserChatMessageEntity::getConversationId));

                        // 分组后的数据转换
                        return groupedMap.values().stream()
                                .map(group -> {
                                    UserChatMessageVO vo = UserChatMessageVO.toSimpleVO(group.get(0)); // 取第一条作为代表
                                    vo.setUpdatedAt(group.stream() // 设置最新消息时间
                                            .map(UserChatMessageEntity::getCreatedAt)
                                            .max(LocalDateTime::compareTo)
                                            .orElse(null));
                                    return vo;
                                })
                                .sorted(Comparator.comparing(UserChatMessageVO::getUpdatedAt, Comparator.reverseOrder())) // 按时间倒序
                                .collect(Collectors.toList());
                    });
                });
    }


    /**
     * 分页查询消息记录
     */
    public Mono<PageResult<UserChatMessageHistoryVO>> queryChatMessageHistory(ChatMessageHistoryQuery queryCondition) {
        Preconditions.checkNotNull(queryCondition, "查询对象不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(queryCondition.getConversationId()), "conversationId 不能为空");
        return HeaderUtils.getHeaderInfo().flatMap(
                headerInfo -> {
                    if (StringUtils.isBlank(queryCondition.getBotCode())) {
                        queryCondition.setBotCode("llm");
                    }
                    if(!queryCondition.isSearchAll()) {
                        queryCondition.setUserId(headerInfo.getUserId());
                    }
                    Criteria criteria = buildQueryCriteria(queryCondition, headerInfo.getSource());
                    Integer pageNumber = queryCondition.getCurrent();
                    Integer pageSize = queryCondition.getPageSize();
                    Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);

                    Sort sort = Sort.by(
                            Sort.Direction.fromString(StringUtils.defaultIfBlank(queryCondition.getOrder(), Sort.Direction.DESC.name())),
                            StringUtils.defaultIfBlank(queryCondition.getSort(), UserChatMessageFieldEnum.id.name())
                    );
                    Query query = Query.query(criteria).with(pageable).sort(sort);
                    Mono<Long> countMono = r2dbcEntityTemplate.count(query, UserChatMessageEntity.class);
                    Flux<UserChatMessageHistoryVO> flux = r2dbcEntityTemplate.select(query, UserChatMessageEntity.class)
                            .map(UserChatMessageHistoryVO::toVO);
                    return flux.collectList().zipWith(countMono).map(tuple2 -> {
                        List<UserChatMessageHistoryVO> dataList = tuple2.getT1();
                        Long total = tuple2.getT2();
                        return PageResult.create(total, dataList);
                    });
                }
        );


    }

    public static Criteria buildQueryCriteria(ChatMessageHistoryQuery queryCondition, String source) {
        Criteria criteria = Criteria.empty();

        // 用户维度过滤,如果是酒店场景，不需要使用userId作为过滤条件
        if (!Objects.equals(source, SourcePlatform.HOTEL.getCode())
                && StringUtils.isNotBlank(queryCondition.getUserId())) {
            criteria = criteria.and(UserChatMessageFieldEnum.created_by.name()).is(queryCondition.getUserId());
        }

        // 会话维度过滤
        if (StringUtils.isNotBlank(queryCondition.getConversationId())) {
            criteria = criteria.and(UserChatMessageFieldEnum.conversation_id.name()).is(queryCondition.getConversationId());
        }

        // 消息树结构过滤
        if (StringUtils.isNotBlank(queryCondition.getParentMessageId())&& NumberUtils.toLong(queryCondition.getParentMessageId()) > 0) {
            criteria = criteria.and(UserChatMessageFieldEnum.parent_message_id.name()).is(queryCondition.getParentMessageId());
        }

        // 智能体维度过滤
        if (StringUtils.isNotBlank(queryCondition.getBotCode())) {
            criteria = criteria.and(UserChatMessageFieldEnum.bot_code.name()).is(queryCondition.getBotCode());
        }

        // 消息类型过滤
        if (StringUtils.isNotBlank(queryCondition.getMessageType())) {
            criteria = criteria.and(UserChatMessageFieldEnum.message_type.name()).is(queryCondition.getMessageType());
        }

        // 设备追踪过滤
        if (StringUtils.isNotBlank(queryCondition.getDeviceId())) {
            criteria = criteria.and(UserChatMessageFieldEnum.device_id.name()).is(queryCondition.getDeviceId());
        }

        // 客户端请求去重
        if (StringUtils.isNotBlank(queryCondition.getClientReqId())) {
            criteria = criteria.and(UserChatMessageFieldEnum.client_req_id.name()).is(queryCondition.getClientReqId());
        }

        // 空间维度过滤（冗余字段）
        if (StringUtils.isNotBlank(queryCondition.getSpaceCode())) {
            criteria = criteria.and(UserChatMessageFieldEnum.space_code.name()).is(queryCondition.getSpaceCode());
        }

        if(StringUtils.isNotBlank(queryCondition.getMaxIdExclusive())){
            criteria = criteria.and(UserChatMessageFieldEnum.id.name()).lessThan(Long.parseLong(queryCondition.getMaxIdExclusive()));
        }

        criteria = criteria.and(UserChatMessageFieldEnum.row_status.name()).is(1);

        return criteria;
    }


    public Mono<Boolean> retryMessageUpdate(String retryMessageId, String conversationId) {
        // 为空直接返回
        if (StringUtils.isBlank(retryMessageId)){
            return Mono.just(Boolean.TRUE);
        }
        Criteria last = Criteria.where(UserChatMessageFieldEnum.conversation_id.name()).is(conversationId);
        return r2dbcEntityTemplate.selectOne(
                Query.query(last)
                        .limit(1)
                        .sort(Sort.by(Sort.Direction.DESC, UserChatMessageFieldEnum.created_at.name())),
                UserChatMessageEntity.class
        ).switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER,"Retried message is not exist")))
                .filter(retry->{
                    return ObjectUtil.equal(retry.getId().toString(), retryMessageId) && ObjectUtil.equal(retry.getMessageType(),ChatRole.ASSISTANT.getValue());
                } )
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "Cannot retry current message")))
                .flatMap(message->{
                    log.info("retryMessageUpdate begin:{}", retryMessageId);
                    Criteria criteria = Criteria.where(UserChatMessageFieldEnum.id.name()).is(retryMessageId);
                    Update update = Update.update(UserChatMessageFieldEnum.retry_count.name(), message.getRetryCount() + 1)
                            .set(UserChatMessageFieldEnum.row_status.name(),0);
                    return r2dbcEntityTemplate.update(Query.query(criteria), update, UserChatMessageEntity.class)
                            .then(Mono.just(Boolean.TRUE));
                })
                .doOnSuccess(entity -> log.debug("Retried message updated successfully | retryMessageId:{} | conversationId:{}",retryMessageId,conversationId))
                .doOnError(e -> log.error("Retried message updated failed | retryMessageId:{} | conversationId:{}",retryMessageId,conversationId));
    }
    public Mono<Boolean> feedback(ChatMsgFeedBackReq req) {

        Mono<UserChatMessageEntity> entityMono;

        if (StringUtils.isNotBlank(req.getId())) {
            entityMono = repository.findById(Long.parseLong(req.getId()))
                    .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "聊息不存在")));
        } else {
            entityMono = repository.findByClientReqIdAndMessageType(req.getClientReqId(), ChatRole.ASSISTANT.getValue())
                    .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "聊息不存在")));
        }


        return entityMono
                .switchIfEmpty(Mono.error(new BusinessException(ResultCode.NOT_FOUND, "聊息不存在"))).flatMap((entity -> {
                    String feedbackTypes = CollUtil.isNotEmpty(req.getFeedbackTypes()) ? String.join(",", req.getFeedbackTypes()) : "";
                    entity.setFeedbackTypes(feedbackTypes);
                    entity.setFeedbackStatus(req.getFeedbackStatus());
                    entity.setFeedbackContent(req.getFeedbackContent());
                    return repository.save(entity);
                })).handle((entity, sink) -> {
                   if(Objects.nonNull(entity)) {
                          sink.next(true);
                     } else {
                          sink.error(new BusinessException(ResultCode.NOT_FOUND, "feedBack error"));
                   }
                });


    }

}