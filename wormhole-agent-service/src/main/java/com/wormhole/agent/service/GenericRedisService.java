package com.wormhole.agent.service;

import jakarta.annotation.Resource;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class GenericRedisService {

    @Resource
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    /**
     * 获取单个键值
     */
    public Mono<String> getValue(String key) {
        return reactiveStringRedisTemplate.opsForValue()
                .get(key)
                .switchIfEmpty(Mono.just(""));
    }

    /**
     * 设置单个键值
     */
    public Mono<Boolean> setValue(String key, String value, Long expireSeconds) {
        if (expireSeconds != null && expireSeconds > 0) {
            return reactiveStringRedisTemplate.opsForValue()
                    .set(key, value, Duration.ofSeconds(expireSeconds));
        } else {
            return reactiveStringRedisTemplate.opsForValue().set(key, value);
        }
    }

    /**
     * 删除单个键
     */
    public Mono<Boolean> deleteKey(String key) {
        return reactiveStringRedisTemplate.delete(key)
                .map(count -> count > 0);
    }

    /**
     * 批量获取键值
     */
    public Mono<Map<String, String>> multiGet(List<String> keys) {
        return reactiveStringRedisTemplate.opsForValue()
                .multiGet(keys)
                .map(values -> {
                    Map<String, String> result = new HashMap<>();
                    for (int i = 0; i < keys.size(); i++) {
                        String value = (i < values.size() && values.get(i) != null) ? values.get(i) : "";
                        result.put(keys.get(i), value);
                    }
                    return result;
                });
    }

    /**
     * 批量设置键值
     */
    public Mono<Boolean> multiSet(Map<String, String> keyValueMap, Map<String, Long> expiryMap) {
        return reactiveStringRedisTemplate.opsForValue()
                .multiSet(keyValueMap)
                .flatMap(success -> {
                    if (success && expiryMap != null && !expiryMap.isEmpty()) {
                        List<Mono<Boolean>> expiryOperations = expiryMap.entrySet().stream()
                                .filter(entry -> entry.getValue() != null && entry.getValue() > 0)
                                .map(entry -> reactiveStringRedisTemplate.expire(
                                        entry.getKey(),
                                        Duration.ofSeconds(entry.getValue())
                                ))
                                .toList();

                        if (expiryOperations.isEmpty()) {
                            return Mono.just(true);
                        }

                        return Flux.concat(expiryOperations)
                                .all(Boolean::booleanValue);
                    }
                    return Mono.just(success);
                });
    }

    /**
     * 查找特定模式的键
     */
    public Flux<String> findKeys(String pattern) {
        return reactiveStringRedisTemplate.keys(pattern);
    }

    /**
     * 检查键是否存在
     */
    public Mono<Boolean> hasKey(String key) {
        return reactiveStringRedisTemplate.hasKey(key);
    }

    /**
     * 设置键的过期时间
     */
    public Mono<Boolean> expire(String key, long seconds) {
        return reactiveStringRedisTemplate.expire(key, Duration.ofSeconds(seconds));
    }

    /**
     * 获取键的剩余过期时间
     */
    public Mono<Long> getExpire(String key) {
        return reactiveStringRedisTemplate.getExpire(key).map(Duration::getSeconds);
    }
}
