package com.wormhole.agent.service.strategy;

import com.wormhole.common.enums.SourcePlatform;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class SourceContext {
    private final Map<String, SourceStrategy> strategies;

    public SourceContext(List<SourceStrategy> strategies) {
        this.strategies = strategies.stream()
                .collect(Collectors.toMap(SourceStrategy::getSource, Function.identity()));
    }

    public SourceStrategy getStrategy(String source) {
        return strategies.getOrDefault(source, strategies.get(SourcePlatform.OTHER.getCode()));
    }
}