package com.wormhole.agent.service;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Preconditions;
import com.wormhole.agent.entity.RtcRoomCallEntity;
import com.wormhole.agent.query.HotelDeviceReq;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.rtc.consts.RedisKeyConstant;
import com.wormhole.agent.vo.DeviceInfoVO;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.req.SearchDeviceReq;
import com.wormhole.hotelds.core.enums.DeviceTypeEnum;
import com.wormhole.hotelds.core.model.resp.DeviceRtcInfoResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: joker.liu
 * @date: 2025/4/8
 * @Description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RtcUserService {

    private final ConnectionService connectionService;

    private final RtcRoomCallService rtcRoomCallService;

    private final ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    private final HotelDsApiClient hotelDsApiClient;

    /**
     * 获取可用用户
     * @param req   请求参数
     */
    public Mono<DeviceInfoVO> getOnlineUsable(HotelDeviceReq req) {

        Preconditions.checkArgument(StringUtils.isNotBlank(req.getHotelCode()), "hotelCode is not null");

        Mono<DeviceInfoResp> deviceInfoMono = connectionService.getDeviceInfo(req.getDeviceId(), null);

        return deviceInfoMono.flatMap(deviceInfo -> getDeviceInfoVO(req)
                .flatMap(deviceInfoVO ->  {
                    Mono<String> roomIdMono = getRoomId(deviceInfo.getDeviceId());
                    return roomIdMono.flatMap(roomId -> {
                        Mono<RtcRoomCallEntity> roomCallInfoMono = rtcRoomCallService.getOnlineRoomCallInfo(roomId);
                        deviceInfoVO.setUserId(deviceInfo.getRtcUserId());
                        deviceInfoVO.setRoomId(roomId);
                        return Mono.when(
                                busyUser(deviceInfoVO.getUserIds()),
                                deviceCallUserIds(deviceInfo.getRtcUserId(), deviceInfoVO.getUserIds()),
                                bindingUser(deviceInfoVO.getUserIds(), deviceInfo.getRtcUserId(), roomId)
                        ).then(roomCallInfoMono.flatMap(roomCallInfo -> {
                            String callSessionId = roomCallInfo.getRtcRoomId();
                            deviceInfoVO.setTaskId(callSessionId);
                            deviceInfoVO.setRoomCode(roomCallInfo.getPositionCode());
                            return Mono.just(deviceInfoVO);
                        }));
                    }).switchIfEmpty(Mono.defer(() -> Mono.error(new BusinessException("not calling", "Not on the calling"))));
                })
        );

    }

    private Mono<DeviceInfoVO> getDeviceInfoVO(HotelDeviceReq req) {
        if (DeviceTypeEnum.FRONT.getCode().equals(req.getDeviceType()) || DeviceTypeEnum.WECHAT_MINI_APP.getCode().equals(req.getDeviceType())) {
            return getDeviceInfoVO(req.getHotelCode(), DeviceTypeEnum.FRONT.getCode());
        }
        if (StringUtils.isBlank(req.getTargetDeviceId())) {
            return Mono.error(new BusinessException("DEVICE-NOT-FOUND", "Target device must not be null"));
        }
        return getDeviceInfoVO(req.getTargetDeviceId());
    }

    private Mono<DeviceInfoVO> getDeviceInfoVO(String deviceId) {
        return connectionService.deviceInitInfo(deviceId, null)
                .flatMap(deviceInfo -> {
                    List<String> rtcUserIds = List.of(deviceInfo.getRtcUserId());
                    return filterBusyDevice(rtcUserIds);
                });

    }

    private Mono<DeviceInfoVO> getDeviceInfoVO(String hotelCode, String type) {
        SearchDeviceReq req =  new SearchDeviceReq();
        req.setHotelCode(hotelCode);
        req.setDeviceType(type);
        req.setEmployeeType(1);
        return hotelDsApiClient.getAvailableDevices(req)
                .map(deviceInfos -> deviceInfos.stream().map(DeviceRtcInfoResp::getRtcUserId).toList())
                .flatMap(this::filterBusyDevice);
    }

    /**
     * 过滤在线且没有被占线的设备
     */
    private Mono<DeviceInfoVO> filterBusyDevice(List<String> userIds) {
        List<String> deviceIds = userIds.stream().map(userId -> userId.split(StrUtil.UNDERLINE)[1]).toList();
        log.info("filterBusyDevice deviceIds: {} userIds: {}", deviceIds, userIds);
        return Flux.fromIterable(deviceIds)
                .flatMap(deviceId -> reactiveStringRedisTemplate.opsForSet().isMember(RedisKeyConstant.ONLINE_DEVICE_KEY, deviceId))
                .collectList()
                .flatMap(results -> {
                    log.info("filterBusyDevice targetUsers: {}, results: {}", deviceIds, results);
                    List<String> onlineUsers = new ArrayList<>();
                    for (int i = 0; i < userIds.size(); i++) {
                        if (results.get(i)) {
                            onlineUsers.add(userIds.get(i));
                        }
                    }
                    return Mono.just(onlineUsers);
                })
                .flatMap(onlineUserIds -> {
                    log.info("filterBusyDevice onlineUsers: {}", onlineUserIds);
                    if (onlineUserIds.isEmpty()) {
                        return Mono.just(new ArrayList<String>());
                    }

                    Mono<List<Boolean>> listMono = Flux.fromIterable(onlineUserIds)
                            .flatMap(userId -> reactiveStringRedisTemplate.opsForSet().isMember(RedisKeyConstant.ONLINE_BUSY_USER_KEY, userId))
                            .collectList();

                    return listMono.flatMap(busyMap -> {
                        log.info("filterBusyDevice busyMap: {}", busyMap);
                        List<String> availableUsers = new ArrayList<>();
                        for (int i = 0; i < onlineUserIds.size(); i++) {
                            if (!busyMap.get(i)) {
                                availableUsers.add(onlineUserIds.get(i));
                            }
                        }
                        return Mono.just(availableUsers);
                    });
                })
                .map(availableUsers -> {
                    log.info("filterBusyDevice Available users: {}", availableUsers);
                    DeviceInfoVO deviceInfoVO = new DeviceInfoVO();
                    deviceInfoVO.setUserIds(availableUsers);
                    deviceInfoVO.setSize(availableUsers.size());
                    return deviceInfoVO;
                });
    }

    /**
     * 占线
     * @param userIds     用户id集合
     */
    private Mono<Void> busyUser(List<String> userIds) {
        if (userIds.isEmpty()) {
            return Mono.empty();
        }
        String key = RedisKeyConstant.ONLINE_BUSY_USER_KEY;
        return reactiveStringRedisTemplate.opsForSet().add(key, userIds.toArray(new String[0]))
                .then();
    }

    /**
     * 用户绑定房间和拨打方
     * @param userIds           用户id集合
     * @param callUserId        拨打的用户
     * @param roomId            房间id
     */
    private Mono<Void> bindingUser(List<String> userIds, String callUserId, String roomId) {
        if (userIds.isEmpty()) {
            return Mono.empty();
        }
        Map<String, String> roomBindings = userIds.stream()
                .collect(Collectors.toMap(
                        userId -> String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId),
                        userId -> roomId + StrUtil.COLON + callUserId
                ));
        roomBindings.put(String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, callUserId), roomId + StrUtil.COLON + callUserId);
        return reactiveStringRedisTemplate.opsForValue().multiSet(roomBindings).then();
    }

    private Mono<String> getRoomIdByBinding(String deviceId) {

        Mono<DeviceInfoResp> deviceInfoMono = connectionService.getDeviceInfo(deviceId, null);

        return deviceInfoMono.flatMap(deviceInfo -> {
            String userId = deviceInfo.getRtcUserId();
            String bindingRoomKey = String.format(RedisKeyConstant.USER_BINDING_ROOM_KEY, userId);

            return reactiveStringRedisTemplate.opsForValue().get(bindingRoomKey)
                    .flatMap(value -> {
                        String[] roomInfo = value.split(StrUtil.COLON);
                        return Mono.just(roomInfo[0]);
                    });
        });
    }


    /**
     * 拨打用户与被拨打用户绑定
     * @param callUserId        拨打的用户
     * @param userIds           用户id集合
     */
    private Mono<Void> deviceCallUserIds(String callUserId, List<String> userIds) {
        if (userIds.isEmpty()) {
            return Mono.empty();
        }
        String callKey = String.format(RedisKeyConstant.FROM_CALL_TO_HUMAN_KEY, callUserId);
        return reactiveStringRedisTemplate.opsForSet().add(callKey, userIds.toArray(new String[0])).then();
    }

    /**
     * 获取在房用户
     * @param req      挂断信息
     */
    public Mono<DeviceInfoVO> getInRoomUser(HotelDeviceReq req) {

        String rtcRoomId = req.getRtcRoomId();
        String deviceId = req.getDeviceId();
        Preconditions.checkArgument(!StrUtil.isBlank(rtcRoomId), "RTC-ROOM-ID-ERR", "rtcRoomId is blank");
        Preconditions.checkArgument(!StrUtil.isBlank(deviceId), "RTC-ROOM-ID-ERR", "deviceId is blank");

        Mono<RtcRoomCallEntity> roomCallInfoMono = rtcRoomCallService.getOnlineRoomCallInfo(rtcRoomId);

        String inRoomUserKey = String.format(RedisKeyConstant.ONLINE_ROOM_USER_KEY, rtcRoomId);
        return roomCallInfoMono.flatMap(roomCallInfo -> reactiveStringRedisTemplate.opsForSet().members(inRoomUserKey)
                .collectList()
                .map(userIds -> {
                    DeviceInfoVO deviceInfoVO = new DeviceInfoVO();
                    deviceInfoVO.setUserIds(userIds);
                    deviceInfoVO.setSize(userIds.size());
                    deviceInfoVO.setTaskId(roomCallInfo.getRtcRoomId());
                    deviceInfoVO.setRoomId(rtcRoomId);
                    return deviceInfoVO;
                }))
                .switchIfEmpty(Mono.defer(() -> {
                    DeviceInfoVO deviceInfoVO = new DeviceInfoVO();
                    deviceInfoVO.setUserIds(new ArrayList<>());
                    deviceInfoVO.setSize(0);
                    deviceInfoVO.setTaskId(null);
                    deviceInfoVO.setRoomId(rtcRoomId);
                    return Mono.just(deviceInfoVO);
                }));
    }

    /**
     * 获取设备对应的房间信息
     * @param deviceId      设备id
     */
    private Mono<String> getRoomId(String deviceId) {
        String deviceBingRoomKey = String.format(RedisKeyConstant.DEVICE_BINDING_ROOM_KEY, deviceId);
        return reactiveStringRedisTemplate.opsForValue().get(deviceBingRoomKey);
    }

    /**
     * 呼叫转移
     * @param roomId    房间id
     */
    public Mono<Boolean> callTransfer(String roomId) {

        return rtcRoomCallService.getOnlineRoomCallInfo(roomId)
                .flatMap(roomCallInfo -> rtcRoomCallService.transfer(roomId, roomCallInfo.getInitiatorId(), roomCallInfo.getInitiatorName(), null, null, null));
    }

    public Mono<Boolean> addHangUpMark(String roomId) {
        String redisKey = String.format(RedisKeyConstant.ROOM_HANG_UP_MARK_KEY, roomId);
        return reactiveStringRedisTemplate.opsForValue().set(redisKey, System.currentTimeMillis() + "", Duration.of(2, ChronoUnit.MINUTES));
    }
}
