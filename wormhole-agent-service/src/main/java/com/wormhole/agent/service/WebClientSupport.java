package com.wormhole.agent.service;

import reactor.netty.http.client.HttpClient;
import reactor.netty.resources.ConnectionProvider;

import java.time.Duration;

/**
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
public class WebClientSupport {

    public static HttpClient getHttpClient(String name, Duration pendingAcquireTimeout, int maxConnections, int pendingAcquireMaxCount, boolean compress) {
        return HttpClient.create(ConnectionProvider
                        .builder(name)
                        // 等待超时时间
                        .pendingAcquireTimeout(pendingAcquireTimeout)
                        // 最大连接数
                        .maxConnections(maxConnections)
                        // 等待队列大小
                        .pendingAcquireMaxCount(pendingAcquireMaxCount).build())
                .compress(compress);
    }

    public static HttpClient getHttpClient(String name) {
        return getHttpClient(name, Duration.ofSeconds(1L), 200, 300, true);
    }

    public static HttpClient getHttpClient(String name, boolean compress) {
        return getHttpClient(name, Duration.ofSeconds(1L), 200, 300, true);
    }
}
