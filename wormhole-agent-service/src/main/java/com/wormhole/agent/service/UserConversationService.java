package com.wormhole.agent.service;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.Preconditions;
import com.wormhole.agent.chat.memory.service.RecentMemoryService;
import com.wormhole.agent.chat.model.ChatConstant;
import com.wormhole.agent.client.chat.params.CreateConversationReq;
import com.wormhole.agent.client.chat.params.UserConversationVO;
import com.wormhole.agent.core.model.bot.*;
import com.wormhole.agent.core.model.entity.UserConversationEntity;
import com.wormhole.agent.core.model.entity.UserConversationFieldEnum;
import com.wormhole.agent.core.util.*;
import com.wormhole.agent.dto.SaveConversationDTO;
import com.wormhole.agent.query.*;
import com.wormhole.agent.repository.UserConversationRepository;
import com.wormhole.agent.service.strategy.SourceContext;
import com.wormhole.agent.service.strategy.SourceStrategy;
import com.wormhole.agent.util.SqlUtils;
import com.wormhole.agent.vo.ConversationSimpleVO;
import com.wormhole.common.constant.RowStatusEnum;
import com.wormhole.common.enums.SourcePlatform;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.BeanUtils;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.IdUtils;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.data.relational.core.query.Update;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.*;

@Slf4j
@Service
@RefreshScope
public class UserConversationService {

    @Resource
    private  R2dbcEntityTemplate r2dbcEntityTemplate;
    @Resource
    private UserConversationRepository repository;
    @Resource
    private BotService botService;
    @Resource
    private RecentMemoryService recentMemoryService;
    @Resource
    private SourceContext sourceContext;

    @Value("${wormhole.ticket.cleanHour:14}")
    private Integer hourConfig;

    public Mono<UserConversationVO> createConversation(SaveConversationDTO dto) {
        // 参数校验
        Preconditions.checkNotNull(dto, "查询对象不能为空");
        SourceStrategy strategy = sourceContext.getStrategy(dto.getSource());
        return strategy.createConversation(dto);

    }

    /**
     * 查询现有对话
     */
    public Mono<UserConversationEntity> queryExistingConversation(SaveConversationDTO dto) {
        UserConversationQuery userConversationQuery = new UserConversationQuery();
        if(StringUtils.isNotBlank(dto.getBotCode())){
            userConversationQuery.setBotCode(dto.isDebug()?dto.getBotCode()+"_debug":dto.getBotCode());
        }else {
            userConversationQuery.setBotCode("llm");
        }
        if (!Objects.equals(dto.getSource(), SourcePlatform.HOTEL.getCode())) {
            userConversationQuery.setUserId(dto.getUserId());
            userConversationQuery.setDeviceId(dto.getDeviceId());
        }
        userConversationQuery.setSource(dto.getSource());
        userConversationQuery.setHotelCode(dto.getHotelCode());
        userConversationQuery.setPositionCode(dto.getPositionCode());
        userConversationQuery.setRowStatus(RowStatusEnum.VALID.getId());
        Criteria criteria = buildCriteria(userConversationQuery);
        Query query = buildQuery(userConversationQuery, criteria)
                .sort(Sort.by(Sort.Direction.DESC, UserConversationFieldEnum.id.name()))
                .limit(1);
        return r2dbcEntityTemplate.selectOne(query, UserConversationEntity.class);
    }

    /**
     * 构建保存对话的DTO
     */
    public SaveConversationDTO buildSaveConversationDTO(CreateConversationReq req, HeaderUtils.HeaderInfo headerInfo) {
        return SaveConversationDTO.builder()
                .userId(headerInfo.getUserId())
                .username(headerInfo.getUsername())
                .source(headerInfo.getSource())
                .botCode(req.getBotCode())
                .isDebug(req.isDebug())
                .hotelCode(req.getHotelCode())
                .deviceId(headerInfo.getDeviceId())
                .title(req.getHotelName())
                .positionCode(req.getPositionCode())
                .build();
    }


    public Mono<UserConversationEntity> saveEntity(SaveConversationDTO dto) {
        return repository.save(processRequest(dto));
    }

    public Mono<UserConversationVO> findByConversationId(String conversationId) {
        return repository.findByConversationId(conversationId)
                .map(this::toVO);
    }

    public Mono<Boolean> deleteConversation(UserConversationDelReq delReq) {
        // 参数校验
        Preconditions.checkNotNull(delReq, "查询对象不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(delReq.getConversationId()), "conversationId 不能为空");

        // 获取Header信息并执行删除操作
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> repository.deleteByCreatedByAndConversationId(headerInfo.getUserId(), delReq.getConversationId())
                        .then(recentMemoryService.deleteMemory(delReq.getConversationId()))
                        .thenReturn(Boolean.TRUE));
    }
    public Mono<Boolean> refreshConversation(UserConversationRefreshReq req) {
        // 参数校验
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getConversationId()), "conversationId 不能为空");
        // 刷新 历史记忆
        return recentMemoryService.deleteMemory(req.getConversationId());
    }


    public Mono<PageResult<UserConversationVO>> listConversations(UserConversationQuery userConversationQuery) {
        // 参数校验
        Preconditions.checkNotNull(userConversationQuery, "查询对象不能为空");
        // 获取Header信息并设置查询条件
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    if (StringUtils.isBlank(userConversationQuery.getUserId())) {
                        userConversationQuery.setUserId(headerInfo.getUserId());
                    }
                    if (StringUtils.isBlank(userConversationQuery.getSource())) {
                        userConversationQuery.setSource(headerInfo.getSource());
                    }
                    if (Objects.equals(userConversationQuery.getSource(), SourcePlatform.HOTEL.getCode())) {
                        //如果是酒店场景，不需要使用userId作为过滤条件
                        userConversationQuery.setUserId(null);
                    }
                    // 构建查询条件
                    Criteria criteria = buildCriteria(userConversationQuery);
                    // 构建分页查询
                    Query query = buildQuery(userConversationQuery, criteria);
                    // 查询总数和数据
                    Mono<Long> countMono = r2dbcEntityTemplate.count(query, UserConversationEntity.class);
                    Flux<UserConversationVO> flux = queryConversations(query);
                    // 返回分页结果
                    return flux.collectList()
                            .zipWith(countMono)
                            .map(tuple2 -> PageResult.create(tuple2.getT2(), tuple2.getT1()));
                });
    }

    /**
     * 构建查询条件
     */
    private Criteria buildCriteria(UserConversationQuery userConversationQuery) {
        Criteria criteria = Criteria.where(UserConversationFieldEnum.source.name())
                .is(userConversationQuery.getSource() != null ? userConversationQuery.getSource() : SourcePlatform.CHAT.getCode());

        criteria = addIfNotNull(criteria, UserConversationFieldEnum.created_by.name(), userConversationQuery.getUserId());
        criteria = addIfNotNull(criteria, UserConversationFieldEnum.bot_code.name(), userConversationQuery.getBotCode());
        criteria = addIfNotNull(criteria, UserConversationFieldEnum.conversation_id.name(), userConversationQuery.getConversationId());
        criteria = addIfNotNull(criteria, UserConversationFieldEnum.device_id.name(), userConversationQuery.getDeviceId());
        criteria = addIfNotNull(criteria, UserConversationFieldEnum.hotel_code.name(), userConversationQuery.getHotelCode());
        criteria = addIfNotNull(criteria, UserConversationFieldEnum.position_code.name(), userConversationQuery.getPositionCode());
        criteria = addIfNotNull(criteria, "row_status", userConversationQuery.getRowStatus());

        return criteria;
    }

    private Criteria addIfNotNull(Criteria criteria, String field, Object value) {
        if (!Objects.isNull(value)) {
            return criteria.and(field).is(value);
        }
        return criteria;
    }



    /**
     * 构建分页查询
     */
    private Query buildQuery(UserConversationQuery userConversationQuery, Criteria criteria) {
        Integer pageNumber = userConversationQuery.getCurrent();
        Integer pageSize = userConversationQuery.getPageSize();
        Pageable pageable = PageRequest.of(pageNumber - 1, pageSize);
        Sort sort = Sort.by(Sort.Direction.DESC, UserConversationFieldEnum.updated_at.name());

        return Query.query(criteria).with(pageable).sort(sort);
    }

    /**
     * 查询数据并转换为VO
     */
    public Flux<UserConversationVO> queryConversations(Query query) {
        return r2dbcEntityTemplate.select(query, UserConversationEntity.class)
                .flatMap(entity -> {
                    UserConversationVO vo = this.toVO(entity);
                    BotInfo botInfo = botService.getBotInfoByCode(entity.getBotCode(),false);
                    if (botInfo != null) {
                        vo.setBotName(botInfo.getName());
                        vo.setOnboardingInfo(botInfo.getOnboardingInfo());
                        vo.setExtMap(botInfo.getExtConfigInfo().getExtMap());
                    }
                    return Mono.just(vo);
                });
    }


    public Mono<Boolean> updateConversation(UserConversationUpdateReq userConversationUpdateReq) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 设置Header信息
                    UserConversationQuery query = new UserConversationQuery();
                    query.setUserId(StringUtils.isNotBlank(headerInfo.getUserId()) ? headerInfo.getUserId() : userConversationUpdateReq.getUserId());
                    query.setSource(StringUtils.isNotBlank(headerInfo.getSource()) ? headerInfo.getSource() : userConversationUpdateReq.getSource());

                    // 判断是否根据ID更新
                    if (Objects.isNull(userConversationUpdateReq.getId())) {
                        // 参数校验
                        Preconditions.checkNotNull(userConversationUpdateReq.getBotCode(), "智能体不能为空");
                        Preconditions.checkNotNull(userConversationUpdateReq.getConversationId(), "会话ID不能为空");
                        query.setBotCode(userConversationUpdateReq.getBotCode());
                        query.setConversationId(userConversationUpdateReq.getConversationId());
                        // 构建查询条件
                        Criteria criteria = buildCriteria(query);

                        // 构建更新操作
                        Update update = Update.update(UserConversationFieldEnum.content.name(), userConversationUpdateReq.getContent())
                                .set(UserConversationFieldEnum.updated_at.name(), LocalDateTime.now())
                                .set(UserConversationFieldEnum.updated_by_name.name(), userConversationUpdateReq.getUsername())
                                .set(UserConversationFieldEnum.updated_by.name(), userConversationUpdateReq.getUserId());

                        // 执行更新
                        return r2dbcEntityTemplate.update(Query.query(criteria), update, UserConversationEntity.class)
                                .then(Mono.just(Boolean.TRUE));
                    } else {
                        // 根据ID更新
                        UserConversationEntity userConversationEntity = BeanUtils.copy(userConversationUpdateReq, UserConversationEntity.class);
                        return r2dbcEntityTemplate.update(userConversationEntity)
                                .then(Mono.just(Boolean.TRUE));
                    }
                });
    }


    public UserConversationEntity processRequest(SaveConversationDTO dto) {
        UserConversationEntity entity = new UserConversationEntity();
        entity.setCreatedBy(dto.getUserId());
        entity.setCreatedByName(dto.getUsername());
        entity.setCreatedAt(LocalDateTime.now());
        entity.setUpdatedBy(dto.getUserId());
        entity.setUpdatedByName(dto.getUsername());
        entity.setUpdatedAt(LocalDateTime.now());
        entity.setSource(dto.getSource());
        entity.setConversationId(Optional.ofNullable(dto.getConversationId()).orElse(IdUtils.generateId()));
        entity.setHotelCode(dto.getHotelCode());
        entity.setPositionCode(dto.getPositionCode());
        entity.setDeviceId(dto.getDeviceId());
        if (Objects.isNull(dto.getBotCode()) || "llm".equals(dto.getBotCode())) {
            setDefaultEntityValues(entity);
        } else {
            String botCode = dto.getBotCode();
            BotInfo botInfo = botService.getBotInfoByCode(botCode,dto.isDebug());

            // 检查 botInfo 是否存在，若不存在则抛出异常
            Optional.ofNullable(botInfo)
                    .orElseThrow(() -> new BusinessException(ResultCode.NOT_FOUND, "智能体不存在"));

            setEntityValuesFromBotInfo(entity, botInfo);
        }
        if (StringUtils.isNotBlank(dto.getTitle())) {
            entity.setTitle(dto.getTitle());
        }
        return entity;
    }

    private void setDefaultEntityValues(UserConversationEntity entity) {
        entity.setBotCode("llm")
                .setTitle(ChatConstant.CHAT_CONVERSATION_DEFAULT_TITLE)
                .setContent(ChatConstant.CHAT_CONVERSATION_DEFAULT_CONTENT)
                .setImageJson(null); // 这里假设 req.getImageJson() 会在默认情况下为 null
    }

    private void setEntityValuesFromBotInfo(UserConversationEntity entity, BotInfo botInfo) {
        entity.setBotCode(botInfo.getBotCode())
                .setTitle(botInfo.getName())
                .setContent(botInfo.getDescription())
                .setImageJson(JSON.toJSONString(botInfo.getImageJson()));
    }
    public UserConversationVO toVO(UserConversationEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        UserConversationVO userConversationVO = new UserConversationVO();
        org.springframework.beans.BeanUtils.copyProperties(entity, userConversationVO);
        userConversationVO.setUserId(entity.getCreatedBy());
        if (entity.getImageJson() !=null) {
            ObjectInfo imageJson = JacksonUtils.readValue(entity.getImageJson(), ObjectInfo.class);
            userConversationVO.setImageJson(imageJson);
        }
        if("llm".equals(entity.getBotCode())){
            userConversationVO.setBotCode(null);
        }
        if (StringUtils.isNotEmpty(userConversationVO.getBotCode())) {
            BotInfo botInfo = botService.getBotInfoByCode(userConversationVO.getBotCode(),false);
            if (botInfo != null) {
                userConversationVO.setBotName(botInfo.getName());
                userConversationVO.setExtMap(botInfo.getExtConfigInfo().getExtMap());
                userConversationVO.setOnboardingInfo(botInfo.getOnboardingInfo());

                processPrologueTemplate(userConversationVO);

            }
        }
        return userConversationVO;
    }

    /**
     * 处理开场白模板 - 替换酒店名称
     */
    private UserConversationVO processPrologueTemplate(UserConversationVO vo) {
        Optional.ofNullable(vo.getOnboardingInfo())
                .map(OnboardingInfo::getPrologue)
                .filter(StringUtils::isNotBlank)
                .filter(prologue -> prologue.contains("${hotel_name}"))
                .filter(prologue -> StringUtils.isNotBlank(vo.getTitle()))
                .ifPresent(prologue -> {
                    String processed = FreeMarkerTemplateCacheUtils.processTemplateIntoString(
                            prologue, Map.of("hotel_name", vo.getTitle())
                    );
                    vo.getOnboardingInfo().setPrologue(processed);
                });

        return vo;
    }


    public Mono<PageResult<ConversationSimpleVO>> conversationPage(ChatConversationPageReq pageReq) {
        Preconditions.checkArgument(Objects.nonNull(pageReq.getCurrent()) && Objects.nonNull(pageReq.getPageSize()), "分页参数不能为空");

        return Mono.fromCallable(() -> {
                    Criteria empty = Criteria.empty();
                    if (StringUtils.isNotBlank(pageReq.getBotCode())) {
                        empty = empty.and(UserConversationFieldEnum.bot_code.name()).is(pageReq.getBotCode());
                    }
                    if (StringUtils.isNotBlank(pageReq.getUserId())) {
                        empty = empty.and(UserConversationFieldEnum.created_by.name()).is(pageReq.getUserId());
                    }
                    if (StringUtils.isNotBlank(pageReq.getTitle())) {
                        empty = empty.and(UserConversationFieldEnum.title.name()).like(SqlUtils.like(pageReq.getTitle()));
                    }
                    if(StringUtils.isNotBlank(pageReq.getSource())) {
                        empty = empty.and(UserConversationFieldEnum.source.name()).is(pageReq.getSource());
                    }
                    PageRequest pageRequest = PageRequest.of(pageReq.getCurrent() - 1, pageReq.getPageSize());
                    Sort sort = Sort.by(Sort.Direction.DESC, UserConversationFieldEnum.updated_at.name());
                    log.info("conversationPage empty{}", JSONUtil.toJsonStr(empty));
                    return Query.query(empty).with(pageRequest).sort(sort);
                }).flatMap(query -> Mono.zip(r2dbcEntityTemplate.count(query, UserConversationEntity.class)
                        , r2dbcEntityTemplate.select(query, UserConversationEntity.class).map(this::toSimpleVO).collectList()))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()));
    }

    private ConversationSimpleVO  toSimpleVO(UserConversationEntity entity){
        ConversationSimpleVO vo = new ConversationSimpleVO();
        vo.setConversationId(entity.getConversationId());
        vo.setTitle(entity.getTitle());
        vo.setBotCode(entity.getBotCode());
        vo.setCreateAt(entity.getCreatedAt());
        vo.setCreateBy(entity.getCreatedBy());
        return vo;
    }

}