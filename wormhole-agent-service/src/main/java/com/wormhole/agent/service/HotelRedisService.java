package com.wormhole.agent.service;

import com.wormhole.agent.query.HotelInfoRedisReq;
import jakarta.annotation.Resource;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class HotelRedisService {

    private static final String KEY_PREFIX = "HOTEL_INFO:";

    @Resource
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    /**
     * 创建Redis键名
     */
    private String createKey(String hotelCode, String category) {
        return KEY_PREFIX + hotelCode + ":" + category;
    }

    /**
     * 获取酒店信息
     */
    public Mono<String> getHotelInfo(String hotelCode, String category) {
        return reactiveStringRedisTemplate.opsForValue()
                .get(createKey(hotelCode, category))
                .switchIfEmpty(Mono.just(""));
    }

    /**
     * 设置酒店信息
     */
    public Mono<Boolean> setHotelInfo(String hotelCode, String category, String info, Long expireSeconds) {
        String key = createKey(hotelCode, category);

        if (expireSeconds != null && expireSeconds > 0) {
            return reactiveStringRedisTemplate.opsForValue()
                    .set(key, info, Duration.ofSeconds(expireSeconds));
        } else {
            return reactiveStringRedisTemplate.opsForValue().set(key, info);
        }
    }

    /**
     * 批量获取酒店信息
     */
    public Mono<Map<String, String>> batchGetHotelInfo(List<HotelInfoRedisReq> reqList) {
        List<String> keys = reqList.stream()
                .map(req -> createKey(req.getHotelCode(), req.getCategory()))
                .toList();

        return reactiveStringRedisTemplate.opsForValue()
                .multiGet(keys)
                .map(values -> {
                    Map<String, String> result = new HashMap<>();
                    for (int i = 0; i < keys.size(); i++) {
                        String value = (i < values.size() && values.get(i) != null) ? values.get(i) : "";
                        result.put(keys.get(i), value);
                    }
                    return result;
                });
    }

    /**
     * 批量设置酒店信息
     */
    public Mono<Boolean> batchSetHotelInfo(List<HotelInfoRedisReq> reqList) {
        Map<String, String> keyValueMap = new HashMap<>();

        for (HotelInfoRedisReq req : reqList) {
            keyValueMap.put(
                    createKey(req.getHotelCode(), req.getCategory()),
                    req.getInfo()
            );
        }

        return reactiveStringRedisTemplate.opsForValue()
                .multiSet(keyValueMap)
                .flatMap(success -> {
                    if (success) {
                        // 设置过期时间（如果有）
                        List<Mono<Boolean>> expiryOperations = reqList.stream()
                                .filter(req -> req.getExpireSeconds() != null && req.getExpireSeconds() > 0)
                                .map(req -> reactiveStringRedisTemplate.expire(
                                        createKey(req.getHotelCode(), req.getCategory()),
                                        Duration.ofSeconds(req.getExpireSeconds())
                                ))
                                .toList();

                        if (expiryOperations.isEmpty()) {
                            return Mono.just(true);
                        }

                        return Flux.concat(expiryOperations)
                                .all(Boolean::booleanValue);
                    }
                    return Mono.just(false);
                });
    }

    /**
     * 删除酒店信息
     */
    public Mono<Boolean> deleteHotelInfo(String hotelCode, String category) {
        return reactiveStringRedisTemplate.delete(createKey(hotelCode, category))
                .map(count -> count > 0);
    }

    /**
     * 查找特定模式的键
     */
    public Flux<String> findKeys(String pattern) {
        return reactiveStringRedisTemplate.keys(KEY_PREFIX + pattern);
    }
}
