package com.wormhole.agent.repository;

import com.wormhole.agent.entity.RtcRoomCallEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/31
 * @Description:
 */
@Repository
public interface RtcRoomCallRepository extends ReactiveCrudRepository<RtcRoomCallEntity, Long> {

    /**
     * 获取正在通话的记录
     * @param rtcRoomId        房间id
     * @param rowStatus     有效记录
     * @return              记录数据
     */
    Mono<RtcRoomCallEntity> findByRtcRoomIdAndRowStatus(String rtcRoomId, Integer rowStatus);

    Mono<RtcRoomCallEntity> findByRtcRoomId(String roomId);
}
