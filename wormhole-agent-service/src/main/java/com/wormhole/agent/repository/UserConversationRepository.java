package com.wormhole.agent.repository;


import com.wormhole.agent.core.model.entity.UserConversationEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * SpacesRepository
 *
 * <AUTHOR>
 * @version 2024/10/18
 */
@Repository
public interface UserConversationRepository extends ReactiveCrudRepository<UserConversationEntity, Long> {
    Mono<UserConversationEntity> findByCreatedByAndBotCode(String createBy, String botCode);

    Mono<UserConversationEntity> findByConversationId(String conversationId);

    Mono<Void> deleteByCreatedByAndConversationId(String createdBy, String conversationId);

}