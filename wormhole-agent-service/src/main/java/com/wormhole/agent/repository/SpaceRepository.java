package com.wormhole.agent.repository;

import com.wormhole.agent.entity.SpaceEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;


/**
 * SpacesRepository
 *
 * <AUTHOR>
 * @version 2024/10/18
 */
@Repository
public interface SpaceRepository extends ReactiveCrudRepository<SpaceEntity, Long> {

    Mono<List<SpaceEntity>> findBySpaceType(String spaceType);

}