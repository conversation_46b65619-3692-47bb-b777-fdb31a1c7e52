package com.wormhole.agent.repository;

import com.wormhole.agent.entity.RoomChatEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public interface RoomChatRepository extends ReactiveCrudRepository<RoomChatEntity, Long> {
    Mono<RoomChatEntity> findByRoomIdAndRowStatus(String roomId,Integer rowStatus);
}
