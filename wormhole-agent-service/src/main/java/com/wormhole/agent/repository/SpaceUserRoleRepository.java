package com.wormhole.agent.repository;

import com.wormhole.agent.entity.SpaceUserRoleEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
@Repository
public interface SpaceUserRoleRepository extends ReactiveCrudRepository<SpaceUserRoleEntity, Long> {

    Mono<Boolean> deleteBySpaceCode(String spaceCode);

    Mono<List<SpaceUserRoleEntity>> findByUserId(String userId);

}
