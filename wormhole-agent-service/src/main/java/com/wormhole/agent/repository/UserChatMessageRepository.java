package com.wormhole.agent.repository;


import com.wormhole.agent.core.model.entity.UserChatMessageEntity;
import org.springframework.data.repository.reactive.ReactiveCrudRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

/**
 * SpacesRepository
 *
 * <AUTHOR>
 * @version 2024/10/18
 */
@Repository
public interface UserChatMessageRepository extends ReactiveCrudRepository<UserChatMessageEntity, Long> {

    Mono<Void> deleteByCreatedByAndConversationId(String createBy, String conversationId);

    Mono<UserChatMessageEntity> findByClientReqIdAndMessageType(String clientReqId, String messageType);

}