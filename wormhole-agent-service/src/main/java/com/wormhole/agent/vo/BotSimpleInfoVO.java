package com.wormhole.agent.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.bot.ObjectInfo;
import com.wormhole.agent.entity.BotEntity;
import com.wormhole.common.util.JacksonUtils;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date  2025/02/18
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BotSimpleInfoVO {

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 图形信息
     */
    private ObjectInfo imageJson;

    /**
     * 空间code
     */
    private String spaceCode;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishTime;
    /**
     * 修改人名称
     */
    private String updatedByName;
    private String category;
    private String categoryName;

    private String conversationId;

    public static BotSimpleInfoVO toSimpleVO(BotEntity botEntity) {
        return new BotSimpleInfoVO()
                .setCode(botEntity.getCode())
                .setName(botEntity.getName())
                .setDescription(botEntity.getDescription())
                .setImageJson(JacksonUtils.readValue(botEntity.getImageJson(), ObjectInfo.class))
                .setSpaceCode(botEntity.getSpaceCode())
                .setPublishTime(botEntity.getPublishTime())
                // TODO 后续处理
                .setUpdatedByName("百达屋AI")
                .setPublishTime(botEntity.getPublishTime())
                ;
    }
}
