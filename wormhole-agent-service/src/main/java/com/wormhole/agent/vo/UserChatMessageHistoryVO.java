package com.wormhole.agent.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.entity.UserChatMessageEntity;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.Objects;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserChatMessageHistoryVO {
    /**
     * 主键ID
     */
    private String id;

    /**
     * 空间编码（冗余存储）
     */
    private String spaceCode;

    /**
     * 会话ID（ULID格式）
     */
    private String conversationId;

    /**
     * 父消息ID（构成消息树）
     */
    private String parentMessageId;

    /**
     * 客户端请求ID（用于去重）
     */
    private String clientReqId;

    /**
     * 原始设备ID（用于设备追踪）
     */
    private String deviceId;


    /**
     * 智能体编码
     */
    private String botCode;

    /**
     * 消息类型（user/assistant/system）
     */
    private String messageType;

    /**
     * 消息内容（支持富文本）
     */
    private String content;

    /**
     * 模型标识（如gpt-4-32k）
     */
    private String model;

    /**
     * 模型供应商（如OpenAI）
     */
    private String modelProvider;

    /**
     * 大模型响应ID（用于审计追踪）
     */
    private String chatCompletionId;


    /**
     * 全链路追踪ID
     */
    private String traceId;
    /**
     *  创建时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;


    /**
     *  创建人ID
     * */
    private String userId;

    /**
     *  创建人姓名
     * */
    private String createdByName;

    /**
     * 反馈状态（1:点赞 2:点踩）
     */
    private Integer feedbackStatus;

    /**
     * @see  com.wormhole.agent.enums.FeedbackCategory
     */
    private String feedbackTypes;


    public static UserChatMessageHistoryVO toVO(UserChatMessageEntity entity) {
        if (Objects.isNull(entity)) {
            return null;
        }
        UserChatMessageHistoryVO userChatMessageVO = new UserChatMessageHistoryVO();
        BeanUtils.copyProperties(entity, userChatMessageVO);
        userChatMessageVO.setParentMessageId(entity.getParentMessageId() == null ? "" : entity.getParentMessageId().toString());
        userChatMessageVO.setId(entity.getId().toString());
        userChatMessageVO.setUserId(entity.getCreatedBy());
        return userChatMessageVO;
    }

}
