package com.wormhole.agent.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/4/8
 * @Description:
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class DeviceInfoVO {

    /**
     * 用户集合
     */
    private List<String> userIds;

    /**
     * 当前用户id
     */
    private String userId;

    /**
     * 当前房间id
     */
    private String roomId;

    /**
     * 拨打方的房间号
     */
    private String roomCode;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 设备数量
     */
    private Integer size;

}
