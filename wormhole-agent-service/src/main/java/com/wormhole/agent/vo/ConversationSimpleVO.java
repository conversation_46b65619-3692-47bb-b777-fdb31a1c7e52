package com.wormhole.agent.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ConversationSimpleVO implements Serializable {

    private String conversationId;
    private String botCode;
    private String userId;
    private LocalDateTime createAt;
    private String createBy;
    private String title;
    private String source;
}
