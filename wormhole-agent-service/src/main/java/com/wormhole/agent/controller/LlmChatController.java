package com.wormhole.agent.controller;

import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.LlmChatParams;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.service.ChatService;
import jakarta.annotation.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

/**
 * 兼容openai的大模型调用协议
 *
 * <AUTHOR>
 * @version 2024/11/22
 */
@Controller
@RequestMapping(value = "/llm")
public class LlmChatController {

    @Resource
    private ChatService chatService;

    @ResponseBody
    @PostMapping(value = {"/chat/completions"}, produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> chatCompletions(@RequestBody LlmChatParams llmChatParams, @RequestHeader HttpHeaders httpHeaders) {
        ChatContext chatContext = ChatContext.builder()
                .chatParams(llmChatParams)
                .httpHeaders(httpHeaders)
                .isDebug(false)
                .chatType(ChatType.LLM)
                .build();
        Flux<ChatCompletions> flux = Flux.defer(() -> chatService.chatCompletions(chatContext));
        if (Boolean.TRUE.equals(llmChatParams.getStream())) {
            return ResponseEntity.ok().contentType(MediaType.TEXT_EVENT_STREAM).body(flux);
        }
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(flux.next());
    }

}