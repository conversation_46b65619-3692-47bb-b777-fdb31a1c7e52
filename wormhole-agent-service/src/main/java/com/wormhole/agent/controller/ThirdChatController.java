package com.wormhole.agent.controller;

import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.AgentChatParams;
import com.wormhole.agent.client.chat.params.LlmChatParams;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.service.ChatService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;

@RefreshScope
@RestController
@RequestMapping(value = "/v1/chat")
public class ThirdChatController {

    @Resource
    private ChatService chatService;
    @Value("${thrid.huamai.bot_code:bot_015c6f3150403493dfbae8efbe82a640}")
    private String botCode;
    @Value("${thrid.huamai.conversation_id:bf7be5136b55397e99d317116f5d69db}")
    private String conversationId;

    @ResponseBody
    @PostMapping(value = {"/completions/huamai"}, produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> rtcChatCompletions(@RequestHeader HttpHeaders httpHeaders,
                                          @RequestBody LlmChatParams chatParams) {
        AgentChatParams agentChatParams = new AgentChatParams();
        agentChatParams.setBotCode(botCode);
        agentChatParams.setConversationId(conversationId);
        List<OpenAiChatMessage> chatMessageList = chatParams.getMessages();
        OpenAiChatMessage openAiChatMessage = chatMessageList.get(chatMessageList.size() - 1);
        String content = openAiChatMessage.getContent();
        agentChatParams.setContent(content);
        ChatContext chatContext = ChatContext.builder()
                .chatParams(agentChatParams)
                .httpHeaders(httpHeaders)
                .isDebug(false)
                .chatType(ChatType.AGENT)
                .build();
        Flux<ChatCompletions> flux = chatService.chatCompletions(chatContext);
        return ResponseEntity.ok().body(flux);
    }

}