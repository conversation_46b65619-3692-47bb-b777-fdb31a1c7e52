package com.wormhole.agent.controller;

import com.wormhole.agent.client.chat.params.CreateConversationReq;
import com.wormhole.agent.client.chat.params.UserConversationVO;
import com.wormhole.agent.dto.SaveConversationDTO;
import com.wormhole.agent.query.*;
import com.wormhole.agent.service.UserConversationService;
import com.wormhole.agent.vo.ConversationSimpleVO;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.HeaderUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 用户会话管理控制器
 * 提供创建、删除、查询用户会话的接口
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/conversations")
public class UserConversationController {

    private final UserConversationService service;

    /**
     * 创建用户会话
     * @param req 创建会话请求对象，包含创建会话所需的信息
     * @return 返回包含创建结果的Mono<Result<UserConversationEntity>>
     */
    @PostMapping("create")
    public Mono<Result<UserConversationVO>> create(@RequestBody CreateConversationReq req) {
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                            SaveConversationDTO saveConversationDTO = service.buildSaveConversationDTO(req, headerInfo);
                            return service.createConversation(saveConversationDTO).flatMap(Result::success);
                        });
    }

    /**
     * 删除用户会话
     * @param delReq 删除会话请求对象，包含删除会话所需的信息
     * @return 返回包含删除结果的Mono<Result<Boolean>>
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody UserConversationDelReq delReq) {
        return service.deleteConversation(delReq).flatMap(Result::success);
    }
    /**
     * 刷新用户会话
     * @param
     * @return 返回包含删除结果的Mono<Result<Boolean>>
     */
    @PostMapping("/refresh")
    public Mono<Result<Boolean>> refresh(@RequestBody UserConversationRefreshReq req) {
        return service.refreshConversation(req).flatMap(Result::success);
    }

    /**
     * 查询用户会话列表
     * @param query 查询条件对象，包含分页和查询条件
     * @return 返回包含查询结果的Mono<Result<PageResult<UserConversationVO>>>
     */
    @PostMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<Result<PageResult<UserConversationVO>>> list(@RequestBody UserConversationQuery query) {
        return service.listConversations(query).flatMap(Result::success);
    }

    /**
     * 更新标题
     * @param userConversationUpdateReq     会话更新信息
     * @return  返回更新结果的Mono<Result<Boolean>>
     */
    @PostMapping("/update/title")
    public Mono<Result<Boolean>> updateTitle(@RequestBody UserConversationUpdateReq userConversationUpdateReq) {
        return service.updateConversation(userConversationUpdateReq).flatMap(Result::success);
    }


    @PostMapping("/admin/page")
    public  Mono<Result<PageResult<ConversationSimpleVO>>> conversationPage(@RequestBody ChatConversationPageReq pageReq) {
        return service.conversationPage(pageReq).flatMap(Result::success);
    }
}
