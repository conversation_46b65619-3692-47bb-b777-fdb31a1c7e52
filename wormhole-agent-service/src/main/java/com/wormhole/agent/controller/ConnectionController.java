package com.wormhole.agent.controller;

import com.wormhole.agent.query.HotelDeviceReq;
import com.wormhole.agent.response.DeviceInfoResp;
import com.wormhole.agent.service.ConnectionService;
import com.wormhole.agent.service.RtcUserService;
import com.wormhole.agent.vo.DeviceInfoVO;
import com.wormhole.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * @author: joker.liu
 * @date: 2025/3/17
 * @Description:
 */
@Slf4j
@RestController
@RequestMapping("/connection")
@RequiredArgsConstructor
public class ConnectionController {

    private final ConnectionService connectionService;

    private final RtcUserService rtcUserService;

    @PostMapping("/user/online_usable")
    public Mono<Result<DeviceInfoVO>> getOnlineUsableUser(@RequestBody HotelDeviceReq req) {
        return rtcUserService.getOnlineUsable(req).flatMap(Result::success);
    }

    @PostMapping("/user/in_room")
    public Mono<Result<DeviceInfoVO>> getInRoomUser(@RequestBody HotelDeviceReq req) {
        return rtcUserService.getInRoomUser(req)
                .flatMap(Result::success)
                .onErrorResume(e -> {
                    log.info("ROOM-FOUND-ERR", e);
                    return Result.failed("ROOM-FOUND-ERR", e.getMessage());
                });
    }

    @GetMapping("/call/transfer")
    public Mono<Result<Boolean>> callTransfer(@RequestParam("room_id") String roomId) {
        return rtcUserService.callTransfer(roomId).flatMap(Result::success);
    }

    @GetMapping("/call/add_hang_up_mark")
    public Mono<Result<Boolean>> addHangUpMark(@RequestParam("room_id") String roomId) {
        return rtcUserService.addHangUpMark(roomId).flatMap(Result::success)
                .onErrorResume(e -> {
                    log.info("ROOM-HANG-UP-ERR", e);
                    return Result.failed("ROOM-HANG-UP-ERR", e.getMessage());
                });
    }

}
