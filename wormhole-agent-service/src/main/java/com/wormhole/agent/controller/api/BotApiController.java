package com.wormhole.agent.controller.api;

import com.wormhole.agent.client.chat.params.query.BotHistoryQuery;
import com.wormhole.agent.client.chat.params.query.BotInfoQuery;
import com.wormhole.agent.client.chat.response.BotInfoResponse;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.query.BotQueryCondition;
import com.wormhole.agent.service.BotApiService;
import com.wormhole.agent.vo.BotCategoryVO;
import com.wormhole.agent.vo.OnlineBotListVO;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

@RestController
@RequestMapping(value = "/bot")
public class BotApiController {

    @Resource
    private BotApiService botApiService;

    @ResponseBody
    @PostMapping(value = {"/get_bot_info"})
    public Mono<BotInfoResponse> getBotInfo(@RequestBody BotInfoQuery botInfoQuery) {
        return botApiService.getBotInfo(botInfoQuery);
    }

    @ResponseBody
    @PostMapping(value = {"/get_history_message"})
    public Mono<List<OpenAiChatMessage>> getHistoryMessage(@RequestBody BotHistoryQuery query) {
        return  botApiService.getHistoryMessage(query);
    }

    @PostMapping("/get_online_bot_list")
    public Mono<Result<List<OnlineBotListVO>>> getOnlineBotList(@RequestBody BotQueryCondition queryCondition) {
        return botApiService.getOnlineBotList(queryCondition).flatMap(Result::success);
    }

    @GetMapping("/get_bot_category_list")
    public Mono<Result<List<BotCategoryVO>>> getBotCategoryList() {
        return botApiService.getBotCategoryList().flatMap(Result::success);
    }

}
