package com.wormhole.agent.controller;

import com.wormhole.agent.amap.AmapDemoService;
import com.wormhole.agent.amap.AmapPoiService;
import com.wormhole.agent.amap.qo.*;
import com.wormhole.agent.amap.vo.AdminAreaVO;
import com.wormhole.agent.amap.vo.NavigationInfoVO;
import com.wormhole.agent.amap.vo.PlaceAroundVO;
import com.wormhole.agent.amap.vo.ReGeoCodeVO;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.concurrent.ExecutionException;


@Slf4j
@RestController
@RequestMapping("/amap")
public class AmapController {
    @Resource
    private AmapPoiService amapPoiService;
    @Resource
    private AmapDemoService amapDemoService;

    @PostMapping("/query_place_around")
    public Mono<Result<String>> queryPlaceAround(@RequestBody POIQuery poiQuery)  {
        return amapDemoService.queryPlaceAroundSimple(poiQuery).flatMap(Result::success);
    }
    @PostMapping("/query_weather")
    public Mono<Result<String>> queryWeather(@RequestBody WeatherInfoQO weatherInfoQO)  {
        return amapPoiService.queryWeather(weatherInfoQO).flatMap(Result::success);
    }

    @PostMapping("/place/aroundF")
    public PlaceAroundVO queryPlaceAround() throws ExecutionException, InterruptedException {
        return amapDemoService.queryPlaceAroundForFood();
    }

    /**
     * 查询导航信息
     * @param trafficType 交通类型（如驾车、步行、骑行等）
     */
    @PostMapping("/navigation/info")
    public Mono<NavigationInfoVO> queryNavigationInfo(
            @RequestBody NavigationSearchQO qo,
            @RequestParam("trafficType") Integer trafficType) {
        return amapPoiService.queryNavigationInfo(qo, trafficType);
    }

    /**
     * 查询行政区域信息
     */
    @PostMapping("/admin/area")
    public Mono<AdminAreaVO> queryAdminAreaInfo(@RequestBody AdminAreaQO qo) {
        return amapPoiService.queryAdminAreaInfo(qo);
    }

    /**
     * 根据关键字匹配地点
     */
    @PostMapping("/place/keyword")
    public Mono<PlaceAroundVO> queryKeywordMatchPlace(@RequestBody KeywordMatchQO qo) {
        return amapPoiService.queryKeywordMatchPlace(qo);
    }

    /**
     * 查询逆地理编码信息
     */
    @PostMapping("/geo/recode")
    public Mono<ReGeoCodeVO> queryReGeoCode(@RequestBody ReGeoCodeQO qo) {
        return amapPoiService.queryReGeoCode(qo);
    }
}

