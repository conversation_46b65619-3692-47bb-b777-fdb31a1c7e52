package com.wormhole.agent.controller;

import com.wormhole.agent.tool.mcp.McpToolProvider;
import com.wormhole.agent.tool.mcp.model.McpClientInfo;
import com.wormhole.agent.tool.mcp.service.McpHealthChecker;
import com.wormhole.agent.tool.mcp.service.McpServiceManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * MCP连接监控控制器
 * 提供实时状态查询和连接健康监控接口
 *
 * <AUTHOR>
 * @version 2025-08-19
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/mcp")
@RequiredArgsConstructor
public class McpConnectionMonitorController {

    private final McpServiceManager mcpServiceManager;
    private final McpHealthChecker mcpHealthChecker;
    private final McpToolProvider mcpToolProvider;
    
    // 时间格式化器
    private static final DateTimeFormatter TIME_FORMATTER = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.systemDefault());

    /**
     * 获取所有MCP连接状态
     */
    @GetMapping("/connections/status")
    public Mono<Map<String, Object>> getConnectionsStatus() {
        return Mono.fromCallable(() -> {
            Map<String, McpClientInfo> clients = mcpServiceManager.getAllClients();
            
            Map<String, Object> result = new HashMap<>();
            result.put("timestamp", formatTime(Instant.now()));
            result.put("totalConnections", clients.size());
            
            Map<String, ConnectionStatus> connections = new HashMap<>();
            int healthyCount = 0;
            int unhealthyCount = 0;
            
            for (Map.Entry<String, McpClientInfo> entry : clients.entrySet()) {
                String clientName = entry.getKey();
                McpClientInfo clientInfo = entry.getValue();
                
                ConnectionStatus status = buildConnectionStatus(clientInfo);
                connections.put(clientName, status);
                
                if (status.isHealthy()) {
                    healthyCount++;
                } else {
                    unhealthyCount++;
                }
            }
            
            result.put("connections", connections);
            result.put("healthyCount", healthyCount);
            result.put("unhealthyCount", unhealthyCount);
            
            return result;
        });
    }

    /**
     * 获取指定连接的详细状态
     */
    @GetMapping("/connections/{clientName}/status")
    public Mono<ConnectionStatus> getConnectionStatus(@PathVariable String clientName) {
        return Mono.fromCallable(() -> {
            McpClientInfo clientInfo = mcpServiceManager.getClient(clientName);
            if (clientInfo == null) {
                throw new RuntimeException("MCP客户端不存在: " + clientName);
            }
            
            return buildConnectionStatus(clientInfo);
        });
    }

    /**
     * 执行连接健康检查
     */
    @PostMapping("/connections/{clientName}/health-check")
    public Mono<McpHealthChecker.HealthCheckResult> performHealthCheck(@PathVariable String clientName) {
        return Mono.fromCallable(() -> {
            McpClientInfo clientInfo = mcpServiceManager.getClient(clientName);
            if (clientInfo == null) {
                throw new RuntimeException("MCP客户端不存在: " + clientName);
            }
            
            log.info("手动触发健康检查: {}", clientName);
            
            CompletableFuture<McpHealthChecker.HealthCheckResult> future = 
                mcpHealthChecker.checkHealth(clientInfo);
            
            try {
                return future.get(); // 同步等待结果
            } catch (Exception e) {
                log.error("健康检查执行失败: {}", clientName, e);
                return McpHealthChecker.HealthCheckResult.unknownError(
                    "健康检查执行失败: " + e.getMessage());
            }
        });
    }

    /**
     * 批量执行健康检查
     */
    @PostMapping("/connections/health-check")
    public Mono<Map<String, McpHealthChecker.HealthCheckResult>> performBatchHealthCheck() {
        return Mono.fromCallable(() -> {
            Map<String, McpClientInfo> clients = mcpServiceManager.getAllClients();
            Map<String, McpHealthChecker.HealthCheckResult> results = new HashMap<>();
            
            log.info("批量触发健康检查，客户端数量: {}", clients.size());
            
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (Map.Entry<String, McpClientInfo> entry : clients.entrySet()) {
                String clientName = entry.getKey();
                McpClientInfo clientInfo = entry.getValue();
                
                CompletableFuture<Void> future = mcpHealthChecker.checkHealth(clientInfo)
                    .thenAccept(result -> results.put(clientName, result))
                    .exceptionally(throwable -> {
                        log.error("客户端 {} 健康检查失败", clientName, throwable);
                        results.put(clientName, McpHealthChecker.HealthCheckResult.unknownError(
                            "健康检查失败: " + throwable.getMessage()));
                        return null;
                    });
                
                futures.add(future);
            }
            
            try {
                // 等待所有健康检查完成
                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
            } catch (Exception e) {
                log.error("批量健康检查执行失败", e);
            }
            
            return results;
        });
    }

    /**
     * 获取MCP初始化状态（性能优化后的新接口）
     */
    @GetMapping("/initialization/status")
    public Mono<Map<String, Object>> getInitializationStatus() {
        return Mono.fromCallable(() -> {
            Map<String, Object> result = new HashMap<>();
            
            // 基本状态信息
            result.put("timestamp", formatTime(Instant.now()));
            result.put("status", mcpToolProvider.getInitializationStatus());
            result.put("initialized", mcpToolProvider.isInitialized());
            
            // 详细状态信息
            McpToolProvider.InitializationStatusDetail detail = mcpToolProvider.getStatusDetail();
            Map<String, Object> statusDetail = new HashMap<>();
            statusDetail.put("startTime", formatTime(Instant.ofEpochMilli(detail.getStartTime())));
            statusDetail.put("endTime", detail.getEndTime() > 0 ? formatTime(Instant.ofEpochMilli(detail.getEndTime())) : null);
            statusDetail.put("durationMs", detail.getDurationMs());
            statusDetail.put("totalConfigs", detail.getTotalConfigs());
            statusDetail.put("successfulClients", detail.getSuccessfulClients());
            statusDetail.put("failedClients", detail.getFailedClients());
            statusDetail.put("successRate", String.format("%.1f%%", detail.getSuccessRate() * 100));
            statusDetail.put("configLoaded", detail.isConfigLoaded());
            statusDetail.put("clientsCreated", detail.isClientsCreated());
            statusDetail.put("lastError", detail.getLastError());
            statusDetail.put("summary", detail.getSummary());
            
            result.put("detail", statusDetail);
            
            // 性能指标
            Map<String, Object> performance = new HashMap<>();
            performance.put("startupTimeMs", detail.getDurationMs());
            performance.put("clientCreationMode", "PARALLEL");
            performance.put("toolDiscoveryMode", "LAZY_LOADING");
            performance.put("configLoadingMode", "ASYNC");
            
            // 计算预计性能提升
            if (detail.getDurationMs() > 0) {
                // 假设之前的顺序模式需要的时间（估算）
                long estimatedSequentialTime = detail.getSuccessfulClients() * 3000; // 每个客户端3秒
                long actualTime = detail.getDurationMs();
                if (estimatedSequentialTime > actualTime) {
                    double improvement = ((double)(estimatedSequentialTime - actualTime) / estimatedSequentialTime) * 100;
                    performance.put("estimatedImprovementPercent", String.format("%.1f%%", improvement));
                    performance.put("estimatedTimeSavedMs", estimatedSequentialTime - actualTime);
                }
            }
            
            result.put("performance", performance);
            
            return result;
        });
    }

    /**
     * 获取连接告警信息
     */
    @GetMapping("/connections/alerts")
    public Mono<List<ConnectionAlert>> getConnectionAlerts() {
        return Mono.fromCallable(() -> {
            Map<String, McpClientInfo> clients = mcpServiceManager.getAllClients();
            List<ConnectionAlert> alerts = new ArrayList<>();
            
            for (Map.Entry<String, McpClientInfo> entry : clients.entrySet()) {
                String clientName = entry.getKey();
                McpClientInfo clientInfo = entry.getValue();
                
                // 检查各种告警条件
                ConnectionAlert alert = checkForAlerts(clientName, clientInfo);
                if (alert != null) {
                    alerts.add(alert);
                }
            }
            
            // 按严重程度排序
            alerts.sort((a, b) -> b.getSeverity().compareTo(a.getSeverity()));
            
            return alerts;
        });
    }

    /**
     * 构建连接状态信息
     */
    private ConnectionStatus buildConnectionStatus(McpClientInfo clientInfo) {
        return ConnectionStatus.builder()
            .clientName(clientInfo.getClientName())
            .serverUrl(clientInfo.getConfig().getUrl())
            .transport(clientInfo.getConfig().getTransport())
            .status(clientInfo.getStatus().getStatus())
            .statusMessage(clientInfo.getStatus().getStatusMessage())
            .isHealthy(clientInfo.getStatus().getStatus().name().equals("ACTIVE"))
            .lastHealthCheck(clientInfo.getStatus().getLastHealthCheck())
            .lastActiveTime(clientInfo.getLastActiveTime())
            .toolCount(clientInfo.getToolCount())
            .createdTime(clientInfo.getLastActiveTime())
            // 添加格式化的时间字段
            .lastHealthCheckFormatted(formatTime(clientInfo.getStatus().getLastHealthCheck()))
            .lastActiveTimeFormatted(formatTime(clientInfo.getLastActiveTime()))
            .createdTimeFormatted(formatTime(clientInfo.getLastActiveTime()))
            .build();
    }

    /**
     * 检查连接告警
     */
    private ConnectionAlert checkForAlerts(String clientName, McpClientInfo clientInfo) {
        Instant now = Instant.now();
        
        // 检查连接状态
        if (!clientInfo.getStatus().getStatus().name().equals("ACTIVE")) {
            return ConnectionAlert.builder()
                .clientName(clientName)
                .alertType("CONNECTION_ERROR")
                .severity(AlertSeverity.HIGH)
                .message("连接异常: " + clientInfo.getStatus().getStatusMessage())
                .timestamp(now)
                .timestampFormatted(formatTime(now))
                .build();
        }
        
        // 检查最后健康检查时间
        Instant lastHealthCheck = clientInfo.getStatus().getLastHealthCheck();
        if (lastHealthCheck != null) {
            long minutesSinceLastCheck = java.time.Duration.between(lastHealthCheck, now).toMinutes();
            
            if (minutesSinceLastCheck > 30) { // 30分钟未检查
                return ConnectionAlert.builder()
                    .clientName(clientName)
                    .alertType("HEALTH_CHECK_OVERDUE")
                    .severity(AlertSeverity.MEDIUM)
                    .message(String.format("健康检查过期: 已超过 %d 分钟未进行健康检查", minutesSinceLastCheck))
                    .timestamp(now)
                    .timestampFormatted(formatTime(now))
                    .build();
            }
        }
        
        // 检查最后活跃时间
        Instant lastActiveTime = clientInfo.getLastActiveTime();
        if (lastActiveTime != null) {
            long hoursSinceLastActive = java.time.Duration.between(lastActiveTime, now).toHours();
            
            if (hoursSinceLastActive > 24) { // 24小时未活跃
                return ConnectionAlert.builder()
                    .clientName(clientName)
                    .alertType("INACTIVE_CONNECTION")
                    .severity(AlertSeverity.LOW)
                    .message(String.format("连接长时间未活跃: 已超过 %d 小时未使用", hoursSinceLastActive))
                    .timestamp(now)
                    .timestampFormatted(formatTime(now))
                    .build();
            }
        }
        
        return null;
    }

    /**
     * 格式化时间
     */
    private String formatTime(Instant instant) {
        if (instant == null) {
            return null;
        }
        return TIME_FORMATTER.format(instant);
    }

    /**
     * 连接状态信息
     */
    @lombok.Data
    @lombok.Builder
    public static class ConnectionStatus {
        private String clientName;
        private String serverUrl;
        private String transport;
        private Object status;
        private String statusMessage;
        private boolean isHealthy;
        private Instant lastHealthCheck;
        private Instant lastActiveTime;
        private int toolCount;
        private Instant createdTime;
        
        // 格式化的时间字段
        private String lastHealthCheckFormatted;
        private String lastActiveTimeFormatted;
        private String createdTimeFormatted;
    }

    /**
     * 连接告警信息
     */
    @lombok.Data
    @lombok.Builder
    public static class ConnectionAlert {
        private String clientName;
        private String alertType;
        private AlertSeverity severity;
        private String message;
        private Instant timestamp;
        
        // 格式化的时间字段
        private String timestampFormatted;
    }

    /**
     * 告警严重程度
     */
    public enum AlertSeverity {
        LOW(1, "低"),
        MEDIUM(2, "中"),
        HIGH(3, "高"),
        CRITICAL(4, "严重");
        
        private final int level;
        private final String description;
        
        AlertSeverity(int level, String description) {
            this.level = level;
            this.description = description;
        }
        
        public int getLevel() {
            return level;
        }
        
        public String getDescription() {
            return description;
        }
    }
}