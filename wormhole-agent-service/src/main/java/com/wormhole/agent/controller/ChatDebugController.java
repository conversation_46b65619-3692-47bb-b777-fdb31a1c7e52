package com.wormhole.agent.controller;

import com.wormhole.agent.client.chat.params.WorkflowParams;
import com.wormhole.agent.client.chat.params.query.BotDebugInfoQuery;
import com.wormhole.agent.client.chat.params.query.PluginExecuteReq;
import com.wormhole.agent.client.chat.params.query.RtcRoomInfoQuery;
import com.wormhole.agent.client.chat.response.BotDebugInfoResponse;
import com.wormhole.agent.client.chat.response.FlameGraph;
import com.wormhole.agent.log.service.ChatAccessLogService;
import com.wormhole.agent.log.service.RtcRoomMetricsService;
import com.wormhole.agent.plugin.entity.PluginToolUniqueKey;
import com.wormhole.agent.plugin.service.PluginService;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.service.WorkflowService;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.constant.Constants;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.apache.commons.lang.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.List;
import java.util.Map;

@Controller
@RequestMapping(value = "/debug")
public class ChatDebugController {
    @Resource
    private WorkflowService workflowService;
    @Resource
    private ChatAccessLogService chatAccessLogService;
    @Resource
    private RtcRoomMetricsService rtcRoomMetricsService;
    @Resource
    private PluginService pluginService;

    @ResponseBody
    @PostMapping(value = {"/workflow"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<Object> workflowDebug(@RequestBody WorkflowParams workflowParams) {
        // 1. 构建工作流上下文
        String debugCode = workflowParams.isDebug()
                ? workflowParams.getWorkflowCode() + Constants.DEBUG_SUFFIX
                : workflowParams.getWorkflowCode();

        WorkflowContext workflowContext = WorkflowContext.builder()
                .workflowCode(debugCode)
                .initialInput(workflowParams.getInitialInput())
                .isDebug(workflowParams.isDebug())
                .sinks(Sinks.many().multicast().onBackpressureBuffer())
                .build();
        // 2. 根据是否有节点ID决定执行方式
        Flux<WorkflowContext> flux = StringUtils.isNotBlank(workflowParams.getNodeId())
                ? Flux.defer(() -> workflowService.executeWorkflowNode(workflowContext, workflowParams.getNodeId()))
                : Flux.defer(() -> workflowService.executeWorkflow(workflowContext));
        // 3. 处理结果并添加错误处理
        return flux.next().map(WorkflowUtils::getWorkflowSimpleList);
    }


    @ResponseBody
    @PostMapping(value = {"/bot_debug_list"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<Result<List<BotDebugInfoResponse>>> botDebugList(@RequestBody BotDebugInfoQuery botDebugInfoQuery) {
        return chatAccessLogService.query(botDebugInfoQuery).flatMap(Result::success);
    }

    @ResponseBody
    @PostMapping(value = {"/rtc_flame_graph"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<Result<List<FlameGraph>>> rtcFlameGraph(@RequestBody RtcRoomInfoQuery query) {
        return rtcRoomMetricsService.queryFlame(query).flatMap(Result::success);
    }
    @ResponseBody
    @PostMapping(value = {"/rtc_flame_graph_to_speedscope"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<Result<List<String>>> rtcFlameGraphToSpeedscope(@RequestBody RtcRoomInfoQuery query) {
        return rtcRoomMetricsService.queryFlameToSpeedscope(query).flatMap(Result::success);
    }


    @ResponseBody
    @PostMapping(value = {"/plugin_execute"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<Map<String,Object>> pluginExecute(@RequestBody PluginExecuteReq pluginExecuteReq) {
        PluginToolUniqueKey uniqueKey = new PluginToolUniqueKey();
        uniqueKey.setPluginCode(pluginExecuteReq.getPluginCode());
        uniqueKey.setPluginToolCode(pluginExecuteReq.getPluginToolCode());
        return pluginService.execute(uniqueKey,pluginExecuteReq.getInputValueMap());
    }

}
