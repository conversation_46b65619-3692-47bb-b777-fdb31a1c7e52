package com.wormhole.agent.controller;

import com.wormhole.agent.query.GenericRedisBatchRequest;
import com.wormhole.agent.query.GenericRedisRequest;
import com.wormhole.agent.query.HotelInfoRedisReq;
import com.wormhole.agent.service.GenericRedisService;
import com.wormhole.agent.service.HotelRedisService;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/redis")
public class RedisController {

    @Resource
    private HotelRedisService hotelRedisService;

    @Resource
    private GenericRedisService genericRedisService;
    /**
     * 获取单个酒店信息 - 异常时返回空字符串
     */
    @PostMapping("/hotel/get")
    public Mono<Result<String>> getHotelInfo(@RequestBody HotelInfoRedisReq req) {
        return hotelRedisService.getHotelInfo(req.getHotelCode(), req.getCategory())
                .flatMap(Result::success)
                .defaultIfEmpty(Result.successDirect(""))
                .onErrorResume(throwable -> {
                    log.warn("获取酒店信息异常，返回默认值, hotelCode: {}, category: {}, error: {}",
                            req.getHotelCode(), req.getCategory(), throwable.getMessage());
                    return Mono.just(Result.successDirect(""));
                });
    }

    /**
     * 设置单个酒店信息 - 异常时返回false
     */
    @PostMapping("/hotel/set")
    public Mono<Result<Boolean>> setHotelInfo(@RequestBody HotelInfoRedisReq req) {
        return hotelRedisService.setHotelInfo(
                        req.getHotelCode(),
                        req.getCategory(),
                        req.getInfo(),
                        req.getExpireSeconds())
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("设置酒店信息异常，返回默认值, hotelCode: {}, category: {}, error: {}",
                            req.getHotelCode(), req.getCategory(), throwable.getMessage());
                    return Mono.just(Result.successDirect(false));
                });
    }

    /**
     * 批量获取酒店信息 - 异常时返回空Map
     */
    @PostMapping("/hotel/batch_get")
    public Mono<Result<Map<String, String>>> batchGetHotelInfo(@RequestBody List<HotelInfoRedisReq> reqList) {
        return hotelRedisService.batchGetHotelInfo(reqList)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("批量获取酒店信息异常，返回默认值, reqList size: {}, error: {}",
                            reqList.size(), throwable.getMessage());
                    return Mono.just(Result.successDirect(Collections.emptyMap()));
                });
    }

    /**
     * 批量设置酒店信息 - 异常时返回false
     */
    @PostMapping("/hotel/batch_set")
    public Mono<Result<Boolean>> batchSetHotelInfo(@RequestBody List<HotelInfoRedisReq> reqList) {
        return hotelRedisService.batchSetHotelInfo(reqList)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("批量设置酒店信息异常，返回默认值, reqList size: {}, error: {}",
                            reqList.size(), throwable.getMessage());
                    return Mono.just(Result.successDirect(false));
                });
    }

    /**
     * 删除酒店信息 - 异常时返回false
     */
    @PostMapping("/hotel/delete")
    public Mono<Result<Boolean>> deleteHotelInfo(@RequestBody HotelInfoRedisReq req) {
        return hotelRedisService.deleteHotelInfo(req.getHotelCode(), req.getCategory())
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("删除酒店信息异常，返回默认值, hotelCode: {}, category: {}, error: {}",
                            req.getHotelCode(), req.getCategory(), throwable.getMessage());
                    return Mono.just(Result.successDirect(false));
                });
    }

    /**
     * 根据酒店代码查询所有相关信息
     */
    @GetMapping("/keys/hotel")
    public Mono<Result<List<String>>> findKeysByHotel(@RequestParam("hotelCode") String hotelCode) {
        return hotelRedisService.findKeys(hotelCode + ":*")
                .collectList()
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("查找酒店信息键异常，返回默认值, hotelCode: {}, error: {}",
                            hotelCode, throwable.getMessage());
                    return Mono.just(Result.successDirect(Collections.emptyList()));
                });
    }

    /**
     * 根据信息类型查询所有相关酒店
     */
    @GetMapping("/keys/category")
    public Mono<Result<List<String>>> findKeysByCategory(@RequestParam("category") String category) {
        return hotelRedisService.findKeys("*:" + category)
                .collectList()
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("查找分类信息键异常，返回默认值, category: {}, error: {}",
                            category, throwable.getMessage());
                    return Mono.just(Result.successDirect(Collections.emptyList()));
                });
    }

    /**
     * 获取单个键值 - 异常时返回空字符串
     */
    @GetMapping("/generic/get")
    public Mono<Result<String>> getValue(@RequestParam("key") String key) {
        return genericRedisService.getValue(key)
                .flatMap(Result::success)
                .defaultIfEmpty(Result.successDirect(""))
                .onErrorResume(throwable -> {
                    log.warn("获取键值异常，返回默认值, key: {}, error: {}", key, throwable.getMessage());
                    return Mono.just(Result.successDirect(""));
                });
    }

    /**
     * 设置单个键值 - 异常时返回false
     */
    @PostMapping("/generic/set")
    public Mono<Result<Boolean>> setValue(@RequestBody GenericRedisRequest req) {
        return genericRedisService.setValue(req.getKey(), req.getValue(), req.getExpireSeconds())
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("设置键值异常，返回默认值, key: {}, error: {}", req.getKey(), throwable.getMessage());
                    return Mono.just(Result.successDirect(false));
                });
    }

    /**
     * 删除单个键 - 异常时返回false
     */
    @DeleteMapping("/generic/delete")
    public Mono<Result<Boolean>> deleteKey(@RequestParam("key") String key) {
        return genericRedisService.deleteKey(key)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("删除键异常，返回默认值, key: {}, error: {}", key, throwable.getMessage());
                    return Mono.just(Result.successDirect(false));
                });
    }

    /**
     * 批量获取键值 - 异常时返回空Map
     */
    @PostMapping("/generic/batch_get")
    public Mono<Result<Map<String, String>>> batchGetValues(@RequestBody List<String> keys) {
        return genericRedisService.multiGet(keys)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("批量获取键值异常，返回默认值, keys size: {}, error: {}",
                            keys.size(), throwable.getMessage());
                    return Mono.just(Result.successDirect(Collections.emptyMap()));
                });
    }

    /**
     * 批量设置键值 - 异常时返回false
     */
    @PostMapping("/generic/batch_set")
    public Mono<Result<Boolean>> batchSetValues(@RequestBody GenericRedisBatchRequest req) {
        return genericRedisService.multiSet(req.getKeyValueMap(), req.getExpiryMap())
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("批量设置键值异常，返回默认值, keys size: {}, error: {}",
                            req.getKeyValueMap().size(), throwable.getMessage());
                    return Mono.just(Result.successDirect(false));
                });
    }

    /**
     * 查找特定模式的键
     */
    @GetMapping("/generic/keys")
    public Mono<Result<List<String>>> findKeys(@RequestParam("pattern") String pattern) {
        return genericRedisService.findKeys(pattern)
                .collectList()
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("查找键异常，返回默认值, pattern: {}, error: {}", pattern, throwable.getMessage());
                    return Mono.just(Result.successDirect(Collections.emptyList()));
                });
    }

    /**
     * 检查键是否存在
     */
    @GetMapping("/generic/exists")
    public Mono<Result<Boolean>> hasKey(@RequestParam("key") String key) {
        return genericRedisService.hasKey(key)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("检查键存在异常，返回默认值, key: {}, error: {}", key, throwable.getMessage());
                    return Mono.just(Result.successDirect(false));
                });
    }

    /**
     * 设置键的过期时间
     */
    @PostMapping("/generic/expire")
    public Mono<Result<Boolean>> setExpire(@RequestParam("key") String key,
                                           @RequestParam("seconds") long seconds) {
        return genericRedisService.expire(key, seconds)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("设置过期时间异常，返回默认值, key: {}, seconds: {}, error: {}",
                            key, seconds, throwable.getMessage());
                    return Mono.just(Result.successDirect(false));
                });
    }

    /**
     * 获取键的剩余过期时间
     */
    @GetMapping("/generic/ttl")
    public Mono<Result<Long>> getExpire(@RequestParam("key") String key) {
        return genericRedisService.getExpire(key)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.warn("获取过期时间异常，返回默认值, key: {}, error: {}", key, throwable.getMessage());
                    return Mono.just(Result.successDirect(-1L));
                });
    }
}
