package com.wormhole.agent.controller;

import cn.hutool.core.date.DateUtil;
import com.google.common.base.Preconditions;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.AgentChatParams;
import com.wormhole.agent.client.chat.params.AllChatParams;
import com.wormhole.agent.client.chat.params.LlmChatParams;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.rtc.consts.CallTimeoutEnums;
import com.wormhole.agent.rtc.message.CallOvertimeMessage;
import com.wormhole.agent.service.ChatService;
import com.wormhole.agent.util.TransitionPhraseUtil;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.event.SubtitleEvent;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.Result;
import com.wormhole.common.result.ResultCode;
import com.wormhole.mq.producer.ReactiveMessageSender;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@RestController
@RequestMapping(value = "/chat")
public class ChatController {

    @Resource
    private ChatService chatService;

    @Resource
    private ReactiveMessageSender reactiveMessageSender;

    @Value("${rocket.topic.call-overtime-delay-message:call_overtime_delay_message}")
    public String CALL_OVERTIME_DELAY_MESSAGE;

    @GetMapping("/random_transition")
    public Mono<Result<String>> randomTransition() {
        return Result.success(TransitionPhraseUtil.getRandomTransitionPhrase());
    }
    @ResponseBody
    @PostMapping(value = {"/completions"}, produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> chatCompletions(@RequestBody AllChatParams chatParams, @RequestHeader HttpHeaders httpHeaders) {
        ChatContext chatContext = buildChatContext(chatParams, httpHeaders);
        Flux<ChatCompletions> flux = chatService.chatCompletions(chatContext);
        return ResponseEntity.ok().body(flux);
    }


    @ResponseBody
    @PostMapping(value = {"/rtc/completions"}, produces = {MediaType.TEXT_EVENT_STREAM_VALUE, MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Object> rtcChatCompletions(@RequestHeader HttpHeaders httpHeaders,
                                          @RequestParam(value = "bot_code") String botCode,
                                          @RequestParam(value = "conversation_id")  String conversationId,
                                          @RequestParam(value = "hotel_code",required = false)  String hotelCode,
                                          @RequestParam(value = "device_id",required = false)  String deviceId,
                                          @RequestParam(value = "room_no",required = false)  String roomNo,
                                          @RequestParam(value = "position_name") String positionName,
                                          @RequestParam(value = "rtc_room_id", required = false) String rtcRoomId,
                                          @RequestParam(value = "client_type", required = false) String clientType,
                                          @RequestBody LlmChatParams chatParams) {
        Preconditions.checkArgument(StringUtils.isNotBlank(botCode), "botCode 不能为空");
        Preconditions.checkArgument(StringUtils.isNotBlank(conversationId), "conversationId 不能为空");
        AgentChatParams agentChatParams = new AgentChatParams();
        agentChatParams.setBotCode(botCode);
        agentChatParams.setConversationId(conversationId);
        List<OpenAiChatMessage> chatMessageList = chatParams.getMessages();
        OpenAiChatMessage openAiChatMessage = chatMessageList.get(chatMessageList.size() - 1);
        String content = openAiChatMessage.getContent();
        agentChatParams.setContent(content);
        roomNo = URLDecoder.decode(roomNo, StandardCharsets.UTF_8);
        ChatContext chatContext = ChatContext.builder()
                .chatParams(agentChatParams)
                .httpHeaders(httpHeaders)
                .roomNo(roomNo)
                .positionCode(roomNo)
                .positionName(positionName)
                .hotelCode(hotelCode)
                .deviceId(deviceId)
                .isDebug(false)
                .chatType(ChatType.AGENT)
                .rtcRoomId(rtcRoomId)
                .clientType(clientType)
                .build();
        Flux<ChatCompletions> flux =
                sendDelayMessage(rtcRoomId).thenMany(chatService.chatCompletions(chatContext));
        return ResponseEntity.ok().body(flux);
    }



    private ChatContext buildChatContext(AllChatParams chatParams, HttpHeaders httpHeaders) {
        if (StringUtils.isBlank(chatParams.getBotCode())) {
            LlmChatParams llmChatParams = new LlmChatParams();
            BeanUtils.copyProperties(chatParams, llmChatParams);
            return ChatContext.builder()
                    .chatParams(llmChatParams)
                    .httpHeaders(httpHeaders)
                    .isDebug(chatParams.isDebug())
                    .chatType(ChatType.LLM)
                    .build();
        } else {
            Preconditions.checkArgument(StringUtils.isNotBlank(chatParams.getConversationId()), "conversationId 不能为空");
            AgentChatParams agentChatParams = new AgentChatParams();
            BeanUtils.copyProperties(chatParams, agentChatParams);
            return ChatContext.builder()
                    .chatParams(agentChatParams)
                    .httpHeaders(httpHeaders)
                    .isDebug(chatParams.isDebug())
                    .chatType(ChatType.AGENT)
                    .build();
        }
    }

    private Mono<Void> sendDelayMessage(String rtcRoomId) {
        MessageBody messageBody = new MessageBody();
        messageBody.setAction(CallTimeoutEnums.CALL_AI_CHAT_DEAL.getType());
        messageBody.setTimestamp(DateUtil.current() + "");

        CallOvertimeMessage callOvertimeMessage = new CallOvertimeMessage();
        callOvertimeMessage.setRtcRoomId(rtcRoomId);
        messageBody.setData(callOvertimeMessage);
        return reactiveMessageSender.sendMessage(CALL_OVERTIME_DELAY_MESSAGE, messageBody)
                .onErrorResume(e -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, "Failed to send subtitle message")))
                .then();
    }
}