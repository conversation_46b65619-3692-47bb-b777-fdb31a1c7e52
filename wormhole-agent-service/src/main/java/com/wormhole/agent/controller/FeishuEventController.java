package com.wormhole.agent.controller;

import com.wormhole.agent.feishu.service.FeishuEventService;
import com.wormhole.common.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


/**
 * <AUTHOR>
 * @date 2024/11/19 15:21
 */
@Slf4j
@RestController
@RequestMapping("/feishu")
public class FeishuEventController {

    @Autowired
    private FeishuEventService feishuEventService;

    @RequestMapping("event/callback")
    public Mono<ResponseEntity<String>> callback(@RequestHeader HttpHeaders httpHeaders, @RequestBody String eventString) {
        log.info("event req,header:{},body:{}", JacksonUtils.writeValueAsString(httpHeaders), eventString);
        return feishuEventService.getEntityMono(httpHeaders, eventString);
    }

}
