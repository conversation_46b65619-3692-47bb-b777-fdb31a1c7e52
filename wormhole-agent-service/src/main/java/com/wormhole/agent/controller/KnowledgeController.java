package com.wormhole.agent.controller;


import com.wormhole.agent.ai.core.rerank.RerankService;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.ai.siliconflow.model.rerank.SiliconflowRerankParams;
import com.wormhole.agent.core.model.ModelProviderEnum;
import com.wormhole.agent.core.rerank.RerankContext;
import com.wormhole.agent.core.rerank.RerankParams;
import com.wormhole.agent.core.rerank.RerankResponse;
import com.wormhole.agent.knowledge.params.KnowledgeSearchParams;
import com.wormhole.agent.knowledge.search.KnowledgeSearchContext;
import com.wormhole.agent.knowledge.search.KnowledgeSearchService;
import com.wormhole.agent.query.KnowledgeRerankReq;
import com.wormhole.agent.client.chat.params.KnowledgeSearchReq;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.Result;
import com.wormhole.common.result.ResultCode;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/knowledge")
public class KnowledgeController {

    @Resource
    private KnowledgeSearchService knowledgeSearchService;
    @Resource
    private RerankService rerankService;

    /**
     * 知识库节点对应的搜索能力
     * @param req
     * @return
     */
    @ResponseBody
    @PostMapping(value = {"/search"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<Result<KnowledgeSearchContext>> search(@RequestBody KnowledgeSearchReq req) {
        KnowledgeSearchContext searchContext = KnowledgeSearchContext.builder()
                .searchInput(req.getSearchInput())
                .knowledgeSearchParams(req.getKnowledgeSearchParams())
                .recentMessageList(req.getRecentMessageList())
                .build();
        return knowledgeSearchService.search(searchContext).flatMap(Result::success);
    }

    /**
     * 重排序接口
     * @param req
     * @return
     */
    @ResponseBody
    @PostMapping(value = {"/rerank"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<Result<RerankResponse>> rerank(@RequestBody KnowledgeRerankReq req) {
        KnowledgeSearchParams.RerankConfig rerankConfig = req.getRerankConfig();
        UnifiedModelEnum unifiedModelEnum = UnifiedModelEnum.findByModel(rerankConfig.getModel(), rerankConfig.getModelProvider());
        RerankParams rerankParams;
        if (ModelProviderEnum.JINA.getProvider().equals(unifiedModelEnum.getProvider())) {
            rerankParams = RerankParams.builder()
                    .model(rerankConfig.getModel())
                    .topN(rerankConfig.getTopN())
                    .build();
        } else if (ModelProviderEnum.SILICONFLOW.getProvider().equals(unifiedModelEnum.getProvider())) {
            rerankParams = SiliconflowRerankParams.builder()
                    .model(rerankConfig.getModel())
                    .topN(rerankConfig.getTopN())
                    .returnDocuments(rerankConfig.getReturnDocuments())
                    .maxChunksPerDoc(rerankConfig.getMaxChunksPerDoc())
                    .overlapTokens(rerankConfig.getOverlapTokens())
                    .build();
        } else {
            throw new BusinessException(ResultCode.NOT_IMPLEMENTED);
        }
        rerankParams.setQuery(req.getQuery());
        rerankParams.setDocuments(req.getDocuments());

        RerankContext rerankContext = RerankContext.builder()
                .model(unifiedModelEnum.getModel())
                .provider(unifiedModelEnum.getProvider())
                .rerankParams(rerankParams)
                .build();
        return rerankService.rerank(rerankContext).flatMap(Result::success);
    }


}
