package com.wormhole.agent.controller;

import com.wormhole.agent.ai.core.embedding.EmbeddingService;
import jakarta.annotation.Resource;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * EmbeddingsController
 *
 * <AUTHOR>
 * @version 2024/12/17
 */
@RestController
@RequestMapping(value = "/embeddings")
public class EmbeddingsController {

    @Resource
    private EmbeddingService embeddingService;

    /**
     * 文本转向量
     *
     * @param body
     * @return
     */
    @ResponseBody
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<List<Float>> create(@RequestBody String body) {
        return embeddingService.create(body);
    }

    /**
     * 批量文本转向量
     *
     * @param body
     * @return
     */
    @ResponseBody
    @PostMapping(value = {"/batchCreate"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Mono<List<List<Float>>> batchCreate(@RequestBody String body) {
        return embeddingService.batchCreate(body);
    }

}
