package com.wormhole.agent.controller;

import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.response.TtsResponse;
import com.wormhole.agent.tts.service.TtsFacadeService;
import com.wormhole.agent.tts.service.TtsResponseService;
import com.wormhole.agent.tts.validation.TtsRequestValidator;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2025/08/22
 * @Description: 文本转语音控制器
 */
@Slf4j
@RestController
@RequestMapping("/tts")
public class TtsController {

    @Resource
    private TtsFacadeService ttsFacadeService;
    
    @Resource
    private TtsResponseService ttsResponseService;
    
    @Resource
    private TtsRequestValidator requestValidator;

    @PostMapping("/synthesize")
    public Mono<Result<TtsResponse>> synthesize(@RequestBody TtsRequest request) {
        return ttsFacadeService.synthesize(request).flatMap(Result::success);
    }

    @PostMapping("/synthesize/{provider}")
    public Mono<Result<TtsResponse>> synthesizeWithProvider(
            @PathVariable("provider") String provider,
            @RequestBody TtsRequest request) {
        return ttsFacadeService.synthesizeWithProvider(provider, request).flatMap(Result::success);
    }

    @PostMapping("/synthesize/audio")
    public Mono<ResponseEntity<byte[]>> synthesizeAudio(@RequestBody TtsRequest request) {
        return validateAndExecute(request, () -> ttsResponseService.createAudioResponse(request));
    }

    @PostMapping("/synthesize/{provider}/audio")
    public Mono<ResponseEntity<byte[]>> synthesizeAudioWithProvider(
            @PathVariable("provider") String provider,
            @RequestBody TtsRequest request) {
        return validateAndExecute(request, () -> ttsResponseService.createAudioResponse(provider, request));
    }

    @PostMapping("/synthesize/stream")
    public Mono<ResponseEntity<Flux<DataBuffer>>> synthesizeStream(@RequestBody TtsRequest request) {
        return validateAndExecute(request, () -> ttsResponseService.createStreamResponse(request));
    }

    @PostMapping("/synthesize/{provider}/stream")
    public Mono<ResponseEntity<Flux<DataBuffer>>> synthesizeStreamWithProvider(
            @PathVariable("provider") String provider,
            @RequestBody TtsRequest request) {
        return validateAndExecute(request, () -> ttsResponseService.createStreamResponse(provider, request));
    }

    @GetMapping("/providers")
    public Mono<Result<List<String>>> getAvailableProviders() {
        return ttsFacadeService.getAvailableProviders();
    }

    private <T> Mono<ResponseEntity<T>> validateAndExecute(TtsRequest request, Supplier<Mono<ResponseEntity<T>>> action) {
        try {
            requestValidator.validateRequest(request);
            return action.get();
        } catch (IllegalArgumentException e) {
            log.warn("请求验证失败: {}", e.getMessage());
            return Mono.just(ResponseEntity.badRequest().build());
        }
    }
}