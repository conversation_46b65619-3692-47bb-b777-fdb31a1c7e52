package com.wormhole.agent.controller;

import com.wormhole.agent.client.chat.mcp.dto.McpClientRequest;
import com.wormhole.agent.client.chat.mcp.dto.McpClientResponse;
import com.wormhole.agent.client.chat.mcp.dto.McpSimpleClientRequest;
import com.wormhole.agent.client.chat.mcp.dto.McpToolCallRequest;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.tool.mcp.cache.CacheStats;
import com.wormhole.agent.tool.mcp.cache.McpToolCacheManager;
import com.wormhole.agent.tool.mcp.service.McpServiceManager;
import com.wormhole.agent.util.McpUtils;
import com.wormhole.common.result.Result;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;

/**
 * MCP管理REST API控制器
 * 提供完整的MCP服务管理REST API
 * 包括动态添加/删除MCP服务、获取工具列表、服务状态查询、系统监控等接口
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
@RestController
@RequestMapping("/mcp")
@RequiredArgsConstructor
public class McpManagementController {

    private final McpServiceManager mcpServiceManager;
    private final McpToolCacheManager cacheManager;

    // ==================== 客户端管理API ====================

    /**
     * 动态添加MCP客户端
     *
     * @param request MCP客户端请求
     * @return 服务状态响应
     */
    @PostMapping("/clients/add")
    public Mono<Result<McpClientResponse>> addClient(@RequestBody McpClientRequest request) {
        log.info("接收到添加MCP客户端请求: {}", request != null ? request.getName() : "null");

        return Mono.fromCallable(() -> {
                    if (request == null) {
                        throw new IllegalArgumentException("请求参数不能为空");
                    }
                    request.validate();
                    return McpUtils.toMcpClientConfig(request);
                })
                .flatMap(mcpServiceManager::addClient)
                .map(McpClientResponse::fromServiceStatus)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("添加MCP客户端失败: {}", request != null ? request.getName() : "null", throwable);
                    return Result.failed("add mcp-server fail","添加MCP客户端失败: " + throwable.getMessage());
                });
    }

    /**
     * 删除MCP客户端
     *
     * @param request 包含客户端名称的简化请求
     * @return 删除结果
     */
    @PostMapping("/clients/remove")
    public Mono<Result<Boolean>> removeClient(@RequestBody McpSimpleClientRequest request) {
        request.validate();
        String clientName = request.getClientName();
        log.info("接收到删除MCP客户端请求: {}", clientName);

        return mcpServiceManager.removeClient(clientName)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("删除MCP客户端失败: {}", clientName, throwable);
                    return Result.failed("delete mcp-server fail","删除MCP客户端失败: " + throwable.getMessage());
                });
    }

    /**
     * 更新MCP客户端配置
     *
     * @param request 新的客户端配置请求
     * @return 更新后的服务状态响应
     */
    @PostMapping("/clients/update")
    public Mono<Result<McpClientResponse>> updateClient(@RequestBody McpClientRequest request) {
        String clientName = request.getName();
        log.info("接收到更新MCP客户端请求: {}", clientName);

        return Mono.fromCallable(() -> {
                    if (request == null) {
                        throw new IllegalArgumentException("请求参数不能为空");
                    }
                    request.validate();
                    return McpUtils.toMcpClientConfig(request);
                })
                .flatMap(mcpServiceManager::updateClient)
                .map(McpClientResponse::fromServiceStatus)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("更新MCP客户端失败: {}", clientName, throwable);
                    return Result.failed("update mcp-server fail","更新MCP客户端失败: " + throwable.getMessage());
                });
    }

    /**
     * 获取所有客户端状态
     *
     * @return 所有客户端状态响应列表
     */
    @GetMapping("/clients/all")
    public Mono<Result<List<McpClientResponse>>> getAllClientStatus() {
        log.debug("接收到获取所有客户端状态请求");

        return mcpServiceManager.getAllClientStatus()
                .map(statusList -> statusList.stream()
                        .map(McpClientResponse::fromServiceStatus)
                        .toList())
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("获取所有客户端状态失败", throwable);
                    return Mono.just(Result.successDirect(Collections.emptyList()));
                });
    }

    /**
     * 获取单个客户端状态
     *
     * @param request 包含客户端名称的简化请求
     * @return 客户端状态响应
     */
    @PostMapping("/clients/status")
    public Mono<Result<McpClientResponse>> getClientStatus(@RequestBody McpSimpleClientRequest request) {
        request.validate();
        String clientName = request.getClientName();
        log.debug("接收到获取客户端状态请求: {}", clientName);

        return mcpServiceManager.getClientStatus(clientName)
                .map(McpClientResponse::fromServiceStatus)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("获取客户端状态失败: {}", clientName, throwable);
                    return Result.failed("get mcp-server status fail","获取客户端状态失败: " + throwable.getMessage());
                });
    }

    /**
     * 获取活跃客户端列表
     *
     * @return 活跃客户端名称列表
     */
    @GetMapping("/clients/active")
    public Mono<Result<List<String>>> getActiveClients() {
        log.debug("接收到获取活跃客户端列表请求");

        return mcpServiceManager.getActiveClients()
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("获取活跃客户端列表失败", throwable);
                    return Mono.just(Result.successDirect(Collections.emptyList()));
                });
    }

    // ==================== 工具管理API ====================

    /**
     * 获取客户端工具列表
     *
     * @param request 包含客户端名称的请求
     * @return 工具列表
     */
    @PostMapping("/clients/tools")
    public Mono<Result<List<OpenAiTool>>> getClientTools(@RequestBody McpSimpleClientRequest request) {
        request.validate();
        String clientName = request.getClientName();
        log.debug("接收到获取客户端工具列表请求: {}", clientName);

        return mcpServiceManager.getClientTools(clientName)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("获取客户端工具列表失败: {}", clientName, throwable);
                    return Mono.just(Result.successDirect(Collections.emptyList()));
                });
    }

    /**
     * 执行MCP工具调用
     *
     * @param request MCP工具调用请求
     * @return 工具执行结果
     */
    @PostMapping("/clients/tool/execute")
    public Mono<Result<String>> executeTool(@RequestBody McpToolCallRequest request) {
        return mcpServiceManager.toolCall(request)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("MCP工具调用失败: 服务器={}, 工具={}, 错误={}",
                            request.getMcpServerName(),
                            request.getToolName(),
                            throwable.getMessage(),
                            throwable);
                    return Result.failed("execute mcp-server tool fail","工具调用失败: " + throwable.getMessage());
                });
    }


    // ==================== 缓存管理API ====================

    /**
     * 预热客户端缓存
     *
     * @param request 包含客户端名称的请求
     * @return 预热结果
     */
    @PostMapping("/cache/preload")
    public Mono<Result<Boolean>> preloadCache(@RequestBody McpSimpleClientRequest request) {
        request.validate();
        String clientName = request.getClientName();
        log.info("接收到预热客户端缓存请求: {}", clientName);

        return mcpServiceManager.getClientTools(clientName)
                .flatMap(tools -> cacheManager.preloadCache(clientName, tools))
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("预热客户端缓存失败: {}", clientName, throwable);
                    return Result.failed("preload mcp-server cache fail","预热客户端缓存失败: " + throwable.getMessage());
                });
    }

    /**
     * 清理客户端缓存
     *
     * @param request 包含客户端名称的请求
     * @return 清理结果
     */
    @PostMapping("/cache/evict")
    public Mono<Result<Boolean>> evictCache(@RequestBody McpSimpleClientRequest request) {
        request.validate();
        String clientName = request.getClientName();
        log.info("接收到清理客户端缓存请求: {}", clientName);

        return cacheManager.evictTools(clientName)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("清理客户端缓存失败: {}", clientName, throwable);
                    return Result.failed("clean mcp-server cache fail","清理客户端缓存失败: " + throwable.getMessage());
                });
    }

    /**
     * 清空所有缓存
     *
     * @return 清空结果
     */
    @PostMapping("/cache/clear")
    public Mono<Result<Boolean>> clearAllCache() {
        log.info("接收到清空所有缓存请求");

        return cacheManager.clearAllCache()
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("清空所有缓存失败", throwable);
                    return Result.failed("clean all cache fail","清空所有缓存失败: " + throwable.getMessage());
                });
    }

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计
     */
    @GetMapping("/cache/stats")
    public Mono<Result<CacheStats>> getCacheStats() {
        log.debug("接收到获取缓存统计请求");

        return cacheManager.getCacheStats()
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("获取缓存统计失败", throwable);
                    return Result.failed("get cache status fail","获取缓存统计失败: " + throwable.getMessage());
                });
    }

    // ==================== 健康检查API ====================

    /**
     * 检查客户端健康状态
     *
     * @param request 包含客户端名称的请求
     * @return 健康检查结果
     */
    @PostMapping("/health/check")
    public Mono<Result<Boolean>> checkClientHealth(@RequestBody McpSimpleClientRequest request) {
        request.validate();
        String clientName = request.getClientName();
        log.debug("接收到检查客户端健康状态请求: {}", clientName);

        return mcpServiceManager.checkClientHealth(clientName)
                .flatMap(Result::success)
                .onErrorResume(throwable -> {
                    log.error("检查客户端健康状态失败: {}", clientName, throwable);
                    return Mono.just(Result.successDirect(false));
                });
    }

    /**
     * 检查所有客户端健康状态
     *
     * @return 健康检查完成状态
     */
    @GetMapping("/health/all")
    public Mono<Result<String>> checkAllClientsHealth() {
        log.debug("接收到检查所有客户端健康状态请求");

        return mcpServiceManager.checkAllClientsHealth()
                .then(Mono.just(Result.successDirect("健康检查已完成")))
                .onErrorResume(throwable -> {
                    log.error("检查所有客户端健康状态失败", throwable);
                    return Result.failed("health check fail","健康检查失败: " + throwable.getMessage());
                });
    }
}
