package com.wormhole.agent.controller;

import com.wormhole.agent.query.ChatConversationQuery;
import com.wormhole.agent.query.ChatMessageHistoryQuery;
import com.wormhole.agent.query.ChatMsgFeedBackReq;
import com.wormhole.agent.query.UserChatMessageDelReq;
import com.wormhole.agent.service.UserChatMessageService;
import com.wormhole.agent.vo.UserChatMessageHistoryVO;
import com.wormhole.agent.vo.UserChatMessageVO;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 用户对话消息控制器
 *
 * <AUTHOR>
 * @version 2024/10/21
 */
@RestController
@RequestMapping("/chat/messages")
@RequiredArgsConstructor
public class UserChatMessageController {

    private final UserChatMessageService messageService;

    /**
     * 获取消息详情
     * @param id 消息主键
     * @return 消息详情（包含完整内容）
     */
    @GetMapping("/get_by_id")
    public Mono<Result<UserChatMessageVO>> getById(@RequestParam("id") String id) {
        return messageService.getById(id).flatMap(Result::success);
    }

    /**
     * 删除会话消息记录
     * @param delReq 删除请求
     * @return 操作结果
     */
    @PostMapping("/delete")
    public Mono<Result<Boolean>> delete(@RequestBody UserChatMessageDelReq delReq) {
        return messageService.deleteMessage(delReq).flatMap(Result::success);
    }

    /**
     * 查询会话列表
     */
    @PostMapping("/get_conversation_list")
    public Mono<Result<List<UserChatMessageVO>>> getConversationList(@RequestBody ChatConversationQuery query) {
        return messageService.getConversationList(query).flatMap(Result::success);
    }
    /**
     * 查询历史消息
     */
    @PostMapping("/history")
    public Mono<Result<PageResult<UserChatMessageHistoryVO>>> getHistory(@RequestBody ChatMessageHistoryQuery req) {
        return messageService.queryChatMessageHistory(req).flatMap(Result::success);
    }
    @PostMapping("/feedback")
    public Mono<Result<Boolean>> feedback(@RequestBody ChatMsgFeedBackReq req) {
        return messageService.feedback(req).flatMap(Result::success);

    }
}