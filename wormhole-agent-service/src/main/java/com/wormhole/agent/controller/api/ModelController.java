package com.wormhole.agent.controller.api;

import com.wormhole.agent.util.ModelMockUtils;
import com.wormhole.agent.vo.ModelInfoVO;
import com.wormhole.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/model")
public class ModelController {

    @GetMapping("/list")
    public Mono<Result<List<ModelInfoVO>>> modelList() {
        return Result.success(ModelMockUtils.chatModelList());
    }

    @GetMapping("/rerank_list")
    public Mono<Result<List<ModelInfoVO>>> rerankList() {
        return Result.success(ModelMockUtils.rerankModelList());
    }

}