package com.wormhole.agent.dto;

import com.wormhole.channel.consts.enums.RtcCallStatusEnum;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/3/31
 * @Description:
 */
@Data
@Builder
public class RtcRoomCallDTO {

    /**
     * 房间唯一标识
     */
    private String rtcRoomId;

    /**
     * 发生的时间
     */
    private Long timestamp;

    /**
     * rtc 用户id
     */
    private String rtcUserId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 智能体id
     */
    private String agentId;

    /**
     * 客户端类型
     */
    private Integer clientType;

    /**
     * 原因
     */
    private String reason;

    /**
     * 状态
     */
    private RtcCallStatusEnum status;

    /**
     * 语言
     */
    private String language;

    /**
     * 是否有AI参与者
     */
    private Integer hasAiParticipant;

}
