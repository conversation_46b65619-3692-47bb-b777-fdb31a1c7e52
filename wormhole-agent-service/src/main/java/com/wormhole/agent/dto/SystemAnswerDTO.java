package com.wormhole.agent.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SystemAnswerDTO {
    private String userId;

    /**
     * 会话ID（ULID格式）
     */
    private String conversationId;

    /**
     * 客户端请求ID（用于去重）
     */
    private String clientReqId;

    /**
     * 智能体编码
     */
    private String botCode;

    /**
     * 消息内容（支持富文本）
     */
    private String content;
    private String traceId;
    private String chatCompletionId;

    /**
     * 模型标识（如gpt-4-32k）
     */
    private String model;

    /**
     * 模型供应商（如OpenAI）
     */
    private String modelProvider;
    /**
     * 执行耗时（毫秒）
     */
    private long elapsedMs;

    /**
     * 请求状态（1:成功 2:失败）
     */
    private Integer reqStatus;

    /**
     * 错误信息（失败时记录）
     */
    private String errorMsg;
}
