package com.wormhole.agent.dto;

import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowDebugDTO {
    /**
     * 工作流code
     */
    private String workflowCode;
    /**
     * 工作流执行实例id
     */
    private String executionId;
    /**
     * 节点上下文
     */
    private Map<String, NodeExecutionInfo<Node.Inputs>> nodeExecutionInfoMap = new ConcurrentHashMap<>();
    /**
     * 开始时间
     */
    private long startTime ;
    /**
     * 结束时间
     */
    private long endTime;

    public static WorkflowDebugDTO covertDTO(WorkflowContext workflowContext) {
        WorkflowDebugDTO workflowDebugDTO = new WorkflowDebugDTO();
        workflowDebugDTO.setWorkflowCode(workflowContext.getWorkflowCode());
        workflowDebugDTO.setExecutionId(workflowContext.getExecutionId());
        workflowDebugDTO.setStartTime(workflowContext.getStartTime());
        workflowDebugDTO.setEndTime(workflowContext.getEndTime());
        workflowDebugDTO.setNodeExecutionInfoMap(workflowContext.getNodeExecutionInfoMap());
        return workflowDebugDTO;
    }
}
