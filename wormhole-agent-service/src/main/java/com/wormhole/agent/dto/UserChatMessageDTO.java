package com.wormhole.agent.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserChatMessageDTO {
    private String userId;

    /**
     * 会话ID（ULID格式）
     */
    private String conversationId;

    /**
     * 原始设备ID（用于设备追踪）
     */
    private String deviceId;
    private String rtcRoomId;
    /**
     * 客户端请求ID（用于去重）
     */
    private String clientReqId;

    /**
     * 智能体编码
     */
    private String botCode;
    /**
     * 消息类型（user/assistant/system）
     */
    private String messageType;
    /**
     * 消息内容（支持富文本）
     */
    private String content;
    private String traceId;
    private String chatCompletionId;

    /**
     * 模型标识（如gpt-4-32k）
     */
    private String model;

    /**
     * 模型供应商（如OpenAI）
     */
    private String modelProvider;
    /**
     * 执行耗时（毫秒）
     */
    private long elapsedMs;

    /**
     * 请求状态（1:成功 2:失败）
     */
    private Integer reqStatus;

    /**
     * 错误信息（失败时记录）
     */
    private String errorMsg;

    private LocalDateTime createdAt;

    /**
     * 更新时间
     * */
    private LocalDateTime updatedAt;

    /**
     *  创建人ID
     * */
    private String createdBy;

    /**
     *  创建人姓名
     * */
    private String createdByName;

    /**
     *  更新人ID
     * */
    private String updatedBy;

    /**
     *  更新人姓名
     * */
    private String updatedByName;}
