package com.wormhole.agent.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
public class SaveConversationDTO {
    private String userId;
    private String username;
    private String botCode;
    private String source;
    private String conversationId;
    @Builder.Default
    private boolean isDebug = false;
    private String deviceId;
    private String hotelCode;
    private String title;
    private boolean create;
    private String positionCode;
}
