package com.wormhole.agent.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserQuestionDTO {
    private String userId;
    /**
     * 空间编码（冗余存储）
     */
    private String spaceCode;

    /**
     * 会话ID（ULID格式）
     */
    private String conversationId;


    /**
     * 客户端请求ID（用于去重）
     */
    private String clientReqId;

    /**
     * 原始设备ID（用于设备追踪）
     */
    private String deviceId;


    /**
     * 智能体编码
     */
    private String botCode;


    /**
     * 消息内容（支持富文本）
     */
    private String content;
    /**
     * 全链路追踪ID
     */
    private String traceId;
}
