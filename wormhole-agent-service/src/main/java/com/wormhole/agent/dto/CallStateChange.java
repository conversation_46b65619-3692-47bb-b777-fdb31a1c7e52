package com.wormhole.agent.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @author: joker.liu
 * @date: 2025/3/31
 * @Description:
 */
@Data
@Accessors(chain = true)
public class CallStateChange implements Serializable {

    /**
     * 发生事件
     */
    private LocalDateTime time;

    /**
     * 事件
     */
    private String event;

    /**
     * 原因
     */
    private String reason;

    /**
     * 来自
     */
    private String from;

    /**
     * 去向
     */
    private String to;

    /**
     * 由
     */
    private String by;

    public static CallStateChange instance(String event, String reason) {
        return instance(event, reason, null, null, null);
    }

    public static CallStateChange instance(String event, String reason, String by) {
        return instance(event, reason, null, null, by);
    }

    public static CallStateChange instance(String event, String reason, String from, String to) {
        return instance(event, reason, from, to, null);
    }

    public static CallStateChange instance(String event, String reason, String from, String to, String by) {
        return new CallStateChange()
                .setTime(LocalDateTime.now())
                .setEvent(event)
                .setReason(reason)
                .setFrom(from)
                .setTo(to)
                .setBy(by);
    }

}
