package com.wormhole.agent.filter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;


/**
 * ApiKeyConfig
 *
 * <AUTHOR>
 * @version 2024/12/31
 */
@Component
@ConfigurationProperties(prefix = ApiKeyConfig.API_KEY_PREFIX)
public class ApiKeyConfig {

    public static final String API_KEY_PREFIX = "wormhole.auth.api-key";

    /**
     * 合法key
     */
    private Set<String> validApiKeys;
    /**
     * 白名单路径
     */
    private List<String> excludedPaths;

    // Getters and Setters
    public Set<String> getValidApiKeys() {
        return validApiKeys;
    }

    public void setValidApiKeys(Set<String> validApiKeys) {
        this.validApiKeys = validApiKeys;
    }

    public List<String> getExcludedPaths() {
        return excludedPaths;
    }

    public void setExcludedPaths(List<String> excludedPaths) {
        this.excludedPaths = excludedPaths;
    }
}

