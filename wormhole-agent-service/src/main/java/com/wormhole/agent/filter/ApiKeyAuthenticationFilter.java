//package com.wormhole.agent.filter;
//
//import com.wormhole.agent.core.util.ChatCompletionUtils;
//import com.wormhole.agent.model.openai.ChatCompletions;
//import com.wormhole.common.result.ResultCode;
//import com.wormhole.common.util.JacksonUtils;
//import jakarta.annotation.Resource;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.core.io.buffer.DataBuffer;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.MediaType;
//import org.springframework.stereotype.Component;
//import org.springframework.util.AntPathMatcher;
//import org.springframework.util.PathMatcher;
//import org.springframework.web.server.ServerWebExchange;
//import org.springframework.web.server.WebFilter;
//import org.springframework.web.server.WebFilterChain;
//import reactor.core.publisher.Mono;
//
//import java.nio.charset.StandardCharsets;
//
///**
// * ApiKeyAuthenticationFilter
// *
// * <AUTHOR>
// * @version 2024/12/31
// */
//@Component
//public class ApiKeyAuthenticationFilter implements WebFilter {
//
//    private static final String API_KEY_HEADER = "api-key";
//    private static final String BEARER_PREFIX = "Bearer ";
//
//    private static final PathMatcher pathMatcher = new AntPathMatcher();
//
//    @Resource
//    private ApiKeyConfig apiKeyConfig;
//
//    @Override
//    public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
//        // 排除不需要验证的路径
//        String path = exchange.getRequest().getPath().value();
//        if (isExcludedPath(path)) {
//            return chain.filter(exchange);
//        }
//
//        String apiKey = exchange.getRequest().getHeaders().getFirst(HttpHeaders.AUTHORIZATION);
//        if (StringUtils.isNotBlank(apiKey)) {
//            if (apiKey.startsWith(BEARER_PREFIX)) {
//                // 移除Bearer前缀（如果存在）
//                apiKey = StringUtils.trim(apiKey.substring(BEARER_PREFIX.length()));
//            }
//        } else {
//            apiKey = exchange.getRequest().getHeaders().getFirst(API_KEY_HEADER);
//        }
//        if (StringUtils.isBlank(apiKey)) {
//            return responseUnauthorizedError(exchange);
//        }
//        // 验证API key
//        if (!isValidApiKey(apiKey)) {
//            return responseUnauthorizedError(exchange);
//        }
//        return chain.filter(exchange);
//    }
//
//    private boolean isExcludedPath(String path) {
//        return apiKeyConfig.getExcludedPaths().stream().anyMatch(pattern -> isPathMatched(path, pattern));
//    }
//
//    private boolean isValidApiKey(String apiKey) {
//        return apiKeyConfig.getValidApiKeys().contains(apiKey);
//    }
//
//    /**
//     * 精确匹配 或 前缀匹配(以/结尾) 或 通配符匹配
//     *
//     * @param path
//     * @param pattern
//     * @return
//     */
//    private boolean isPathMatched(String path, String pattern) {
//        return path.equals(pattern) || (path.startsWith(pattern) && pattern.endsWith("/")) || pathMatcher.match(pattern, path);
//    }
//
//    private Mono<Void> responseUnauthorizedError(ServerWebExchange exchange) {
//        exchange.getResponse().setStatusCode(HttpStatus.OK);
//        exchange.getResponse().getHeaders().setContentType(MediaType.APPLICATION_JSON);
//        ChatCompletions chatCompletions = ChatCompletionUtils.buildChatError(ResultCode.UNAUTHORIZED);
//        String jsonResponse = JacksonUtils.writeValueAsString(chatCompletions);
//        byte[] bytes = jsonResponse.getBytes(StandardCharsets.UTF_8);
//        DataBuffer buffer = exchange.getResponse().bufferFactory().wrap(bytes);
//        return exchange.getResponse().writeWith(Mono.just(buffer));
//    }
//}
