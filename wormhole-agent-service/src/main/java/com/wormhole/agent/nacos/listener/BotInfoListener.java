package com.wormhole.agent.nacos.listener;

import com.fasterxml.jackson.databind.JsonNode;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.nacos.IndexBaseNacosConfigListener;
import com.wormhole.agent.nacos.NacosConstant;
import com.wormhole.agent.nacos.config.ConfigInfo;
import com.wormhole.common.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ContextedRuntimeException;
import org.springframework.stereotype.Component;

/**
 * BotInfoListener
 *
 * <AUTHOR>
 * @version 2025/1/1
 */
@Slf4j
@Component
public class BotInfoListener extends IndexBaseNacosConfigListener<BotInfo> {

    @Override
    public ConfigInfo getConfigInfo() {
        return ConfigInfo.builder().dataId("bot_config_list").group(NacosConstant.WORMHOLE_AGENT_GROUP_ID).build();
    }

    @Override
    protected BotInfo parseContent(String content) {
        JsonNode node;
        try {
            node = JacksonUtils.mapper().readTree(content);
        } catch (Exception e) {
            throw new ContextedRuntimeException(e).addContextValue("botInfo.content", content);
        }
        BotInfo botInfo = new BotInfo();
        // Handle the is_published field specifically
        if (node.has("is_published")) {
            JsonNode isPublishedNode = node.get("is_published");
            if (isPublishedNode.isBoolean()) {
                // Convert boolean to int (true -> 1, false -> 0)
                botInfo.setIsPublished(isPublishedNode.asBoolean() ? 1 : 0);
            } else if (isPublishedNode.isInt()) {
                botInfo.setIsPublished(isPublishedNode.asInt());
            }
        } else {
            botInfo = JacksonUtils.readValue(content, BotInfo.class);
        }
        return botInfo;
    }

    @Override
    protected String getBizKey(BotInfo content) {
        return content.getBotCode();
    }
}