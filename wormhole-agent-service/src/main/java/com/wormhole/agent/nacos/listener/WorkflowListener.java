package com.wormhole.agent.nacos.listener;

import com.wormhole.agent.nacos.IndexBaseNacosConfigListener;
import com.wormhole.agent.nacos.NacosConstant;
import com.wormhole.agent.nacos.config.ConfigInfo;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.model.NodeTypeEnum;
import com.wormhole.agent.workflow.model.inputs.CodeInputs;
import com.wormhole.agent.workflow.script.groovy.GroovyScriptExecutor;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * WorkflowListener
 *
 * <AUTHOR>
 * @version 2025/1/1
 */
@Slf4j
@Component
public class WorkflowListener extends IndexBaseNacosConfigListener<Workflow> {

    @Resource
    private GroovyScriptExecutor groovyScriptExecutor;

    @Override
    public ConfigInfo getConfigInfo() {
        return ConfigInfo.builder().dataId("workflow_config_list").group(NacosConstant.WORMHOLE_AGENT_GROUP_ID).build();
    }

    @Override
    protected Workflow parseContent(String content) {
        return Workflow.fromJson(content);
    }

    @Override
    protected String getBizKey(Workflow content) {
        return content.getWorkflowDefinition().getWorkflowCode();
    }

    @Override
    public void onAddChange(Workflow workflow) {
        // 缓存groovy脚本
        WorkflowDefinition workflowDefinition = workflow.getWorkflowDefinition();
        workflowDefinition.getNodes()
                .stream()
                .filter(node -> {
                    NodeTypeEnum nodeTypeEnum = NodeTypeEnum.getByType(node.getType());
                    return NodeTypeEnum.CODE.equals(nodeTypeEnum);
                })
                .forEach(node -> {
                    CodeInputs inputs = WorkflowUtils.getInputs(node.getData().getInputs(), CodeInputs.class);
                    String code = inputs.getCode();
                    String groovyClassName = WorkflowUtils.getGroovyClassName(workflowDefinition.getWorkflowCode(), node.getId());
                    groovyScriptExecutor.updateScript(groovyClassName, code);
                });
    }

    public List<Workflow> getWorkflowByCodeList(List<String> workflowCodeList) {
        return Optional.ofNullable(workflowCodeList)
                .map(this::getContents)
                .orElse(Collections.emptyList());
    }

    /**
     * 根据name找到工作流
     *
     * @param workflowName
     * @return
     */
    public Optional<Workflow> getWorkflowByName(String workflowName) {
        if (StringUtils.isBlank(workflowName)) {
            return Optional.empty();
        }
        return getContents()
                .stream()
                .filter(item -> StringUtils.equalsIgnoreCase(item.getWorkflowDefinition().getWorkflowName(), workflowName))
                .findFirst();
    }
}
