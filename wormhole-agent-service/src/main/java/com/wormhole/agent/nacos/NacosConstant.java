package com.wormhole.agent.nacos;

import com.alibaba.nacos.api.config.listener.AbstractListener;
import com.alibaba.nacos.api.config.listener.Listener;

/**
 * <AUTHOR>
 * @date 2024/10/2 17:10
 **/
public class NacosConstant {

    public static final int TIMEOUT_MS = 6000;

    public static final String BOT_INFO_DATA_ID_LIST = "bot_info_data_id_list";

    public static final String WORMHOLE_PLATFORM_GROUP_ID = "wormhole-platform";

    public static final String WORMHOLE_AGENT_GROUP_ID = "wormhole-agent";

    public static final String TEMPLATE_DATA_ID_LIST = "template_data_id_list";

    public static final Listener EMPTY_LISTENER = new AbstractListener() {
        @Override
        public void receiveConfigInfo(String s) {
            // do nothing
        }
    };

}