package com.wormhole.agent.nacos.config;

import com.wormhole.agent.nacos.NacosConstant;
import lombok.Getter;

/**
 * TemplateEnum
 *
 * <AUTHOR>
 * @version 2025/1/1
 */
@Getter
public enum TemplateEnum {
    intent_system_prompt("intent_system_prompt"),
    intent_user_prompt("intent_user_prompt"),

    rag_content_chunk_system_prompt("rag_content_chunk_system_prompt",true),

    hotel_reply_system_prompt("hotel_reply_system_prompt", true),
    hotel_reply_user_prompt("hotel_reply_user_prompt"),

    hotel_search_system_prompt("hotel_search_system_prompt"),
    hotel_search_user_prompt("hotel_search_user_prompt"),

    hotel_full_text_search_code("hotel_full_text_search_code"),

    langchain_system_prompt("langchain_system_prompt"),

    prompt_expert_system_prompt("prompt_expert_system_prompt"),
    prompt_expert_user_prompt("prompt_expert_user_prompt"),

    knowledge_search_system_prompt("knowledge_search_system_prompt"),
    knowledge_search_user_prompt("knowledge_search_user_prompt"),

    travel_search_code("travel_search_code", true),
    travel_search_system_prompt("travel_search_system_prompt", true),
    travel_search_user_prompt("travel_search_user_prompt", true),

    suggest_system_prompt("suggest_system_prompt", true),


    llm_intent_system_prompt("llm_intent_system_prompt",true),
    llm_intent_user_prompt("llm_intent_user_prompt",true),

    ;

    private final String dataId;

    private final String group;

    private final boolean useLocal;

    TemplateEnum(String dataId, String group) {
        this.dataId = dataId;
        this.group = group;
        this.useLocal = false;
    }

    TemplateEnum(String dataId) {
        this.dataId = dataId;
        this.group = NacosConstant.WORMHOLE_AGENT_GROUP_ID;
        this.useLocal = false;
    }

    TemplateEnum(String dataId, boolean useLocal) {
        this.dataId = dataId;
        this.group = NacosConstant.WORMHOLE_AGENT_GROUP_ID;
        this.useLocal = useLocal;
    }

    public Object parseContent(String content) {
        return content;
    }
}