package com.wormhole.agent.nacos.listener;

import com.wormhole.agent.nacos.IndexBaseNacosConfigListener;
import com.wormhole.agent.nacos.NacosConstant;
import com.wormhole.agent.nacos.config.ConfigInfo;
import com.wormhole.agent.plugin.entity.PluginDetailInfo;
import com.wormhole.common.util.JacksonUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025-04-01 09:28:48
 * @Description:
 */
@Component
public class PluginListener extends IndexBaseNacosConfigListener<PluginDetailInfo> {
    @Override
    public ConfigInfo getConfigInfo() {
        return ConfigInfo.builder().dataId("plugin_config_list").group(NacosConstant.WORMHOLE_AGENT_GROUP_ID).build();
    }

    @Override
    protected PluginDetailInfo parseContent(String content) {
        return JacksonUtils.readValue(content, PluginDetailInfo.class);
    }

    @Override
    protected String getBizKey(PluginDetailInfo content) {
        return content.getCode();
    }
}
