package com.wormhole.agent.nacos;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.listener.AbstractListener;
import com.alibaba.nacos.api.config.listener.Listener;
import com.alibaba.nacos.api.exception.NacosException;
import com.wormhole.agent.nacos.config.ConfigInfo;
import com.wormhole.agent.nacos.config.ConfigItem;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * IndexBaseNacosConfigListener
 *
 * <AUTHOR>
 * @version 2025/1/1
 */
@Slf4j
public abstract class IndexBaseNacosConfigListener<T> {

    private static final String LOCAL_PREFIX = "local:";

    private final Map<String, ConfigItem<T>> configCache = new ConcurrentHashMap<>();

    @Resource
    private NacosConfigManager nacosConfigManager;

    @PostConstruct
    public void init() throws NacosException {
        ConfigInfo configInfo = getConfigInfo();
        Listener listener = new AbstractListener() {
            @Override
            public void receiveConfigInfo(String content) {
                handleConfigChange(content, NacosConstant.WORMHOLE_AGENT_GROUP_ID);
            }
        };
        nacosConfigManager.getConfigService().addListener(configInfo.getDataId(), configInfo.getGroup(), listener);
        String config = nacosConfigManager.getConfigService().getConfig(configInfo.getDataId(), configInfo.getGroup(), NacosConstant.TIMEOUT_MS);
        listener.receiveConfigInfo(config);
    }

    public abstract ConfigInfo getConfigInfo();

    protected abstract T parseContent(String content);

    protected void onAddChange(T content) {

    }

    protected void onRemoveConfig(T content) {

    }

    protected abstract String getBizKey(T content);

    public void handleConfigChange(String rawContent, String group) {
        if (StringUtils.isBlank(rawContent)) {
            return;
        }
        List<String> newKeyList = JSON.parseArray(rawContent, String.class);

        Set<String> oldKeys = configCache.keySet().stream().filter(key -> !StringUtils.startsWith(key, LOCAL_PREFIX)).collect(Collectors.toSet());
        Set<String> newKeys = new HashSet<>(newKeyList);

        // 需要删除的监听器
        Set<String> deleteKeys = oldKeys.stream().filter(key -> !newKeys.contains(key)).collect(Collectors.toSet());

        // 需要新增的监听器
        List<String> addKeys = newKeyList.stream().filter(dataId -> !oldKeys.contains(dataId)).collect(Collectors.toList());

        log.info("Config changes - to delete: {}, to add: {}", deleteKeys, addKeys);

        // 删除不需要的监听器和缓存
        for (String key : deleteKeys) {
            ConfigItem<T> item = configCache.remove(key);
            onRemoveConfig(item.getContent());
            // 正常情况下直接删除缓存即可，因为在源头会直接删除config，触发content为空的变更
            nacosConfigManager.getConfigService().removeListener(item.getDataId(), item.getGroup(), NacosConstant.EMPTY_LISTENER);
            log.info("Removed listener for: {}", key);
        }

        // 添加新的监听器
        for (String dataId : addKeys) {
            try {
                AbstractListener listener = new AbstractListener() {
                    @Override
                    public void receiveConfigInfo(String content) {
                        if (StringUtils.isNotBlank(content)) {
                            try {
                                ConfigItem<T> item = ConfigItem.<T>builder()
                                        .dataId(dataId)
                                        .group(group)
                                        .content(parseContent(content))
                                        .build();
                                configCache.put(buildKey(dataId, group), item);
                                onAddChange(item.getContent());
                            } catch (Exception e) {
                                log.error("listener parse  dataId: {}, group: {} error", dataId, group, e);
                            }
                        }
                    }
                };

                // 添加监听器
                nacosConfigManager.getConfigService().addListener(dataId, group, listener);

                // 获取初始配置
                String content = nacosConfigManager.getConfigService().getConfig(dataId, group, 3000);
                listener.receiveConfigInfo(content);
            } catch (NacosException e) {
                log.error("Failed to add listener for dataId: {}, group: {}", dataId, group, e);
            }
        }
    }

    private String buildKey(String dataId, String group) {
        return dataId + "#" + group;
    }

    public List<ConfigItem<T>> getAllConfigs() {
        return new ArrayList<>(configCache.values());
    }

    public void addLocalConfig(ConfigItem<T> configItem) {
        if (Objects.nonNull(configItem) && Objects.nonNull(configItem.getContent()) && StringUtils.isNotBlank(getBizKey(configItem.getContent()))) {
            ConfigInfo configInfo = getConfigInfo();
            configItem.setDataId(configInfo.getDataId());
            configItem.setGroup(configInfo.getGroup());
            configCache.put(LOCAL_PREFIX + getBizKey(configItem.getContent()), configItem);
            onAddChange(configItem.getContent());
        }
    }

    public T getContent(String bizKey) {
        return Optional.ofNullable(bizKey)
                .filter(StringUtils::isNotBlank)
                .flatMap(t -> getAllConfigs().stream()
                        .filter(item -> bizKey.equalsIgnoreCase(getBizKey(item.getContent())))
                        .map(ConfigItem::getContent)
                        .findFirst())

                .orElse(null);
    }

    public List<T> getContents(List<String> bizKeyList) {
        Set<String> keySet = new HashSet<>(bizKeyList);
        return getAllConfigs().stream()
                .filter(item -> keySet.contains(getBizKey(item.getContent())))
                .map(ConfigItem::getContent)
                .collect(Collectors.toList());
    }

    public List<T> getContents() {
        return getAllConfigs().stream().map(ConfigItem::getContent).collect(Collectors.toList());
    }

}
