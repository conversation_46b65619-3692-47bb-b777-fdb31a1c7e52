package com.wormhole.agent.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * SpacesEntity
 *
 * <AUTHOR>
 * @version 2024/10/21
 */
@Data
@Table("wp_space")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SpaceEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 图形信息
     */
    private String imageJson;

    /**
     * 空间类型PUBLIC公共空间, PERSONAL个人空间, OTHER其他空间
     */
    private String spaceType;

    /**
     * 状态
     */
    private String status;



}