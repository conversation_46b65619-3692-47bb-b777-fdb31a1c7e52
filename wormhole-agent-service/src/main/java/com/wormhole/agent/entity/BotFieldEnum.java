package com.wormhole.agent.entity;

/**
 * BotFieldEnum
 *
 * <AUTHOR>
 * @version 2024/10/21
 */
public enum BotFieldEnum {
    /**
     * 主键
     */
    id,
    /**
     * 编码
     */
    code,
    /**
     * 名称
     */
    name,
    /**
     * 描述
     */
    description,
    /**
     * 图形信息
     */
    image_json,
    /**
     * 空间code
     */
    space_code,
    /**
     * 状态
     */
    status,
    /**
     * 是否收藏
     */
    is_fav,
    /**
     * 是否可见
     */
    visibility,
    /**
     * 是否发布
     */
    is_published,
    /**
     * 发布时间
     */
    publish_time,
    /**
     * 版本
     */
    version,
    is_hot,
    /**
     * 分类
     */
    category,
    /**
     * 创建人id
     */
    created_by,
    /**
     * 创建人名称
     */
    created_by_name,
    /**
     * 修改人id
     */
    updated_by,
    /**
     * 修改人名称
     */
    updated_by_name,
    /**
     * 创建时间
     */
    created_at,
    /**
     * 修改时间
     */
    updated_at;

}