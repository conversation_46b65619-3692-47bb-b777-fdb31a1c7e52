package com.wormhole.agent.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 设备信息实体类
 * 对应数据库表 rtc_device_info
 */
@Data
@Table("rtc_device_info")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DeviceInfoEntity extends BaseEntity {

    @Id // 主键字段
    @Column("id") // 映射数据库列名
    private Long id;

    @Column("device_id") // 设备唯一标识
    private String deviceId;

    @Column("hotel_code") // 酒店编码
    private String hotelCode;

    @Column("room_no") // 房间号
    private String roomNo;

    @Column("type") // 设备类型（如：前台设备、房间设备）
    private String type;

    @Column("status") // 设备状态（如：1-已激活，0-待绑定）
    private Integer status;

    @Column("rtc_status") // RTC 状态（如：0-未上线，1-已上线）
    private Integer rtcStatus;
}
