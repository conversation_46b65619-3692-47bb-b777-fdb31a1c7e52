package com.wormhole.agent.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * BotEntity
 *
 * <AUTHOR>
 * @version 2024/10/21
 */
@Data
@Table("wp_bot")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BotEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 图形信息
     */
    private String imageJson;

    /**
     * 空间code
     */
    private String spaceCode;

    /**
     * 状态,draft-草稿 online-线上 offline-线下 deleted-删除
     */
    private String status;

    /**
     * 是否收藏
     */
    private Boolean isFav;

    /**
     * 是否可见
     */
    private Boolean visibility;

    /**
     * 是否发布
     */
    private Boolean isPublished;

    /**
     * 发布时间
     */
    private LocalDateTime publishTime;

    /**
     * 版本
     */
    private Long version;
    /**
     * 分类
     */
    private String category;
    /**
     * 是否热门
     */
    private Boolean isHot;


}