package com.wormhole.agent.entity;

/**
 * BotExtendedInfoFieldEnum
 *
 * <AUTHOR>
 * @version 2024/10/21
 */
public enum BotExtendedInfoFieldEnum {
    /**
     * 主键
     */
    id,
    /**
     * bot 表主键
     */
    bot_id,
    /**
     * bot编码
     */
    bot_code,
    /**
     * bot模式
     */
    bot_model,
    /**
     * 模型信息
     */
    model_info,
    /**
     * 工作流列表
     */
    workflow_info_list,
    /**
     * 插件信息列表
     */
    plugin_info_list,
    /**
     * 知识库
     */
    knowledge,
    /**
     * 开场白信息
     */
    onboarding_info,
    /**
     * 人设与回复逻辑
     */
    prompt_info,
    /**
     * 用户问题建议
     */
    suggest_reply_info,
    /**
     * 背景图
     */
    background_image_info_list,
    /**
     * 声音信息
     */
    voices_info,
    /**
     * 创建人id
     */
    created_by,
    /**
     * 创建人名称
     */
    created_by_name,
    /**
     * 修改人id
     */
    updated_by,
    /**
     * 修改人名称
     */
    updated_by_name,
    /**
     * 创建时间
     */
    created_at,
    /**
     * 修改时间
     */
    updated_at;

}