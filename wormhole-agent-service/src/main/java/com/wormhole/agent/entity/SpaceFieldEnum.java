package com.wormhole.agent.entity;

/**
 * SpacesFieldEnum
 *
 * <AUTHOR>
 * @version 2024/10/21
 */
public enum SpaceFieldEnum {
    /**
     * 主键
     */
    id,
    /**
     * 编码
     */
    code,
    /**
     * 名称
     */
    name,
    /**
     * 描述
     */
    description,
    /**
     * 图形信息
     */
    image_json,
    /**
     * 空间类型PUBLIC公共空间, PERSONAL个人空间, OTHER其他空间
     */
    space_type,
    /**
     * 状态
     */
    status,
    /**
     * 创建人id
     */
    created_by,
    /**
     * 创建人名称
     */
    created_by_name,
    /**
     * 修改人id
     */
    updated_by,
    /**
     * 修改人名称
     */
    updated_by_name,
    /**
     * 创建时间
     */
    created_at,
    /**
     * 修改时间
     */
    updated_at;

}