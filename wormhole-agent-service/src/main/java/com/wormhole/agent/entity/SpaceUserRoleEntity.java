package com.wormhole.agent.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;

/**
 * @author: joker.liu
 * @date: 2025/2/25
 * @Description:
 */
@Table("wp_space_user_role")
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class SpaceUserRoleEntity extends BaseEntity implements Serializable {

    /**
     * 主键
     */
    @Id
    @Column("id")
    private Long id;

    private String userId;

    private String spaceCode;

    private String roleCode;

}
