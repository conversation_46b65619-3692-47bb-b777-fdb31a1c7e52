package com.wormhole.agent.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * BotExtendedInfoEntity
 *
 * <AUTHOR>
 * @version 2024/10/21
 */
@Data
@Accessors(chain = true)
@Table("wp_bot_extended_info")
@EqualsAndHashCode(callSuper = true)
public class BotExtendedInfoEntity extends BaseEntity {
    /**
     * 主键
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * bot 表主键
     */
    private Long botId;

    /**
     * bot编码
     */
    private String botCode;

    /**
     * bot模式
     */
    private String botMode;

    /**
     * 模型信息
     */
    private String modelInfo;

    /**
     * 工作流列表
     */
    private String workflowInfoList;

    /**
     * 插件信息列表
     */
    private String pluginInfoList;

    /**
     * 知识库
     */
    private String knowledge;

    /**
     * 开场白信息
     */
    private String onboardingInfo;

    /**
     * 人设与回复逻辑
     */
    private String promptInfo;

    /**
     * 拓展配置信息
     */
    private String extConfigInfo;

    /**
     * 用户问题建议
     */
    private String suggestReplyInfo;

    /**
     * 背景图
     */
    private String backgroundImageInfoList;

    /**
     * 声音信息
     */
    private String voicesInfo;




}