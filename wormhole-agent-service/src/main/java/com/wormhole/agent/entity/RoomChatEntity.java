package com.wormhole.agent.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

/**
 * 房间聊天信息实体类
 * 对应数据库表 rtc_room_chat
 */
@Data
@Table("rtc_room_chat")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class RoomChatEntity extends BaseEntity {

    @Id // 主键字段
    @Column("id") // 映射数据库列名
    private Long id;

    @Column("room_id") // 房间唯一标识
    private String roomId;

    @Column("task_id") // 任务唯一标识
    private String taskId;

    @Column("chat_user_id") // 智能体唯一标识
    private String chatUserId;

    @Column("conversation_id") // 会话id
    private String conversationId;

}
