package com.wormhole.agent.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

/**
 * @author: joker.liu
 * @date: 2025/3/31
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table("rtc_room_call")
@Accessors(chain = true)
public class RtcRoomCallEntity extends BaseEntity {

    @Id
    @Column("id")
    private Long id;

    /**
     * 房间唯一标识
     */
    @Column("rtc_room_id")
    private String rtcRoomId;

    /**
     * 房间code
     */
    @Column("position_code")
    private String positionCode;

    /**
     * 会话id
     */
    @Column("conversation_id")
    private String conversationId;

    /**
     * 通话方向 1-前台到客房 2-客房到前台
     */
    @Column("call_direction")
    private Integer callDirection;

    /**
     * 通话状态
     */
    @Column("call_status")
    private Integer callStatus;

    /**
     * 发起方id
     */
    @Column("initiator_id")
    private String initiatorId;

    /**
     * 发起方名称
     */
    @Column("initiator_name")
    private String initiatorName;

    /**
     * 发起方客户端类型
     */
    @Column("initiator_client_type")
    private Integer initiatorClientType;

    /**
     * 发起方类型 1-前台 2-客房
     */
    @Column("initiator_type")
    private Integer initiatorType;

    /**
     * 接收方id
     */
    @Column("receiver_id")
    private String receiverId;

    /**
     * 接收方名称
     */
    @Column("receiver_name")
    private String receiverName;

    /**
     * 接收方客户端类型
     */
    @Column("receiver_client_type")
    private Integer receiverClientType;

    /**
     * 接收方类型 1-前台 2-客房 3-agent
     */
    @Column("receiver_type")
    private Integer receiverType;

    /**
     * 当前参与方id
     */
    @Column("current_participant_id")
    private String currentParticipantId;

    /**
     * 当前参与方名称
     */
    @Column("current_participant_name")
    private String currentParticipantName;

    /**
     * 当前参与方客户端类型
     */
    @Column("current_participant_client_type")
    private Integer currentParticipantClientType;

    /**
     * 当前参与方类型 1-前台 2-客房 3-agent
     */
    @Column("current_participant_type")
    private Integer currentParticipantType;

    /**
     * 是否有AI参与方
     */
    @Column("has_ai_participant")
    private Integer hasAiParticipant;

    /**
     * AI参与方id
     */
    @Column("ai_agent_id")
    private String aiAgentId;

    /**
     * 是否有转接
     */
    @Column("has_transfer")
    private Integer hasTransfer;

    /**
     * 转接时间
     */
    @Column("transfer_time")
    private LocalDateTime transferTime;

    /**
     * 通话开始时间
     */
    @Column("call_start_time")
    private LocalDateTime callStartTime;

    /**
     * 智能体接听时间
     */
    @Column("call_agent_answer_time")
    private LocalDateTime callAgentAnswerTime;

    /**
     * 用户通话接听时间
     */
    @Column("call_human_answer_time")
    private LocalDateTime callHumanAnswerTime;

    /**
     * 通话结束时间
     */
    @Column("call_end_time")
    private LocalDateTime callEndTime;

    /**
     * 通话时长
     */
    @Column("call_duration")
    private Integer callDuration;

    /**
     * 与agent通话时长
     */
    @Column("call_agent_duration")
    private Integer callAgentDuration;

    /**
     * 与用户通话时长
     */
    @Column("call_human_duration")
    private Integer callHumanDuration;

    /**
     * 通话状态变化记录
     */
    @Column("state_changes")
    private String stateChanges;

}
