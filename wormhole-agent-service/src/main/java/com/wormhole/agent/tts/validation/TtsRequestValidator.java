package com.wormhole.agent.tts.validation;

import com.wormhole.agent.client.chat.params.TtsRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

@Component
public class TtsRequestValidator {

    public void validateRequest(TtsRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("请求不能为空");
        }
        
        if (!StringUtils.hasText(request.getText())) {
            throw new IllegalArgumentException("文本内容不能为空");
        }
        
        // 可以添加更多验证规则
        if (request.getText().length() > 300) {
            throw new IllegalArgumentException("文本长度不能超过300字符");
        }
    }
}