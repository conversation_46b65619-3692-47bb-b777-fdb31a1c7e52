package com.wormhole.agent.tts.service;

import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.response.TtsResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TtsResponseService {

    @Resource
    private TtsManager ttsManager;

    public Mono<ResponseEntity<byte[]>> createAudioResponse(String provider, TtsRequest request) {
        return getTtsResponse(provider, request)
                .map(this::buildByteArrayResponse)
                .onErrorResume(throwable -> {
                    log.error("TTS音频合成失败，提供商: {}", provider, throwable);
                    return Mono.just(ResponseEntity.internalServerError().<byte[]>build());
                });
    }

    public Mono<ResponseEntity<byte[]>> createAudioResponse(TtsRequest request) {
        return createAudioResponse(null, request);
    }

    public Mono<ResponseEntity<Flux<DataBuffer>>> createStreamResponse(String provider, TtsRequest request) {
        return getTtsResponse(provider, request)
                .map(this::buildStreamResponse)
                .onErrorResume(throwable -> {
                    log.error("TTS流式音频合成失败，提供商: {}", provider, throwable);
                    return Mono.just(ResponseEntity.internalServerError().<Flux<DataBuffer>>build());
                });
    }

    public Mono<ResponseEntity<Flux<DataBuffer>>> createStreamResponse(TtsRequest request) {
        return createStreamResponse(null, request);
    }

    private Mono<TtsResponse> getTtsResponse(String provider, TtsRequest request) {
        if (StringUtils.hasText(provider)) {
            return ttsManager.textToSpeech(provider, request);
        } else {
            return ttsManager.textToSpeech(request);
        }
    }

    private ResponseEntity<byte[]> buildByteArrayResponse(TtsResponse response) {
        if (!response.isSuccess() || response.getAudioData() == null) {
            return ResponseEntity.badRequest().build();
        }

        HttpHeaders headers = createAudioHeaders(response.getAudioFormat());
        headers.setContentLength(response.getAudioData().length);
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(response.getAudioData());
    }

    private ResponseEntity<Flux<DataBuffer>> buildStreamResponse(TtsResponse response) {
        if (!response.isSuccess() || response.getAudioData() == null) {
            return ResponseEntity.badRequest().build();
        }

        HttpHeaders headers = createAudioHeaders(response.getAudioFormat());
        Flux<DataBuffer> dataBufferFlux = createDataBufferFlux(response.getAudioData());
        
        return ResponseEntity.ok()
                .headers(headers)
                .body(dataBufferFlux);
    }

    private HttpHeaders createAudioHeaders(String format) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(getMediaType(format));
        headers.set("Content-Disposition", "attachment; filename=\"tts_audio." + 
                (StringUtils.hasText(format) ? format : "audio") + "\"");
        return headers;
    }

    private MediaType getMediaType(String format) {
        if (!StringUtils.hasText(format)) {
            return MediaType.APPLICATION_OCTET_STREAM;
        }
        
        return switch (format.toLowerCase()) {
            case "mp3" -> MediaType.parseMediaType("audio/mpeg");
            case "wav" -> MediaType.parseMediaType("audio/wav");
            case "pcm" -> MediaType.parseMediaType("audio/pcm");
            case "ogg" -> MediaType.parseMediaType("audio/ogg");
            default -> MediaType.APPLICATION_OCTET_STREAM;
        };
    }

    private Flux<DataBuffer> createDataBufferFlux(byte[] audioData) {
        DefaultDataBufferFactory bufferFactory = new DefaultDataBufferFactory();
        int chunkSize = 8192; // 8KB chunks
        
        return Flux.range(0, (int) Math.ceil((double) audioData.length / chunkSize))
                .map(i -> {
                    int start = i * chunkSize;
                    int end = Math.min(start + chunkSize, audioData.length);
                    byte[] chunk = new byte[end - start];
                    System.arraycopy(audioData, start, chunk, 0, end - start);
                    return bufferFactory.wrap(chunk);
                });
    }
}