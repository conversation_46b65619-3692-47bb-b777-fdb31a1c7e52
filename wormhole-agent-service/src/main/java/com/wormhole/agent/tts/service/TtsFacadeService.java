package com.wormhole.agent.tts.service;

import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.response.TtsResponse;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.Result;
import com.wormhole.common.result.ResultCode;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Service
public class TtsFacadeService {

    @Resource
    private TtsManager ttsManager;

    public Mono<TtsResponse> synthesize(TtsRequest request) {
        return validateAndProcess(null, request);
    }

    public Mono<TtsResponse> synthesizeWithProvider(String provider, TtsRequest request) {
        return validateAndProcess(provider, request);
    }

    public Mono<Result<List<String>>> getAvailableProviders() {
        List<String> providers = ttsManager.getAvailableProviders();
        return Mono.just(Result.successDirect(providers));
    }

    private Mono<TtsResponse> validateAndProcess(String provider, TtsRequest request) {
        // 参数验证
        if (!StringUtils.hasText(request.getText())) {
            return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER, "文本内容不能为空"));
        }

        // 业务处理
        Mono<TtsResponse> ttsResponseMono = StringUtils.hasText(provider) 
                ? ttsManager.textToSpeech(provider, request)
                : ttsManager.textToSpeech(request);

        return ttsResponseMono
                .onErrorResume(throwable -> {
                    log.error("TTS合成失败, 提供商: {}", provider, throwable);
                    return Mono.error(new BusinessException("TTS_ERROR", "TTS合成失败:" + throwable.getMessage()));
                });
    }
}