package com.wormhole.agent.tts.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.response.TtsResponse;
import com.wormhole.agent.tts.config.BytePlusTtsConfig;
import com.wormhole.agent.tts.enums.TtsProvidersEnum;
import com.wormhole.agent.tts.service.TtsService;
import com.wormhole.common.util.IdUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Flux;
import reactor.util.retry.Retry;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class BytePlusTtsService implements TtsService {

    @Resource
    private BytePlusTtsConfig bytePlusTtsConfig;
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public BytePlusTtsService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(1024 * 1024))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public Mono<TtsResponse> textToSpeech(TtsRequest request) {
        if (!isEnabled()) {
            return Mono.just(TtsResponse.builder()
                    .success(false)
                    .message("BytePlus TTS服务未启用")
                    .build());
        }
        Map<String, Object> requestBody = buildRequestBody(request);
        try {
            String requestBodyJson = objectMapper.writeValueAsString(requestBody);
            log.info("构建BytePlus TTS请求, accessKey: {}, 请求参数：{}", 
                    maskAccessKey(bytePlusTtsConfig.getAccessKey()), requestBodyJson);
            return webClient.post()
                    .uri(bytePlusTtsConfig.getBaseUrl())
                    .headers(this::addHeaders)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestBodyJson)
                    .retrieve()
                    .bodyToFlux(DataBuffer.class)
                    .reduce(new StringBuilder(), (stringBuilder, dataBuffer) -> {
                        byte[] bytes = new byte[dataBuffer.readableByteCount()];
                        dataBuffer.read(bytes);
                        DataBufferUtils.release(dataBuffer);
                        stringBuilder.append(new String(bytes, StandardCharsets.UTF_8));
                        return stringBuilder;
                    })
                    .map(StringBuilder::toString)
                    .flatMap(completeResponse -> parseStreamingResponse(completeResponse, request))
                    .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                            .maxBackoff(Duration.ofSeconds(10))
                            .doBeforeRetry(retrySignal -> 
                                log.warn("BytePlus TTS请求失败，正在进行第{}次重试, 错误: {}", 
                                        retrySignal.totalRetries() + 1, retrySignal.failure().getMessage())))
                    .onErrorResume(this::handleError);
                    
        } catch (Exception e) {
            log.error("构建BytePlus TTS请求失败", e);
            return Mono.just(TtsResponse.builder()
                    .success(false)
                    .message("构建BytePlus TTS请求失败: " + e.getMessage())
                    .build());
        }
    }

    private Map<String, Object> buildRequestBody(TtsRequest request) {
        Map<String, Object> body = new HashMap<>();

        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("text", request.getText());
        reqParams.put("speaker", StringUtils.hasText(request.getVoice()) ?
                request.getVoice() : bytePlusTtsConfig.getVoice());
        Map<String, Object> audioParams = new HashMap<>();
        audioParams.put("format", StringUtils.hasText(request.getFormat()) ?
                request.getFormat() : bytePlusTtsConfig.getFormat());
        audioParams.put("sample_rate", 16000);
        reqParams.put("audio_params", audioParams);

        /*Map<String, Object> additions = new HashMap<>();
        additions.put("explicit_language", StringUtils.hasText(request.getLanguage()) ?
                request.getLanguage() : bytePlusTtsConfig.getLanguage());
        Map<String, Object> cacheConfig = new HashMap<>();
        cacheConfig.put("text_type", 1);
        cacheConfig.put("use_cache", true);
        additions.put("cache_config", cacheConfig);*/
        reqParams.put("additions", "");

        body.put("req_params", reqParams);

        return body;
    }

    private void addHeaders(HttpHeaders headers) {
        headers.set("Content-Type", "application/json");
        headers.set("X-Api-App-Id", bytePlusTtsConfig.getAccessId());
        headers.set("X-Api-Access-Key", bytePlusTtsConfig.getAccessKey());
        headers.set("X-Api-Resource-Id", bytePlusTtsConfig.getResourceId());
        headers.set("X-Api-App-Key", bytePlusTtsConfig.getApiKey());
        headers.set("X-Api-Request-Id", IdUtils.generateId());
    }

    private Mono<TtsResponse> parseStreamingResponse(String completeResponse, TtsRequest request) {
        try {
            StringBuilder audioDataBuilder = new StringBuilder();
            String requestId = null;
            boolean hasError = false;
            String errorMessage = null;
            
            log.info("开始解析BytePlus TTS流式响应，响应长度: {}", completeResponse.length());
            
            // 按行分割完整响应
            String[] lines = completeResponse.split("\n");
            log.info("响应包含{}行数据", lines.length);
            
            for (String line : lines) {
                if (StringUtils.hasText(line.trim())) {
                    try {
                        JsonNode responseNode = objectMapper.readTree(line.trim());
                        
                        if (responseNode.has("request_id")) {
                            requestId = responseNode.get("request_id").asText();
                        }
                        
                        if (responseNode.has("code")) {
                            int code = responseNode.get("code").asInt();
                            if (code != 0 && code != 20000000) {
                                hasError = true;
                                errorMessage = responseNode.has("message") ?
                                        responseNode.get("message").asText() : "BytePlus TTS转换失败";
                                log.error("BytePlus TTS API返回错误: code={}, message={}", code, errorMessage);
                                break;
                            }
                        }
                        
                        if (responseNode.has("data")) {
                            String audioChunk = responseNode.get("data").asText();
                            if (StringUtils.hasText(audioChunk)) {
                                audioDataBuilder.append(audioChunk);
                                log.debug("接收到音频分片，长度: {}", audioChunk.length());
                            }
                        }
                    } catch (Exception parseException) {
                        log.warn("解析JSON行失败，跳过该行: {}, 错误: {}", line, parseException.getMessage());
                    }
                }
            }
            
            if (hasError) {
                return Mono.just(TtsResponse.builder()
                        .success(false)
                        .message(errorMessage)
                        .build());
            }
            
            if (!audioDataBuilder.isEmpty()) {
                byte[] audioBytes = Base64.getDecoder().decode(audioDataBuilder.toString());
                log.info("BytePlus TTS转换成功，音频数据大小: {} bytes", audioBytes.length);
                return Mono.just(TtsResponse.builder()
                        .success(true)
                        .message("BytePlus TTS转换成功")
                        .audioData(audioBytes)
                        .audioFormat(StrUtil.isNotBlank(request.getFormat()) ? 
                                request.getFormat() : bytePlusTtsConfig.getFormat())
                        .requestId(requestId)
                        .build());
            } else {
                log.warn("BytePlus TTS未返回音频数据");
                return Mono.just(TtsResponse.builder()
                        .success(false)
                        .message("BytePlus TTS未返回音频数据")
                        .build());
            }
            
        } catch (Exception e) {
            log.error("解析BytePlus TTS流式响应失败", e);
            return Mono.just(TtsResponse.builder()
                    .success(false)
                    .message("解析BytePlus TTS响应失败: " + e.getMessage())
                    .build());
        }
    }

    private Mono<TtsResponse> handleError(Throwable throwable) {
        log.error("BytePlus TTS请求失败", throwable);
        return Mono.just(TtsResponse.builder()
                .success(false)
                .message("BytePlus TTS请求失败: " + throwable.getMessage())
                .build());
    }

    private String maskAccessKey(String accessKey) {
        if (StringUtils.hasText(accessKey) && accessKey.length() > 8) {
            return accessKey.substring(0, 4) + "****" + accessKey.substring(accessKey.length() - 4);
        }
        return "****";
    }

    @Override
    public String getProviderName() {
        return TtsProvidersEnum.BYTE_PLUS.getCode();
    }

    @Override
    public boolean isEnabled() {
        return bytePlusTtsConfig.isEnabled() && 
               StringUtils.hasText(bytePlusTtsConfig.getAccessKey());
    }
}