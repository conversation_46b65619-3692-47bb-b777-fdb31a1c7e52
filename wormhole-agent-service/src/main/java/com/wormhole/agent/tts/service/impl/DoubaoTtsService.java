package com.wormhole.agent.tts.service.impl;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.response.TtsResponse;
import com.wormhole.agent.tts.config.DoubaoTtsConfig;
import com.wormhole.agent.tts.enums.TtsProvidersEnum;
import com.wormhole.agent.tts.service.TtsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class DoubaoTtsService implements TtsService {

    @Resource
    private DoubaoTtsConfig doubaoTtsConfig;
    
    private final WebClient webClient;
    private final ObjectMapper objectMapper;

    public DoubaoTtsService() {
        this.webClient = WebClient.builder()
                .codecs(configurer -> configurer
                        .defaultCodecs()
                        .maxInMemorySize(5 * 1024 * 1024))  // 增加到5MB
                .build();
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public Mono<TtsResponse> textToSpeech(TtsRequest request) {
        if (!isEnabled()) {
            return Mono.just(TtsResponse.builder()
                    .success(false)
                    .message("豆包TTS服务未启用")
                    .build());
        }
        Map<String, Object> requestBody = buildRequestBody(request);
        try {
            String requestBodyJson = objectMapper.writeValueAsString(requestBody);
            log.info("构建TTS请求, token: {}, 请求参数：{}", ("Authorization;" + doubaoTtsConfig.getToken()), requestBodyJson);
            return webClient.post()
                    .uri(doubaoTtsConfig.getBaseUrl())
                    .headers(this::addHeaders)
                    .contentType(MediaType.APPLICATION_JSON)
                    .bodyValue(requestBodyJson)
                    .retrieve()
                    .bodyToMono(String.class)
                    .flatMap(result -> parseResponse(result, request))
                    .retryWhen(Retry.backoff(3, Duration.ofSeconds(1))
                            .maxBackoff(Duration.ofSeconds(10))
                            .doBeforeRetry(retrySignal -> 
                                log.warn("TTS请求失败，正在进行第{}次重试, 错误: {}", 
                                        retrySignal.totalRetries() + 1, retrySignal.failure().getMessage())))
                    .onErrorResume(this::handleError);
                    
        } catch (Exception e) {
            log.error("构建TTS请求失败", e);
            return Mono.just(TtsResponse.builder()
                    .success(false)
                    .message("构建TTS请求失败: " + e.getMessage())
                    .build());
        }
    }

    private Map<String, Object> buildRequestBody(TtsRequest request) {
        Map<String, Object> body = new HashMap<>();
        
        Map<String, Object> app = new HashMap<>();
        app.put("appid", doubaoTtsConfig.getAppId());
        app.put("token", doubaoTtsConfig.getToken());
        app.put("cluster", "volcano_tts");
        
        Map<String, Object> user = new HashMap<>();
        user.put("uid", "default_user");
        
        Map<String, Object> audio = new HashMap<>();
        audio.put("voice_type", StringUtils.hasText(request.getVoice()) ? 
                request.getVoice() : doubaoTtsConfig.getVoice());
        audio.put("encoding", StringUtils.hasText(request.getFormat()) ? 
                request.getFormat() : doubaoTtsConfig.getFormat());
        audio.put("speed_ratio", Objects.nonNull(request.getSpeed()) ?
                request.getSpeed() : Double.parseDouble(doubaoTtsConfig.getSpeed()));
        audio.put("emotion", StringUtils.hasText(request.getEmotion()) ? 
                request.getEmotion() : doubaoTtsConfig.getEmotion());
        audio.put("language", StringUtils.hasText(request.getLanguage()) ?
                request.getLanguage() : doubaoTtsConfig.getLanguage());
        audio.put("sample_rate", 16000);
        
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("reqid", generateRequestId());
        requestMap.put("text", request.getText());
        requestMap.put("text_type", "plain");
        requestMap.put("operation", "query");
        requestMap.put("with_frontend", 1);
        requestMap.put("frontend_type", "unitTson");
        
        body.put("app", app);
        body.put("user", user);
        body.put("audio", audio);
        body.put("request", requestMap);
        
        return body;
    }

    private void addHeaders(HttpHeaders headers) {
        headers.set("Content-Type", "application/json");
        headers.set("Authorization", "Bearer;" + doubaoTtsConfig.getToken());
    }

    private Mono<TtsResponse> parseResponse(String responseBody, TtsRequest request) {
        try {
            JsonNode responseNode = objectMapper.readTree(responseBody);
            
            if (responseNode.has("code") && responseNode.get("code").asInt() == 3000) {
                // 检查data字段是否存在，避免空指针异常
                JsonNode dataNode = responseNode.get("data");
                if (dataNode == null || dataNode.isNull() || !dataNode.isTextual()) {
                    return Mono.just(TtsResponse.builder()
                            .success(false)
                            .message("TTS响应中缺少音频数据")
                            .build());
                }
                
                String audioData = dataNode.asText();
                if (StrUtil.isBlank(audioData)) {
                    return Mono.just(TtsResponse.builder()
                            .success(false)
                            .message("TTS响应中音频数据为空")
                            .build());
                }
                
                byte[] audioBytes;
                try {
                    audioBytes = Base64.getDecoder().decode(audioData);
                } catch (IllegalArgumentException e) {
                    log.error("Base64解码失败", e);
                    return Mono.just(TtsResponse.builder()
                            .success(false)
                            .message("音频数据格式错误: " + e.getMessage())
                            .build());
                }

                return Mono.just(TtsResponse.builder()
                        .success(true)
                        .message("TTS转换成功")
                        .audioData(audioBytes)
                        .audioFormat(StrUtil.isNotBlank(request.getFormat()) ? request.getFormat() : doubaoTtsConfig.getFormat())
                        .requestId(responseNode.has("reqid") ? responseNode.get("reqid").asText() : null)
                        .build());
            } else {
                String errorMessage = responseNode.has("message") ? 
                        responseNode.get("message").asText() : "TTS转换失败";
                return Mono.just(TtsResponse.builder()
                        .success(false)
                        .message(errorMessage)
                        .build());
            }
        } catch (Exception e) {
            log.error("解析TTS响应失败", e);
            return Mono.just(TtsResponse.builder()
                    .success(false)
                    .message("解析TTS响应失败: " + e.getMessage())
                    .build());
        }
    }

    private Mono<TtsResponse> handleError(Throwable throwable) {
        log.error("TTS请求失败", throwable);
        return Mono.just(TtsResponse.builder()
                .success(false)
                .message("TTS请求失败: " + throwable.getMessage())
                .build());
    }

    private String generateRequestId() {
        try {
            String timestamp = String.valueOf(Instant.now().toEpochMilli());
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(timestamp.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.substring(0, 16);
        } catch (NoSuchAlgorithmException e) {
            return String.valueOf(System.currentTimeMillis());
        }
    }

    @Override
    public String getProviderName() {
        return TtsProvidersEnum.DOU_BAO.getCode();
    }

    @Override
    public boolean isEnabled() {
        return doubaoTtsConfig.isEnabled() && 
               StringUtils.hasText(doubaoTtsConfig.getAppId()) && 
               StringUtils.hasText(doubaoTtsConfig.getToken());
    }
}