package com.wormhole.agent.tts.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "wormhole.tts.byteplus")
public class BytePlusTtsConfig {
    private String accessId = "5701196523";
    private String accessKey = "Ce--F7U7a26gmiucfc1ZT0spqhNwCgia";
    private String apiKey = "aGjiRDfUWi";
    private String baseUrl = "https://voice.ap-southeast-1.bytepluses.com/api/v3/tts/unidirectional";
    private String resourceId = "volc.megatts.default";
    private String voice = "en_us_001";
    private String format = "mp3";      // mp3/ogg_opus/pcm
    private String speed = "1.0";
    private String emotion = "normal";
    private String language = "en";
    private boolean enabled = false;
}