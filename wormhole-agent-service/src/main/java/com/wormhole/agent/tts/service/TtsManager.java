package com.wormhole.agent.tts.service;

import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.response.TtsResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Service
public class TtsManager {

    @Resource
    private List<TtsService> ttsServices;

    public Mono<TtsResponse> textToSpeech(String providerName, TtsRequest request) {
        TtsService ttsService = findTtsService(providerName);
        if (ttsService == null) {
            return Mono.just(TtsResponse.builder()
                    .success(false)
                    .message("未找到指定的TTS服务提供商: " + providerName)
                    .build());
        }
        
        if (!ttsService.isEnabled()) {
            return Mono.just(TtsResponse.builder()
                    .success(false)
                    .message("TTS服务提供商未启用: " + providerName)
                    .build());
        }
        
        return ttsService.textToSpeech(request);
    }

    public Mono<TtsResponse> textToSpeech(TtsRequest request) {
        TtsService defaultService = findDefaultTtsService();
        if (defaultService == null) {
            return Mono.just(TtsResponse.builder()
                    .success(false)
                    .message("未找到可用的TTS服务")
                    .build());
        }
        
        return defaultService.textToSpeech(request);
    }

    private TtsService findTtsService(String providerName) {
        return ttsServices.stream()
                .filter(service -> service.getProviderName().equalsIgnoreCase(providerName))
                .findFirst()
                .orElse(null);
    }

    private TtsService findDefaultTtsService() {
        return ttsServices.stream()
                .filter(TtsService::isEnabled)
                .findFirst()
                .orElse(null);
    }

    public List<String> getAvailableProviders() {
        return ttsServices.stream()
                .filter(TtsService::isEnabled)
                .map(TtsService::getProviderName)
                .toList();
    }
}