package com.wormhole.agent.tts.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "wormhole.tts.doubao")
public class DoubaoTtsConfig {
    private String appId = "7962686137";
    private String token = "XY5V1VbaX3iUSGNpoGBtK45GqvMjqd8L";
    private String baseUrl = "https://openspeech.bytedance.com/api/v1/tts";
    private String voice = "BV001_streaming";
    private String format = "mp3";
    private String speed = "1.0";
    private String emotion = "normal";
    private String language = "zh";
    private boolean enabled = false;
}