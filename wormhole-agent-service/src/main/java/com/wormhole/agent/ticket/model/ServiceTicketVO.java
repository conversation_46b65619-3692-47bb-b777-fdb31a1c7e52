package com.wormhole.agent.ticket.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

@Data
public class ServiceTicketVO {

    /**
     * 工单唯一编号
     */
    private String ticketNo;

    /**
     * 状态: 0-待处理 1-已完成
     */
    private Integer status;

    /**
     * 是否超时
     */
    private Boolean isOverdue;

    /**
     * 优先级: 0-普通 1-紧急 2-置顶
     */
    private Integer priority;

    /**
     * 服务类别代码
     */
    private String serviceCategory;

    /**
     * 服务子类别代码
     */
    private String serviceSubcategory;

    /**
     * 服务类别名称
     */
    private String serviceCategoryName;

    /**
     * 服务子类别名称
     */
    private String serviceSubcategoryName;

    /**
     * 客户诉求内容
     */
    private String guestRequest;


    /**
     * 完成人员工号
     */
    private String completedBy;

    /**
     * 完成人员姓名
     */
    private String completedByName;


    private String createdUser;

    private String createdUserName;


}