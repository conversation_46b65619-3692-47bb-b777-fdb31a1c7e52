package com.wormhole.agent.ticket.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wormhole.agent.ticket.enums.ServiceCategory;
import com.wormhole.agent.ticket.enums.ServiceSubcategory;
import com.wormhole.agent.ticket.enums.ServiceType;
import com.wormhole.agent.ticket.model.ServiceTicketVO;
import com.wormhole.agent.ticket.model.entity.ServiceTicketFieldEnum;
import com.wormhole.common.result.PageResult;
import com.google.common.base.Preconditions;
import com.wormhole.agent.ticket.model.CreateTicketReq;
import com.wormhole.agent.ticket.model.GetTicketReq;
import com.wormhole.agent.ticket.model.entity.ServiceTicketEntity;
import com.wormhole.agent.ticket.repository.ServiceTicketRepository;
import com.wormhole.common.util.HeaderUtils;
import com.wormhole.common.util.IdUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate;
import org.springframework.data.redis.core.ReactiveHashOperations;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.data.relational.core.query.Criteria;
import org.springframework.data.relational.core.query.Query;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.wormhole.agent.rtc.consts.RedisKeyConstant.CONVERSATION_TICKETS_KEY;

@Component
@Slf4j
public class ServiceTicketService {

    // 缓存相关常量
    private static final Duration CACHE_TTL = Duration.ofDays(1);

    // 默认值常量
    private static final String DEFAULT_HOTEL_CODE = "KYAGGJ";
    private static final String DEFAULT_ROOM_NO = "101";
    private static final String DEFAULT_RTC_ROOM_ID = "2";
    private static final String DEFAULT_DEVICE_ID = "ED9816CE61AD4652AB83294A025527C8";
    private static final int DEFAULT_PAGE_SIZE = 15;
    private static final int DEFAULT_STATUS = 0;

    // 错误消息常量
    private static final String ERROR_EMPTY_GUEST_REQUEST = "用户输入内容不能为空";
    private static final String ERROR_EMPTY_CATEGORY = "工单类型不能为空";
    private static final String ERROR_EMPTY_SUBCATEGORY = "工单子类型不能为空";
    private static final String SUCCESS_MESSAGE = "酒店服务工单创建成功";

    @Resource
    private ServiceTicketRepository repository;

    @Resource
    private R2dbcEntityTemplate r2dbcEntityTemplate;

    @Resource
    private ReactiveRedisTemplate<String, String> reactiveRedisTemplate;

    /**
     * 创建工单
     *
     * @param req 创建工单请求参数
     * @return 创建结果
     */
    public Mono<String> createTicket(CreateTicketReq req) {
        // 参数校验
        validateTicketRequest(req);

        // 构建工单实体
        ServiceTicketEntity ticket = buildTicketEntity(req);

        // 获取Header信息并保存工单
        return HeaderUtils.getHeaderInfo()
                .flatMap(headerInfo -> {
                    // 设置用户信息
                    populateUserInfo(ticket, req, headerInfo);

                    // 保存工单
                    return repository.save(ticket)
                            .flatMap(this::handleSavedTicket);
                })
                .doOnSuccess(result -> log.debug("工单创建成功 | ticketId:{}", ticket.getId()))
                .doOnError(e -> log.error("工单创建失败 | ticketId:{} | error:{}", ticket.getId(), e.getMessage()));
    }

    /**
     * 验证工单请求参数
     *
     * @param req 创建工单请求
     * @throws IllegalArgumentException 如果参数验证失败
     */
    private void validateTicketRequest(CreateTicketReq req) {
        // 检查用户输入内容
        Preconditions.checkArgument(StringUtils.isNotBlank(req.getGuestRequest()), ERROR_EMPTY_GUEST_REQUEST);

        // 检查工单类型
        ServiceCategory category = ServiceCategory.getByCode(req.getServiceCategory());
        Preconditions.checkNotNull(category, ERROR_EMPTY_CATEGORY);

        // 检查工单子类型
        ServiceSubcategory subcategory = ServiceSubcategory.getByCode(req.getServiceSubcategory());
        Preconditions.checkNotNull(subcategory, ERROR_EMPTY_SUBCATEGORY);
    }

    /**
     * 构建工单实体
     */
    private ServiceTicketEntity buildTicketEntity(CreateTicketReq req) {
        ServiceTicketEntity ticket = new ServiceTicketEntity();
        BeanUtils.copyProperties(req, ticket);

        // 设置服务信息
        ServiceCategory category = ServiceCategory.getByCode(req.getServiceCategory());
        ServiceSubcategory subcategory = ServiceSubcategory.getByCode(req.getServiceSubcategory());
        ServiceType type = ServiceType.getByCode(req.getServiceType());

        ticket.setServiceType(type == null ? ServiceType.SERVICE.name() : type.name());
        ticket.setStatus(DEFAULT_STATUS);
        ticket.setTicketNo(IdUtils.generateId());
        ticket.setServiceCategory(category.getCode());
        ticket.setServiceCategoryName(category.getChineseName());
        ticket.setServiceSubcategory(subcategory.getCode());
        ticket.setServiceSubcategoryName(subcategory.getChineseName());

        // 设置酒店相关字段（使用默认值）
        ticket.setHotelCode(StringUtils.defaultIfBlank(req.getHotelCode(), DEFAULT_HOTEL_CODE));
        ticket.setRoomNo(StringUtils.defaultIfBlank(req.getRoomNo(), DEFAULT_ROOM_NO));
        ticket.setRtcRoomId(StringUtils.defaultIfBlank(req.getRtcRoomId(), DEFAULT_RTC_ROOM_ID));
        ticket.setDeviceId(StringUtils.defaultIfBlank(req.getDeviceId(), DEFAULT_DEVICE_ID));

        // 设置审计字段
        LocalDateTime now = LocalDateTime.now();
        ticket.setCreatedAt(now);
        ticket.setUpdatedAt(now);

        return ticket;
    }

    /**
     * 设置用户信息
     */
    private void populateUserInfo(ServiceTicketEntity ticket, CreateTicketReq req, Object headerInfo) {
        // 提取HeaderInfo信息
        String userId = Optional.ofNullable(req.getUserId())
                .orElseGet(() -> extractUserIdFromHeader(headerInfo));

        String userName = Optional.ofNullable(req.getUserName())
                .orElseGet(() -> extractUsernameFromHeader(headerInfo));

        ticket.setCreatedBy(userId);
        ticket.setCreatedByName(userName);
        ticket.setUpdatedBy(userId);
        ticket.setUpdatedByName(userName);
    }

    /**
     * 从Header中提取用户ID
     */
    private String extractUserIdFromHeader(Object headerInfo) {
        try {
            return (String) headerInfo.getClass().getMethod("getUserId").invoke(headerInfo);
        } catch (Exception e) {
            log.warn("获取Header中的userId失败", e);
            return null;
        }
    }

    /**
     * 从Header中提取用户名
     */
    private String extractUsernameFromHeader(Object headerInfo) {
        try {
            return (String) headerInfo.getClass().getMethod("getUsername").invoke(headerInfo);
        } catch (Exception e) {
            log.warn("获取Header中的username失败", e);
            return null;
        }
    }

    /**
     * 处理已保存的工单
     */
    private Mono<String> handleSavedTicket(ServiceTicketEntity savedEntity) {
        if (StringUtils.isNotBlank(savedEntity.getConversationId())) {
            return addTicketToCache(savedEntity.getConversationId(), savedEntity)
                    .then(Mono.just("酒店服务工单创建成功"));
        } else {
            return Mono.just("酒店服务工单创建成功");
        }
    }

    /**
     * 将工单添加到Redis缓存（使用Hash结构）
     */
    private Mono<Void> addTicketToCache(String conversationId, ServiceTicketEntity ticketEntity) {
        if (StringUtils.isBlank(conversationId) || ticketEntity == null) {
            return Mono.empty();
        }

        String cacheKey = String.format(CONVERSATION_TICKETS_KEY, conversationId);
        String ticketJson = JSONUtil.toJsonStr(ticketEntity);

        // 使用Hash结构存储，key是会话ID，field是工单ID，value是工单JSON
        ReactiveHashOperations<String, String, String> hashOps = reactiveRedisTemplate.opsForHash();

        return hashOps.put(cacheKey, ticketEntity.getId().toString(), ticketJson)
                .then(reactiveRedisTemplate.expire(cacheKey, CACHE_TTL))
                .doOnSuccess(v -> log.debug("工单已添加到缓存 | conversationId: {} | ticketId: {}",
                        conversationId, ticketEntity.getId()))
                .then();
    }

    /**
     * 获取工单列表（后台接口，不使用缓存）
     */
    public Mono<PageResult<ServiceTicketVO>> getTickets(GetTicketReq req) {
        return Mono.fromCallable(() -> buildTicketQuery(req))
                .flatMap(query -> Mono.zip(
                        r2dbcEntityTemplate.count(query, ServiceTicketEntity.class),
                        r2dbcEntityTemplate.select(query, ServiceTicketEntity.class)
                                .map(this::toSimpleVO)
                                .collectList()
                ))
                .map(tuple -> PageResult.create(tuple.getT1(), tuple.getT2()));
    }

    /**
     * 构建工单查询条件
     */
    private Query buildTicketQuery(GetTicketReq req) {
        Criteria criteria = Criteria.empty();

        // 添加过滤条件
        if (StringUtils.isNotBlank(req.getServiceCategory())) {
            criteria = criteria.and(ServiceTicketFieldEnum.service_category.name()).is(req.getServiceCategory());
        }

        if (req.getStatus() != null) {
            criteria = criteria.and(ServiceTicketFieldEnum.status.name()).is(req.getStatus());
        }

        if (req.getIsOverdue() != null) {
            criteria = criteria.and(ServiceTicketFieldEnum.is_overdue.name()).is(req.getIsOverdue());
        }

        if (StringUtils.isNotBlank(req.getHotelCode())) {
            criteria = criteria.and(ServiceTicketFieldEnum.hotel_code.name()).is(req.getHotelCode());
        }

        if (StringUtils.isNotBlank(req.getRoomNo())) {
            criteria = criteria.and(ServiceTicketFieldEnum.room_no.name()).is(req.getRoomNo());
        }

        if (StringUtils.isNotBlank(req.getDeviceId())) {
            criteria = criteria.and(ServiceTicketFieldEnum.device_id.name()).is(req.getDeviceId());
        }

        if (StringUtils.isNotBlank(req.getUserId())) {
            criteria = criteria.and(ServiceTicketFieldEnum.created_by.name()).is(req.getUserId());
        }

        if (StringUtils.isNotBlank(req.getConversationId())) {
            criteria = criteria.and(ServiceTicketFieldEnum.conversation_id.name()).is(req.getConversationId());
        }

        // 设置排序
        Sort sort;
        if (StringUtils.isNotBlank(req.getSortName()) && Objects.nonNull(req.getSort())) {
            sort = Sort.by(req.getSort(), req.getSortName());
        } else {
            sort = Sort.by(Sort.Direction.ASC, ServiceTicketFieldEnum.created_at.name(), ServiceTicketFieldEnum.status.name());
        }

        // 设置分页
        PageRequest pageRequest = PageRequest.of(req.getCurrent() - 1, req.getPageSize());

        return Query.query(criteria).with(pageRequest).sort(sort);
    }

    /**
     * 实体转VO
     */
    private ServiceTicketVO toSimpleVO(ServiceTicketEntity entity) {
        ServiceTicketVO vo = new ServiceTicketVO();
        BeanUtils.copyProperties(entity, vo);
        vo.setCreatedUser(entity.getCreatedBy());
        vo.setCreatedUserName(entity.getCreatedByName());

        if (entity.getSlaDeadline() != null) {
            vo.setIsOverdue(entity.getSlaDeadline().isBefore(LocalDateTime.now()));
        }

        return vo;
    }

    /**
     * 通过Groovy创建工单（用于外部调用）
     */
    public Mono<String> createTicketByGroovy(Map<String, Object> inputParams) {
        return createTicket(buildReq(inputParams));
    }

    /**
     * 从参数Map构建创建工单请求
     */
    private CreateTicketReq buildReq(Map<String, Object> inputParams) {
        CreateTicketReq req = new CreateTicketReq();
        req.setServiceCategory(getStringValue(inputParams, ServiceTicketFieldEnum.service_category.name()));
        req.setServiceSubcategory(getStringValue(inputParams, ServiceTicketFieldEnum.service_subcategory.name()));
        req.setGuestRequest(getStringValue(inputParams, ServiceTicketFieldEnum.guest_request.name()));
        req.setHotelCode(getStringValue(inputParams, ServiceTicketFieldEnum.hotel_code.name()));
        req.setRoomNo(getStringValue(inputParams, ServiceTicketFieldEnum.room_no.name()));
        req.setRtcRoomId(getStringValue(inputParams, ServiceTicketFieldEnum.rtc_room_id.name()));
        req.setDeviceId(getStringValue(inputParams, ServiceTicketFieldEnum.device_id.name()));
        req.setConversationId(getStringValue(inputParams, ServiceTicketFieldEnum.conversation_id.name()));
        req.setClientReqId(getStringValue(inputParams, ServiceTicketFieldEnum.client_req_id.name()));
        req.setUserId(getStringValue(inputParams, "user_id"));
        req.setUserName(getStringValue(inputParams, "user_name"));
        return req;
    }

    /**
     * 从Map获取字符串值
     */
    private String getStringValue(Map<String, Object> inputParams, String key) {
        Object value = inputParams.get(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 从Redis缓存获取工单实体列表
     *
     * @param conversationId 会话ID
     * @return 工单实体列表
     */
    private Mono<List<ServiceTicketEntity>> getTicketEntitiesFromCache(String conversationId) {
        if (StringUtils.isBlank(conversationId)) {
            return Mono.just(new ArrayList<>());
        }

        String cacheKey = String.format(CONVERSATION_TICKETS_KEY, conversationId);
        ReactiveHashOperations<String, String, String> hashOps = reactiveRedisTemplate.opsForHash();

        // 检查缓存是否存在
        return reactiveRedisTemplate.hasKey(cacheKey)
                .flatMap(exists -> {
                    if (!exists) {
                        // 缓存不存在，直接从数据库加载
                        return loadAndCacheTicketEntities(conversationId);
                    }

                    // 获取Hash中的所有值
                    return hashOps.values(cacheKey)
                            .map(this::parseTicketEntity)
                            .filter(Objects::nonNull)
                            .collectList()
                            .flatMap(entities -> {
                                if (entities.isEmpty()) {
                                    // 缓存解析失败或为空，从数据库重新加载
                                    return loadAndCacheTicketEntities(conversationId);
                                }
                                return Mono.just(entities);
                            });
                });
    }

    /**
     * 解析JSON字符串为工单实体
     */
    private ServiceTicketEntity parseTicketEntity(String json) {
        try {
            return JSONUtil.toBean(json, ServiceTicketEntity.class);
        } catch (Exception e) {
            log.warn("解析缓存工单实体失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从数据库加载工单实体并缓存
     *
     * @param conversationId 会话ID
     * @return 工单实体列表
     */
    private Mono<List<ServiceTicketEntity>> loadAndCacheTicketEntities(String conversationId) {
        if (StringUtils.isBlank(conversationId)) {
            return Mono.just(new ArrayList<>());
        }

        // 构建查询
        Criteria criteria = Criteria.where(ServiceTicketFieldEnum.conversation_id.name()).is(conversationId);
        Query query = Query.query(criteria);

        // 使用Schedulers.boundedElastic()执行I/O操作
        return r2dbcEntityTemplate.select(query, ServiceTicketEntity.class)
                .collectList()
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(entities -> {
                    if (CollUtil.isEmpty(entities)) {
                        log.debug("数据库中不存在会话 {} 的工单", conversationId);
                        return Mono.just(new ArrayList<ServiceTicketEntity>());
                    }

                    cacheTicketEntities(conversationId, entities)
                            .doOnSuccess(result -> log.info("成功缓存会话ID为{}的工单实体，共{}条数据", conversationId, entities.size()))
                            .subscribeOn(Schedulers.boundedElastic())
                            .subscribe();
                    return Mono.just(entities);
                })
                .doOnError(e -> log.error("从数据库加载工单失败 | conversationId: {} | error: {}",
                        conversationId, e.getMessage()));
    }

    /**
     * 将工单实体列表缓存到Redis
     *
     * @param conversationId 会话ID
     * @param entities 工单实体列表
     * @return 缓存成功后返回原工单实体列表
     */
    private Mono<List<ServiceTicketEntity>> cacheTicketEntities(String conversationId, List<ServiceTicketEntity> entities) {
        if (CollUtil.isEmpty(entities)) {
            return Mono.just(entities);
        }

        String cacheKey = String.format(CONVERSATION_TICKETS_KEY, conversationId);
        ReactiveHashOperations<String, String, String> hashOps = reactiveRedisTemplate.opsForHash();

        // 构建批量存储操作
        Map<String, String> entityMap = entities.stream()
                .filter(entity -> entity.getId() != null)
                .collect(Collectors.toMap(
                        entity -> entity.getId().toString(),
                        JSONUtil::toJsonStr,
                        (v1, v2) -> v2  // 处理可能的ID冲突，保留后值
                ));

        if (entityMap.isEmpty()) {
            return Mono.just(entities);
        }

        // 删除旧缓存，然后添加新缓存并设置过期时间
        return reactiveRedisTemplate.delete(cacheKey)
                .then(hashOps.putAll(cacheKey, entityMap))
                .then(reactiveRedisTemplate.expire(cacheKey, CACHE_TTL))
                .thenReturn(entities)
                .doOnSuccess(result -> log.debug("成功缓存 {} 条工单 | conversationId: {}",
                        entityMap.size(), conversationId))
                .onErrorResume(e -> {
                    log.error("缓存工单失败 | conversationId: {} | error: {}",
                            conversationId, e.getMessage());
                    return Mono.just(entities);  // 缓存失败仍返回实体列表
                });
    }

    /**
     * 根据条件过滤工单实体
     *
     * @param entities 工单实体列表
     * @param inputParams 过滤参数
     * @return 过滤后的工单列表
     */
    private List<ServiceTicketEntity> filterTicketEntities(List<ServiceTicketEntity> entities, Map<String, Object> inputParams) {
        if (CollUtil.isEmpty(entities)) {
            return new ArrayList<>();
        }

        // 提前获取过滤条件，避免在循环中重复获取
        Object statusParam = inputParams.get(ServiceTicketFieldEnum.status.name());
        String categoryParam = getStringValue(inputParams, ServiceTicketFieldEnum.service_category.name());

        // 过滤工单
        List<ServiceTicketEntity> filteredList = entities.stream()
                .filter(entity -> matchesStatusFilter(entity, statusParam))
                .filter(entity -> matchesCategoryFilter(entity, categoryParam))
                .collect(Collectors.toList());

        // 限制返回条数
        int limit = getIntValue(inputParams, "limit", DEFAULT_PAGE_SIZE);
        return limit < filteredList.size() ?
                filteredList.subList(0, limit) :
                filteredList;
    }

    /**
     * 检查工单是否匹配状态过滤条件
     */
    private boolean matchesStatusFilter(ServiceTicketEntity entity, Object statusParam) {
        if (statusParam == null || entity.getStatus() == null) {
            return true;
        }
        return entity.getStatus().toString().equals(statusParam.toString());
    }

    /**
     * 检查工单是否匹配类型过滤条件
     */
    private boolean matchesCategoryFilter(ServiceTicketEntity entity, String categoryParam) {
        if (StringUtils.isBlank(categoryParam)) {
            return true;
        }
        return categoryParam.equals(entity.getServiceCategory());
    }

    /**
     * 从Map中获取整数值，并确保不超过最大值
     *
     * @param params 参数Map
     * @param key 键名
     * @param defaultValue 默认值
     * @param maxValue 最大允许值
     * @return 解析后的整数值
     */
    private int getIntValue(Map<String, Object> params, String key, int defaultValue, int maxValue) {
        Object value = params.get(key);
        if (value == null) {
            return defaultValue;
        }

        try {
            int intValue = Integer.parseInt(value.toString());
            return intValue <= maxValue ? intValue : maxValue;
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 从Map中获取整数值（使用默认的最大页面大小限制）
     */
    private int getIntValue(Map<String, Object> params, String key, int defaultValue) {
        return getIntValue(params, key, defaultValue, DEFAULT_PAGE_SIZE);
    }

    /**
     * 工单实体列表转VO列表
     */
    private List<ServiceTicketVO> entitiesToVOs(List<ServiceTicketEntity> entities) {
        if (CollUtil.isEmpty(entities)) {
            return new ArrayList<>();
        }

        return entities.stream()
                .map(this::toSimpleVO)
                .collect(Collectors.toList());
    }

    /**
     * 通过Groovy获取工单列表（前端接口，使用缓存）
     *
     * @param inputParams 查询参数
     * @return JSON格式的分页结果
     */
    public Mono<String> getTicketsByGroovy(Map<String, Object> inputParams) {
        log.info("getTicketGuestRequest,获取工单列表 | inputParams: {}", JSONUtil.toJsonStr(inputParams));
        String conversationId = getStringValue(inputParams, ServiceTicketFieldEnum.conversation_id.name());
        if (StringUtils.isBlank(conversationId)) {
            log.warn("获取工单列表失败：会话ID为空");
            return Mono.just(JSONUtil.toJsonStr(PageResult.create(0L, new ArrayList<>())));
        }

        return getTicketEntitiesFromCache(conversationId)
                .map(entities -> filterTicketEntities(entities, inputParams))
                .map(this::convertToPageResult)
                .onErrorResume(e -> {
                    log.error("从缓存获取工单列表失败: {}", e.getMessage());
                    // 出错时回退到数据库查询
                    return queryTicketsFromDatabase(inputParams);
                })
                .switchIfEmpty(queryTicketsFromDatabase(inputParams))
                .doOnSuccess(result -> log.debug("获取工单列表成功 | conversationId: {}", conversationId));
    }

    /**
     * 将过滤后的工单实体转换为分页结果JSON
     */
    private String convertToPageResult(List<ServiceTicketEntity> entities) {
        List<ServiceTicketVO> vos = entitiesToVOs(entities);
        PageResult<ServiceTicketVO> pageResult = PageResult.create((long) vos.size(), vos);
        return JSONUtil.toJsonStr(pageResult);
    }

    /**
     * 从数据库查询工单并转换为JSON
     */
    private Mono<String> queryTicketsFromDatabase(Map<String, Object> inputParams) {
        GetTicketReq getTicketReq = createGetTicketReq(inputParams);
        return getTickets(getTicketReq)
                .map(JSONUtil::toJsonStr)
                .onErrorResume(e -> {
                    log.error("从数据库查询工单失败: {}", e.getMessage());
                    return Mono.just(JSONUtil.toJsonStr(PageResult.create(0L, new ArrayList<>())));
                });
    }

    /**
     * 通过Groovy获取工单用户请求内容（前端接口，使用缓存）
     */
    public Mono<String> getTicketGuestRequestByGroovy(Map<String, Object> inputParams) {
        log.info("getTicketGuestRequest,获取工单列表 | inputParams: {}", JSONUtil.toJsonStr(inputParams));
        String conversationId = getStringValue(inputParams, ServiceTicketFieldEnum.conversation_id.name());
        if (StringUtils.isBlank(conversationId)) {
            return Mono.empty();
        }

        return getTicketEntitiesFromCache(conversationId)
                .map(entities -> filterTicketEntities(entities, inputParams))
                .map(filteredEntities -> {
                    if (CollUtil.isEmpty(filteredEntities)) {
                        return "";
                    }

                    // 获取并连接所有请求内容
                    return filteredEntities.stream()
                            .map(ServiceTicketEntity::getGuestRequest)
                            .filter(Objects::nonNull)
                            .collect(Collectors.joining(", "));
                })
                .filter(StringUtils::isNotBlank)
                .switchIfEmpty(
                        // 缓存为空时查询数据库
                        Mono.defer(() -> {
                            GetTicketReq getTicketReq = createGetTicketReq(inputParams);
                            return getTickets(getTicketReq)
                                    .flatMap(pageResult -> {
                                        if (pageResult == null || CollUtil.isEmpty(pageResult.getDataList())) {
                                            return Mono.empty();
                                        }

                                        // 连接所有请求内容
                                        String allRequests = pageResult.getDataList().stream()
                                                .map(ServiceTicketVO::getGuestRequest)
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.joining(", "));

                                        return Mono.justOrEmpty(allRequests);
                                    });
                        })
                )
                .doOnSuccess(result -> log.debug("获取工单用户请求内容 | conversationId: {}", conversationId));
    }

    /**
     * 创建查询工单请求
     */
    @NotNull
    private GetTicketReq createGetTicketReq(Map<String, Object> inputParams) {
        GetTicketReq getTicketReq = new GetTicketReq();

        // 设置状态
        Object status = inputParams.get(ServiceTicketFieldEnum.status.name());
        getTicketReq.setStatus(Objects.nonNull(status) ? Integer.parseInt(status.toString()) : null);

        // 设置是否超时
        Object overdue = inputParams.get(ServiceTicketFieldEnum.is_overdue.name());
        getTicketReq.setIsOverdue(Objects.nonNull(overdue) ? Boolean.parseBoolean(overdue.toString()) : null);

        // 设置其他参数
        getTicketReq.setServiceCategory(getStringValue(inputParams, ServiceTicketFieldEnum.service_category.name()));
        getTicketReq.setConversationId(getStringValue(inputParams, ServiceTicketFieldEnum.conversation_id.name()));
        getTicketReq.setCurrent(1);

        // 设置分页大小
        int limit = getIntValue(inputParams, "limit", DEFAULT_PAGE_SIZE);
        getTicketReq.setPageSize(limit);

        return getTicketReq;
    }
}