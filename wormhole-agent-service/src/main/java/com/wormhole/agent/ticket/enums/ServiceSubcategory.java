package com.wormhole.agent.ticket.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ServiceSubcategory {
    // 送物服务子分类
    DE_BATHROOM("DE_BATHROOM", "卫浴用品", ServiceCategory.DELIVERY,
            "毛巾、洗漱用品、浴袍、拖鞋、浴巾等卫生间用品"),
    DE_BEDDING("DE_BEDDING", "床上用品", ServiceCategory.DELIVERY,
            "枕头、被子、床单、毯子等床铺相关用品"),
    DE_BEVERAGE("DE_BEVERAGE", "饮品服务", ServiceCategory.DELIVERY,
            "饮用水、迷你吧饮料、咖啡、茶、冰桶等饮品相关"),
    DE_ELECTRONIC("DE_ELECTRONIC", "电子用品", ServiceCategory.DELIVERY,
            "充电器、转换器、遥控器、电池等电子相关设备"),
    DE_AMENITY("DE_AMENITY", "便利用品", ServiceCategory.DELIVERY,
            "衣架、洗衣袋、蚊香液、医疗用品、缝纫包等杂项用品"),
    DE_SERVICE("DE_SERVICE", "特殊服务", ServiceCategory.DELIVERY,
            "夜床服务、额外物品、特殊请求等其他送物服务"),

    // 清洁服务子分类
    CL_ROOM("CL_ROOM", "房间清洁", ServiceCategory.CLEANING, ""),

    // 维修服务子分类
    MT_PLUMBING("MT_PLUMBING", "水暖问题", ServiceCategory.MAINTENANCE,
            "水龙头、淋浴、马桶、漏水等水暖相关问题"),
    MT_ELECTRIC("MT_ELECTRIC", "电器问题", ServiceCategory.MAINTENANCE,
            "电灯、电视、插座、电器、空调等电力相关问题"),
    MT_FACILITY("MT_FACILITY", "设施问题", ServiceCategory.MAINTENANCE,
            "家具、门窗、保险箱等房间设施问题"),
    MT_NETWORK("MT_NETWORK", "网络问题", ServiceCategory.MAINTENANCE,
            "WiFi连接、网速、电话等通信问题"),

    // 洗衣服务子分类
    LD_STANDARD("LD_STANDARD", "标准洗衣", ServiceCategory.LAUNDRY,
            "包括普通洗衣和干洗服务"),
    LD_EXPRESS("LD_EXPRESS", "快速洗衣", ServiceCategory.LAUNDRY,
            "加急洗衣服务，更快完成"),
    LD_SPECIAL("LD_SPECIAL", "特殊洗衣", ServiceCategory.LAUNDRY,
            "熨烫服务及其他特殊需求"),

    // 入住相关子分类
    ST_EXTENSION("ST_EXTENSION", "续住服务", ServiceCategory.STAY, ""),
    ST_EARLY("ST_EARLY", "提前入住", ServiceCategory.STAY, ""),
    ST_LATE("ST_LATE", "延迟退房", ServiceCategory.STAY, ""),
    ST_CHANGE("ST_CHANGE", "换房", ServiceCategory.STAY, ""),
    ST_UPGRADE("ST_UPGRADE", "升级房型", ServiceCategory.STAY, ""),
    ST_ADD_GUEST("ST_ADD_GUEST", "增加入住人", ServiceCategory.STAY, ""),
    ST_RETURN_ITEMS("ST_RETURN_ITEMS", "归还物品", ServiceCategory.STAY, ""),

    // 礼宾服务子分类
    CO_PORTER("CO_PORTER", "行李搬运", ServiceCategory.CONCIERGE, ""),
    CO_STORAGE("CO_STORAGE", "行李寄存", ServiceCategory.CONCIERGE, ""),
    CO_DELIVERY("CO_DELIVERY", "行李递送", ServiceCategory.CONCIERGE, ""),

    // 交通服务子分类
    TP_TAXI("TP_TAXI", "出租车预订", ServiceCategory.TRANSPORT, ""),
    TP_SHUTTLE("TP_SHUTTLE", "接送服务", ServiceCategory.TRANSPORT, ""),
    TP_RENTAL("TP_RENTAL", "租车服务", ServiceCategory.TRANSPORT, ""),
    TP_PARKING("TP_PARKING", "停车服务", ServiceCategory.TRANSPORT, ""),

    // 餐饮服务子分类
    FD_ORDER("FD_ORDER", "点餐服务", ServiceCategory.FOOD, ""),
    FD_DELIVERY("FD_DELIVERY", "送餐服务", ServiceCategory.FOOD, ""),

    // 预订服务子分类
    BK_DINING("BK_DINING", "餐饮预订", ServiceCategory.BOOKING,
            "包含餐厅、酒吧等所有餐饮相关预订"),
    BK_FACILITY("BK_FACILITY", "设施预订", ServiceCategory.BOOKING,
            "健身房、游泳池等酒店设施预订"),
    BK_TOUR("BK_TOUR", "旅游预订", ServiceCategory.BOOKING,
            "周边游览、景点门票等预订"),
    BK_EVENT("BK_EVENT", "活动预订", ServiceCategory.BOOKING,
            "酒店活动、演出等预订"),
    BK_VENUE("BK_VENUE", "场地预订", ServiceCategory.BOOKING,
            "会议室、多功能厅、宴会厅等场地预订"),
    BK_WELLNESS("BK_WELLNESS", "康体预订", ServiceCategory.BOOKING,
            "SPA、按摩等康体服务预订"),

    // 特殊需求子分类
    SP_WELCOME("SP_WELCOME", "客房欢迎", ServiceCategory.SPECIAL, ""),
    SP_PRIVACY("SP_PRIVACY", "客房隐私", ServiceCategory.SPECIAL, ""),
    SP_REQUIREMENT("SP_REQUIREMENT", "房间特殊要求", ServiceCategory.SPECIAL, ""),
    SP_DISABLE("SP_DISABLE", "残障人士要求", ServiceCategory.SPECIAL, ""),
    SP_MORNINGCALL("SP_MORNINGCALL", "叫醒服务", ServiceCategory.SPECIAL, ""),

    // 投诉处理子分类
    CP_SERVICE("CP_SERVICE", "服务投诉", ServiceCategory.COMPLAINT, ""),
    CP_FACILITY("CP_FACILITY", "设施投诉", ServiceCategory.COMPLAINT, ""),
    CP_NOISE("CP_NOISE", "噪音投诉", ServiceCategory.COMPLAINT, ""),
    CP_BILLING("CP_BILLING", "账单投诉", ServiceCategory.COMPLAINT, ""),
    CP_ROOM("CP_ROOM", "客房投诉", ServiceCategory.COMPLAINT, ""),
    CP_FOOD("CP_FOOD", "餐饮投诉", ServiceCategory.COMPLAINT, ""),
    CP_ENVIRONMENT("CP_ENVIRONMENT", "环境卫生投诉", ServiceCategory.COMPLAINT, ""),
    CP_PRIVACY("CP_PRIVACY", "隐私问题投诉", ServiceCategory.COMPLAINT, ""),
    CP_PRICE("CP_PRICE", "价格倒挂投诉", ServiceCategory.COMPLAINT, ""),

    // 通用服务子分类
    GE_OTHER("GE_OTHER", "其他服务", ServiceCategory.GENERAL, ""),

    // 新增紧急分类子项
    EM_MEDICAL("EM_MEDICAL", "医疗紧急", ServiceCategory.EMERGENCY,
              "医疗协助、救护车需求、急救需求、食品安全问题"),
    EM_SECURITY("EM_SECURITY", "安全紧急", ServiceCategory.EMERGENCY,
              "可疑情况、盗窃事件、人身威胁、人身安全等安全问题"),
    EM_FACILITY("EM_FACILITY", "设施紧急", ServiceCategory.EMERGENCY,
              "火灾隐患、水患事件、电力故障、结构问题等设施紧急故障"),
    EM_IMMEDIATE("EM_IMMEDIATE", "紧急协助", ServiceCategory.EMERGENCY,
              "被锁事件、其他需要立即协助的紧急情况"),

    ;
    private final String code;
    private final String chineseName;
    private final ServiceCategory parentCategory;
    private final String description;


    public static ServiceSubcategory getByCode(String code) {
        return Arrays.stream(ServiceSubcategory.values()).filter(ele-> ele.getCode().equals(code)).findFirst().orElse(null);
    }



    // 根据父分类获取所有子分类（静态工厂方法）
    public static List<ServiceSubcategory> getSubcategoriesByCategory(ServiceCategory category) {
        return Arrays.stream(ServiceSubcategory.values())
                .filter(sub -> sub.getParentCategory().equals(category))
                .collect(Collectors.toList());
    }

    // 反向查找：根据子分类获取服务类型
    public ServiceType getServiceType() {
        return getParentCategory().getServiceType();
    }
}