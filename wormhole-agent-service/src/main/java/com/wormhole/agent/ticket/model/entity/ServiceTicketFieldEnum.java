package com.wormhole.agent.ticket.model.entity;

public enum ServiceTicketFieldEnum {
    id,
    ticket_no,
    status,
    is_overdue,
    priority,
    service_category,
    service_subcategory,
    service_category_name,
    service_subcategory_name,
    guest_request,
    tool,
    hotel_code,
    room_no,
    rtc_room_id,
    sla_deadline,
    completion_time,
    completed_by,
    completed_by_name,
    device_id,
    conversation_id,
    client_req_id,
    created_by,
    created_by_name,
    updated_by,
    updated_by_name,
    created_at,
    updated_at,
    row_status,
    ;
    private ServiceTicketFieldEnum() {
    }
}