package com.wormhole.agent.ticket.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@Table("ws_dept_service_mapping")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DeptServiceMapping extends BaseEntity implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;
    private String departmentCodes;
    private String serviceCategory;
    private String hotelCode;
    private String serviceCategoryName;
}
