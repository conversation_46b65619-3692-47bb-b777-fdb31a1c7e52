package com.wormhole.agent.ticket.controller;

import com.wormhole.agent.ticket.model.*;
import com.wormhole.agent.ticket.model.entity.GetCallStatsReq;
import com.wormhole.agent.ticket.model.entity.ServiceTicketEntity;
import com.wormhole.agent.ticket.service.ServiceTicketService;
import com.wormhole.agent.workflow.bot.user.UserSearchHttpService;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

/**
 * 服务工单管理
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/tickets")
public class ServiceTicketController {

    private final ServiceTicketService serviceTicketService;

    private final UserSearchHttpService userSearchHttpService;

    @PostMapping("/create_ticket")
    public Mono<Result<String>> createTicket(@RequestBody CreateTicketReq req) {
        return serviceTicketService.createTicket(req).flatMap(Result::success);

    }

    @PostMapping("/get_tickets")
    public Mono<Result<PageResult<ServiceTicketVO>>> getTickets(@RequestBody GetTicketReq req) {
        return serviceTicketService.getTickets(req).flatMap(Result::success);

    }


    @PostMapping("/update_ticket_status")
    public Mono<Result<String>> updateTicketStatus(@RequestBody UpdateTicketReq req) {
        return Mono.just(null);

    }
    @PostMapping("/get_call_stats")
    public Mono<Result<Boolean>> getCallStats(@RequestBody GetCallStatsReq req) {
        return Mono.just(null);
    }

    /**
     * 查询房间工单列表（酒店员工侧）
     * @param req
     * @return
     */
    @PostMapping("/get_room_tickets_by_staff")
    public Mono<Result<List<RoomTicketVO>>> getRoomTicketsByStaff(@RequestBody GetRoomTicketReq req) {
        return Mono.just(null);
    }

    /**
     * 查询房间工单列表（用户侧）
     * @param req
     * @return
     */
    @PostMapping("/get_room_tickets_by_user")
    public Mono<Result<RoomTicketVO>> getRoomTicketsByUser(@RequestBody GetRoomTicketReq req) {
        return Mono.just(null);
    }

    /**
     * 催单(用户侧)
     * @param req
     * @return
     */
    @PostMapping("/reminder")
    public Mono<Boolean> reminder(@RequestBody UpdateTicketReq req ){
        return Mono.just(null);
    }



}
