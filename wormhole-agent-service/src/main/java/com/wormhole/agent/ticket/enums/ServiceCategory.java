package com.wormhole.agent.ticket.enums;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.wormhole.agent.ticket.enums.ServiceSubcategory;
import com.wormhole.agent.ticket.enums.ServiceType;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.stream.Collectors;

import static com.wormhole.agent.ticket.enums.ServiceSubcategory.*;

@Getter
@AllArgsConstructor
public enum ServiceCategory {
    DELIVERY("DELIVERY", "送物服务", Lists.newArrayList(
            ServiceSubcategory.DE_BATHROOM,
            ServiceSubcategory.DE_BEDDING,
            ServiceSubcategory.DE_BEVERAGE,
            ServiceSubcategory.DE_ELECTRONIC,
            ServiceSubcategory.DE_AMENITY,
            ServiceSubcategory.DE_SERVICE
    )),

    CLEANING("CLEANING", "清洁服务", Lists.newArrayList(
            ServiceSubcategory.CL_ROOM
    )),

    MAINTENANCE("MAINTENANCE", "维修服务", Lists.newArrayList(
            ServiceSubcategory.MT_PLUMBING,
            ServiceSubcategory.MT_ELECTRIC,
            ServiceSubcategory.MT_FACILITY,
            ServiceSubcategory.MT_NETWORK
    )),

    LAUNDRY("LAUNDRY", "洗衣服务", Lists.newArrayList(
            ServiceSubcategory.LD_STANDARD,
            ServiceSubcategory.LD_EXPRESS,
            ServiceSubcategory.LD_SPECIAL
    )),

    STAY("STAY", "入住相关", Lists.newArrayList(
            ServiceSubcategory.ST_EXTENSION,
            ServiceSubcategory.ST_EARLY,
            ServiceSubcategory.ST_LATE,
            ServiceSubcategory.ST_CHANGE,
            ServiceSubcategory.ST_UPGRADE,
            ServiceSubcategory.ST_ADD_GUEST,
            ServiceSubcategory.ST_RETURN_ITEMS
    )),

    CONCIERGE("CONCIERGE", "礼宾服务", Lists.newArrayList(
            ServiceSubcategory.CO_PORTER,
            ServiceSubcategory.CO_STORAGE,
            ServiceSubcategory.CO_DELIVERY
    )),

    TRANSPORT("TRANSPORT", "交通服务", Lists.newArrayList(
            ServiceSubcategory.TP_TAXI,
            ServiceSubcategory.TP_SHUTTLE,
            ServiceSubcategory.TP_RENTAL,
            ServiceSubcategory.TP_PARKING
    )),

    FOOD("FOOD", "餐饮服务", Lists.newArrayList(
            ServiceSubcategory.FD_ORDER,
            ServiceSubcategory.FD_DELIVERY
    )),

    BOOKING("BOOKING", "预订服务", Lists.newArrayList(
            ServiceSubcategory.BK_DINING,
            ServiceSubcategory.BK_FACILITY,
            ServiceSubcategory.BK_TOUR,
            ServiceSubcategory.BK_EVENT,
            ServiceSubcategory.BK_VENUE,
            ServiceSubcategory.BK_WELLNESS
    )),

    SPECIAL("SPECIAL", "特殊需求", Lists.newArrayList(
            ServiceSubcategory.SP_WELCOME,
            ServiceSubcategory.SP_PRIVACY,
            ServiceSubcategory.SP_REQUIREMENT,
            ServiceSubcategory.SP_DISABLE,
            ServiceSubcategory.SP_MORNINGCALL
    )),

    COMPLAINT("COMPLAINT", "投诉处理", Lists.newArrayList(
            ServiceSubcategory.CP_SERVICE,
            ServiceSubcategory.CP_FACILITY,
            ServiceSubcategory.CP_NOISE,
            ServiceSubcategory.CP_BILLING,
            ServiceSubcategory.CP_ROOM,
            ServiceSubcategory.CP_FOOD,
            ServiceSubcategory.CP_ENVIRONMENT,
            ServiceSubcategory.CP_PRIVACY,
            ServiceSubcategory.CP_PRICE
    )),

    GENERAL("GENERAL", "通用服务", Lists.newArrayList(
            ServiceSubcategory.GE_OTHER
    )),

    EMERGENCY("EMERGENCY", "紧急事项",Lists.newArrayList(
            ServiceSubcategory.EM_MEDICAL,
            ServiceSubcategory.EM_SECURITY,
            ServiceSubcategory.EM_FACILITY,
            ServiceSubcategory.EM_IMMEDIATE
    ));


    private final String code;
    private final String chineseName;
    private final List<ServiceSubcategory> subcategories;
    private final ServiceType serviceType;



    ServiceCategory(String code, String chineseName, List<ServiceSubcategory> subcategories) {
        this.code = code;
        this.chineseName = chineseName;
        this.subcategories = new CopyOnWriteArrayList<>(subcategories);
        this.serviceType = determineServiceType();
    }

    private ServiceType determineServiceType() {
        return switch (this.code) {
            case "DELIVERY", "CLEANING", "MAINTENANCE", "LAUNDRY", "STAY",
                    "CONCIERGE", "TRANSPORT", "FOOD", "BOOKING", "SPECIAL", "COMPLAINT", "EMERGENCY"
                    -> ServiceType.SERVICE;
            case "GENERAL" -> ServiceType.INQUIRY;
            default -> throw new IllegalArgumentException("未知分类代码: " + code);
        };
    }
    private final static Map<String, ServiceCategory> SERVICE_CATEGORIES_MAP = new HashMap<>();
    static {
        for (ServiceCategory value : ServiceCategory.values()) {
            SERVICE_CATEGORIES_MAP.put(value.getCode(), value);
        }
    }

    public static ServiceCategory getByCode(String code){
        return SERVICE_CATEGORIES_MAP.get(code);
    }

    public static List<ServiceCategory> getServiceCategoriesByType(ServiceType type) {
        return Arrays.stream(ServiceCategory.values())
                .filter(category -> category.getServiceType().equals(type))
                .collect(Collectors.toList());
    }
}