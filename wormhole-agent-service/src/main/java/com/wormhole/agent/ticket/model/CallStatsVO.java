package com.wormhole.agent.ticket.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/17
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CallStatsVO implements Serializable {
    private Integer totalCallCounts;
    private Integer totalDuration;
    private Integer totalAnsweredCalls;
    private List<TicketStats> ticketStatsList;
    private List<InquiryStats> inquiryStatsList;
    private String hotTicketName;
    private String hotInquiryName;
    private String peelHourTime;
    private String peelHourCount;


    @Data
    public static class TicketStats implements Serializable {
        private String serviceCategoryCode;
        private String serviceCategoryName;
        private Integer totalCount;
        private Integer successCount;
    }
    @Data
    public static class  InquiryStats implements Serializable {
        private String inquiryType;
        private String inquiryTypeName;
        private Integer totalCount;
    }
}
