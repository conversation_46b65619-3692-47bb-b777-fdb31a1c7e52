package com.wormhole.agent.ticket.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreateTicketReq {
    private String serviceType;
    private String serviceCategory;
    private String serviceSubcategory;

    private String guestRequest;
    private String tool;

    private String hotelCode;
    private String roomNo;
    private String rtcRoomId;

    private String deviceId;
    private String conversationId;
    private String clientReqId;


    private String userId; // 用户id 填充为工单创建人
    private String userName;

    // ticketNo 不为空时修改工单为完成状态
//    private String ticketNo;
}
