package com.wormhole.agent.ticket.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.domain.Sort;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class GetTicketReq {
    private Integer status;

    private String serviceCategory;

    private Boolean isOverdue;

    /**
     * 页码从1开始
     */
    private Integer current = 1;

    /**
     * 每页显示记录数，必须大于0
     */
    private Integer pageSize = 10;

    private String hotelCode;

    private String roomNo;

    private String deviceId;

    private String userId;

    private String conversationId;

    private Sort.Direction sort;

    private String sortName;

}
