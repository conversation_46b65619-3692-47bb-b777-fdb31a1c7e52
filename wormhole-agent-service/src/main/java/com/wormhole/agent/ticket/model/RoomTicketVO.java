package com.wormhole.agent.ticket.model;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;
import software.amazon.awssdk.services.bedrockruntime.endpoints.internal.Value;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class RoomTicketVO implements Serializable {
    private String roomNumber;
    private String roomMobile;
    private List<TicketSimpleVO> ticketSimpleVOList;

    @Data
    public static class TicketSimpleVO {
        private String ticketNo;
        private LocalDateTime createAt;
        private String serviceCategoryName;
        private String serviceSubcategoryName;

        private String guestRequest;
        private String guestMobile;
        /**
         * 处理类型
         */
        private String processType;
        /**
         * 处理名称：如立即派送，回电，立即上门...
         */
        private String processTypeName;
        /**
         * 处理状态 0-待处理 1-已完成
         */
        private Integer status;
        /**
         * 是否显示提醒按钮
         */
        private Boolean showReminder;
    }
}
