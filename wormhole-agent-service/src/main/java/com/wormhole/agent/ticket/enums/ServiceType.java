package com.wormhole.agent.ticket.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 顶层服务类型枚举
@Getter
public enum ServiceType {
    SERVICE("服务工单"),
    EMERGENCY("紧急事项"),
    INQUIRY("咨询类"),
    FEEDBACK("反馈类"),
    NOTIFICATION("通知类");

    private final String chineseName;

    ServiceType(String chineseName) {
        this.chineseName = chineseName;
    }

    private final static Map<String, ServiceType> SERVICE_TYPE_MAP = new HashMap<>();
    static {
        for (ServiceType value : ServiceType.values()) {
            SERVICE_TYPE_MAP.put(value.name(), value);
        }
    }

    public static ServiceType getByCode(String code){
        return SERVICE_TYPE_MAP.get(code);
    }
    // 根据类型获取所有服务分类
    public List<ServiceCategory> getServiceCategories() {
        return ServiceCategory.getServiceCategoriesByType(this);
    }
}



