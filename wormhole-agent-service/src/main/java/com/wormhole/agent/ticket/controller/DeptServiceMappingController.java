package com.wormhole.agent.ticket.controller;

import com.wormhole.agent.ticket.model.CreateTicketReq;
import com.wormhole.agent.ticket.model.DeptServiceMappingVO;
import com.wormhole.agent.ticket.model.GetDeptServiceMappingReq;
import com.wormhole.agent.ticket.model.UpsertDeptServiceMappingReq;
import com.wormhole.common.result.Result;
import lombok.RequiredArgsConstructor;
import org.redisson.api.annotation.REntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/dept_service_mapping")
public class DeptServiceMappingController {



    @PostMapping("/get")
    public Mono<Result<List<DeptServiceMappingVO>>> get(@RequestBody GetDeptServiceMappingReq req) {
        return Mono.just(null);
    }
    @PostMapping("/upsert")
    public Mono<Boolean> upsert(@RequestBody UpsertDeptServiceMappingReq req){
        return Mono.just(null);
    }
}
