package com.wormhole.agent.ticket.model.entity;

import com.wormhole.common.model.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Column;
import org.springframework.data.relational.core.mapping.Table;

import java.time.LocalDateTime;

@Data
@Table("ws_service_tickets")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ServiceTicketEntity extends BaseEntity {
    /**
     * 主键ID
     */
    @Id
    @Column("id")
    private Long id;

    /**
     * 工单唯一编号
     */
    @Column("ticket_no")
    private String ticketNo;

    /**
     * 状态: 0-待处理 1-已完成
     */
    private Integer status;

    /**
     * 是否超时
     */
    @Column("is_overdue")
    private Boolean isOverdue;

    /**
     * 优先级: 0-普通 1-紧急 2-置顶
     */
    private Integer priority;

    private String serviceType;
    /**
     * 服务类别代码
     */
    @Column("service_category")
    private String serviceCategory;

    /**
     * 服务子类别代码
     */
    @Column("service_subcategory")
    private String serviceSubcategory;

    /**
     * 服务类别名称
     */
    @Column("service_category_name")
    private String serviceCategoryName;

    /**
     * 服务子类别名称
     */
    @Column("service_subcategory_name")
    private String serviceSubcategoryName;

    /**
     * 客户诉求内容
     */
    @Column("guest_request")
    private String guestRequest;

    /**
     * 结构化工具输出
     */
    private String tool; // 需配置JSON转换器

    /**
     * 酒店代码
     */
    @Column("hotel_code")
    private String hotelCode;

    /**
     * 房间号
     */
    @Column("room_no")
    private String roomNo;

    /**
     * RTC语音房间ID
     */
    @Column("rtc_room_id")
    private String rtcRoomId;

    /**
     * 服务级别协议截止时间
     */
    @Column("sla_deadline")
    private LocalDateTime slaDeadline;

    /**
     * 实际完成时间
     */
    @Column("completion_time")
    private LocalDateTime completionTime;

    /**
     * 完成人员工号
     */
    @Column("completed_by")
    private String completedBy;

    /**
     * 完成人员姓名
     */
    @Column("completed_by_name")
    private String completedByName;

    /**
     * 设备标识
     */
    @Column("device_id")
    private String deviceId;

    /**
     * 对话标识
     */
    @Column("conversation_id")
    private String conversationId;

    /**
     * 客户端请求标识
     */
    @Column("client_req_id")
    private String clientReqId;




}