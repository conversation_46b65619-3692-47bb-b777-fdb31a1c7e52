package com.wormhole.agent.chat.function;

import com.wormhole.agent.chat.function.context.FunctionNameEnum;
import com.wormhole.agent.chat.function.context.FunctionResult;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.model.openai.ChatFunctionCall;
import com.wormhole.agent.model.openai.ChatToolCall;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
@Component
public class ChatbotChatFunction extends AbstractChatFunction {

    @Override
    public boolean support(ChatToolCall chatToolCallDTO) {
        ChatFunctionCall chatFunctionCall = chatToolCallDTO.getFunction();
        return StringUtils.equalsIgnoreCase(chatFunctionCall.getName(), FunctionNameEnum.chatbot.name());
    }

    @Override
    public Mono<FunctionResult> call(ChatContext chatContext, ChatToolCall chatToolCall) {
        return Mono.just(new FunctionResult());
    }

}
