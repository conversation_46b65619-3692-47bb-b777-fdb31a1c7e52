package com.wormhole.agent.chat.memory.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.wormhole.agent.chat.memory.params.MemoryChatMessage;
import com.wormhole.agent.chat.memory.params.ReadMemoryParams;
import com.wormhole.agent.chat.memory.params.StoreMemoryParams;
import com.wormhole.agent.chat.model.ChatConstant;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.service.ChatService;
import com.wormhole.common.util.DateParsePatterns;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.LocalDateTimeUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.StringJoiner;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@Slf4j
@Service
public class RecentMemoryService {

    private static final Duration EXPIRATION = Duration.of(30, ChronoUnit.DAYS);

    @Resource
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    @Autowired
    private ChatService chatService;

    /**
     * user.recent.memory.v2#{conversationId}
     *
     * @param conversationId
     * @return
     */
    private String getCacheKey( String conversationId) {
        conversationId = StringUtils.defaultIfBlank(conversationId, StringUtils.EMPTY);
        return new StringJoiner("#").add("user.recent.memory.v2").add(conversationId).toString();
    }

    public Mono<ChatContext> storeMemory(ChatContext chatContext) {
        if (!chatContext.isStoreMemory()) {
            return Mono.just(chatContext);
        }
        chatContext.getExecutionStatManager().start(ExecutionStatType.store_memory, "Beginning memory storage");
        StoreMemoryParams storeMemoryParams = new StoreMemoryParams();
        storeMemoryParams.setConversationId(chatContext.getConversationId());
        storeMemoryParams.setQuestion(chatContext.getQuestion());
        storeMemoryParams.setAnswer(chatContext.getAnswer());
        String retryMessageId = chatService.getValueFromPayLoad(chatContext.getChatParams().getPayload(), ChatConstant.PAYLOAD_KEY_RETRY_MESSAGE_ID);
        storeMemoryParams.setRetryMessageId(retryMessageId);
        return storeMemory(storeMemoryParams)
                .onErrorResume(e -> {
                    // 捕获异常并记录日志
                    log.error("Memory storage failed", e);
                    chatContext.getExecutionStatManager().fail(ExecutionStatType.store_memory, e);
                    return Mono.empty();
                })
                .doOnSuccess( signalType -> {
                    chatContext.getExecutionStatManager().end(ExecutionStatType.store_memory, "Memory storage completed");
                    long elapsedMs = chatContext.getExecutionStatManager().getStatData(ExecutionStatType.store_memory).getElapsedMs();
                    log.info("Memory storage completed, elapsed: {}ms", elapsedMs);
                })
                .thenReturn(chatContext);
    }

    public Mono<Boolean> storeMemory(StoreMemoryParams storeMemoryParams) {
        if (StringUtils.isBlank(storeMemoryParams.getConversationId())
                || StringUtils.isBlank(storeMemoryParams.getQuestion())
                || StringUtils.isBlank(storeMemoryParams.getAnswer())) {
            return Mono.just(true);
        }
        String redisKey = getCacheKey(storeMemoryParams.getConversationId());
        return reactiveStringRedisTemplate.opsForValue().get(redisKey)
                .switchIfEmpty(Mono.just(StringUtils.EMPTY))
                .flatMap(json -> {
                    List<MemoryChatMessage> memoryChatMessages = Lists.newArrayList();

                    LocalDateTime localDateTime = LocalDateTime.now();
                    long bornTime = LocalDateTimeUtils.toEpochMilli(localDateTime);
                    String bornTimeStr = localDateTime.format(DateParsePatterns.FULL_FORMATTER);

                    if (StringUtils.isNotBlank(json)) {
                        memoryChatMessages = JacksonUtils.readValue(json, new TypeReference<>() {
                        });
                    }

                    // question
                    if (StringUtils.isNotBlank(storeMemoryParams.getRetryMessageId())){
                        // 重试信息不为空，则删除之前的回答+不保存问题
                        memoryChatMessages.remove(memoryChatMessages.size()-1);
                    } else {
                        MemoryChatMessage userMessage = new MemoryChatMessage(ChatRole.USER.getValue()).setContent(storeMemoryParams.getQuestion());
                        userMessage.setBornTime(bornTime);
                        userMessage.setBornTimeStr(bornTimeStr);
                        userMessage.setScore(0d);
                        memoryChatMessages.add(userMessage);
                    }

                    // answer
                    MemoryChatMessage answerMessage = new MemoryChatMessage(ChatRole.ASSISTANT.getValue()).setContent(storeMemoryParams.getAnswer());
                    answerMessage.setBornTime(bornTime);
                    answerMessage.setBornTimeStr(bornTimeStr);
                    answerMessage.setScore(0d);
                    memoryChatMessages.add(answerMessage);
                    if (StringUtils.isNotBlank(storeMemoryParams.getRetryMessageId())){
                        log.info("retry message memory origin:{} now:{}",json, JacksonUtils.writeValueAsString(memoryChatMessages));
                    }

                    // 存储的对话数
                    int maxSize = storeMemoryParams.getMaxSize();
                    List<MemoryChatMessage> resultList = memoryChatMessages;
                    if (memoryChatMessages.size() > 2 * maxSize) {
                        resultList = memoryChatMessages.subList(memoryChatMessages.size() - 2 * maxSize, memoryChatMessages.size());
                    }
                    return reactiveStringRedisTemplate.opsForValue().set(redisKey, JacksonUtils.writeValueAsString(resultList), EXPIRATION);
                });

    }

    public Mono<List<OpenAiChatMessage>> getRecentMemory(ReadMemoryParams readMemoryParams) {
        return Mono.defer(() -> {
            if (readMemoryParams.getMaxSize() <= 0) {
                return Mono.just(Lists.newArrayList());
            }
            String redisKey = getCacheKey(readMemoryParams.getConversationId());
            return reactiveStringRedisTemplate.opsForValue().get(redisKey)
                    .map(str -> {
                        List<MemoryChatMessage> cachedList = JacksonUtils.readValue(str, new TypeReference<>() {
                        });
                        List<MemoryChatMessage> filteredList = cachedList;
                        if (filteredList.size() > 2 * readMemoryParams.getMaxSize()) {
                            // 否则，从最后的2 * N条消息中获取问答对
                            filteredList = cachedList.subList(filteredList.size() - 2 * readMemoryParams.getMaxSize(), filteredList.size());
                        }
                        return filteredList.stream()
                                .map(item -> {
                                    OpenAiChatMessage openAIChatMessage = new OpenAiChatMessage();
                                    openAIChatMessage.setRole(item.getRole());
                                    openAIChatMessage.setContent(item.getContent());
                                    openAIChatMessage.setName(item.getName());
                                    return openAIChatMessage;
                                })
                                .toList();
                    })
                    .onErrorResume(e -> {
                        log.error(e.getMessage(), e);
                        return Mono.just(Lists.newArrayList());
                    })
                    .switchIfEmpty(Mono.just(Lists.newArrayList()));
        });
    }

    public Mono<Boolean> deleteMemory(String conversationId) {
        return Mono.defer(() -> {
            String redisKey = getCacheKey( conversationId);
            return reactiveStringRedisTemplate.opsForValue().delete(redisKey)
                    .onErrorResume(e -> {
                        log.error(e.getMessage(), e);
                        return Mono.just(Boolean.FALSE);
                    })
                    .switchIfEmpty(Mono.just(Boolean.TRUE));
        });
    }

}
