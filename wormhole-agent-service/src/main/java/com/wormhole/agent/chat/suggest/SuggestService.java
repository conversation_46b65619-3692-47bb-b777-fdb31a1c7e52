package com.wormhole.agent.chat.suggest;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wormhole.agent.ai.core.service.ChatClientService;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.model.openai.OpenAiChatParams;
import com.wormhole.agent.nacos.config.TemplateEnum;
import com.wormhole.agent.nacos.listener.TemplateService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @date 2025/2/18 11:42
 */
@Slf4j
@Service
public class SuggestService {

    @Resource
    private ChatClientService chatClientService;

    @Resource
    private TemplateService templateService;

    public Flux<ChatCompletions> generateSuggestedQuestions(ChatContext chatContext) {
        ModelContext modelContext = buildModelContext(chatContext);
        return chatClientService.chatCompletions(modelContext)
                .doOnNext(chatCompletions -> {
                    chatContext.sinkNext(chatCompletions);
                    if (chatCompletions.isChatEnd()) {
                        chatContext.setAnswer(modelContext.getAnswer());
                    }
                });
    }

    private ModelContext buildModelContext(ChatContext chatContext) {
        BotInfo botInfo = chatContext.getBotInfo();
        ModelInfo modelInfo = botInfo.getModelInfo();
        OpenAiChatParams openAiChatParams = buildOpenAiChatParams(chatContext, modelInfo);
        ModelContext.ModelLogContext modelLogContext = chatContext.bulidModelLogContext();
        modelLogContext.setQuestion(chatContext.getAnswer());
        return ModelContext.builder()
                .openAiChatParams(openAiChatParams)
                .modelLogContext(modelLogContext)
                .build();
    }

    private OpenAiChatParams buildOpenAiChatParams(ChatContext chatContext, ModelInfo modelInfo) {
        return OpenAiChatParams.builder()
                .stream(true)
                .model(modelInfo.getModel())
                .modelProvider(modelInfo.getModelProvider())
                .temperature(modelInfo.getTemperature())
                .maxTokens(Objects.nonNull(modelInfo.getMaxTokens()) && modelInfo.getMaxTokens() > 0 ? modelInfo.getMaxTokens() : null)
                .frequencyPenalty(modelInfo.getFrequencyPenalty())
                .messages(buildChatMessageList(chatContext))
                .build();
    }

    private List<OpenAiChatMessage> buildChatMessageList(ChatContext chatContext) {
        List<OpenAiChatMessage> chatMessageList = Lists.newArrayList();
        String template = templateService.getTemplate(TemplateEnum.suggest_system_prompt);
        Map<String, Object> map = Maps.newHashMap();
        map.put("answer", chatContext.getAnswer());
        String systemPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(template, map);

        OpenAiChatMessage systemMessage = OpenAiChatMessage.builder().role(ChatRole.SYSTEM.getValue()).content(systemPrompt).build();
        chatMessageList.add(systemMessage);
        OpenAiChatMessage userMessage = OpenAiChatMessage.builder().role(ChatRole.USER.getValue()).content("").build();
        chatMessageList.add(userMessage);
        return chatMessageList;
    }
}
