package com.wormhole.agent.chat.memory.params;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class StoreMemoryParams {
    private String conversationId;
    private String question;
    private String answer;
    @Builder.Default
    private int maxSize = 3;
    private String retryMessageId;
}
