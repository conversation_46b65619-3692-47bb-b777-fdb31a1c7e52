package com.wormhole.agent.chat.function;

import com.google.common.collect.Maps;
import com.wormhole.agent.chat.function.context.FunctionNameEnum;
import com.wormhole.agent.chat.function.context.FunctionResult;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.model.openai.ChatFunctionCall;
import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.common.util.JacksonUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
@Component
public class DirectAnswerChatFunction extends AbstractChatFunction {

    @Override
    public boolean support(ChatToolCall chatToolCallDTO) {
        ChatFunctionCall chatFunctionCall = chatToolCallDTO.getFunction();
        return StringUtils.equalsIgnoreCase(chatFunctionCall.getName(), FunctionNameEnum.direct_answer.name());
    }

    @Override
    public Mono<FunctionResult> call(ChatContext chatContext, ChatToolCall chatToolCall) {
        FunctionResult functionResult = FunctionResult.builder().directAnswer(getAnswerText(chatToolCall)).build();
        return Mono.just(functionResult);
    }

    public String getAnswerText(ChatToolCall chatToolCall) {
        Map<String, Object> arguments = Optional.ofNullable(chatToolCall.getFunction().getArguments())
                .map(JacksonUtils::readValue)
                .orElse(Maps.newHashMap());
        return MapUtils.getString(arguments, "answer_text");
    }
}