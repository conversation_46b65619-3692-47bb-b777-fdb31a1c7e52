package com.wormhole.agent.chat.intent.model;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

/**
 * Content
 *
 * <AUTHOR>
 * @version 2025/1/27
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = TextContent.class, name = "text"),
        @JsonSubTypes.Type(value = ImageUrlContent.class, name = "image_url")
})
public interface Content {
    String getType();
}