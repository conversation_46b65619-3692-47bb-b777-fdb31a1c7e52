package com.wormhole.agent.chat.memory.params;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * <AUTHOR>
 * @date 2024/8/30 22:17
 **/
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class MemoryChatMessage {
    /*
     * The role associated with this message payload.
     */
    @JsonProperty(value = "role", required = true)
    private String role;

    /*
     * The text associated with this message payload.
     */
    @JsonProperty(value = "content")
    private String content;

    /*
     * The name of the author of this message.
     */
    @JsonProperty(value = "name")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String name;

    @JsonProperty(value = "born_time")
    private Long bornTime = 0L;

    @JsonProperty(value = "born_time_str")
    private String bornTimeStr = "1970-01-01 08:00:00";

    @JsonProperty(value = "score")
    private Double score = 0D;

    /**
     * Creates an instance of OpenAIChatMessage class.
     *
     * @param role the role value to set.
     */
    @JsonCreator
    public MemoryChatMessage(@JsonProperty(value = "role", required = true) String role) {
        this.role = role;
    }

    /**
     * Get the role property: The role associated with this message payload.
     *
     * @return the role value.
     */
    public String getRole() {
        return this.role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    /**
     * Get the content property: The text associated with this message payload.
     *
     * @return the content value.
     */
    public String getContent() {
        return this.content;
    }

    /**
     * Set the content property: The text associated with this message payload.
     *
     * @param content the content value to set.
     * @return the ChatMessage object itself.
     */
    public MemoryChatMessage setContent(String content) {
        this.content = content;
        return this;
    }

    /**
     * Get the content property: The name of the author of this message.
     *
     * @return the content value.
     */
    public String getName() {
        return name;
    }

    /**
     * Set the content property: The name of the author of this message.
     *
     * @param name the content value to set.
     * @return the ChatMessage object itself.
     */
    public MemoryChatMessage setName(String name) {
        this.name = name;
        return this;
    }

    public Long getBornTime() {
        return bornTime;
    }

    public void setBornTime(Long bornTime) {
        this.bornTime = bornTime;
    }

    public String getBornTimeStr() {
        return bornTimeStr;
    }

    public void setBornTimeStr(String bornTimeStr) {
        this.bornTimeStr = bornTimeStr;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }
}
