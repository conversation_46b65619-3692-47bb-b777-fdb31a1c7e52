package com.wormhole.agent.chat.context;

import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.ChatParams;
import com.wormhole.agent.core.constant.AiConstant;
import com.wormhole.agent.core.model.chat.ChatInputType;
import com.wormhole.agent.core.model.chat.ChatProtocol;
import com.wormhole.agent.core.model.chat.ChatResponseMode;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.service.ConnectionService;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.common.constant.HeaderConstant;
import com.wormhole.common.util.IdUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.trace.TraceContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import reactor.core.publisher.Mono;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/2 17:10
 **/
@Slf4j
public abstract class ChatContextPreProcessor<T extends ChatParams> {

    @Resource
    private ConnectionService connectionService;

    public abstract boolean support(ChatContext chatContext);

    public abstract Mono<ChatContext> setupSpecificParameters(ChatContext chatContext);

    public Mono<ChatContext> preProcess(ChatContext chatContext) {
        return Mono.deferContextual(contextView -> {
            ChatParams chatParams = chatContext.getChatParams();
            chatContext.getExecutionStatManager().start(ExecutionStatType.chat_access, "chat request start");
            chatContext.setChatInputType(ChatInputType.from(chatParams.getChatInputType()));
            chatContext.setChatResponseMode(ChatResponseMode.from(chatParams.getChatResponseMode()));
            chatContext.setChatProtocol(ChatProtocol.from(chatParams.getChatProtocol()));
            
            chatContext.setTraceId(TraceContext.getTraceId());
            // 基本信息
            chatContext.setClientReqId(Optional.ofNullable(chatParams.getClientReqId()).orElse(IdUtils.generateId()));
            chatContext.setConversationId(Optional.ofNullable(chatParams.getConversationId()).orElse(IdUtils.generateId()));

            if (Objects.nonNull(chatContext.getHttpHeaders())){
                chatContext.setUserId(Optional.ofNullable(chatContext.getHttpHeaders().getFirst(HeaderConstant.USER_ID)).orElse(contextView.get(HeaderConstant.USER_ID)));
                chatContext.setUsername(contextView.get(HeaderConstant.USERNAME));
                chatContext.setSource(Optional.ofNullable(chatContext.getHttpHeaders().getFirst(HeaderConstant.SOURCE)).orElse(contextView.get(HeaderConstant.SOURCE)));
                if (StringUtils.isNotBlank(chatContext.getHttpHeaders().getFirst(HeaderConstant.CLIENT_TYPE))) {
                    chatContext.setClientType(Optional.ofNullable(chatContext.getHttpHeaders().getFirst(HeaderConstant.CLIENT_TYPE)).orElse(contextView.get(HeaderConstant.CLIENT_TYPE)));
                }
                if (StringUtils.isBlank(chatContext.getDeviceId())) {
                    chatContext.setDeviceId(Optional.ofNullable(chatContext.getHttpHeaders().getFirst(HeaderConstant.DEVICE_ID)).orElse(contextView.get(HeaderConstant.DEVICE_ID)));
                }
            }
            MDC.put(AiConstant.CLIENT_REQ_ID, chatContext.getClientReqId());
            MDC.put(AiConstant.CONVERSATION_ID, chatContext.getConversationId());
            return setupSpecificParameters(chatContext);
        });
    }

    // 临时方法，废弃
    @Deprecated
    public Mono<ChatContext> setDeviceInfo(ChatContext chatContext, String deviceId) {

        log.info("set device info for deviceId:{} chatContext chatParams:{}", deviceId, chatContext.getChatParams());

        // 如果 deviceId 为空，则尝试从 payload 中获取
        if (StringUtils.isBlank(deviceId)) {
            deviceId = Optional.ofNullable(chatContext.getChatParams())
                    .map(ChatParams::getPayload)
                    .map(payload -> (String) payload.get(WorkflowConstant.DEVICE_ID))
                    .orElse(null);
        }

        // 如果 deviceId 仍然为空，则直接返回 chatContext
        if (StringUtils.isBlank(deviceId)) {
            return Mono.just(chatContext);
        }

        // 设置 deviceId 并获取设备信息
        chatContext.setDeviceId(deviceId);
        return connectionService.getDeviceInfo(deviceId, 1)
                .flatMap(resp -> {
                    ChatParams chatParams = chatContext.getChatParams();
                    Map<String, Object> payload = chatContext.getChatParams().getPayload();
                    if (MapUtils.isNotEmpty(payload)) {
                        payload.put(WorkflowConstant.HOTEL_CODE, resp.getHotelCode());
                        payload.put(WorkflowConstant.ROOM_NO, resp.getPositionCode());
                    }
                    chatParams.setPayload(payload);
                    chatContext.setRoomNo(resp.getPositionCode());
                    chatContext.setHotelCode(resp.getHotelCode());
                    log.info("setDeviceInfo chatContext: {}", JacksonUtils.writeValueAsString(chatContext));
                    return Mono.just(chatContext);
                }).onErrorResume(throwable -> Mono.just(chatContext));
    }

}
