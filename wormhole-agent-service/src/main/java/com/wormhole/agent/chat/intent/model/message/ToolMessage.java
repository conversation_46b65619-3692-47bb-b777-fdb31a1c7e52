package com.wormhole.agent.chat.intent.model.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * ToolMessage
 *
 * <AUTHOR>
 * @version 2025/1/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ToolMessage extends AbstractChatMessage {
    private final String toolCallId;
    private final String name;
    private final String content;

    @JsonCreator
    public ToolMessage(@JsonProperty("tool_call_id") String toolCallId, @JsonProperty("name") String name, @JsonProperty("content") String content) {
        this.role = "tool";
        this.toolCallId = toolCallId;
        this.name = name;
        this.content = content;
    }
}
