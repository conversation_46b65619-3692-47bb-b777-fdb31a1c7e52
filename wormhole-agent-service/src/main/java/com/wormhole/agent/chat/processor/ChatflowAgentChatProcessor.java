package com.wormhole.agent.chat.processor;

import com.google.common.base.Stopwatch;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.workflow.service.WorkflowService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 对话流
 *
 * <AUTHOR>
 * @version 2025/2/17
 */
@Slf4j
@Component
public class ChatflowAgentChatProcessor extends AgentChatProcessor {

    @Resource
    private WorkflowService workflowService;



    @Override
    public boolean support(ChatContext chatContext) {
        BotModeEnum botModeEnum = Optional.ofNullable(chatContext.getBotInfo()).map(BotInfo::getBotMode).map(BotModeEnum::from).orElse(null);
        return ChatType.AGENT.equals(chatContext.getChatType()) && Objects.equals(BotModeEnum.CHATFLOW_AGENT, botModeEnum);
    }

    @Override
    public Flux<ChatContext> doChatCompletions(ChatContext chatContext) {
        return Flux.defer(() -> {
            Stopwatch stopwatch = Stopwatch.createStarted();
            return loadMemory(chatContext)
                    .flatMap(cc -> workflowService.executeWorkflow(chatContext))
                    .flux()
                    // .flatMapMany(cc -> postProcessWorkflow(chatContext))
                    .doOnSubscribe(subscription -> log.info("gatewayToNow:{}, doPostProcessor", System.currentTimeMillis() - chatContext.getGatewayStartTime().toEpochMilli()))
                    .doOnError(throwable -> chatContext.addContextValue("chat_processor_error_msg", throwable.getMessage()))
                    .doOnTerminate(() -> {
                        stopwatch.stop();
                        log.info("gatewayToNow:{}, doPostProcessor, elapsed:{}", System.currentTimeMillis() - chatContext.getGatewayStartTime().toEpochMilli(), stopwatch.elapsed(TimeUnit.MILLISECONDS));
                    });
        });
    }

}
