package com.wormhole.agent.chat.intent.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.agent.chat.intent.model.message.ChatMessage;
import com.wormhole.agent.chat.intent.model.message.UserMessage;
import com.wormhole.common.util.JacksonUtils;

import java.util.List;

/**
 * ChatMessageTest
 *
 * <AUTHOR>
 * @version 2025/1/27
 */
public class ChatMessageTest {

    public static void main(String[] args) throws Exception {
        // 配置ObjectMapper
        ObjectMapper mapper = new ObjectMapper()
                .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
                .setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // 1. 处理简单文本消息
        String simpleJson = "{\"role\":\"user\",\"content\":\"Hello\"}";
        ChatMessage simpleMessage = mapper.readValue(simpleJson, ChatMessage.class);
        System.out.println(simpleMessage.getContent()); // 输出: Hello

        // 2. 处理带图片的复合消息
        String complexJson = "{\"role\":\"user\",\"content\":[" +
                "{\"type\":\"text\",\"text\":\"What's in this image?\"}," +
                "{\"type\":\"image_url\",\"image_url\":{\"url\":\"https://example.com/image.jpg\"}}" +
                "]}";
        ChatMessage complexMessage = mapper.readValue(complexJson, ChatMessage.class);
        List<Content> contents = (List<Content>) complexMessage.getContent();
        System.out.println(contents);

        UserMessage userMessage = UserMessage.builder().content("test").build();
        System.out.println(JacksonUtils.writeValueAsString(userMessage));
    }

}
