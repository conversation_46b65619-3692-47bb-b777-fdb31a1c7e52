package com.wormhole.agent.chat.intent.model.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.wormhole.common.util.JacksonUtils;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * SystemMessage
 *
 * <AUTHOR>
 * @version 2025/1/27
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(value = {"role"}, allowGetters = true)
public class SystemMessage extends AbstractChatMessage {
    String content;

    @Builder.Default
    String role = "system";

    public static void main(String[] args) {
        SystemMessage systemMessage = SystemMessage.builder().content("test").build();
        System.out.println(JacksonUtils.writeValueAsString(systemMessage));
    }
}
