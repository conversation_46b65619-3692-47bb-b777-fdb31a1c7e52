package com.wormhole.agent.chat.intent.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * TextContent
 *
 * <AUTHOR>
 * @version 2025/1/27
 */
public class TextContent implements Content {
    private final String text;

    @JsonCreator
    public TextContent(@JsonProperty("text") String text) {
        this.text = text;
    }

    @Override
    public String getType() {
        return "text";
    }

    public String getText() {
        return text;
    }
}