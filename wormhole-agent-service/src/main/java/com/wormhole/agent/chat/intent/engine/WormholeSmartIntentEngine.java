package com.wormhole.agent.chat.intent.engine;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.wormhole.agent.ai.core.service.ChatClientService;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.chat.intent.model.IntentEngineEnum;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.constant.AiConstant;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.intent.SmartIntentResult;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.core.util.ChatMessageUtils;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.model.openai.ChatChoice;
import com.wormhole.agent.model.openai.ChatMessage;
import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.model.openai.OpenAiChatParams;
import com.wormhole.agent.model.openai.OpenAiFunction;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.nacos.config.TemplateEnum;
import com.wormhole.agent.nacos.listener.TemplateService;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowDefinition;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
@Slf4j
@Component
public class WormholeSmartIntentEngine implements SmartIntentEngine {

    @Resource
    private ChatClientService chatClientService;
    @Resource
    private TemplateService templateService;

    @Override
    public IntentEngineEnum getIntentEngine() {
        return IntentEngineEnum.wormhole;
    }

    @Override
    public Mono<ChatContext> smartIntent(ChatContext chatContext) {
        return Mono.defer(() -> {
            // bot没有绑定工作流，则并不需要进行意图识别
            if (CollectionUtils.isEmpty(chatContext.getWorkflowList())) {
                return Mono.just(chatContext);
            }
            List<OpenAiTool> openAiTools = chatContext.getWorkflowList().stream()
                    .map(Workflow::getWorkflowDefinition)
                    .map(WorkflowDefinition::getTool)
                    .collect(Collectors.toList());
            return Mono.just(openAiTools)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(tools -> dispatcherIntent(chatContext, openAiTools))
                    .onErrorResume(throwable -> {
                        log.error("smart_intent_error: {}", throwable.getMessage(), throwable);
                        chatContext.addContextValue("smart_intent_error_msg", throwable.getMessage());
                        return Mono.just(chatContext);
                    })
                    .thenReturn(chatContext);
        });
    }

    /**
     * 返回的内容包含：content+tools，content，tools
     *
     * @param chatContext
     * @param toolCallList
     * @return
     */
    private Mono<ChatContext> dispatcherIntent(ChatContext chatContext, List<OpenAiTool> toolCallList) {
        if (CollectionUtils.isEmpty(toolCallList)) {
            return Mono.just(chatContext);
        }
        ModelContext modelContext = buildModelContext(chatContext, toolCallList);
        if (Boolean.TRUE.equals(modelContext.getOpenAiChatParams().getStream())) {
            return asyncIntent(chatContext, modelContext);
        } else {
            return syncIntent(chatContext, modelContext);
        }
    }

    public Mono<ChatContext> asyncIntent(ChatContext chatContext, ModelContext modelContext) {
        StringBuffer contentBuffer = new StringBuffer();
        return chatClientService.chatCompletions(modelContext)
                .doOnNext(chatCompletions -> {
                    String content = ChatCompletionUtils.getContent(chatCompletions);
                    if (StringUtils.isNotBlank(content)) {
                        // 输出当前分片
                        contentBuffer.append(content);
                        chatCompletions.getMetadata().setConversationId(chatContext.getConversationId());
                        chatContext.sinkNext(chatCompletions);
                    }
                    if (chatCompletions.isChatEnd() && !contentBuffer.isEmpty()) {
                        // 记录完整结果
                        chatContext.setAnswer(contentBuffer.toString());
                    }
                })
                .reduce(new ArrayList<ChatToolCall>(), (chatToolCallList, chatCompletionsDTO) -> {
                    List<ChatChoice> choices = chatCompletionsDTO.getChoices();
                    if (CollectionUtils.isNotEmpty(choices)) {
                        ChatMessage delta = choices.get(0).getDelta();
                        List<ChatToolCall> list = delta.getToolCalls();
                        if (CollectionUtils.isNotEmpty(list)) {
                            chatToolCallList.addAll(list);
                        }
                    }
                    return chatToolCallList;
                })
                .doOnNext(chatToolCallList -> {
                    SmartIntentResult smartIntentResult = new SmartIntentResult();
                    chatContext.setSmartIntentResult(smartIntentResult);
                    if (CollectionUtils.isNotEmpty(chatToolCallList)) {
                        Map<Integer, ChatToolCall> indexToToolCallMap = Maps.newLinkedHashMap();
                        for (ChatToolCall current : chatToolCallList) {
                            if (Objects.isNull(current.getIndex())) {
                                continue;
                            }
                            ChatToolCall toolCall = indexToToolCallMap.get(current.getIndex());
                            if (Objects.nonNull(toolCall)) {
                                StringBuilder arguments = new StringBuilder();
                                if (StringUtils.isNotEmpty(toolCall.getFunction().getArguments())) {
                                    arguments.append(toolCall.getFunction().getArguments());
                                }
                                if (StringUtils.isNotEmpty(current.getFunction().getArguments())) {
                                    arguments.append(current.getFunction().getArguments());
                                }
                                toolCall.getFunction().setArguments(arguments.toString());
                            } else {
                                indexToToolCallMap.put(current.getIndex(), current);
                            }
                        }
                        List<ChatToolCall> toolCalls = new ArrayList<>(indexToToolCallMap.values());
                        log.info("smart_intent_result, resultList:\n{}", JacksonUtils.writeValuePretty(toolCalls));
                        smartIntentResult.setChatToolCallList(toolCalls);
                    }
                    if (!contentBuffer.isEmpty()) {
                        String content = contentBuffer.toString();
                        log.info("smart_intent_result, content: {}", content);
                        smartIntentResult.setContent(content);
                    }
                })
                .thenReturn(chatContext);
    }

    private Mono<ChatContext> syncIntent(ChatContext chatContext, ModelContext modelContext) {
        return chatClientService.chatCompletions(modelContext)
                .next()
                .doOnNext(chatCompletions -> {
                    Optional<SmartIntentResult> smartIntentResultOptional = ChatCompletionUtils.getSmartIntentResult(chatCompletions);
                    if (smartIntentResultOptional.isPresent()) {
                        SmartIntentResult smartIntentResult = smartIntentResultOptional.get();
                        chatContext.setSmartIntentResult(smartIntentResult);
                        String content = ChatCompletionUtils.getContent(chatCompletions);
                        if (StringUtils.isNotBlank(content)) {
                            smartIntentResult.setContent(content);
                            log.info("smart_intent_result: {}", smartIntentResult);
                            chatContext.sinkNext(chatCompletions);
                            chatContext.setAnswer(content);
                        }
                    }
                })
                .thenReturn(chatContext);
    }

    private ModelContext buildModelContext(ChatContext chatContext, List<OpenAiTool> toolsList) {
        // system prompt
        Map<String, Object> systemPromptDataMap = ImmutableMap.of("current_time", LocalDateTime.now());
        String systemTemplate = templateService.getTemplate(TemplateEnum.intent_system_prompt);

        String systemPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(systemTemplate, systemPromptDataMap);
        OpenAiChatMessage systemMessage = OpenAiChatMessage.builder().role(ChatRole.SYSTEM.getValue()).content(systemPrompt).build();

        // user prompt
        Map<String, Object> userPromptDataMap = ImmutableMap.of(AiConstant.QUESTION, chatContext.getQuestion());
        String userTemplate = templateService.getTemplate(TemplateEnum.intent_user_prompt);
        String userPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(userTemplate, userPromptDataMap);
        OpenAiChatMessage userMessage = OpenAiChatMessage.builder().role(ChatRole.USER.getValue()).content(userPrompt).build();

        List<OpenAiChatMessage> chatMessageList = new ArrayList<>();
        chatMessageList.add(systemMessage);
        if (CollectionUtils.isNotEmpty(chatContext.getRecentMessageList())) {
            int recentRound = Optional.ofNullable(chatContext.getBotInfo()).map(BotInfo::getModelInfo).map(ModelInfo::getRecentRound).orElse(0);
            List<OpenAiChatMessage> recentMessageList = ChatMessageUtils.getRecentMessageList(chatContext.getRecentMessageList(), recentRound);
            chatMessageList.addAll(recentMessageList);
        }
        chatMessageList.add(userMessage);

        toolsList.forEach(tool -> {
            OpenAiFunction function = tool.getFunction();
            OpenAiFunction.OpenAiParameters parameters = function.getParameters();
            Map<String, OpenAiFunction.PropertiesInfo> properties = parameters.getProperties();
            properties.put(WorkflowConstant.ENHANCED_QUERY, OpenAiFunction.PropertiesInfo.builder()
                    .type("string")
                    .description("利用多轮对话的上下文信息对用户的输入进行增强和重写，用于RAG的检索查询。")
                    .build());
        });
        OpenAiChatParams openAiChatParams = OpenAiChatParams.builder()
                .stream(false)
                .model(UnifiedModelEnum.GPT_4_O.getModel())
                .modelProvider(UnifiedModelEnum.GPT_4_O.getProvider())
                .temperature(0d)
                .messages(chatMessageList)
                .tools(toolsList)
                .build();

        return ModelContext.builder()
                .openAiChatParams(openAiChatParams)
                .modelLogContext(chatContext.bulidModelLogContext())
                .build();
    }

}
