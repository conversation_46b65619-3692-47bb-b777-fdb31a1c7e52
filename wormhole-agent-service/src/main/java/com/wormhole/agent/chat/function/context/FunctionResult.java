package com.wormhole.agent.chat.function.context;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FunctionResult {

    private List<FunctionOrganicResult> organicResults;

    private Map<String, Object> financeAnswer;

    private Map<String, Object> answerBox;

    private String directAnswer;

}
