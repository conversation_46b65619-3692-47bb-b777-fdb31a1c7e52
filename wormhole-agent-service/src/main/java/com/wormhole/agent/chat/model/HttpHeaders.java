package com.wormhole.agent.chat.model;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.constant.CommonHeaderKeys;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/9/2 17:10
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HttpHeaders implements Serializable {

    @Serial
    private static final long serialVersionUID = 7397402270001194538L;

    @JsonProperty(CommonHeaderKeys.SOURCE)
    private String source;

    @JsonAlias({"UserId", "user_id", "acccount_id"})
    private String userId;

    @JsonProperty(CommonHeaderKeys.TIMESTAMP)
    private Long timestamp;

    @JsonProperty(CommonHeaderKeys.TRACE_ID)
    private String traceId;
}
