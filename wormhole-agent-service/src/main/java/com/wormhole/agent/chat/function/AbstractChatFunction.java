package com.wormhole.agent.chat.function;

import com.wormhole.agent.chat.function.context.FunctionResult;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.model.openai.ChatToolCall;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
public abstract class AbstractChatFunction {

    public abstract boolean support(ChatToolCall chatToolCallDTO);

    public abstract Mono<FunctionResult> call(ChatContext chatContext, ChatToolCall chatToolCall);

}