package com.wormhole.agent.chat.intent.model;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.List;

/**
 * ContentDeserializer
 *
 * <AUTHOR>
 * @version 2025/1/27
 */
public class ContentDeserializer extends JsonDeserializer<Object> {
    @Override
    public Object deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);
        ObjectMapper mapper = (ObjectMapper) p.getCodec();

        if (node.isTextual()) {
            return node.asText();
        } else if (node.isArray()) {
            // 使用 TypeReference 指定具体的类型
            return mapper.convertValue(node,
                    new TypeReference<List<Content>>() {
                    });
        }
        return null;
    }
}
