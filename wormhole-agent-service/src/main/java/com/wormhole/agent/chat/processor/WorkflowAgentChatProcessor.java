package com.wormhole.agent.chat.processor;

import com.google.common.base.Stopwatch;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.workflow.service.WorkflowService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * 工作流执行
 *
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@Slf4j
@Service
public class WorkflowAgentChatProcessor extends AgentChatProcessor {

    @Resource
    private WorkflowService workflowService;


    @Override
    public boolean support(ChatContext chatContext) {
        BotModeEnum botModeEnum = Optional.ofNullable(chatContext.getBotInfo()).map(BotInfo::getBotMode).map(BotModeEnum::from).orElse(null);
        return ChatType.AGENT.equals(chatContext.getChatType()) && Objects.equals(BotModeEnum.WORKFLOW_AGENT, botModeEnum);
    }

    @Override
    public Flux<ChatContext> doChatCompletions(ChatContext chatContext) {
        Stopwatch stopwatch = Stopwatch.createStarted();
        return workflowService.executeWorkflow(chatContext).flux()
                // .flatMapMany(cc -> postProcessWorkflow(chatContext))
                .doOnSubscribe(subscription -> log.info("gatewayToNow:{}, doPostProcessor", System.currentTimeMillis() - chatContext.getGatewayStartTime().toEpochMilli()))
                .doOnError(throwable -> chatContext.addContextValue("chat_processor_error_msg", throwable.getMessage()))
                .doOnTerminate(() -> {
                    stopwatch.stop();
                    log.info("gatewayToNow:{}, doPostProcessor, elapsed:{}", System.currentTimeMillis() - chatContext.getGatewayStartTime().toEpochMilli(), stopwatch.elapsed(TimeUnit.MILLISECONDS));
                });
    }

}