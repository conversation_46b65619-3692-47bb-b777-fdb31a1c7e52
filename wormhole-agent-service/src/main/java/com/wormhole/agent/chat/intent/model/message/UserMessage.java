package com.wormhole.agent.chat.intent.model.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.wormhole.agent.chat.intent.model.ContentDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * UserMessage
 *
 * <AUTHOR>
 * @version 2025/1/27
 */
@Data
@Builder
public class UserMessage extends AbstractChatMessage {

    @JsonDeserialize(using = ContentDeserializer.class)
    private final Object content;

    @JsonCreator
    public UserMessage(@JsonProperty("content") Object content) {
        this.role = "user";
        this.content = content;
    }

    @Override
    public Object getContent() {
        return content;
    }
}