package com.wormhole.agent.chat.intent.engine;

import com.fasterxml.jackson.databind.*;
import com.google.common.collect.*;
import com.wormhole.agent.chat.intent.base.*;
import com.wormhole.agent.chat.intent.model.*;
import com.wormhole.agent.chat.model.*;
import com.wormhole.agent.core.constant.*;
import com.wormhole.agent.core.util.*;
import com.wormhole.agent.model.openai.*;
import com.wormhole.common.exception.*;
import com.wormhole.common.result.*;
import com.wormhole.common.util.*;
import jakarta.annotation.*;
import lombok.extern.slf4j.*;
import org.apache.commons.lang3.*;
import org.springframework.http.HttpHeaders;
import org.springframework.http.*;
import org.springframework.stereotype.*;
import org.springframework.web.reactive.function.client.*;
import reactor.core.publisher.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.time.*;
import java.util.*;

/**
 * python dataflow smart intent engine
 */
@Component
@Slf4j
public class DataflowSmartIntentEngine implements SmartIntentEngine {

    @Resource
    private DataflowConfig dataflowConfig;

    private static final WebClient WEB_CLIENT;

    static {
        WEB_CLIENT = WebClient.builder()
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .build();
    }

    @Override
    public IntentEngineEnum getIntentEngine() {
        return IntentEngineEnum.dataflow;
    }

    @Override
    public Mono<ChatContext> smartIntent(ChatContext chatContext) {
        return chatCompletions(chatContext)
                .doOnNext(chatContext::sinkNext)
                .then(Mono.just(chatContext));
    }

//    public Flux<ChatCompletions> chatCompletions(ChatContext chatContext) {
//        return processChatCompletions(chatContext)
//                .filter(StringUtils::isNotBlank)
//                .filter(json -> !StringUtils.equalsIgnoreCase(StringUtils.trim(json), AiConstant.DONE))
//                .flatMapSequential(this::postProcessChatCompletions)
//                .onErrorResume(t -> {
//                    log.error("chatCompletions error:{}", t.getMessage(), t);
//                    return Flux.empty();
//                });
//    }
//
//    public Flux<ChatCompletions> postProcessChatCompletions(String json){
//        return Flux.just(json)
//                .map(t -> JacksonUtils.readValue(json, Node.Value.class))
//                .filter(Objects::nonNull)
//                .filter(t -> t.getType().equals("token"))
//                .map(this::convertToChatCompletions); //转换逻辑
//    }
//
//    private ChatCompletions convertToChatCompletions(Node.Value value) {
//        ChatCompletionUtils.extractJsonString(value.getContent().toString());
//        ChatCompletions chatCompletions = new ChatCompletions();
//        // 根据实际需求进行转换
//        return chatCompletions;
//    }


    public Flux<String> processChatCompletions(ChatContext chatContext) {
        Map<String, String> reqMap = Maps.newHashMap();
        reqMap.put("model", "gpt-4o-mini");// default
        reqMap.put("message", chatContext.getQuestion());
        reqMap.put("thread_id", chatContext.getConversationId());
        String url = dataflowConfig.getLlmStreamUrl();
        return WEB_CLIENT
                .post()
                .uri(url)
                .bodyValue(JacksonUtils.writeValueAsString(reqMap))
                .retrieve()
                .bodyToFlux(String.class)
                .timeout(Duration.ofMinutes(1L))
                .onErrorResume(t -> {
                    if (t instanceof WebClientResponseException.BadRequest) {
                        String responseBodyAsString = ((WebClientResponseException.BadRequest) t).getResponseBodyAsString();
                        return Flux.just(responseBodyAsString);
                    }
                    return Mono.error(t);
                });
    }

    public Flux<ChatCompletions> chatCompletions(ChatContext chatContext) {
        Sinks.Many<ChatCompletions> sink = Sinks.many().multicast().onBackpressureBuffer();
        // 调用 processChatCompletions 方法获取第三方流式响应
        processChatCompletions(chatContext)
                .filter(StringUtils::isNotBlank)
                .filter(json -> !StringUtils.equalsIgnoreCase(StringUtils.trim(json), AiConstant.DONE))
                .subscribe(
                        chunk -> {
                            ChatCompletions chatCompletions = toChatCompletions(chunk);
                            if (chatCompletions.isChatEnd()) {
                                sink.tryEmitComplete();
                            } else {
                                sink.tryEmitNext(chatCompletions); // 推送数据
                            }
                        },
                        error -> {
                            // 错误处理
                            log.error("Stream processing failed: {}", error.getMessage(), error);
                            sink.tryEmitError(error);
                        }, // 错误处理
                        () -> sink.tryEmitComplete() // 完成信号
                );

        return sink.asFlux();
    }


    /**
     *
     * 响应
     * {"type": "message", "content": {"type": "ai", "content": "", "tool_calls": [{"name": "send_item", "args": {"hit_item": "水", "amount": 1, "alternative_item": "None"}, "id": "call_5Ns997SSjCkwnQ8xrYkePe2r", "type": "tool_call"}], "tool_call_id": null, "run_id": "fd37a347-3d1e-45ee-bb24-bccfa6fa4e4d", "response_metadata": {"finish_reason": "tool_calls", "model_name": "gpt-4o-mini-2024-07-18", "system_fingerprint": "fp_f3927aa00d"}, "custom_data": {}}}
     *
     * data: {"type": "message", "content": {"type": "tool", "content": "{\"hit_name\": \"水\", \"num\": 1, \"alternative_item\": \"None\"}", "tool_calls": [], "tool_call_id": "call_5Ns997SSjCkwnQ8xrYkePe2r", "run_id": "fd37a347-3d1e-45ee-bb24-bccfa6fa4e4d", "response_metadata": {}, "custom_data": {}}}
     *
     * data: {"type": "token", "content": "已"}
     *
     * data: {"type": "token", "content": "为"}
     *
     * data: {"type": "token", "content": "您"}
     *
     * data: {"type": "token", "content": "送"}
     *
     * data: {"type": "token", "content": "上一"}
     *
     * data: {"type": "token", "content": "瓶"}
     *
     * data: {"type": "token", "content": "水"}
     *
     * data: {"type": "token", "content": "，请"}
     *
     * data: {"type": "token", "content": "稍"}
     *
     * data: {"type": "token", "content": "等"}
     *
     * data: {"type": "token", "content": "片"}
     *
     * data: {"type": "token", "content": "刻"}
     *
     * data: {"type": "token", "content": "。"}
     *
     * data: {"type": "message", "content": {"type": "ai", "content": "已为您送上一瓶水，请稍等片刻。", "tool_calls": [], "tool_call_id": null, "run_id": "fd37a347-3d1e-45ee-bb24-bccfa6fa4e4d", "response_metadata": {"finish_reason": "stop", "model_name": "gpt-4o-mini-2024-07-18", "system_fingerprint": "fp_f3927aa00d"}, "custom_data": {}}}
     *
     * data: [DONE]
     *
     */
    private ChatCompletions toChatCompletions(String chunk) {
        JsonNode response;
        try {
            response = JacksonUtils.mapper().readTree(chunk);
        } catch (Exception e) {
            log.error("Failed to parse chunk: {}", e.getMessage(), e);
            throw new BusinessException(ResultCode.MODEL_ERROR);
        }

        if (response == null) {
            return new ChatCompletions();
        }

        // 提取 type 字段并映射到枚举
        String type = response.path("type").asText(null);
        return switch (type) {
            case "message" -> handleMessageType(response).orElse(new ChatCompletions());
            case "token" -> handleTokenType(response).orElse(new ChatCompletions());
            default -> new ChatCompletions();
        };
    }

    // 处理 message 类型
    private Optional<ChatCompletions> handleMessageType(JsonNode response) {
        JsonNode contentNode = response.path("content");
        String messageType = contentNode.path("type").asText(null);
        String traceId = contentNode.path("run_id").asText(null);

        ChatCompletions completions = new ChatCompletions();
        completions.setId(traceId);

        if ("ai".equals(messageType)) {
            if ("stop".equals(contentNode.path("response_metadata").path("finish_reason").asText())) {
                String answer = contentNode.path("content").asText();
                return Optional.of(ChatCompletionUtils.buildChatCompletionsDeltaLast(answer));
            }

        } else if ("tool".equals(messageType)) {
            // 工具调用结果处理 todo

        }

        return Optional.of(completions);
    }

    // 处理 token 类型
    private Optional<ChatCompletions> handleTokenType(JsonNode response) {
        String content = response.path("content").asText();
        return Optional.ofNullable(ChatCompletionUtils.buildChatCompletionsDelta(content));
    }


}