package com.wormhole.agent.chat.function.context;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FunctionOrganicResult {

    private String provider;
    private String source;
    private String title;
    private String snippet;
    private String link;
    private Double score;
    private Double relevanceScore;
    private Long contentPublishTime;
    private Long crawlTime;
    private Object data;

}
