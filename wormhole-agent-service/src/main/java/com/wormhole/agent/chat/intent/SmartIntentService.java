package com.wormhole.agent.chat.intent;

import com.wormhole.agent.chat.intent.engine.SmartIntentEngine;
import com.wormhole.agent.chat.intent.model.IntentEngineEnum;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.intent.SmartIntentResult;
import com.wormhole.agent.model.openai.ChatFunctionCall;
import com.wormhole.agent.model.openai.ChatToolCall;
import com.wormhole.agent.nacos.listener.WorkflowListener;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @date 2024/9/15
 */
@Slf4j
@Component
public class SmartIntentService {

    private static final Map<IntentEngineEnum, SmartIntentEngine> SMART_INTENT_STRATEGY_MAP = new ConcurrentHashMap<>();

    @Resource
    private WorkflowListener workflowListener;

    public static Optional<SmartIntentEngine> getSmartIntentEngine(ChatContext chatContext) {
        IntentEngineEnum intentEngine = Optional.ofNullable(chatContext.getIntentEngineEnum())
                .orElse(IntentEngineEnum.wormhole);
        return Optional.ofNullable(SMART_INTENT_STRATEGY_MAP.get(intentEngine));
    }

    @Autowired
    public void SmartIntentService(List<SmartIntentEngine> smartIntentEngines) {
        for (SmartIntentEngine smartIntentEngine : smartIntentEngines) {
            SMART_INTENT_STRATEGY_MAP.put(smartIntentEngine.getIntentEngine(), smartIntentEngine);
        }
    }

    public Mono<ChatContext> smartIntent(ChatContext chatContext) {
        return Mono.defer(() -> {
            Optional<SmartIntentEngine> smartIntentEngineOptional = getSmartIntentEngine(chatContext);
            if (smartIntentEngineOptional.isEmpty()) {
                return Mono.error(new BusinessException(ResultCode.INVALID_PARAMETER));
            }
            SmartIntentEngine smartIntentEngine = smartIntentEngineOptional.get();
            IntentEngineEnum intentEngine = smartIntentEngine.getIntentEngine();
            if (Objects.isNull(chatContext.getIntentEngineEnum())) {
                chatContext.setIntentEngineEnum(intentEngine);
            }
            return smartIntentEngine.smartIntent(chatContext)
                    .doOnError(t -> log.error("smart_intent error:" + t.getMessage(), t));
        });
    }

    public Optional<List<ChatToolCall>> getChatToolCallList(SmartIntentResult smartIntentResult) {
        return Optional.ofNullable(smartIntentResult)
                .map(SmartIntentResult::getChatToolCallList)
                .filter(CollectionUtils::isNotEmpty);
    }

    /**
     * 读取
     *
     * @param smartIntentResult 意图识别结果
     * @return
     */
    private Optional<List<ChatFunctionCall>> getChatFunctionCallList(SmartIntentResult smartIntentResult) {
        return getChatToolCallList(smartIntentResult)
                .map(toolCalls -> toolCalls.stream()
                        .map(ChatToolCall::getFunction)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()))
                .filter(CollectionUtils::isNotEmpty);
    }

    /**
     * 根据workflowCode找到意图识别结果对应的function
     * 意图识别中只有workflowName
     * workflowCode可以找到workflowName
     *
     * @param workflowCode
     * @param smartIntentResult
     * @return
     */
    public Optional<ChatToolCall> getChatToolCall(String workflowCode, SmartIntentResult smartIntentResult) {
        if (Objects.isNull(smartIntentResult)) {
            return Optional.empty();
        }
        Workflow workflow = workflowListener.getContent(workflowCode);
        return smartIntentResult.getChatToolCallList().stream()
                .filter(toolCall -> {
                    ChatFunctionCall function = toolCall.getFunction();
                    String workflowName = function.getName();
                    return StringUtils.equalsIgnoreCase(workflow.getWorkflowDefinition().getWorkflowName(), workflowName);
                })
                .findFirst();
    }
}
