package com.wormhole.agent.chat.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wormhole.agent.ai.core.service.ChatClientService;
import com.wormhole.agent.chat.intent.SmartIntentService;
import com.wormhole.agent.chat.intent.model.IntentEngineEnum;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.chat.suggest.SuggestService;
import com.wormhole.agent.core.constant.AiConstant;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.model.bot.*;
import com.wormhole.agent.core.model.chat.ChatEngine;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.core.util.ChatMessageUtils;
import com.wormhole.agent.core.util.FreeMarkerTemplateCacheUtils;
import com.wormhole.agent.knowledge.model.dto.KnowledgeMessageDTO;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.model.openai.OpenAiChatParams;
import com.wormhole.agent.service.ChatLogService;
import com.wormhole.agent.tool.ToolChainDriver;
import com.wormhole.agent.tool.ToolDiscoveryProvider;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.core.model.ToolType;
import com.wormhole.agent.workflow.model.inputs.LlmInputs;
import com.wormhole.agent.workflow.service.WorkflowService;
import com.wormhole.common.util.DateParsePatterns;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Clock;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * LlmAgentChatProcessor
 *
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@Slf4j
@Service
public class LlmAgentChatProcessor extends AgentChatProcessor {

    @Resource
    private SmartIntentService smartIntentService;
    @Resource
    private WorkflowService workflowService;
    @Resource
    private ChatClientService chatClientService;
    @Resource
    private SuggestService suggestService;
    @Resource
    private ChatLogService chatLogService;
    @Resource
    private ToolChainDriver toolChainDriver;
    @Resource
    private ToolDiscoveryProvider toolDiscoveryProvider;;

    @Override
    public boolean support(ChatContext chatContext) {
        BotModeEnum botModeEnum = Optional.ofNullable(chatContext.getBotInfo()).map(BotInfo::getBotMode).map(BotModeEnum::from).orElse(null);
        return ChatType.AGENT.equals(chatContext.getChatType()) && Objects.equals(BotModeEnum.LLM_AGENT, botModeEnum);
    }

    @Override
    public Flux<ChatContext> doChatCompletions(ChatContext chatContext) {
        BotInfo botInfo = chatContext.getBotInfo();
        ChatEngine chatEngine = ChatEngine.from(Optional.ofNullable(botInfo.getExtConfigInfo())
                .map(ExtConfigInfo::getChatEngine)
                .orElse(ChatEngine.JAVA.getValue()));
        switch (chatEngine) {
            case JAVA: {
                Stopwatch stopwatch = Stopwatch.createStarted();
                return Mono.zip(searchKnowledge(chatContext), loadMemory(chatContext))
                        // 工具调用
                        .flatMap(cc -> executeToolChain(chatContext))
                        // 后处理
                         .flatMapMany(cc -> postProcessWorkflow(chatContext))
                        .doOnError(throwable -> chatContext.addContextValue("chat_processor_error_msg", throwable.getMessage()))
                        .doOnTerminate(() -> {
                            stopwatch.stop();
                            log.info("gatewayToNow:{}, doPostProcessor, elapsed:{}", System.currentTimeMillis() - chatContext.getGatewayStartTime().toEpochMilli(), stopwatch.elapsed(TimeUnit.MILLISECONDS));
                        });
            }
            case PYTHON: {
                Stopwatch stopwatch = Stopwatch.createStarted();
                chatContext.setIntentEngineEnum(IntentEngineEnum.dataflow);
                return smartIntent(chatContext)
                        .doOnSubscribe(subscription -> log.info("gatewayToNow:{}, invoke smartIntent", System.currentTimeMillis() - chatContext.getGatewayStartTime().toEpochMilli()))
                        .doOnError(throwable -> {
                            chatContext.addContextValue("chat_processor_error_msg", throwable.getMessage());
                            log.error("Error in smartIntent", throwable);
                        })
                        .doOnTerminate(() -> {
                            stopwatch.stop();
                            log.info("gatewayToNow:{}, invoke smartIntent, elapsed:{}", System.currentTimeMillis() - chatContext.getGatewayStartTime().toEpochMilli(), stopwatch.elapsed(TimeUnit.MILLISECONDS));
                        }).flux();
            }
        }
        return Flux.empty();
    }

    protected Mono<ChatContext> executeToolChain(ChatContext chatContext) {

        BotInfo botInfo = chatContext.getBotInfo();

        if (CollectionUtils.isEmpty(botInfo.getPluginList()) && CollectionUtils.isEmpty(botInfo.getWorkflowCodeList())) {
            log.info("no tools or workflow code list or plugin list is empty , botInfo id :{}" ,botInfo.getBotCode());
            return Mono.just(chatContext);
        }
        chatContext.getExecutionStatManager().start(ExecutionStatType.tool_use, chatContext.getQuestion());

        ModelInfo modelInfo = botInfo.getModelInfo();
        LlmInputs.IntentLlmParams intentLlmParams = new LlmInputs.IntentLlmParams();
        intentLlmParams.setModel(modelInfo.getModel());
        intentLlmParams.setModelProvider(modelInfo.getModelProvider());
        intentLlmParams.setTemperature(modelInfo.getTemperature());
        intentLlmParams.setUserPrompt(chatContext.getQuestion());
        intentLlmParams.setSystemPrompt(botInfo.getPromptInfo().getSystemPrompt());
        intentLlmParams.setChatHistoryRound(modelInfo.getChatHistoryRound());
        intentLlmParams.setEnableChatHistory(modelInfo.getEnableChatHistory() != null ? modelInfo.getEnableChatHistory() : false);
        intentLlmParams.setMaxRecursionDepth(10);

        List<PluginInfo<?>> pluginList = Optional.ofNullable(botInfo.getPluginList()).orElse(Collections.emptyList());
        List<LlmInputs.PluginFunctionCallParam> pluginFunctionCallParams = pluginList.stream().filter(pluginInfo -> ToolType.PLUGIN.name().equalsIgnoreCase(pluginInfo.getType()))
                .map(pluginInfo -> {
                    PluginInfo.PluginToolInfo PluginToolInfo = JacksonUtils.readValue(JacksonUtils.writeValueAsString(pluginInfo.getPluginInfo()),PluginInfo.PluginToolInfo.class);
                    return new LlmInputs.PluginFunctionCallParam(PluginToolInfo.getPluginToolCode(), PluginToolInfo.getPluginCode(), null);
                }).toList();

        List<LlmInputs.McpFunctionCallParam> mcpFunctionCallParams = pluginList.stream().filter(pluginInfo -> ToolType.MCP.name().equalsIgnoreCase(pluginInfo.getType()))
                .map(pluginInfo -> {
                    PluginInfo.McpToolInfo mcpToolInfo = JacksonUtils.readValue(JacksonUtils.writeValueAsString(pluginInfo.getPluginInfo()),PluginInfo.McpToolInfo.class);
                    return new LlmInputs.McpFunctionCallParam(mcpToolInfo.getMcpServerName(),mcpToolInfo.getToolNames());
                }).toList();

        List<LlmInputs.WorkflowFunctionCallParam> workflowFunctionCallParams =
                Optional.ofNullable(botInfo.getWorkflowCodeList()).orElse(Collections.emptyList()).stream().map(LlmInputs.WorkflowFunctionCallParam::new).toList();

        LlmInputs.FunctionCallParams functionCallParams =
                LlmInputs.FunctionCallParams.builder()
                        .pluginList(pluginFunctionCallParams)
                        .workflowList(workflowFunctionCallParams)
                        .mcpServerList(mcpFunctionCallParams)
                        .build();

        LlmInputs llmInputs = LlmInputs.builder()
                .llmParams(intentLlmParams)
                .intentSwitch(true)
                .intentLlmParams(intentLlmParams)
                .functionCallParams(functionCallParams)
                .build();

        ExtConfigInfo extConfigInfo = botInfo.getExtConfigInfo();

        ToolChainContext toolChainContext = new ToolChainContext();

        // 使用异步版本的 getToolsAsync 避免响应式线程阻塞
        return toolDiscoveryProvider.getToolsAsync(llmInputs.getFunctionCallParams(), extConfigInfo.getExtMap(), toolChainContext)
                .flatMap(tools -> {
                    Mono<Map<String, Object>> toolChainResultMono = toolChainDriver.executeToolChain(
                            intentLlmParams,
                            tools,
                            extConfigInfo.getExtMap(),
                            toolChainContext);
                    return toolChainResultMono;
                })
                .flatMap(finalResults -> {
            if(finalResults.isEmpty()){
                return Mono.just(chatContext);
            }
            chatContext.setToolMessageList(toolChainContext.getConversationHistory());
            chatContext.getExecutionStatManager().end(ExecutionStatType.tool_use, toolChainContext.getConversationHistory());
            return Mono.just(chatContext);

        });
    }

    protected Mono<ChatContext> smartIntent(ChatContext chatContext) {
        return smartIntentService.smartIntent(chatContext)
                .switchIfEmpty(Mono.just(chatContext))
                .doOnSubscribe(subscription -> chatContext.getExecutionStatManager().start(ExecutionStatType.smart_intent, "意图识别"))
                .onErrorResume(throwable -> {
                    log.error("smartIntent error", throwable);
                    chatContext.getExecutionStatManager().fail(ExecutionStatType.smart_intent, throwable);
                    return Mono.just(chatContext);
                })
                .doOnSuccess(signalType -> {
                    chatContext.getExecutionStatManager().end(ExecutionStatType.smart_intent, chatContext.getSmartIntentResult());
                });
    }

    /**
     * 有工作流：返回变量，由智能体生成回答。需要调用大模型
     * 无工作流：根据机器人上的设置，大模型回复
     *
     * @param chatContext 对话上下文
     * @return
     */
    private Mono<ChatContext> postProcessWorkflow(ChatContext chatContext) {
        ModelContext modelContext = buildModelContext(chatContext);
        return chatClientService.chatCompletions(modelContext)
                .flatMap(chatCompletions -> {
                    chatContext.sinkNext(chatCompletions);
                    return Mono.just(chatCompletions);
                })
                .then(Mono.defer(() -> {
                    // 确保最终返回处理后的上下文
                    return Mono.just(chatContext);
                }));
    }


    private ModelContext buildModelContext(ChatContext chatContext) {
        BotInfo botInfo = chatContext.getBotInfo();
        ModelInfo modelInfo = botInfo.getModelInfo();

        Map<String, Object> contextData = Maps.newHashMap();
        contextData.put(AiConstant.QUESTION, chatContext.getQuestion());

        // add date time
        contextData.put(AiConstant.CURRENT_DATE, LocalDate.now(Clock.systemDefaultZone()).format(DateParsePatterns.SHORT_FORMATTER));
        contextData.put(AiConstant.CURRENT_TIME, LocalTime.now(Clock.systemDefaultZone()).format(DateTimeFormatter.ofPattern(DateParsePatterns.DATE_FORMAT_HH_MM_SS)));
        contextData.put(AiConstant.CURRENT_DATE_TIME, LocalDateTime.now(Clock.systemDefaultZone()).format(DateParsePatterns.FULL_FORMATTER));

        Map<String, Object> payload = chatContext.getChatParams().getPayload();
        contextData.putAll(payload);

        // 提示词
        List<OpenAiChatMessage> chatMessageList = Lists.newArrayList();

        // system prompt
        if (StringUtils.isNotBlank(botInfo.getPromptInfo().getSystemPrompt())) {
            String systemPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(botInfo.getPromptInfo().getSystemPrompt(), contextData);
            OpenAiChatMessage systemMessage = OpenAiChatMessage.builder().role(ChatRole.SYSTEM.getValue()).content(systemPrompt).build();
            chatMessageList.add(systemMessage);
        }

        // recent message
        List<OpenAiChatMessage> recentMessageList = ChatMessageUtils.getRecentMessageList(chatContext.getRecentMessageList(), modelInfo.getChatHistoryRound());
        chatMessageList.addAll(recentMessageList);


        // user prompt
        String userPrompt;
        if (StringUtils.isBlank(botInfo.getPromptInfo().getUserPrompt())) {
            userPrompt = chatContext.getQuestion();
        } else {
            userPrompt = FreeMarkerTemplateCacheUtils.processTemplateIntoString(botInfo.getPromptInfo().getUserPrompt(), contextData);
        }
        if (CollUtil.isNotEmpty(chatContext.getKnowledgeMessageList())) {
            String text = chatContext.getKnowledgeMessageList()
                    .stream()
                    .map(KnowledgeMessageDTO::getMetadata)
                    .map(e -> Optional.ofNullable(e.get("text")).map(Objects::toString).orElse(StrUtil.EMPTY))
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.joining(","));
            userPrompt = Optional.ofNullable(userPrompt).orElse(StrUtil.EMPTY) + "知识库内容：" + text;
        }

        OpenAiChatMessage userMessage = OpenAiChatMessage.builder().role(ChatRole.USER.getValue()).content(userPrompt).build();
        chatMessageList.add(userMessage);
        if (CollectionUtils.isNotEmpty(chatContext.getToolMessageList())) {
            chatMessageList.addAll(chatContext.getToolMessageList());
        }

        OpenAiChatParams openAiChatParams = OpenAiChatParams.builder()
                .stream(true)
                .model(modelInfo.getModel())
                .modelProvider(modelInfo.getModelProvider())
                .temperature(modelInfo.getTemperature())
                .maxTokens(Objects.nonNull(modelInfo.getMaxTokens()) && modelInfo.getMaxTokens() > 0 ? modelInfo.getMaxTokens() : null)
                .frequencyPenalty(modelInfo.getFrequencyPenalty())
                .messages(chatMessageList)
                .build();

        // 大模型总结回复
        return ModelContext.builder()
                .openAiChatParams(openAiChatParams)
                .modelLogContext(chatContext.bulidModelLogContext())
                .build();
    }
}