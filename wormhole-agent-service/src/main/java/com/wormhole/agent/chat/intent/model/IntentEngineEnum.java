package com.wormhole.agent.chat.intent.model;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
public enum IntentEngineEnum {
    /**
     * wormhole
     */
    wormhole("wormhole"),
    /**
     * chatbot
     */
    chatbot("chatbot"),
    /**
     * dataflow
     */
    dataflow("dataflow"),
    ;

    private final String engine;

    IntentEngineEnum(String engine) {
        this.engine = engine;
    }

    public static IntentEngineEnum fromEngine(String engine) {
        for (IntentEngineEnum intentEngineEnum : IntentEngineEnum.values()) {
            if (StringUtils.equalsIgnoreCase(intentEngineEnum.getEngine(), engine)) {
                return intentEngineEnum;
            }
        }
        return chatbot;
    }

    public String getEngine() {
        return engine;
    }
}
