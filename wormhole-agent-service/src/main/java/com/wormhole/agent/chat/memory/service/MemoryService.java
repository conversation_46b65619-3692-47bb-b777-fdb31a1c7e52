package com.wormhole.agent.chat.memory.service;

import com.wormhole.agent.chat.memory.params.ReadMemoryParams;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.LlmChatParams;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.chat.ChatType;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.context.Context;

import java.util.Optional;

/**
 * 智能体记忆
 *
 * <AUTHOR>
 * @date 2024/8/30 22:17
 **/
@Slf4j
@Component
public class MemoryService {

    @Resource
    private RecentMemoryService recentMemoryService;

    public Mono<Void> asyncStoreMemory(ChatContext chatContext) {
        return Mono.deferContextual(contextView -> {
            if (!chatContext.isStoreMemory()) {
                return Mono.empty();
            }
            recentMemoryService.storeMemory(chatContext)
                    .subscribeOn(Schedulers.boundedElastic())
                    .contextCapture()
                    .subscribe(t -> {
                    }, null, null, Context.of(contextView));
            return Mono.empty();
        });
    }

    public Mono<ChatContext> storeMemory(ChatContext chatContext) {
        return recentMemoryService.storeMemory(chatContext);
    }

    public int getRecentRound(ChatContext chatContext) {
        int recentRound = 0;
        ChatType chatType = chatContext.getChatType();
        switch (chatType) {
            case LLM -> {
                recentRound = ((LlmChatParams) chatContext.getChatParams()).getRecentRound();
            }
            case AGENT -> {
                recentRound = Optional.ofNullable(chatContext.getBotInfo()).map(BotInfo::getModelInfo).map(ModelInfo::getRecentRound).orElse(3);
            }
        }
        return recentRound;
    }

    public Mono<ChatContext> readRecentMessage(ChatContext chatContext) {
        return Mono.defer(() -> {
            if (!chatContext.isReadMemory()) {
                return Mono.just(chatContext);
            }
            String conversationId = chatContext.getConversationId();

            int recentRound = getRecentRound(chatContext);
            ReadMemoryParams readMemoryParams = ReadMemoryParams.builder()
                    .conversationId(conversationId)
                    .maxSize(recentRound)
                    .build();
            return recentMemoryService.getRecentMemory(readMemoryParams)
                    .flatMap(list -> {
                        chatContext.setRecentMessageList(list);
                        return Mono.just(chatContext);
                    })
                    .onErrorResume(throwable -> {
                        log.error(throwable.getMessage(), throwable);
                        return Mono.just(chatContext);
                    });
        });
    }


}
