package com.wormhole.agent.chat.processor;

import com.wormhole.agent.ai.core.service.ChatClientService;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.LlmChatParams;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.model.chat.ChatRole;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.core.util.ChatMessageUtils;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.model.openai.*;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.trace.TraceContext;
import com.wormhole.trace.log.PerformanceMetric;
import com.wormhole.trace.log.PerformanceMonitor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@Slf4j
@Service
public class LlmChatProcessor extends AbstractChatProcessor {

    @Resource
    private ChatClientService chatClientService;

    @Override
    public boolean support(ChatContext chatContext) {
        return ChatType.LLM.equals(chatContext.getChatType());
    }

    @Override
    public Flux<ChatCompletions> chatCompletions(ChatContext chatContext) {
        return loadMemory(chatContext)
                .flatMapMany(t -> {
                    LlmChatParams chatParams = (LlmChatParams) chatContext.getChatParams();
                    int recentRound = chatParams.getRecentRound();
                    List<OpenAiChatMessage> recentMessageList = ChatMessageUtils.getRecentMessageList(chatContext.getRecentMessageList(), recentRound);
                    OpenAiChatParams openAiChatParams = JacksonUtils.readValue(JacksonUtils.writeValueAsString(chatParams), OpenAiChatParams.class);
                    List<OpenAiChatMessage> chatMessageList = addMemory(openAiChatParams.getMessages(), recentMessageList);
                    openAiChatParams.setMessages(chatMessageList);
                    chatContext.getExecutionStatManager().start(ExecutionStatType.llm, JacksonUtils.writeValueAsString(openAiChatParams));
                    ModelContext modelContext = ModelContext.builder()
                            .openAiChatParams(openAiChatParams)
                            .modelLogContext(chatContext.bulidModelLogContext())
                            .build();
                    return chatClientService.chatCompletions(modelContext)
                            .doOnNext(chatCompletions -> {
                                log.info("chatCompletions info={}", JacksonUtils.writeValueAsString(chatCompletions));
                                Optional<ChatChoice> firstChatChoiceOptional = chatCompletions.getChoices().stream().findFirst();
                                if (firstChatChoiceOptional.isPresent()) {
                                    ChatChoice chatChoice = firstChatChoiceOptional.get();
                                    ChatMessage delta = chatChoice.getDelta();
                                    if (Objects.nonNull(delta)) {
                                        if (Objects.nonNull(delta.getReasoningContent())) {
                                            delta.setContent(delta.getReasoningContent());
                                        }

                                    }
                                }
                                if( chatContext.getFirstTokenTime() == 0L){
                                    long start = chatContext.getGatewayStartTime().toEpochMilli();
                                    long firstTokenTime = System.currentTimeMillis();
                                    chatContext.setFirstTokenTime(firstTokenTime);
                                    PerformanceMetric performanceMetric = PerformanceMetric.builder()
                                            .op("LlmChatProcessor-FirstCompletion")
                                            .opDesc("LlmChat首片耗时")
                                            .startTime(start )
                                            .endTime(firstTokenTime)
                                            .traceId(TraceContext.getTraceId())
                                            .rtcRoomId(chatContext.getRtcRoomId())
                                            .convId(chatContext.getConversationId())
                                            .clientReqId(chatContext.getClientReqId())
                                            .rt(firstTokenTime-start)
                                            .build()
                                            .addExtra("first_completion",chatCompletions);
                                    PerformanceMonitor.log(JacksonUtils.writeValueAsString(performanceMetric));
                                }
                                if (chatCompletions.isChatEnd()) {
                                    chatContext.setAnswer(modelContext.getAnswer());
                                    chatContext.getExecutionStatManager().end(ExecutionStatType.llm, modelContext.getAnswer());
                                }
                                chatContext.setChatCompletionId(chatCompletions.getId());
                                chatCompletions.getMetadata().setTraceId(chatContext.getTraceId());
                            });
                });
    }

    public List<OpenAiChatMessage> addMemory(List<OpenAiChatMessage> chatMessageList, List<OpenAiChatMessage> recentMessageList) {
        // 参数校验
        if (CollectionUtils.isEmpty(chatMessageList) || CollectionUtils.isEmpty(recentMessageList)) {
            return chatMessageList;
        }
        // 如果只有一条消息且是system消息，忽略记忆
        if (chatMessageList.size() == 1 && ChatRole.SYSTEM.getValue().equalsIgnoreCase(chatMessageList.get(0).getRole())) {
            return chatMessageList;
        }

        List<OpenAiChatMessage> result = new ArrayList<>();
        // 处理第一条消息
        if (ChatRole.SYSTEM.getValue().equalsIgnoreCase(chatMessageList.get(0).getRole())) {
            result.add(chatMessageList.get(0));  // 添加system消息
            result.addAll(recentMessageList);    // 插入历史记忆
            // 添加剩余消息
            result.addAll(chatMessageList.subList(1, chatMessageList.size()));
        } else {
            // 如果第一条不是system消息，在第一条消息前插入记忆
            result.addAll(recentMessageList);    // 先插入历史记忆
            result.addAll(chatMessageList);      // 添加所有原始消息
        }
        return result;
    }

}