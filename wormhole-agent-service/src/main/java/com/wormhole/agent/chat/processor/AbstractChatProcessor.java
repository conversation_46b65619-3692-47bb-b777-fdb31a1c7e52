package com.wormhole.agent.chat.processor;

import cn.hutool.core.collection.CollUtil;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.chat.memory.service.MemoryService;
import com.wormhole.agent.chat.model.ChatConstant;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.knowledge.model.constant.SearchStrategyEnum;
import com.wormhole.agent.knowledge.model.dto.KnowledgeMessageDTO;
import com.wormhole.agent.knowledge.params.KnowledgeSearchParams;
import com.wormhole.agent.knowledge.search.KnowledgeSearchContext;
import com.wormhole.agent.knowledge.search.KnowledgeSearchService;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.common.constant.HeaderConstant;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@Slf4j
public abstract class AbstractChatProcessor implements ChatProcessor {

    @Resource
    protected MemoryService memoryService;

    public abstract Flux<ChatCompletions> chatCompletions(ChatContext chatContext);

    @Override
    public Flux<ChatCompletions> processChat(ChatContext chatContext) {
        return innerProcessChat(chatContext);
    }

    public Flux<ChatCompletions> innerProcessChat(ChatContext chatContext) {
        return Flux.deferContextual(contextView -> {
                    String connectionId = contextView.getOrDefault(HeaderConstant.CONNECTION_ID, StringUtils.EMPTY);
                    chatContext.setConnectionId(connectionId);
                    return chatCompletions(chatContext)
                            .onErrorResume(e -> {
                                if (e instanceof BusinessException) {
                                    return Mono.error(e);
                                } else {
                                    return Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, e));
                                }
                            });
                })
                .contextWrite(context -> context.put(ChatConstant.CHAT_CONTEXT_KEY, chatContext));
    }

    protected Mono<ChatContext> loadMemory(ChatContext chatContext) {
        chatContext.getExecutionStatManager().start(ExecutionStatType.read_memory, "read memory start");
        return memoryService.readRecentMessage(chatContext)
                .switchIfEmpty(Mono.just(chatContext))
                .doOnSuccess(result -> chatContext.getExecutionStatManager().end(ExecutionStatType.read_memory, JacksonUtils.writeValueAsString(chatContext.getRecentMessageList())))
                .onErrorResume(throwable -> {
                    log.error(throwable.getMessage(), throwable);
                    chatContext.getExecutionStatManager().fail(ExecutionStatType.read_memory, throwable);
                    return Mono.just(chatContext);
                });
    }

    @Resource
    private KnowledgeSearchService knowledgeSearchService;

    /**
     * 检索知识库
     *
     * @param chatContext
     * @return
     */
    protected Mono<ChatContext> searchKnowledge(ChatContext chatContext) {

        String userInput = chatContext.getQuestion();
        chatContext.getExecutionStatManager().start(ExecutionStatType.search_knowledge, userInput);
        List<String> knoledgeCodeList = chatContext.getBotInfo().getKnowledgeCodeList();
        log.info("knoledgeCodeList:{}", knoledgeCodeList);
        if (CollUtil.isEmpty(knoledgeCodeList)) {
            return Mono.just(chatContext);
        }

        KnowledgeSearchParams.SearchInput searchInput = new KnowledgeSearchParams.SearchInput();
        searchInput.setQuery(userInput);

        KnowledgeSearchContext searchContext = KnowledgeSearchContext.builder()
                .searchInput(searchInput)
                .knowledgeSearchParams(buildKnowledgeSearchParams(knoledgeCodeList))
                .recentMessageList(null)
                .build();
        return knowledgeSearchService.search(searchContext)
                .doOnNext(context -> {
                    List<KnowledgeMessageDTO> searchResultList = searchContext.getSearchResultList();
                    chatContext.setKnowledgeMessageList(searchResultList);
                    chatContext.getExecutionStatManager().end(ExecutionStatType.search_knowledge, JacksonUtils.writeValueAsString(searchResultList));
                })
                .then(Mono.just(chatContext));
    }

    private KnowledgeSearchParams buildKnowledgeSearchParams(List<String> codeList) {
        KnowledgeSearchParams knowledgeSearchParams = new KnowledgeSearchParams();
        knowledgeSearchParams.setKnowledgeCodeList(codeList);
        knowledgeSearchParams.setTopK(20);
        knowledgeSearchParams.setMinScore(0.5);
        knowledgeSearchParams.setStrategy(SearchStrategyEnum.HYBRID.getValue());
        knowledgeSearchParams.setEmbeddingModel(UnifiedModelEnum.TEXT_EMBEDDING_3_SMALL.getModel());
        knowledgeSearchParams.setEmbeddingModelProvider(null);
        knowledgeSearchParams.setUseRerank(true);
        knowledgeSearchParams.setUseRewrite(false);
        knowledgeSearchParams.setPersonalOnly(false);
        KnowledgeSearchParams.RerankConfig rerankConfig = KnowledgeSearchParams.RerankConfig.builder()
                .model(UnifiedModelEnum.SF_BAAI_BGE_RERANKER_V2_M3.getModel())
                .modelProvider(UnifiedModelEnum.SF_BAAI_BGE_RERANKER_V2_M3.getProvider())
                .topN(20)
                .maxChunksPerDoc(1)
                .overlapTokens(100)
                .build();
        knowledgeSearchParams.setRerankConfig(rerankConfig);

        return knowledgeSearchParams;
    }

}
