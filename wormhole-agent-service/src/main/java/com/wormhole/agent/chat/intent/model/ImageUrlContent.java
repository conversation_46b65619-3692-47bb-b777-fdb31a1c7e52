package com.wormhole.agent.chat.intent.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * ImageUrlContent
 *
 * <AUTHOR>
 * @version 2025/1/27
 */
public class ImageUrlContent implements Content {
    private final ImageUrl imageUrl;

    @JsonCreator
    public ImageUrlContent(@JsonProperty("image_url") ImageUrl imageUrl) {
        this.imageUrl = imageUrl;
    }

    @Override
    public String getType() {
        return "image_url";
    }

    public ImageUrl getImageUrl() {
        return imageUrl;
    }
}