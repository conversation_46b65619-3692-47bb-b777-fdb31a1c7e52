package com.wormhole.agent.chat.context;

import com.google.common.base.Preconditions;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.AgentChatParams;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.service.BotService;
import com.wormhole.common.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/2 17:10
 **/
@Slf4j
@Component
public class AgentChatContextPreProcessor<T extends AgentChatParams> extends ChatContextPreProcessor<T> {

    @Autowired
    private BotService botService;

    @Override
    public boolean support(ChatContext chatContext) {
        return ChatType.AGENT.equals(chatContext.getChatType());
    }

    @Override
    public Mono<ChatContext> setupSpecificParameters(ChatContext chatContext) {
        return Mono.defer(() -> {
            AgentChatParams chatParams = (AgentChatParams) chatContext.getChatParams();
            Preconditions.checkArgument(StringUtils.isNotBlank(chatParams.getContent()), "content must not be blank");
            chatContext.setQuestion(chatParams.getContent());
            chatContext.setBotCode(chatParams.getBotCode());
            return loadBotInfoAndWorkflow(chatContext)
                    .flatMap(c -> {
                        BotInfo botInfo = chatContext.getBotInfo();

                        Preconditions.checkArgument(Objects.nonNull(botInfo), "bot_code is not valid");
                        chatContext.setConversationId(chatParams.getConversationId());
                        chatContext.setReadMemory(true);
                        chatContext.setStoreMemory(true);
                        // 获取 payload，如果为空则初始化为空 Map
                        Map<String, Object> payload = chatParams.getPayload();
                        if (payload == null) {
                            payload = new HashMap<>();
                        }
                        Map<String, Object> extMap = botInfo.getExtConfigInfo().getExtMap();
                        if (extMap != null && !extMap.isEmpty()) {
                            extMap.forEach(payload::putIfAbsent);
                        }
                        chatParams.setPayload(payload);

                        BotModeEnum botModeEnum = BotModeEnum.from(botInfo.getBotMode());
                        switch (botModeEnum) {
                            case WORKFLOW_AGENT -> handleWorkflowAgent(chatContext);
                            case LLM_AGENT -> handleLlmAgent(chatContext);
                        }
                        log.info("setupSpecificParameters chatContext:{}", JacksonUtils.writeValueAsString(chatContext));
                        return Mono.just(chatContext);
                    });
        });
    }

    protected Mono<ChatContext> loadBotInfoAndWorkflow(ChatContext chatContext) {
        return botService.getBotInfo(chatContext.getBotCode(),chatContext.isDebug())
                .doOnNext(chatContext::setBotInfo)
                .map(botInfo -> botService.getWorkflow(botInfo))
                .doOnNext(chatContext::setWorkflowList)
                .then(Mono.just(chatContext));
    }

    /**
     * workflow agent
     */
    private void handleWorkflowAgent(ChatContext chatContext) {
        BotInfo botInfo = chatContext.getBotInfo();
        ModelInfo modelInfo = botInfo.getModelInfo();
        if (Objects.isNull(modelInfo)) {
            // TODO 默认3轮
            modelInfo = ModelInfo.builder().recentRound(3).build();
            botInfo.setModelInfo(modelInfo);
        }
        // TODO 暂时关闭记忆读写
        chatContext.setReadMemory(false);
        chatContext.setStoreMemory(false);
    }

    /**
     * llm agent
     */
    private void handleLlmAgent(ChatContext chatContext) {
        BotInfo botInfo = chatContext.getBotInfo();
        ModelInfo modelInfo = botInfo.getModelInfo();
        Preconditions.checkArgument(Objects.nonNull(modelInfo), "model_info is not valid");

        UnifiedModelEnum unifiedModelEnum = UnifiedModelEnum.findByModel(modelInfo.getModel());
        Preconditions.checkArgument(Objects.nonNull(unifiedModelEnum), "model and provider not supported");

        modelInfo.setModelProvider(unifiedModelEnum.getProvider());
    }


}