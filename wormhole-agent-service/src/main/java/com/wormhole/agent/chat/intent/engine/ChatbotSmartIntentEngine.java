package com.wormhole.agent.chat.intent.engine;

import com.google.common.collect.Lists;
import com.wormhole.agent.chat.intent.model.IntentEngineEnum;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.intent.SmartIntentResult;
import com.wormhole.agent.core.util.ChatToolCallUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @date 2024/9/10 11:34
 **/
@Component
public class ChatbotSmartIntentEngine implements SmartIntentEngine {

    @Override
    public IntentEngineEnum getIntentEngine() {
        return IntentEngineEnum.chatbot;
    }

    @Override
    public Mono<ChatContext> smartIntent(ChatContext chatContext) {
        SmartIntentResult smartIntentResult = SmartIntentResult.builder()
                .chatToolCallList(Lists.newArrayList(ChatToolCallUtils.createChatbotTool()))
                .build();
        chatContext.setSmartIntentResult(smartIntentResult);
        return Mono.just(chatContext);
    }

}