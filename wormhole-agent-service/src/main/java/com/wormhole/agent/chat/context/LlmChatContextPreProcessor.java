package com.wormhole.agent.chat.context;

import com.google.common.base.Preconditions;
import com.wormhole.agent.ai.model.UnifiedModelEnum;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.LlmChatParams;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.IdUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 大模型调用
 *
 * <AUTHOR>
 * @date 2024/9/2 17:10
 **/
@Component
public class LlmChatContextPreProcessor<T extends LlmChatParams> extends ChatContextPreProcessor<T> {

    @Override
    public boolean support(ChatContext chatContext) {
        return ChatType.LLM.equals(chatContext.getChatType());
    }

    @Override
    public Mono<ChatContext> setupSpecificParameters(ChatContext chatContext) {
        return Mono.defer(() -> {
            LlmChatParams chatParams = (LlmChatParams) chatContext.getChatParams();
            if (CollectionUtils.isEmpty(chatParams.getMessages())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER, "messages must not be empty");
            }
            Preconditions.checkArgument(StringUtils.isNotBlank(chatParams.getModel()), "model must not be blank");
            Preconditions.checkArgument(Objects.nonNull(chatParams.getTemperature()), "temperature must not be null");

            UnifiedModelEnum unifiedModelEnum = UnifiedModelEnum.findByModel(chatParams.getModel(), chatParams.getModelProvider());
            Preconditions.checkArgument(Objects.nonNull(unifiedModelEnum), "model and provider not supported");

            chatParams.setModelProvider(unifiedModelEnum.getProvider());

            int recentRound = chatParams.getRecentRound();
            chatContext.setReadMemory(recentRound > 0);
            chatContext.setStoreMemory(true);

            chatContext.setClientReqId(Optional.ofNullable(chatParams.getClientReqId()).orElse(IdUtils.generateId()));
            chatContext.setConversationId(Optional.ofNullable(chatParams.getConversationId()).orElse(IdUtils.generateId()));

            // text
            List<OpenAiChatMessage> chatMessageList = chatParams.getMessages();
            OpenAiChatMessage openAiChatMessage = chatMessageList.get(chatMessageList.size() - 1);

            String content = openAiChatMessage.getContent();
            Preconditions.checkArgument(StringUtils.isNotBlank(content), "text must not be blank");
            chatContext.setQuestion(content);

            // llm 会话默认llm
            chatContext.setBotCode(ChatType.LLM.getValue());

            return Mono.just(chatContext);
        });
    }

}