package com.wormhole.agent.chat.model;

import com.google.common.collect.Lists;
import com.wormhole.agent.chat.intent.model.IntentEngineEnum;
import com.wormhole.agent.client.chat.params.ChatParams;
import com.wormhole.agent.core.context.DefaultContext;
import com.wormhole.agent.core.context.ModelContext;
import com.wormhole.agent.core.intent.SmartIntentResult;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.chat.ChatInputType;
import com.wormhole.agent.core.model.chat.ChatProtocol;
import com.wormhole.agent.core.model.chat.ChatResponseMode;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.knowledge.model.dto.KnowledgeMessageDTO;
import com.wormhole.agent.log.stat.ExecutionStatManager;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.model.WorkflowResult;
import com.wormhole.common.util.LocalDateTimeUtils;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.http.HttpHeaders;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Sinks;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/28 11:34
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ChatContext extends DefaultContext {

    @Builder.Default
    private final ExecutionStatManager executionStatManager = new ExecutionStatManager();
    /**
     * traceId
     */
    private String traceId;
    /**
     * 记录开始时间
     */
    @Builder.Default
    private Instant gatewayStartTime = LocalDateTimeUtils.nowToInstant();

    /**
     * 连接id
     */
    private String connectionId;

    /**
     * 客户端clientReqId
     */
    private String clientReqId;
    /**
     * userId
     */
    private String userId;
    /**
     * username
     */
    private String username;
    /**
     * 会话id
     */
    private String conversationId;

    private String messageId;
    /**
     * botCode
     */
    private String botCode;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * agent/llm
     */
    private ChatType chatType;
    /**
     * text/voice_input/voice_call
     */
    @Builder.Default
    private ChatInputType chatInputType = ChatInputType.TEXT;
    /**
     * stream/push
     */
    @Builder.Default
    private ChatResponseMode chatResponseMode = ChatResponseMode.STREAM;
    /**
     * json/protobuf
     */
    @Builder.Default
    private ChatProtocol chatProtocol = ChatProtocol.JSON;
    /**
     * 文本聊天入參
     */
    private ChatParams chatParams;
    /**
     * httpHeaders
     */
    private HttpHeaders httpHeaders;
    /**
     * 对话输入
     */
    private String question;
    /**
     * 对话回复
     */
    private String answer;

    /**
     * botInfo
     */
    private BotInfo botInfo;
    /**
     * workflowList
     */
    private List<Workflow> workflowList;
    /**
     * 从redis中读取最近的N条记录
     */
    private List<OpenAiChatMessage> recentMessageList;
    /**
     * 插件工具调用的结果
     */
    private List<OpenAiChatMessage> toolMessageList;
    /**
     * 知识库命中数据
     */
    private List<KnowledgeMessageDTO> knowledgeMessageList;
    /**
     * 意图识别引擎
     */
    private IntentEngineEnum intentEngineEnum;
    /**
     * 意图识别结果
     */
    private SmartIntentResult smartIntentResult;
    /**
     * 是否存储记忆
     */
    private boolean storeMemory;
    /**
     * 是否读取记忆
     */
    private boolean readMemory;
    /**
     * 大模型总结回复id
     */
    private String chatCompletionId;
    /**
     * 工作流执行结果
     */
    private WorkflowResult workflowResult;
    /**
     * 是否是调试模式
     */
    @Builder.Default
    private List<WorkflowResult> workflowResultList = Lists.newArrayList();

    private boolean isDebug = false;
    /**
     * 来源
     */
    private String source ;
    /**
     * 房间号
     */
    private String roomNo;
    private String positionCode;
    private String positionName;
    /**
     * 酒店code
     */
    private String hotelCode;
    /**
     * 音视频房间ID，用于音视频场景
     */
    private String rtcRoomId;
    /**
     * 客户端类型，用于音视频场景
     */
    private String clientType;
    private long firstTokenTime;
    /**
     * 输出流
     */
    @Builder.Default
    private Sinks.Many<ChatCompletions> sinks = Sinks.many().multicast().onBackpressureBuffer();

    public void sinkNext(ChatCompletions chatCompletions) {
        if (Objects.nonNull(chatCompletions)) {
            ChatCompletions.Metadata metadata = chatCompletions.getMetadata();
            if (Objects.nonNull(metadata)) {
                metadata.setConversationId(getConversationId());
                metadata.setTraceId(getTraceId());
            }
            sinks.tryEmitNext(chatCompletions);
        }
    }

    public void sinkComplete() {
        sinks.tryEmitComplete();
    }

    public void sinkError(Throwable throwable) {
        sinks.tryEmitError(throwable);
    }

    public Flux<ChatCompletions> getFlux() {
        return sinks.asFlux();
    }

    public ModelContext.ModelLogContext bulidModelLogContext() {
        return ModelContext.ModelLogContext.builder()
                .traceId(this.getTraceId())
                .clientReqId(this.getClientReqId())
                .accountId(this.getUserId())
                .conversationId(this.getConversationId())
                .botCode(this.getBotCode())
                .chatType(this.getChatType().name())
                .question(this.getQuestion())
                .build();
    }
}
