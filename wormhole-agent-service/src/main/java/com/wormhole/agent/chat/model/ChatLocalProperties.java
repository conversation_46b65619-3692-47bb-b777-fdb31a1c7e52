package com.wormhole.agent.chat.model;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * ChatLocalConfig
 *
 * <AUTHOR>
 * @version 2024/11/11
 */
@Data
@ConfigurationProperties(prefix = "wormhole.chat.local")
public class ChatLocalProperties {

    private boolean readLocalFunctionSystemPrompt = false;
    /**
     * 读取本地定义的workflow
     */
    private boolean readLocalWorkflow = true;

}
