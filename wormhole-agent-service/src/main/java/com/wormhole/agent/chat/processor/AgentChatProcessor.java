package com.wormhole.agent.chat.processor;

import com.wormhole.agent.channel.ReactiveSseService;
import com.wormhole.agent.channel.SseMessage;
import com.wormhole.agent.channel.message.ChatElem;
import com.wormhole.agent.channel.message.EventType;
import com.wormhole.agent.channel.message.MessageType;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.model.chat.ChatResponseMode;
import com.wormhole.agent.core.util.ChatCompletionUtils;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.model.openai.ChatChoice;
import com.wormhole.agent.model.openai.ChatCompletions;
import com.wormhole.agent.model.openai.ChatMessage;
import com.wormhole.agent.service.ChatLogService;
import com.wormhole.agent.service.UserChatMessageService;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.trace.TraceContext;
import com.wormhole.trace.log.PerformanceMetric;
import com.wormhole.trace.log.PerformanceMonitor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.util.Objects;
import java.util.Optional;

/**
 * AgentChatProcessor
 *
 * <AUTHOR>
 * @version 2025/1/12
 */
@Slf4j
public abstract class AgentChatProcessor extends AbstractChatProcessor {

    @Resource
    private ReactiveSseService reactiveSseService;
    @Resource
    private UserChatMessageService userChatMessageService;
    @Resource
    private ChatLogService chatLogService;

    public abstract Flux<ChatContext> doChatCompletions(ChatContext chatContext);

    @Override
    public Flux<ChatCompletions> chatCompletions(ChatContext chatContext) {
        ChatResponseMode chatResponseMode = chatContext.getChatResponseMode();
        doChatCompletions(chatContext)
                .contextCapture()
                .subscribeOn(Schedulers.boundedElastic())
                .doOnError(chatContext::sinkError)
                .doOnComplete(chatContext::sinkComplete)
                .subscribe();

        doChatLogSave(chatContext)
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe();

        switch (chatResponseMode) {
            case STREAM -> {
                return chatContext.getFlux()
                        .doOnNext(completion -> {
                            log.info("chatCompletions info={}", JacksonUtils.writeValueAsString(completion));
                            Optional<ChatChoice> firstChatChoiceOptional = completion.getChoices().stream().findFirst();
                            if (firstChatChoiceOptional.isPresent()) {
                                ChatChoice chatChoice = firstChatChoiceOptional.get();
                                ChatMessage delta = chatChoice.getDelta();
                                if (Objects.nonNull(delta)) {
                                    if (Objects.nonNull(delta.getReasoningContent())) {
                                        delta.setContent(delta.getReasoningContent());
                                    }

                                }
                            }
                            if(chatContext.getFirstTokenTime() == 0L){
                                long start = chatContext.getGatewayStartTime().toEpochMilli();
                                long firstTokenTime = System.currentTimeMillis();
                                chatContext.setFirstTokenTime(firstTokenTime);
                                PerformanceMetric  performanceMetric = PerformanceMetric.builder()
                                        .op("AgentChatProcessor-FirstCompletion")
                                        .opDesc("AgentChat首片耗时")
                                        .startTime(start )
                                        .endTime(firstTokenTime)
                                        .traceId(TraceContext.getTraceId())
                                        .rtcRoomId(chatContext.getRtcRoomId())
                                        .convId(chatContext.getConversationId())
                                        .clientReqId(chatContext.getClientReqId())
                                        .rt(firstTokenTime-start)
                                        .build()
                                        .addExtra("bot_code",chatContext.getBotCode());
                                PerformanceMonitor.log(JacksonUtils.writeValueAsString(performanceMetric));
                            }
                            if (completion.isChatEnd()) {
                                chatContext.setChatCompletionId(completion.getId());
                            }
                        });
            }
            case PUSH -> {
                chatContext.getFlux()
                        .contextCapture()
                        .publishOn(Schedulers.boundedElastic())
                        .doOnNext(completion -> {
                            reactiveSseService.sendEvent(buildSseMessage(chatContext, completion)).subscribe();
                            if (completion.isChatEnd()) {
                                chatContext.setChatCompletionId(completion.getId());
                            }
                        })
                        .doOnError(error -> {
                            ChatCompletions chatCompletions = ChatCompletionUtils.buildChatError(error);
                            // log.info("send push error {}", JacksonUtils.writeValueAsString(chatCompletions));
                            reactiveSseService.sendEvent(buildSseMessage(chatContext, chatCompletions)).subscribe();
                        })
                        .subscribe();

                return Flux.just(ChatCompletionUtils.buildChatCompletions(StringUtils.EMPTY));
            }
            default -> throw new UnsupportedOperationException("Unsupported chat response mode: " + chatResponseMode);
        }
    }

    private Mono<Void> doChatLogSave(ChatContext chatContext) {
        chatContext.setAnswer(StringUtils.EMPTY);
        return chatContext.getFlux()
                .collectList()
                .flatMap(chatCompletionsList -> {
                    String answer = String.join("", chatCompletionsList.stream()
                            .map(ChatCompletionUtils::getContent)
                            .filter(Objects::nonNull)
                            .toList());
                    // 记录完整结果
                    chatContext.setAnswer(answer);
                    chatContext.getExecutionStatManager().end(ExecutionStatType.chat_access, chatContext.getQuestion(), chatContext.getAnswer());
                    return chatLogService.asyncSaveChatLogs(chatContext)
                            .subscribeOn(Schedulers.boundedElastic())
                            .retryWhen(Retry.backoff(3, Duration.ofMillis(100)))
                            .doOnError(e -> log.error("Log save failed"));
                });
    }

    private SseMessage buildSseMessage(ChatContext context, ChatCompletions chatCompletions) {
        return SseMessage.builder()
                .type(MessageType.SINGLE.name())
                .event(EventType.MESSAGE.getEvent())
                .content(new ChatElem(chatCompletions))
                .connectionId(context.getConnectionId())
                .clientReqId(context.getClientReqId())
                .conversationId(context.getConversationId())
                .messageId(context.getMessageId())
                .botCode(context.getBotCode())
                .userId(context.getUserId())
                .traceId(context.getTraceId())
                .build();
    }

}
