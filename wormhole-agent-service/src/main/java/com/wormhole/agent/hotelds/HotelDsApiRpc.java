package com.wormhole.agent.hotelds;

import com.wormhole.channel.consts.message.TicketCommentMessage;
import com.wormhole.common.util.BeanUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.hotelds.api.hotel.client.HotelDsApiClient;
import com.wormhole.hotelds.api.hotel.req.TicketFeedbackMessage;
import com.wormhole.hotelds.core.model.req.DeviceHeartReq;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Component
@Slf4j
public class HotelDsApiRpc {

    @Resource
    private HotelDsApiClient hotelDsApiClient;
    public Mono<Boolean> updateFeedbackStatus(TicketCommentMessage message) {
        TicketFeedbackMessage req = new TicketFeedbackMessage();
        BeanUtils.copy(message, req);
        req.setFeedbackStatus(NumberUtils.toInt(message.getFeedbackStatus()));
        return hotelDsApiClient.feedbackInTicketService(req)
                .onErrorResume(e -> {
                    log.error("updateFeedbackStatus error  {}", JacksonUtils.writeValueAsString(message), e);
                    return Mono.just(false);
                });
    }

    public Mono<Boolean> cleanDeviceTickets(String hotelCode, String positionCode) {
        return hotelDsApiClient.cleanDeviceTickets(hotelCode, positionCode)
                .onErrorResume(e -> {
                    log.error("cleanDeviceTickets error hotelCode {},positionCode {}", hotelCode, positionCode, e);
                    return Mono.just(false);
                });
    }

    public Mono<Boolean> deviceHeartAlive(String deviceId,boolean alive) {
        DeviceHeartReq req = new DeviceHeartReq();
        req.setAlive(alive);
        req.setDeviceId(deviceId);
        return hotelDsApiClient.deviceHeartAlive(req)
                .onErrorResume(e -> {
                    log.error("deviceHeartAlive error req {}",req, e);
                    return Mono.just(false);
                });
    }
}
