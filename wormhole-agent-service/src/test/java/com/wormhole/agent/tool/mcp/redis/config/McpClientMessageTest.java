package com.wormhole.agent.tool.mcp.redis.config;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.wormhole.agent.tool.mcp.config.StandardMcpConfig;
import com.wormhole.common.util.JacksonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * McpClientMessage单元测试
 * 测试MCP客户端消息数据结构的序列化、反序列化、验证等功能
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
class McpClientMessageTest {
    
    private McpClientMessage validMessage;
    private StandardMcpConfig.StandardMcpServer validServer;
    private Map<String, StandardMcpConfig.StandardMcpServer> validServers;
    
    @BeforeEach
    void setUp() {
        // 创建有效的服务器配置
        validServer = StandardMcpConfig.StandardMcpServer.builder()
            .type("stdio")
            .command("python")
            .args(java.util.Arrays.asList("-m", "weather_mcp"))
            .enabled(true)
            .timeout(30)
            .build();
        
        validServers = new HashMap<>();
        validServers.put("weather-service", validServer);
        
        // 创建有效的客户端消息
        validMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .timestamp(Instant.now().toEpochMilli())
            .mcpServers(validServers)
            .action("ADD")
            .build();
    }
    
    @Test
    void testValidMessage() {
        assertTrue(validMessage.isValid());
        assertEquals(McpClientMessage.ActionType.ADD, validMessage.getActionType());
        assertEquals(1, validMessage.getServerCount());
        assertFalse(validMessage.isEmpty());
    }
    
    @Test
    void testInvalidMessage() {
        // 测试空消息ID
        McpClientMessage emptyMessageIdMessage = McpClientMessage.builder()
            .messageId("")
            .action("ADD")
            .mcpServers(validServers)
            .build();
        assertFalse(emptyMessageIdMessage.isValid());
        
        // 测试null消息ID
        McpClientMessage nullMessageIdMessage = McpClientMessage.builder()
            .messageId(null)
            .action("ADD")
            .mcpServers(validServers)
            .build();
        assertFalse(nullMessageIdMessage.isValid());
        
        // 测试无效操作类型
        McpClientMessage invalidActionMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .action("INVALID")
            .mcpServers(validServers)
            .build();
        assertFalse(invalidActionMessage.isValid());
        
        // 测试空操作类型
        McpClientMessage emptyActionMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .action("")
            .mcpServers(validServers)
            .build();
        assertFalse(emptyActionMessage.isValid());
    }
    
    @Test
    void testReloadMessage() {
        // RELOAD操作不需要mcpServers
        McpClientMessage reloadMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .action("RELOAD")
            .build();
        assertTrue(reloadMessage.isValid());
        assertEquals(McpClientMessage.ActionType.RELOAD, reloadMessage.getActionType());
    }
    
    @Test
    void testSerialization() throws JsonProcessingException {
        // 测试序列化
        String json = JacksonUtils.writeValueAsString(validMessage);
        assertNotNull(json);
        assertTrue(json.contains("weather-service"));
        assertTrue(json.contains("ADD"));
        
        // 测试反序列化
        McpClientMessage deserializedMessage = JacksonUtils.readValue(json, McpClientMessage.class);
        assertNotNull(deserializedMessage);
        assertEquals(validMessage.getMessageId(), deserializedMessage.getMessageId());
        assertEquals(validMessage.getAction(), deserializedMessage.getAction());
        assertEquals(validMessage.getServerCount(), deserializedMessage.getServerCount());
        assertTrue(deserializedMessage.isValid());
    }
    
    @Test
    void testActionTypes() {
        // 测试所有有效的操作类型
        String[] validActions = {"ADD", "UPDATE", "DELETE", "RELOAD"};
        
        for (String action : validActions) {
            McpClientMessage message = McpClientMessage.builder()
                .messageId(UUID.randomUUID().toString())
                .action(action)
                .mcpServers(action.equals("RELOAD") ? null : validServers)
                .build();
            
            assertTrue(message.isValid(), "Action " + action + " should be valid");
            assertEquals(McpClientMessage.ActionType.valueOf(action), message.getActionType());
        }
    }
    
    @Test
    void testGetEnabledServers() {
        // 添加一个禁用的服务器
        StandardMcpConfig.StandardMcpServer disabledServer = StandardMcpConfig.StandardMcpServer.builder()
            .type("sse")
            .url("http://localhost:8080/mcp")
            .enabled(false)
            .build();
        
        validServers.put("disabled-service", disabledServer);
        
        Map<String, StandardMcpConfig.StandardMcpServer> enabledServers = validMessage.getEnabledServers();
        assertEquals(1, enabledServers.size());
        assertTrue(enabledServers.containsKey("weather-service"));
        assertFalse(enabledServers.containsKey("disabled-service"));
    }
    
    @Test
    void testGetServerNames() {
        assertEquals(1, validMessage.getServerNames().size());
        assertTrue(validMessage.getServerNames().contains("weather-service"));
        
        // 测试空服务器
        McpClientMessage emptyServersMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .action("RELOAD")
            .build();
        assertTrue(emptyServersMessage.getServerNames().isEmpty());
    }
    
    @Test
    void testToGlobalMcpConfig() {
        GlobalMcpConfig globalConfig = validMessage.toGlobalMcpConfig();
        assertNotNull(globalConfig);
        assertEquals(validMessage.getTimestamp(), globalConfig.getLastModified());
        assertEquals(validMessage.getMcpServers(), globalConfig.getMcpServers());
    }
    
    @Test
    void testFromGlobalMcpConfig() {
        GlobalMcpConfig globalConfig = GlobalMcpConfig.builder()
            .lastModified(Instant.now().toEpochMilli())
            .mcpServers(validServers)
            .build();
        
        McpClientMessage message = McpClientMessage.fromGlobalMcpConfig(globalConfig, McpClientMessage.ActionType.UPDATE);
        assertNotNull(message);
        assertEquals("UPDATE", message.getAction());
        assertEquals(globalConfig.getMcpServers(), message.getMcpServers());
        assertTrue(message.isValid());
        
        // 测试null输入
        McpClientMessage nullResult = McpClientMessage.fromGlobalMcpConfig(null, McpClientMessage.ActionType.ADD);
        assertNull(nullResult);
        
        McpClientMessage nullActionResult = McpClientMessage.fromGlobalMcpConfig(globalConfig, null);
        assertNull(nullActionResult);
    }
    
    @Test
    void testGenerateLockKey() {
        // 测试单个服务器的锁key
        String lockKey = validMessage.generateLockKey();
        assertEquals("mcp:add:weather-service", lockKey);
        
        // 测试多个服务器的锁key
        validServers.put("another-service", validServer);
        String batchLockKey = validMessage.generateLockKey();
        assertEquals("mcp:add:batch", batchLockKey);
        
        // 测试RELOAD操作的锁key
        McpClientMessage reloadMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .action("RELOAD")
            .build();
        String reloadLockKey = reloadMessage.generateLockKey();
        assertEquals("mcp:reload:batch", reloadLockKey);
        
        // 测试null action
        McpClientMessage nullActionMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .mcpServers(validServers)
            .build();
        assertNull(nullActionMessage.generateLockKey());
    }
    
    @Test
    void testBuilderDefaults() {
        McpClientMessage defaultMessage = McpClientMessage.builder().build();
        assertNotNull(defaultMessage.getMessageId());
        assertTrue(defaultMessage.getTimestamp() > 0);
        assertNull(defaultMessage.getAction());
        assertNull(defaultMessage.getMcpServers());
    }
    
    @Test
    void testJsonIgnoreAnnotations() throws JsonProcessingException {
        String json = JacksonUtils.writeValueAsString(validMessage);
        
        // 验证@JsonIgnore注解的方法不会被序列化
        assertFalse(json.contains("valid"));
        assertFalse(json.contains("actionType"));
        assertFalse(json.contains("serverNames"));
        assertFalse(json.contains("enabledServers"));
        assertFalse(json.contains("empty"));
        assertFalse(json.contains("serverCount"));
    }
    
    @Test
    void testCaseInsensitiveActionType() {
        // 测试大小写不敏感的操作类型
        McpClientMessage lowerCaseMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .action("add")
            .mcpServers(validServers)
            .build();
        
        assertTrue(lowerCaseMessage.isValid());
        assertEquals(McpClientMessage.ActionType.ADD, lowerCaseMessage.getActionType());
    }
}
