package com.wormhole.agent.tool.mcp.redis.integration;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.tool.mcp.config.StandardMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.GlobalMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.McpClientMessage;
import com.wormhole.agent.tool.mcp.redis.config.McpRedisProperties;
import com.wormhole.agent.tool.mcp.redis.manager.McpGlobalConfigManager;
import com.wormhole.common.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertFalse;

/**
 * MCP Redis集成测试
 * 测试完整的配置管理流程，包括Redis操作和组件集成
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@Slf4j
class McpRedisIntegrationTest extends BaseTest {
    
    @Autowired
    private McpGlobalConfigManager mcpGlobalConfigManager;
    
    @Autowired
    private McpRedisProperties mcpRedisProperties;
    
    @Autowired
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    private GlobalMcpConfig testConfig;
    private StandardMcpConfig.StandardMcpServer testServer;
    private Map<String, StandardMcpConfig.StandardMcpServer> testServers;
    
    @BeforeEach
    void setUp() {
        log.info("=== 开始设置MCP Redis集成测试环境 ===");

        // 清理测试数据
        cleanupTestData();
        
        log.info("测试环境设置完成");
    }
    
    @AfterEach
    void tearDown() {
        log.info("=== 开始清理MCP Redis集成测试环境 ===");
        cleanupTestData();
        log.info("测试环境清理完成");
    }
    
    private void cleanupTestData() {
        try {
            // 删除测试相关的Redis数据
            reactiveStringRedisTemplate.delete(mcpRedisProperties.getGlobalConfigKey()).block();
            reactiveStringRedisTemplate.delete(mcpRedisProperties.getProcessedMessagesKey()).block();
        } catch (Exception e) {
            log.warn("清理测试数据时发生异常", e);
        }
    }
    
    @Test
    void testCompleteConfigManagementFlow() {
        log.info("开始测试完整的配置管理流程");
        
        // 1. 验证初始状态 - 配置不存在
        StepVerifier.create(mcpGlobalConfigManager.hasGlobalConfig())
            .expectNext(false)
            .verifyComplete();
        
        // 2. 保存配置
        StepVerifier.create(mcpGlobalConfigManager.saveGlobalConfig(testConfig))
            .expectNext(true)
            .verifyComplete();
        
        // 3. 验证配置存在
        StepVerifier.create(mcpGlobalConfigManager.hasGlobalConfig())
            .expectNext(true)
            .verifyComplete();
        
        // 4. 读取配置并验证
        StepVerifier.create(mcpGlobalConfigManager.getGlobalConfigFromRedis())
            .expectNextMatches(config -> 
                config != null && 
                config.getServerCount() == 1 &&
                config.getServerNames().contains("test-service"))
            .verifyComplete();
        
        // 5. 更新服务器配置
        StandardMcpConfig.StandardMcpServer newServer = StandardMcpConfig.StandardMcpServer.builder()
            .type("sse")
            .url("http://localhost:8080/mcp")
            .enabled(true)
            .timeout(60)
            .build();
        
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig("new-test-service", newServer))
            .expectNext(true)
            .verifyComplete();
        
        // 6. 验证更新后的配置
        StepVerifier.create(mcpGlobalConfigManager.getGlobalConfigFromRedis())
            .expectNextMatches(config -> 
                config != null && 
                config.getServerCount() == 2 &&
                config.getServerNames().contains("test-service") &&
                config.getServerNames().contains("new-test-service"))
            .verifyComplete();
        
        // 7. 删除服务器配置
        StepVerifier.create(mcpGlobalConfigManager.removeServerConfig("test-service"))
            .expectNext(true)
            .verifyComplete();
        
        // 8. 验证删除后的配置
        StepVerifier.create(mcpGlobalConfigManager.getGlobalConfigFromRedis())
            .expectNextMatches(config -> 
                config != null && 
                config.getServerCount() == 1 &&
                !config.getServerNames().contains("test-service") &&
                config.getServerNames().contains("new-test-service"))
            .verifyComplete();
        
        // 9. 删除全局配置
        StepVerifier.create(mcpGlobalConfigManager.deleteGlobalConfig())
            .expectNext(true)
            .verifyComplete();
        
        // 10. 验证配置已删除
        StepVerifier.create(mcpGlobalConfigManager.hasGlobalConfig())
            .expectNext(false)
            .verifyComplete();
        
        log.info("完整的配置管理流程测试完成");
    }
    
    @Test
    void testConfigLoadingPriority() {
        log.info("开始测试配置加载优先级");
        
        // 1. Redis中没有配置时，应该返回默认配置
        StepVerifier.create(mcpGlobalConfigManager.loadGlobalConfig())
            .expectNextMatches(config -> 
                config != null && 
                config.isEmpty())
            .verifyComplete();
        
        // 2. 保存配置到Redis
        StepVerifier.create(mcpGlobalConfigManager.saveGlobalConfig(testConfig))
            .expectNext(true)
            .verifyComplete();
        
        // 3. 再次加载配置，应该从Redis读取
        StepVerifier.create(mcpGlobalConfigManager.loadGlobalConfig())
            .expectNextMatches(config -> 
                config != null && 
                config.getServerCount() == 1)
            .verifyComplete();
        
        log.info("配置加载优先级测试完成");
    }
    
    @Test
    void testMessageSerialization() {
        log.info("开始测试消息序列化");
        
        // 创建测试消息
        McpClientMessage testMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .timestamp(Instant.now().toEpochMilli())
            .mcpServers(testServers)
            .action("ADD")
            .build();
        
        // 测试序列化
        String messageJson = JacksonUtils.writeValueAsString(testMessage);
        assertNotNull(messageJson);
        assertTrue(messageJson.contains("ADD"));
        assertTrue(messageJson.contains("test-service"));
        
        // 测试反序列化
        McpClientMessage deserializedMessage = JacksonUtils.readValue(messageJson, McpClientMessage.class);
        assertNotNull(deserializedMessage);
        assertEquals(testMessage.getMessageId(), deserializedMessage.getMessageId());
        assertEquals(testMessage.getAction(), deserializedMessage.getAction());
        assertEquals(testMessage.getServerCount(), deserializedMessage.getServerCount());
        assertTrue(deserializedMessage.isValid());
        
        log.info("消息序列化测试完成");
    }
    
    @Test
    void testConfigValidation() {
        log.info("开始测试配置验证");
        
        // 测试有效配置
        assertTrue(mcpGlobalConfigManager.validateConfig(testConfig));
        
        // 测试无效配置
        GlobalMcpConfig invalidConfig = GlobalMcpConfig.builder()
            .mcpServers(java.util.Collections.emptyMap())
            .build();
        assertFalse(mcpGlobalConfigManager.validateConfig(invalidConfig));
        
        // 测试null配置
        assertFalse(mcpGlobalConfigManager.validateConfig(null));
        
        log.info("配置验证测试完成");
    }
    
    @Test
    void testErrorHandling() {
        log.info("开始测试错误处理");
        
        // 测试保存null配置
        StepVerifier.create(mcpGlobalConfigManager.saveGlobalConfig(null))
            .expectNext(false)
            .verifyComplete();
        
        // 测试更新不存在的服务器
        StepVerifier.create(mcpGlobalConfigManager.removeServerConfig("non-existent-service"))
            .expectNext(false)
            .verifyComplete();
        
        // 测试无效的服务器名称
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig("", testServer))
            .expectNext(false)
            .verifyComplete();
        
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig(null, testServer))
            .expectNext(false)
            .verifyComplete();
        
        log.info("错误处理测试完成");
    }
    
    // 辅助方法
    private void assertNotNull(Object obj) {
        if (obj == null) {
            throw new AssertionError("Expected non-null value");
        }
    }
    
    private void assertTrue(boolean condition) {
        if (!condition) {
            throw new AssertionError("Expected true but was false");
        }
    }
    
    private void assertEquals(Object expected, Object actual) {
        if (!java.util.Objects.equals(expected, actual)) {
            throw new AssertionError("Expected: " + expected + " but was: " + actual);
        }
    }
}
