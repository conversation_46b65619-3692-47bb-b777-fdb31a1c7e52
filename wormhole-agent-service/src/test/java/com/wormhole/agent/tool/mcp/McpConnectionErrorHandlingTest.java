package com.wormhole.agent.tool.mcp;

import com.wormhole.agent.tool.mcp.service.McpHealthChecker;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * MCP连接错误处理测试
 * 验证各种错误场景的处理逻辑
 *
 * <AUTHOR>
 * @version 2025-08-19
 */
public class McpConnectionErrorHandlingTest {

    @Test
    @DisplayName("测试404错误检测和分类")
    public void testDetect404Error() {
        // 模拟404错误
        Exception error404 = new RuntimeException("HTTP error: 404 Not Found");
        
        // 验证错误类型检测
        assertTrue(isNotFoundError(error404), "应该检测到404错误");
        
        // 模拟嵌套的404错误
        Exception nestedError = new RuntimeException("Request failed", 
            new RuntimeException("Unexpected status code: 404"));
        
        assertTrue(isNotFoundError(nestedError), "应该检测到嵌套的404错误");
    }

    @Test
    @DisplayName("测试连接错误分类")
    public void testConnectionErrorClassification() {
        // 连接被拒绝错误
        Exception connectionRefused = new RuntimeException("Connection refused");
        assertTrue(isConnectionError(connectionRefused), "应该检测到连接被拒绝错误");
        
        // 超时错误
        Exception timeout = new RuntimeException("Connection timeout");
        assertTrue(isTimeoutError(timeout), "应该检测到超时错误");
        
        // 认证错误
        Exception auth = new RuntimeException("401 Unauthorized");
        assertTrue(isAuthError(auth), "应该检测到认证错误");
    }

    @Test
    @DisplayName("测试重试逻辑判断")
    public void testRetryLogic() {
        // 不可重试的错误
        Exception notFoundError = new RuntimeException("404 Not Found");
        assertFalse(shouldRetry(notFoundError), "404错误不应该重试");
        
        Exception authError = new RuntimeException("401 Unauthorized");
        assertFalse(shouldRetry(authError), "认证错误不应该重试");
        
        // 可重试的错误
        Exception timeoutError = new RuntimeException("timeout");
        assertTrue(shouldRetry(timeoutError), "超时错误应该重试");
        
        Exception serverError = new RuntimeException("500 Internal Server Error");
        assertTrue(shouldRetry(serverError), "服务器错误应该重试");
    }

    @Test
    @DisplayName("测试错误类型枚举")
    public void testErrorTypeEnum() {
        McpHealthChecker.ErrorType notFound = McpHealthChecker.ErrorType.NOT_FOUND;
        assertEquals("服务不存在(404)", notFound.getDescription());
        
        McpHealthChecker.ErrorType timeout = McpHealthChecker.ErrorType.TIMEOUT;
        assertEquals("超时", timeout.getDescription());
    }

    @Test
    @DisplayName("测试健康检查结果")
    public void testHealthCheckResult() {
        // 测试健康状态
        McpHealthChecker.HealthCheckResult healthy = 
            McpHealthChecker.HealthCheckResult.healthy(100);
        
        assertTrue(healthy.isHealthy());
        assertEquals(100, healthy.getResponseTime());
        assertEquals(McpHealthChecker.ErrorType.NONE, healthy.getErrorType());
        
        // 测试404错误状态
        McpHealthChecker.HealthCheckResult notFound = 
            McpHealthChecker.HealthCheckResult.connectionNotFound("服务不可达");
        
        assertFalse(notFound.isHealthy());
        assertEquals(McpHealthChecker.ErrorType.NOT_FOUND, notFound.getErrorType());
        assertTrue(notFound.requiresReconfiguration());
        assertFalse(notFound.isRetriable());
        
        // 测试超时错误状态
        McpHealthChecker.HealthCheckResult timeout = 
            McpHealthChecker.HealthCheckResult.timeout("连接超时");
        
        assertFalse(timeout.isHealthy());
        assertEquals(McpHealthChecker.ErrorType.TIMEOUT, timeout.getErrorType());
        assertTrue(timeout.isRetriable());
        assertFalse(timeout.requiresReconfiguration());
    }

    // 辅助方法，模拟实际的错误检测逻辑
    private boolean isNotFoundError(Throwable throwable) {
        String message = throwable.getMessage();
        String causeMessage = throwable.getCause() != null ? throwable.getCause().getMessage() : "";
        
        return (message != null && message.contains("404")) || 
               causeMessage.contains("404") || 
               causeMessage.contains("Unexpected status code: 404");
    }
    
    private boolean isConnectionError(Throwable throwable) {
        String message = throwable.getMessage();
        return message != null && (
            message.contains("Connection refused") ||
            message.contains("ConnectException")
        );
    }
    
    private boolean isTimeoutError(Throwable throwable) {
        String message = throwable.getMessage();
        return message != null && (
            message.contains("timeout") ||
            message.contains("Timeout")
        );
    }
    
    private boolean isAuthError(Throwable throwable) {
        String message = throwable.getMessage();
        return message != null && (
            message.contains("401") ||
            message.contains("403") ||
            message.contains("Unauthorized")
        );
    }
    
    private boolean shouldRetry(Throwable throwable) {
        String message = throwable.getMessage();
        
        // 不可重试的错误类型
        if (message != null && (
            message.contains("404") ||
            message.contains("401") ||
            message.contains("403"))) {
            return false;
        }
        
        // 可重试的错误类型
        return message != null && (
            message.contains("timeout") ||
            message.contains("500") ||
            message.contains("502") ||
            message.contains("503")
        );
    }
}