package com.wormhole.agent.tool.mcp;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.model.openai.OpenAiTool;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MCP工具预加载功能测试
 */
@Slf4j
@SpringBootTest
class McpPreloadTest extends BaseTest {

    @Autowired
    private McpToolProvider mcpToolProvider;

    @Test
    void testPreloadedToolsAvailability() {
        log.info("=== 测试预加载工具可用性 ===");
        
        // 获取所有预加载的工具
        Map<String, List<OpenAiTool>> preloadedTools = mcpToolProvider.getAllPreloadedTools();
        assertNotNull(preloadedTools, "预加载工具映射不应该为null");
        
        log.info("预加载的客户端数量: {}", preloadedTools.size());
        
        // 获取可用客户端
        Set<String> availableClients = mcpToolProvider.getAvailableClientNames();
        assertNotNull(availableClients, "可用客户端集合不应该为null");
        
        log.info("可用客户端: {}", availableClients);
        
        // 验证每个可用客户端都有对应的预加载工具
        for (String clientName : availableClients) {
            assertTrue(mcpToolProvider.isClientAvailable(clientName), 
                    "客户端 " + clientName + " 应该是可用的");
            
            List<OpenAiTool> tools = mcpToolProvider.getPreloadedTools(clientName);
            assertNotNull(tools, "客户端 " + clientName + " 的工具列表不应该为null");
            
            log.info("客户端 {} 预加载了 {} 个工具", clientName, tools.size());
        }
        
        // 测试不存在的客户端
        List<OpenAiTool> nonExistentTools = mcpToolProvider.getPreloadedTools("non-existent-client");
        assertTrue(nonExistentTools.isEmpty(), "不存在的客户端应该返回空工具列表");
        
        assertFalse(mcpToolProvider.isClientAvailable("non-existent-client"), 
                "不存在的客户端应该不可用");
        
        log.info("✅ 预加载工具可用性测试通过");
    }
    
    @Test
    void testMcpStatus() {
        log.info("=== 测试MCP状态信息 ===");
        
        McpToolProvider.McpStatus status = mcpToolProvider.getStatus();
        assertNotNull(status, "MCP状态不应该为null");
        
        log.info("MCP启用状态: {}", status.isEnabled());
        log.info("总客户端数量: {}", status.getTotalClients());
        log.info("总工具数量: {}", status.getTotalTools());
        log.info("可用客户端: {}", status.getAvailableClients());
        
        // 验证状态一致性
        assertEquals(status.getAvailableClients().size(), status.getTotalClients(), 
                "可用客户端数量应该与总客户端数量一致");
        
        Map<String, Integer> clientToolCounts = status.getClientToolCounts();
        assertNotNull(clientToolCounts, "客户端工具计数不应该为null");
        
        int calculatedTotal = clientToolCounts.values().stream()
                .mapToInt(Integer::intValue)
                .sum();
        assertEquals(calculatedTotal, status.getTotalTools(), 
                "计算的总工具数量应该与状态中的一致");
        
        // 打印详细信息
        log.info("客户端工具分布:");
        clientToolCounts.forEach((clientName, toolCount) -> {
            log.info("  {}: {} 个工具", clientName, toolCount);
        });
        
        log.info("✅ MCP状态信息测试通过");
    }
    
    @Test
    void testPreloadVsRuntimeDiscovery() {
        log.info("=== 测试预加载与运行时发现的一致性 ===");
        
        Map<String, List<OpenAiTool>> preloadedTools = mcpToolProvider.getAllPreloadedTools();
        
        if (preloadedTools.isEmpty()) {
            log.warn("没有预加载的工具，跳过一致性测试");
            return;
        }
        
        // 验证预加载的工具在多次获取时保持一致
        for (String clientName : preloadedTools.keySet()) {
            List<OpenAiTool> tools1 = mcpToolProvider.getPreloadedTools(clientName);
            List<OpenAiTool> tools2 = mcpToolProvider.getPreloadedTools(clientName);
            
            assertEquals(tools1.size(), tools2.size(), 
                    "同一客户端的工具数量应该保持一致");
            
            // 验证工具名称一致性
            for (int i = 0; i < tools1.size(); i++) {
                assertEquals(tools1.get(i).getFunction().getName(), 
                           tools2.get(i).getFunction().getName(),
                           "工具名称应该保持一致");
            }
            
            log.info("客户端 {} 的工具一致性验证通过", clientName);
        }
        
        log.info("✅ 预加载一致性测试通过");
    }
    
    @Test
    void testToolCaching() {
        log.info("=== 测试工具缓存机制 ===");
        
        Set<String> availableClients = mcpToolProvider.getAvailableClientNames();
        
        if (availableClients.isEmpty()) {
            log.warn("没有可用的客户端，跳过缓存测试");
            return;
        }
        
        // 测试缓存命中
        for (String clientName : availableClients) {
            long startTime = System.currentTimeMillis();
            List<OpenAiTool> tools1 = mcpToolProvider.getPreloadedTools(clientName);
            long firstCallTime = System.currentTimeMillis() - startTime;
            
            startTime = System.currentTimeMillis();
            List<OpenAiTool> tools2 = mcpToolProvider.getPreloadedTools(clientName);
            long secondCallTime = System.currentTimeMillis() - startTime;
            
            // 第二次调用应该更快（缓存命中）
            assertTrue(secondCallTime <= firstCallTime + 10, // 允许10ms误差
                    "缓存命中应该更快");
            
            assertEquals(tools1.size(), tools2.size(), 
                    "缓存的工具数量应该一致");
            
            log.info("客户端 {} 缓存测试: 首次{}ms, 缓存{}ms", 
                    clientName, firstCallTime, secondCallTime);
        }
        
        log.info("✅ 工具缓存测试通过");
    }
}
