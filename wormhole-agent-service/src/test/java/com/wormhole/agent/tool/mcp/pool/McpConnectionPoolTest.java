package com.wormhole.agent.tool.mcp.pool;

import dev.langchain4j.mcp.client.McpClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import reactor.test.StepVerifier;

import java.time.Duration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * MCP连接池测试
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
class McpConnectionPoolTest {
    
    @Mock
    private McpClient mockClient;
    
    private McpConnectionPool connectionPool;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        connectionPool = McpConnectionPool.builder()
            .poolName("test-pool")
            .maxConnections(5)
            .acquireTimeout(Duration.ofSeconds(5))
            .idleTimeout(Duration.ofMinutes(1))
            .maxLifetime(Duration.ofMinutes(10))
            .pendingAcquireMaxCount(10)
            .build();
    }
    
    @Test
    void testAcquireConnection_Success() {
        // 测试成功获取连接
        String clientName = "test-client";
        
        StepVerifier.create(connectionPool.acquireConnection(clientName, () -> mockClient))
            .expectNext(mockClient)
            .verifyComplete();
        
        // 验证统计信息
        ConnectionPoolStats stats = connectionPool.getStats();
        assertEquals(1, stats.getTotalConnections());
        assertEquals(1, stats.getActiveConnections());
        assertEquals(0, stats.getIdleConnections());
        assertEquals(1, stats.getConnectionRequests());
    }
    
    @Test
    void testReleaseConnection_Success() {
        // 先获取连接
        String clientName = "test-client";
        
        StepVerifier.create(connectionPool.acquireConnection(clientName, () -> mockClient))
            .expectNext(mockClient)
            .verifyComplete();
        
        // 释放连接
        StepVerifier.create(connectionPool.releaseConnection(clientName))
            .expectNext(true)
            .verifyComplete();
        
        // 验证统计信息
        ConnectionPoolStats stats = connectionPool.getStats();
        assertEquals(1, stats.getTotalConnections());
        assertEquals(0, stats.getActiveConnections());
        assertEquals(1, stats.getIdleConnections());
    }
    
    @Test
    void testConnectionReuse() {
        // 测试连接复用
        String clientName = "test-client";
        
        // 第一次获取连接
        StepVerifier.create(connectionPool.acquireConnection(clientName, () -> mockClient))
            .expectNext(mockClient)
            .verifyComplete();
        
        // 释放连接
        StepVerifier.create(connectionPool.releaseConnection(clientName))
            .expectNext(true)
            .verifyComplete();
        
        // 第二次获取连接（应该复用）
        StepVerifier.create(connectionPool.acquireConnection(clientName, () -> {
            fail("不应该创建新连接");
            return null;
        }))
            .expectNext(mockClient)
            .verifyComplete();
        
        // 验证命中率
        ConnectionPoolStats stats = connectionPool.getStats();
        assertEquals(2, stats.getConnectionRequests());
        assertEquals(1, stats.getConnectionHits());
        assertEquals(0.5, stats.getHitRate(), 0.01);
    }
    
    @Test
    void testMaxConnectionsLimit() {
        // 测试最大连接数限制
        connectionPool = McpConnectionPool.builder()
            .maxConnections(1)
            .acquireTimeout(Duration.ofSeconds(1))
            .build();
        
        // 第一个连接成功
        StepVerifier.create(connectionPool.acquireConnection("client1", () -> mockClient))
            .expectNext(mockClient)
            .verifyComplete();
        
        // 第二个连接应该失败
        McpClient mockClient2 = mock(McpClient.class);
        StepVerifier.create(connectionPool.acquireConnection("client2", () -> mockClient2))
            .expectError(RuntimeException.class)
            .verify();
    }
    
    @Test
    void testRemoveClientConnections() throws Exception {
        // 测试移除客户端连接
        String clientName = "test-client";
        
        // 获取连接
        StepVerifier.create(connectionPool.acquireConnection(clientName, () -> mockClient))
            .expectNext(mockClient)
            .verifyComplete();
        
        // 移除连接
        StepVerifier.create(connectionPool.removeClientConnections(clientName))
            .expectNext(true)
            .verifyComplete();
        
        // 验证连接已被移除
        ConnectionPoolStats stats = connectionPool.getStats();
        assertEquals(0, stats.getTotalConnections());
        assertEquals(0, stats.getActiveConnections());
        
        // 验证客户端的close方法被调用
        verify(mockClient, times(1)).close();
    }
    
    @Test
    void testConnectionPoolStats() {
        // 测试连接池统计信息
        ConnectionPoolStats stats = connectionPool.getStats();
        
        assertNotNull(stats);
        assertEquals("test-pool", stats.getPoolName());
        assertEquals(0, stats.getTotalConnections());
        assertEquals(0, stats.getActiveConnections());
        assertEquals(0, stats.getIdleConnections());
        assertEquals(5, stats.getMaxConnections());
        assertEquals(0, stats.getConnectionRequests());
        assertEquals(0, stats.getConnectionHits());
        assertEquals(0.0, stats.getHitRate());
        assertTrue(stats.isHealthy());
    }
    
    @Test
    void testConnectionPoolDestroy() throws Exception {
        // 测试连接池销毁
        String clientName = "test-client";
        
        // 获取连接
        StepVerifier.create(connectionPool.acquireConnection(clientName, () -> mockClient))
            .expectNext(mockClient)
            .verifyComplete();
        
        // 销毁连接池
        connectionPool.destroy();
        
        // 验证客户端的close方法被调用
        verify(mockClient, times(1)).close();
        
        // 验证统计信息被重置
        ConnectionPoolStats stats = connectionPool.getStats();
        assertEquals(0, stats.getTotalConnections());
        assertEquals(0, stats.getActiveConnections());
    }
}
