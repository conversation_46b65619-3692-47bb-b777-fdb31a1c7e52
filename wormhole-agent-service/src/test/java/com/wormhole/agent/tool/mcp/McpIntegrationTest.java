package com.wormhole.agent.tool.mcp;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wormhole.agent.BaseTest;
import com.wormhole.agent.model.openai.OpenAiFunction;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import com.wormhole.agent.tool.mcp.config.McpProperties;
import dev.langchain4j.mcp.client.McpClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * MCP统一集成测试
 * 提供简单的方法验证MCP客户端初始化、工具发现和工具调用功能
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Slf4j
class McpIntegrationTest extends BaseTest {

    @Autowired
    private McpToolProvider mcpToolProvider;

    @Autowired
    private McpProperties mcpProperties;

    @Autowired
    private McpToolAdapter mcpToolAdapter;

    private ToolChainContext testContext;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        testContext = new ToolChainContext();
        testContext.setMcpToolClientMap(new ConcurrentHashMap<>());
        objectMapper = new ObjectMapper();
    }

    /**
     * 验证MCP客户端是否能初始化成功
     */
    @Test
    void testMcpClientInitialization() {
        log.info("=== 测试MCP客户端初始化 ===");

        try {
            // 获取配置信息
            assertNotNull(mcpProperties, "MCP配置应该被正确注入");
            log.info("MCP启用状态: {}", mcpProperties.isEnabled());
            log.info("默认超时时间: {}秒", mcpProperties.getDefaultTimeout());

            // 获取客户端实例
            Map<String, McpClient> clients = getMcpClients();
            log.info("成功创建的客户端数量: {}", clients.size());

            // 验证每个客户端
            for (Map.Entry<String, McpClient> entry : clients.entrySet()) {
                String clientName = entry.getKey();
                McpClient client = entry.getValue();

                assertNotNull(client, "客户端 " + clientName + " 不应该为null");
                log.info("✅ 客户端 {} 初始化成功 (类型: {})",
                        clientName, client.getClass().getSimpleName());
            }

            // 验证配置与实际创建的客户端一致性
            if (mcpProperties.getClients() != null) {
                long enabledCount = mcpProperties.getClients().stream()
                        .filter(McpProperties.McpClientConfig::isEnabled)
                        .count();
                log.info("配置中启用的客户端: {}, 实际创建的客户端: {}", enabledCount, clients.size());
            }

            assertTrue(clients.size() >= 0, "应该至少尝试创建MCP客户端");

        } catch (Exception e) {
            log.error("客户端初始化测试失败", e);
            fail("客户端初始化测试失败: " + e.getMessage());
        }
    }

    /**
     * 验证工具预加载功能
     */
    @Test
    void testToolPreloading() {
        log.info("=== 测试工具预加载功能 ===");

        try {
            // 获取预加载的工具
            Map<String, List<OpenAiTool>> preloadedTools = mcpToolProvider.getAllPreloadedTools();
            log.info("预加载的客户端数量: {}", preloadedTools.size());

            int totalPreloadedTools = 0;

            // 验证每个客户端的预加载工具
            for (Map.Entry<String, List<OpenAiTool>> entry : preloadedTools.entrySet()) {
                String clientName = entry.getKey();
                List<OpenAiTool> tools = entry.getValue();

                log.info("客户端 {} 预加载了 {} 个工具", clientName, tools.size());
                totalPreloadedTools += tools.size();

                // 验证工具列表不为null
                assertNotNull(tools, "预加载的工具列表不应该为null");

                // 验证客户端是否可用
                assertTrue(mcpToolProvider.isClientAvailable(clientName),
                        "客户端 " + clientName + " 应该是可用的");

                // 打印前3个工具的信息
                for (int i = 0; i < Math.min(3, tools.size()); i++) {
                    OpenAiTool tool = tools.get(i);
                    log.info("  工具 {}: {} - {}",
                            i + 1,
                            tool.getFunction().getName(),
                            tool.getFunction().getDescription());
                }

                if (tools.size() > 3) {
                    log.info("  ... 还有 {} 个工具", tools.size() - 3);
                }
            }

            log.info("总预加载工具数量: {}", totalPreloadedTools);

            // 验证状态信息
            McpToolProvider.McpStatus status = mcpToolProvider.getStatus();
            assertNotNull(status, "MCP状态不应该为null");
            assertEquals(totalPreloadedTools, status.getTotalTools(), "状态中的工具数量应该与实际一致");

            log.info("✅ 工具预加载验证通过");

        } catch (Exception e) {
            log.error("工具预加载测试失败", e);
            fail("工具预加载测试失败: " + e.getMessage());
        }
    }

    /**
     * 验证每个MCP客户端有哪些工具（使用预加载的工具）
     */
    @Test
    void testDiscoverToolsForEachClient() {
        log.info("=== 测试每个客户端的工具发现 ===");

        try {
            // 优先使用预加载的工具
            Map<String, List<OpenAiTool>> clientTools = mcpToolProvider.getAllPreloadedTools();

            if (clientTools.isEmpty()) {
                log.warn("没有预加载的工具，尝试直接从客户端发现...");

                Map<String, McpClient> clients = getMcpClients();
                if (clients.isEmpty()) {
                    log.warn("没有可用的MCP客户端，跳过工具发现测试");
                    return;
                }

                clientTools = new HashMap<>();

                // 为每个客户端发现工具
                for (Map.Entry<String, McpClient> entry : clients.entrySet()) {
                    String clientName = entry.getKey();
                    McpClient client = entry.getValue();

                    log.info("--- 发现客户端 {} 的工具 ---", clientName);

                    try {
                        List<OpenAiTool> tools = mcpToolAdapter.discoverTools(client);
                        clientTools.put(clientName, tools);
                        log.info("客户端 {} 提供 {} 个工具:", clientName, tools.size());
                    } catch (Exception e) {
                        log.error("❌ 客户端 {} 工具发现失败: {}", clientName, e.getMessage());
                        clientTools.put(clientName, Collections.emptyList());
                    }
                }
            } else {
                log.info("使用预加载的工具进行测试");
            }

            int totalTools = 0;

            // 遍历所有客户端的工具
            for (Map.Entry<String, List<OpenAiTool>> entry : clientTools.entrySet()) {
                String clientName = entry.getKey();
                List<OpenAiTool> tools = entry.getValue();

                log.info("--- 客户端 {} 的工具列表 ---", clientName);
                totalTools += tools.size();
                log.info("客户端 {} 提供 {} 个工具:", clientName, tools.size());

                // 打印工具详情
                for (int i = 0; i < tools.size(); i++) {
                    OpenAiTool tool = tools.get(i);
                    log.info("  {}. 工具: {} | 描述: {}",
                            i + 1,
                            tool.getFunction().getName(),
                            tool.getFunction().getDescription());

                    // 打印参数信息
                    if (tool.getFunction().getParameters() != null &&
                        tool.getFunction().getParameters().getProperties() != null) {
                        int paramCount = tool.getFunction().getParameters().getProperties().size();
                        log.info("     参数数量: {}", paramCount);

                        // 只打印前3个参数避免日志过长
                        tool.getFunction().getParameters().getProperties().entrySet().stream()
                                .limit(3)
                                .forEach(param -> {
                                    OpenAiFunction.PropertiesInfo paramDef = param.getValue();
                                    String type = extractParameterType(paramDef);
                                    log.info("       - {}: {}", param.getKey(), type);
                                });

                        if (paramCount > 3) {
                            log.info("       ... 还有{}个参数", paramCount - 3);
                        }
                    }

                    // 验证工具基本信息
                    assertNotNull(tool.getFunction().getName(), "工具名称不应该为null");
                    assertFalse(tool.getFunction().getName().trim().isEmpty(), "工具名称不应该为空");
                }

                assertNotNull(tools, "工具列表不应该为null");
            }

            // 总结
            log.info("=== 工具发现总结 ===");
            log.info("总客户端数量: {}", clientTools.size());
            log.info("总工具数量: {}", totalTools);

            clientTools.forEach((clientName, tools) -> {
                log.info("客户端 {}: {} 个工具", clientName, tools.size());
            });

            assertTrue(totalTools >= 0, "总工具数量应该大于等于0");

            // 验证与状态信息的一致性
            McpToolProvider.McpStatus status = mcpToolProvider.getStatus();
            assertEquals(totalTools, status.getTotalTools(), "发现的工具数量应该与状态一致");

            log.info("✅ 工具发现测试完成");

        } catch (Exception e) {
            log.error("工具发现测试失败", e);
            fail("工具发现测试失败: " + e.getMessage());
        }
    }

    /**
     * 验证工具能否调用成功
     */
    @Test
    void testToolExecution() {
        log.info("=== 测试工具调用 ===");

        try {
            Map<String, McpClient> clients = getMcpClients();

            if (clients.isEmpty()) {
                log.warn("没有可用的MCP客户端，跳过工具调用测试");
                return;
            }

            boolean hasSuccessfulCall = false;

            // 为每个客户端测试工具调用
            for (Map.Entry<String, McpClient> entry : clients.entrySet()) {
                String clientName = entry.getKey();
                McpClient client = entry.getValue();

                log.info("--- 测试客户端 {} 的工具调用 ---", clientName);

                try {
                    List<OpenAiTool> tools = mcpToolAdapter.discoverTools(client);

                    if (tools.isEmpty()) {
                        log.info("客户端 {} 没有可用工具", clientName);
                        continue;
                    }

                    // 选择第一个工具进行测试调用
                    OpenAiTool testTool = tools.get(0);
                    String toolName = testTool.getFunction().getName();

                    log.info("测试调用工具: {}", toolName);

                    // 构造简单的测试参数
                    Map<String, Object> testArgs = createTestArguments(testTool);
                    String argsJson = objectMapper.writeValueAsString(testArgs);

                    log.info("测试参数: {}", argsJson);

                    try {
                        // 尝试调用工具
                        String result = mcpToolAdapter.executeTool(client, toolName, testArgs);

                        assertNotNull(result, "工具调用结果不应该为null");
                        log.info("✅ 工具 {} 调用成功", toolName);
                        log.info("调用结果: {}", result.length() > 200 ? result.substring(0, 200) + "..." : result);

                        hasSuccessfulCall = true;

                    } catch (Exception e) {
                        log.warn("⚠️ 工具 {} 调用失败: {}", toolName, e.getMessage());
                        // 不让单个工具调用失败影响整个测试
                    }

                } catch (Exception e) {
                    log.error("❌ 客户端 {} 工具调用测试失败: {}", clientName, e.getMessage());
                }
            }

            // 如果没有任何成功的调用，记录警告但不失败测试
            if (!hasSuccessfulCall) {
                log.warn("⚠️ 没有成功的工具调用，可能是因为工具需要特定参数或外部依赖");
            } else {
                log.info("✅ 至少有一个工具调用成功");
            }

        } catch (Exception e) {
            log.error("工具调用测试失败", e);
            fail("工具调用测试失败: " + e.getMessage());
        }
    }

    /**
     * 综合测试：客户端初始化 + 工具发现 + 工具调用
     */
    @Test
    void testMcpIntegrationSummary() {
        log.info("=== MCP集成测试总结 ===");

        try {
            // 1. 客户端初始化状态
            Map<String, McpClient> clients = getMcpClients();
            log.info("📊 客户端初始化状态:");
            log.info("  配置的客户端数量: {}", mcpProperties.getClients() != null ? mcpProperties.getClients().size() : 0);
            log.info("  成功创建的客户端数量: {}", clients.size());
            log.info("  MCP功能启用状态: {}", mcpProperties.isEnabled());

            // 2. 工具发现状态
            int totalTools = 0;
            Map<String, Integer> clientToolCounts = new HashMap<>();

            for (Map.Entry<String, McpClient> entry : clients.entrySet()) {
                String clientName = entry.getKey();
                try {
                    List<OpenAiTool> tools = mcpToolAdapter.discoverTools(entry.getValue());
                    int toolCount = tools.size();
                    totalTools += toolCount;
                    clientToolCounts.put(clientName, toolCount);
                } catch (Exception e) {
                    clientToolCounts.put(clientName, 0);
                    log.warn("客户端 {} 工具发现失败: {}", clientName, e.getMessage());
                }
            }

            log.info("📊 工具发现状态:");
            log.info("  总可用工具数量: {}", totalTools);
            clientToolCounts.forEach((clientName, toolCount) -> {
                log.info("  客户端 {} 提供 {} 个工具", clientName, toolCount);
            });

            // 3. 整体状态评估
            log.info("📊 整体集成状态:");
            if (clients.size() > 0 && totalTools > 0) {
                log.info("✅ MCP集成成功！客户端和工具都可用");
            } else if (clients.size() > 0) {
                log.warn("⚠️ MCP客户端创建成功，但没有发现可用工具");
            } else {
                log.warn("⚠️ 没有成功创建MCP客户端");
            }

            // 基本断言
            assertTrue(clients.size() >= 0, "客户端数量应该大于等于0");
            assertTrue(totalTools >= 0, "工具数量应该大于等于0");

        } catch (Exception e) {
            log.error("集成测试总结失败", e);
            fail("集成测试总结失败: " + e.getMessage());
        }
    }

    // ==================== 辅助方法 ====================

    /**
     * 获取MCP客户端实例（通过反射）
     */
    private Map<String, McpClient> getMcpClients() throws Exception {
        Field mcpClientsField = McpToolProvider.class.getDeclaredField("mcpClients");
        mcpClientsField.setAccessible(true);

        @SuppressWarnings("unchecked")
        Map<String, McpClient> mcpClients = (Map<String, McpClient>) mcpClientsField.get(mcpToolProvider);

        return mcpClients != null ? mcpClients : Collections.emptyMap();
    }

    /**
     * 提取参数类型信息
     */
    private String extractParameterType(OpenAiFunction.PropertiesInfo paramDef) {
        if (paramDef != null) {
            String type = paramDef.getType() != null ? paramDef.getType() : "unknown";
            String description = paramDef.getDescription();

            if (description != null && !description.trim().isEmpty()) {
                if (description.length() > 50) {
                    description = description.substring(0, 50) + "...";
                }
                return type + " (" + description + ")";
            }
            return type;
        }
        return "unknown";
    }

    /**
     * 为工具创建测试参数
     */
    private Map<String, Object> createTestArguments(OpenAiTool tool) {
        Map<String, Object> args = new HashMap<>();

        if (tool.getFunction().getParameters() != null &&
            tool.getFunction().getParameters().getProperties() != null) {

            Map<String, OpenAiFunction.PropertiesInfo> properties = tool.getFunction().getParameters().getProperties();

            // 为每个参数创建简单的测试值
            for (Map.Entry<String, OpenAiFunction.PropertiesInfo> entry : properties.entrySet()) {
                String paramName = entry.getKey();
                OpenAiFunction.PropertiesInfo paramDef = entry.getValue();

                Object testValue = createTestValue(paramName, paramDef);
                if (testValue != null) {
                    args.put(paramName, testValue);
                }
            }
        }

        return args;
    }

    /**
     * 根据参数定义创建测试值
     */
    private Object createTestValue(String paramName, OpenAiFunction.PropertiesInfo paramDef) {
        if (paramDef == null) {
            return null;
        }

        String type = paramDef.getType() != null ? paramDef.getType() : "string";

        switch (type.toLowerCase()) {
            case "string":
                // 根据参数名称提供合适的测试值
                if (paramName.toLowerCase().contains("path")) {
                    return "/tmp/test";
                } else if (paramName.toLowerCase().contains("url")) {
                    return "https://example.com";
                } else if (paramName.toLowerCase().contains("name")) {
                    return "test-name";
                } else {
                    return "test-value";
                }

            case "integer":
            case "number":
                return 1;

            case "boolean":
                return true;

            case "array":
                return Arrays.asList("test-item");

            case "object":
                Map<String, Object> testObj = new HashMap<>();
                testObj.put("test", "value");
                return testObj;

            default:
                return "test-default";
        }
    }
}
