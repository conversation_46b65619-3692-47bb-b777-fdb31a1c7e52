package com.wormhole.agent.tool.mcp;

import com.wormhole.agent.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * MCP测试使用示例
 * 展示如何使用统一的MCP测试类进行简单验证
 */
@Slf4j
public class McpTestExample extends BaseTest {

    @Autowired
    private McpIntegrationTest mcpIntegrationTest;

    /**
     * 快速验证MCP功能是否正常
     * 这个方法展示了如何使用统一测试类的各个方法
     */
    @Test
    void quickMcpValidation() {
        log.info("🚀 开始MCP快速验证");
        
        try {
            // 1. 验证客户端初始化
            log.info("1️⃣ 验证客户端初始化...");
            mcpIntegrationTest.testMcpClientInitialization();
            
            // 2. 验证工具发现
            log.info("2️⃣ 验证工具发现...");
            mcpIntegrationTest.testDiscoverToolsForEachClient();
            
            // 3. 验证工具调用
            log.info("3️⃣ 验证工具调用...");
            mcpIntegrationTest.testToolExecution();
            
            // 4. 查看总结
            log.info("4️⃣ 查看集成总结...");
            mcpIntegrationTest.testMcpIntegrationSummary();
            
            log.info("✅ MCP快速验证完成");
            
        } catch (Exception e) {
            log.error("❌ MCP验证失败", e);
            throw new RuntimeException("MCP验证失败", e);
        }
    }
    
    /**
     * 仅验证客户端初始化
     */
    @Test
    void testClientInitOnly() {
        log.info("🔧 仅验证MCP客户端初始化");
        mcpIntegrationTest.testMcpClientInitialization();
    }
    
    /**
     * 仅验证工具发现
     */
    @Test
    void testToolDiscoveryOnly() {
        log.info("🔍 仅验证MCP工具发现");
        mcpIntegrationTest.testDiscoverToolsForEachClient();
    }
    
    /**
     * 仅验证工具调用
     */
    @Test
    void testToolExecutionOnly() {
        log.info("⚡ 仅验证MCP工具调用");
        mcpIntegrationTest.testToolExecution();
    }
}
