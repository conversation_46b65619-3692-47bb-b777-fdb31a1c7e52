package com.wormhole.agent.tool.mcp.redis.util;

import com.wormhole.agent.tool.mcp.config.StandardMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.GlobalMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.McpClientMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;

/**
 * McpRedisUtils单元测试
 * 测试MCP Redis工具类的所有方法
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
class McpRedisUtilsTest {
    
    private StandardMcpConfig standardConfig;
    private GlobalMcpConfig globalConfig;
    private McpClientMessage clientMessage;
    private StandardMcpConfig.StandardMcpServer validServer;
    private Map<String, StandardMcpConfig.StandardMcpServer> validServers;
    
    @BeforeEach
    void setUp() {
        // 创建有效的服务器配置
        validServer = StandardMcpConfig.StandardMcpServer.builder()
            .type("stdio")
            .command("python")
            .args(java.util.Arrays.asList("-m", "weather_mcp"))
            .enabled(true)
            .timeout(30)
            .build();
        
        validServers = new HashMap<>();
        validServers.put("weather-service", validServer);
        
        // 创建标准配置
        standardConfig = StandardMcpConfig.builder()
            .mcpServers(validServers)
            .build();
        
        // 创建全局配置
        globalConfig = GlobalMcpConfig.builder()
            .lastModified(Instant.now().toEpochMilli())
            .mcpServers(validServers)
            .build();
        
        // 创建客户端消息
        clientMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .timestamp(Instant.now().toEpochMilli())
            .mcpServers(validServers)
            .action("ADD")
            .build();
    }
    
    @Test
    void testStandardToGlobal() {
        GlobalMcpConfig result = McpRedisUtils.standardToGlobal(standardConfig);
        assertNotNull(result);
        assertEquals(validServers, result.getMcpServers());
        assertTrue(result.isValid());
        
        // 测试null输入
        GlobalMcpConfig nullResult = McpRedisUtils.standardToGlobal(null);
        assertNotNull(nullResult);
        assertTrue(nullResult.getMcpServers().isEmpty());
    }
    
    @Test
    void testGlobalToStandard() {
        StandardMcpConfig result = McpRedisUtils.globalToStandard(globalConfig);
        assertNotNull(result);
        assertEquals(validServers, result.getMcpServers());
        
        // 测试null输入
        StandardMcpConfig nullResult = McpRedisUtils.globalToStandard(null);
        assertNotNull(nullResult);
        assertTrue(nullResult.getMcpServers().isEmpty());
    }
    
    @Test
    void testMessageToConfig() {
        GlobalMcpConfig result = McpRedisUtils.messageToConfig(clientMessage);
        assertNotNull(result);
        assertEquals(clientMessage.getTimestamp(), result.getLastModified());
        assertEquals(validServers, result.getMcpServers());
        
        // 测试null输入
        GlobalMcpConfig nullResult = McpRedisUtils.messageToConfig(null);
        assertNotNull(nullResult);
        assertTrue(nullResult.getMcpServers().isEmpty());
    }
    
    @Test
    void testValidateGlobalConfig() {
        // 测试有效配置
        McpRedisUtils.ValidationResult validResult = McpRedisUtils.validateGlobalConfig(globalConfig);
        assertTrue(validResult.isValid());
        assertTrue(validResult.getErrors().isEmpty());
        
        // 测试null配置
        McpRedisUtils.ValidationResult nullResult = McpRedisUtils.validateGlobalConfig(null);
        assertFalse(nullResult.isValid());
        assertFalse(nullResult.getErrors().isEmpty());
        
        // 测试无效配置
        GlobalMcpConfig invalidConfig = GlobalMcpConfig.builder()
            .mcpServers(validServers)
            .build();
        McpRedisUtils.ValidationResult invalidResult = McpRedisUtils.validateGlobalConfig(invalidConfig);
        assertFalse(invalidResult.isValid());
        assertTrue(invalidResult.getErrorMessage().contains("配置版本不能为空"));
    }
    
    @Test
    void testValidateClientMessage() {
        // 测试有效消息
        McpRedisUtils.ValidationResult validResult = McpRedisUtils.validateClientMessage(clientMessage);
        assertTrue(validResult.isValid());
        assertTrue(validResult.getErrors().isEmpty());
        
        // 测试null消息
        McpRedisUtils.ValidationResult nullResult = McpRedisUtils.validateClientMessage(null);
        assertFalse(nullResult.isValid());
        assertEquals("消息不能为空", nullResult.getErrorMessage());
        
        // 测试无效消息
        McpClientMessage invalidMessage = McpClientMessage.builder()
            .messageId("")
            .action("INVALID")
            .build();
        McpRedisUtils.ValidationResult invalidResult = McpRedisUtils.validateClientMessage(invalidMessage);
        assertFalse(invalidResult.isValid());
        assertTrue(invalidResult.getErrors().size() > 1);
    }
    

    @Test
    void testExtractClientNames() {
        Set<String> names = McpRedisUtils.extractClientNames(globalConfig);
        assertEquals(1, names.size());
        assertTrue(names.contains("weather-service"));
        
        // 测试null配置
        Set<String> emptyNames = McpRedisUtils.extractClientNames(null);
        assertTrue(emptyNames.isEmpty());
        
        // 测试空服务器配置
        GlobalMcpConfig emptyConfig = GlobalMcpConfig.builder()
            .mcpServers(null)
            .build();
        Set<String> nullServerNames = McpRedisUtils.extractClientNames(emptyConfig);
        assertTrue(nullServerNames.isEmpty());
    }
    
    @Test
    void testMergeConfigs() {
        // 创建另一个配置
        Map<String, StandardMcpConfig.StandardMcpServer> otherServers = new HashMap<>();
        otherServers.put("file-service", validServer);
        
        GlobalMcpConfig otherConfig = GlobalMcpConfig.builder()
            .lastModified(Instant.now().toEpochMilli() + 1000)
            .mcpServers(otherServers)
            .build();
        
        // 测试合并
        GlobalMcpConfig mergedConfig = McpRedisUtils.mergeConfigs(globalConfig, otherConfig);
        assertNotNull(mergedConfig);
        assertEquals(2, mergedConfig.getServerCount());
        assertTrue(mergedConfig.getServerNames().contains("weather-service"));
        assertTrue(mergedConfig.getServerNames().contains("file-service"));

        // 测试空输入
        GlobalMcpConfig emptyMerge = McpRedisUtils.mergeConfigs();
        assertNotNull(emptyMerge);
        assertTrue(emptyMerge.isEmpty());
        
        // 测试null输入
        GlobalMcpConfig nullMerge = McpRedisUtils.mergeConfigs(globalConfig, null);
        assertNotNull(nullMerge);
        assertEquals(1, nullMerge.getServerCount());
    }
    
    @Test
    void testGenerateMessageId() {
        String messageId1 = McpRedisUtils.generateMessageId();
        String messageId2 = McpRedisUtils.generateMessageId();
        
        assertNotNull(messageId1);
        assertNotNull(messageId2);
        assertNotEquals(messageId1, messageId2);
        
        // 验证UUID格式
        UUID.fromString(messageId1); // 如果不是有效UUID会抛出异常
        UUID.fromString(messageId2);
    }
    
    @Test
    void testFormatConfigForLogging() {
        String formatted = McpRedisUtils.formatConfigForLogging(globalConfig);
        assertNotNull(formatted);
        assertTrue(formatted.contains("GlobalMcpConfig"));
        assertTrue(formatted.contains("version='1.0'"));
        assertTrue(formatted.contains("serverCount=1"));
        assertTrue(formatted.contains("weather-service"));
        
        // 测试null配置
        String nullFormatted = McpRedisUtils.formatConfigForLogging(null);
        assertEquals("null", nullFormatted);
    }
    
    @Test
    void testFormatMessageForLogging() {
        String formatted = McpRedisUtils.formatMessageForLogging(clientMessage);
        assertNotNull(formatted);
        assertTrue(formatted.contains("McpClientMessage"));
        assertTrue(formatted.contains("action='ADD'"));
        assertTrue(formatted.contains("serverCount=1"));
        assertTrue(formatted.contains(clientMessage.getMessageId()));
        
        // 测试null消息
        String nullFormatted = McpRedisUtils.formatMessageForLogging(null);
        assertEquals("null", nullFormatted);
    }
    
    @Test
    void testValidationResultClass() {
        // 测试成功结果
        McpRedisUtils.ValidationResult success = McpRedisUtils.ValidationResult.success();
        assertTrue(success.isValid());
        assertTrue(success.getErrors().isEmpty());
        assertEquals("", success.getErrorMessage());
        
        // 测试失败结果（单个错误）
        McpRedisUtils.ValidationResult singleFailure = 
            McpRedisUtils.ValidationResult.failure("Test error");
        assertFalse(singleFailure.isValid());
        assertEquals(1, singleFailure.getErrors().size());
        assertEquals("Test error", singleFailure.getErrorMessage());
        
        // 测试失败结果（多个错误）
        java.util.List<String> errors = java.util.Arrays.asList("Error 1", "Error 2");
        McpRedisUtils.ValidationResult multipleFailure = 
            McpRedisUtils.ValidationResult.failure(errors);
        assertFalse(multipleFailure.isValid());
        assertEquals(2, multipleFailure.getErrors().size());
        assertEquals("Error 1; Error 2", multipleFailure.getErrorMessage());
    }
    
    @Test
    void testCompatibilityResultClass() {
        // 测试兼容结果
        McpRedisUtils.CompatibilityResult compatible = 
            McpRedisUtils.CompatibilityResult.compatible("Test message");
        assertTrue(compatible.isCompatible());
        assertTrue(compatible.getConflicts().isEmpty());
        assertEquals(1, compatible.getWarnings().size());
        
        // 测试不兼容结果
        java.util.List<String> conflicts = java.util.Arrays.asList("Conflict 1");
        java.util.List<String> warnings = java.util.Arrays.asList("Warning 1");
        McpRedisUtils.CompatibilityResult incompatible = 
            McpRedisUtils.CompatibilityResult.incompatible(conflicts, warnings);
        assertFalse(incompatible.isCompatible());
        assertEquals(1, incompatible.getConflicts().size());
        assertEquals(1, incompatible.getWarnings().size());
    }
}
