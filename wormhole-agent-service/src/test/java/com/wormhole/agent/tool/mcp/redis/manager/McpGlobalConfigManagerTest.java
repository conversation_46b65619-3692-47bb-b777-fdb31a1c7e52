package com.wormhole.agent.tool.mcp.redis.manager;

import com.wormhole.agent.tool.mcp.config.McpConfigAdapter;
import com.wormhole.agent.tool.mcp.config.McpProperties;
import com.wormhole.agent.tool.mcp.config.StandardMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.GlobalMcpConfig;
import com.wormhole.agent.tool.mcp.redis.config.McpClientMessage;
import com.wormhole.agent.tool.mcp.redis.config.McpRedisProperties;
import com.wormhole.agent.tool.mcp.service.McpServiceManager;
import com.wormhole.common.util.JacksonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import org.springframework.data.redis.core.ReactiveValueOperations;
import org.springframework.data.redis.listener.ReactiveRedisMessageListenerContainer;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * McpGlobalConfigManager单元测试
 * 测试MCP全局配置管理器的CRUD操作、配置优先级、初始化和消息变更通知等功能
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@ExtendWith(MockitoExtension.class)
class McpGlobalConfigManagerTest {

    @Mock
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    @Mock
    private ReactiveValueOperations<String, String> reactiveValueOperations;

    @Mock
    private McpRedisProperties mcpRedisProperties;

    @Mock
    private McpProperties mcpProperties;

    @Mock
    private McpConfigAdapter mcpConfigAdapter;


    @Mock
    private McpServiceManager mcpServiceManager;

    @Mock
    private ReactiveRedisMessageListenerContainer messageListenerContainer;

    @InjectMocks
    private McpGlobalConfigManager mcpGlobalConfigManager;
    
    private GlobalMcpConfig validConfig;
    private StandardMcpConfig.StandardMcpServer validServer;
    private StandardMcpConfig.StandardMcpServer sseServer;
    private Map<String, StandardMcpConfig.StandardMcpServer> validServers;
    private Map<String, StandardMcpConfig.StandardMcpServer> sseServers;
    private String testKey = "mcp:config:global";
    private String messageChannelKey = "mcp:client:msg";
    private McpClientMessage testAddMessage;
    
    @BeforeEach
    void setUp() {
        // 创建有效的服务器配置 - stdio类型
        validServer = StandardMcpConfig.StandardMcpServer.builder()
            .type("stdio")
            .command("python")
            .args(java.util.Arrays.asList("-m", "weather_mcp"))
            .enabled(true)
            .timeout(30)
            .build();

        validServers = new HashMap<>();
        validServers.put("weather-service", validServer);

        // 创建SSE类型的服务器配置（用于测试你提供的消息格式）
        sseServer = StandardMcpConfig.StandardMcpServer.builder()
            .type("sse")
            .url("https://mcp-51e45dbb-c92a-4569.api-inference.modelscope.net/sse")
            .enabled(true)
            .timeout(30)
            .build();

        sseServers = new HashMap<>();
        sseServers.put("fetch", sseServer);

        // 创建有效的全局配置
        validConfig = GlobalMcpConfig.builder()
            .lastModified(Instant.now().toEpochMilli())
            .mcpServers(validServers)
            .build();

        // 创建测试用的ADD消息（基于你提供的格式）
        testAddMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .timestamp(Instant.now().toEpochMilli())
            .mcpServers(sseServers)
            .action("ADD")
            .build();

        // 设置Mock行为
        when(mcpRedisProperties.isEnabled()).thenReturn(true);
        when(mcpRedisProperties.getGlobalConfigKey()).thenReturn(testKey);
        when(mcpRedisProperties.getClientMessageChannel()).thenReturn(messageChannelKey);
        when(reactiveStringRedisTemplate.opsForValue()).thenReturn(reactiveValueOperations);
    }
    
    @Test
    void testLoadGlobalConfig_RedisEnabled() {
        // 模拟从Redis成功读取配置
        String configJson = JacksonUtils.writeValueAsString(validConfig);
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.just(configJson));
        
        StepVerifier.create(mcpGlobalConfigManager.loadGlobalConfig())
            .expectNextMatches(config -> 
                config.getServerCount() == 1)
            .verifyComplete();
        
        verify(reactiveValueOperations).get(testKey);
    }
    
    @Test
    void testLoadGlobalConfig_RedisDisabled() {
        when(mcpRedisProperties.isEnabled()).thenReturn(false);
        
        StepVerifier.create(mcpGlobalConfigManager.loadGlobalConfig())
            .expectNextMatches(config -> 
                config.isEmpty())
            .verifyComplete();
        
        verify(reactiveValueOperations, never()).get(anyString());
    }
    
    @Test
    void testGetGlobalConfigFromRedis_Success() {
        String configJson = JacksonUtils.writeValueAsString(validConfig);
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.just(configJson));
        
        StepVerifier.create(mcpGlobalConfigManager.getGlobalConfigFromRedis())
            .expectNextMatches(config -> 
                config.getServerCount() == 1)
            .verifyComplete();
    }
    
    @Test
    void testGetGlobalConfigFromRedis_NotFound() {
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.empty());
        
        StepVerifier.create(mcpGlobalConfigManager.getGlobalConfigFromRedis())
            .verifyComplete();
    }
    
    @Test
    void testGetGlobalConfigFromRedis_InvalidJson() {
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.just("invalid json"));
        
        StepVerifier.create(mcpGlobalConfigManager.getGlobalConfigFromRedis())
            .verifyComplete();
    }
    
    @Test
    void testSaveGlobalConfig_Success() {
        when(reactiveValueOperations.set(eq(testKey), anyString())).thenReturn(Mono.just(true));
        
        StepVerifier.create(mcpGlobalConfigManager.saveGlobalConfig(validConfig))
            .expectNext(true)
            .verifyComplete();
        
        verify(reactiveValueOperations).set(eq(testKey), anyString());
    }
    
    @Test
    void testSaveGlobalConfig_NullConfig() {
        StepVerifier.create(mcpGlobalConfigManager.saveGlobalConfig(null))
            .expectNext(false)
            .verifyComplete();
        
        verify(reactiveValueOperations, never()).set(anyString(), anyString());
    }
    
    @Test
    void testSaveGlobalConfig_InvalidConfig() {
        GlobalMcpConfig invalidConfig = GlobalMcpConfig.builder()
            .mcpServers(java.util.Collections.emptyMap())
            .build();
        
        StepVerifier.create(mcpGlobalConfigManager.saveGlobalConfig(invalidConfig))
            .expectNext(false)
            .verifyComplete();
        
        verify(reactiveValueOperations, never()).set(anyString(), anyString());
    }
    
    @Test
    void testUpdateServerConfig_Success() {
        // 模拟从Redis读取现有配置
        String configJson = JacksonUtils.writeValueAsString(validConfig);
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.just(configJson));
        when(reactiveValueOperations.set(eq(testKey), anyString())).thenReturn(Mono.just(true));
        
        StandardMcpConfig.StandardMcpServer newServer = StandardMcpConfig.StandardMcpServer.builder()
            .type("sse")
            .url("http://localhost:8080/mcp")
            .enabled(true)
            .build();
        
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig("new-service", newServer))
            .expectNext(true)
            .verifyComplete();
        
        verify(reactiveValueOperations).get(testKey);
        verify(reactiveValueOperations).set(eq(testKey), anyString());
    }
    
    @Test
    void testUpdateServerConfig_InvalidServerName() {
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig("", validServer))
            .expectNext(false)
            .verifyComplete();
        
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig(null, validServer))
            .expectNext(false)
            .verifyComplete();
        
        verify(reactiveValueOperations, never()).get(anyString());
    }
    
    @Test
    void testUpdateServerConfig_InvalidServer() {
        StandardMcpConfig.StandardMcpServer invalidServer = StandardMcpConfig.StandardMcpServer.builder()
            .type("invalid")
            .build();
        
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig("test-server", invalidServer))
            .expectNext(false)
            .verifyComplete();
        
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig("test-server", null))
            .expectNext(false)
            .verifyComplete();
        
        verify(reactiveValueOperations, never()).get(anyString());
    }
    
    @Test
    void testRemoveServerConfig_Success() {
        // 模拟从Redis读取现有配置
        String configJson = JacksonUtils.writeValueAsString(validConfig);
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.just(configJson));
        when(reactiveValueOperations.set(eq(testKey), anyString())).thenReturn(Mono.just(true));
        
        StepVerifier.create(mcpGlobalConfigManager.removeServerConfig("weather-service"))
            .expectNext(true)
            .verifyComplete();
        
        verify(reactiveValueOperations).get(testKey);
        verify(reactiveValueOperations).set(eq(testKey), anyString());
    }
    
    @Test
    void testRemoveServerConfig_ServerNotFound() {
        // 模拟从Redis读取现有配置
        String configJson = JacksonUtils.writeValueAsString(validConfig);
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.just(configJson));
        
        StepVerifier.create(mcpGlobalConfigManager.removeServerConfig("non-existent-service"))
            .expectNext(false)
            .verifyComplete();
        
        verify(reactiveValueOperations).get(testKey);
        verify(reactiveValueOperations, never()).set(anyString(), anyString());
    }
    
    @Test
    void testRemoveServerConfig_ConfigNotFound() {
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.empty());
        
        StepVerifier.create(mcpGlobalConfigManager.removeServerConfig("weather-service"))
            .expectNext(false)
            .verifyComplete();
        
        verify(reactiveValueOperations).get(testKey);
        verify(reactiveValueOperations, never()).set(anyString(), anyString());
    }
    
    @Test
    void testHasGlobalConfig_Exists() {
        when(reactiveStringRedisTemplate.hasKey(testKey)).thenReturn(Mono.just(true));
        
        StepVerifier.create(mcpGlobalConfigManager.hasGlobalConfig())
            .expectNext(true)
            .verifyComplete();
        
        verify(reactiveStringRedisTemplate).hasKey(testKey);
    }
    
    @Test
    void testHasGlobalConfig_NotExists() {
        when(reactiveStringRedisTemplate.hasKey(testKey)).thenReturn(Mono.just(false));
        
        StepVerifier.create(mcpGlobalConfigManager.hasGlobalConfig())
            .expectNext(false)
            .verifyComplete();
        
        verify(reactiveStringRedisTemplate).hasKey(testKey);
    }
    
    @Test
    void testDeleteGlobalConfig_Success() {
        when(reactiveStringRedisTemplate.delete(testKey)).thenReturn(Mono.just(1L));
        
        StepVerifier.create(mcpGlobalConfigManager.deleteGlobalConfig())
            .expectNext(true)
            .verifyComplete();
        
        verify(reactiveStringRedisTemplate).delete(testKey);
    }
    
    @Test
    void testDeleteGlobalConfig_NotFound() {
        when(reactiveStringRedisTemplate.delete(testKey)).thenReturn(Mono.just(0L));
        
        StepVerifier.create(mcpGlobalConfigManager.deleteGlobalConfig())
            .expectNext(false)
            .verifyComplete();
        
        verify(reactiveStringRedisTemplate).delete(testKey);
    }
    
    @Test
    void testValidateConfig() {
        assertTrue(mcpGlobalConfigManager.validateConfig(validConfig));
        assertFalse(mcpGlobalConfigManager.validateConfig(null));

        GlobalMcpConfig invalidConfig = GlobalMcpConfig.builder()
            .mcpServers(java.util.Collections.emptyMap())
            .build();
        assertFalse(mcpGlobalConfigManager.validateConfig(invalidConfig));
    }

    // ==================== 新增测试方法：初始化和消息变更通知 ====================

    @Test
    void testInitialization_RedisEnabled() {
        // 模拟Redis启用状态
        when(mcpRedisProperties.isEnabled()).thenReturn(true);

        // 模拟从Redis加载配置
        String configJson = JacksonUtils.writeValueAsString(validConfig);
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.just(configJson));

        StepVerifier.create(mcpGlobalConfigManager.loadGlobalConfig())
            .expectNextMatches(config ->
                config.getServerCount() == 1 &&
                config.getMcpServers().containsKey("weather-service"))
            .verifyComplete();

        verify(reactiveValueOperations).get(testKey);
    }

    @Test
    void testInitialization_RedisDisabledFallbackToDefault() {
        // 模拟Redis禁用状态
        when(mcpRedisProperties.isEnabled()).thenReturn(false);

        StepVerifier.create(mcpGlobalConfigManager.loadGlobalConfig())
            .expectNextMatches(config ->
                config.isEmpty())
            .verifyComplete();

        // 验证没有访问Redis
        verify(reactiveValueOperations, never()).get(anyString());
    }

    @Test
    void testInitialization_JsonMigration() {
        // 模拟Redis启用但没有配置，需要从JSON迁移
        when(mcpRedisProperties.isEnabled()).thenReturn(true);
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.empty());

        // 模拟JSON文件配置
        when(mcpProperties.isEnabled()).thenReturn(true);
        when(mcpProperties.getClients()).thenReturn((List<McpProperties.McpClientConfig>) Collections.singletonMap("test-client",
            McpProperties.McpClientConfig.builder()
                .name("test-client")
                .transport("stdio")
                .command(Arrays.asList("python", "-m", "test"))
                .enabled(true)
                .build()));

        // 模拟配置转换
        StandardMcpConfig standardConfig = StandardMcpConfig.builder()
            .mcpServers(validServers)
            .build();
        when(mcpConfigAdapter.convertLangChain4jToStandard(any())).thenReturn(standardConfig);

        // 模拟保存成功
        when(reactiveValueOperations.set(eq(testKey), anyString())).thenReturn(Mono.just(true));

        StepVerifier.create(mcpGlobalConfigManager.loadGlobalConfig())
            .expectNextMatches(config ->
                config.getServerCount() == 1)
            .verifyComplete();

        verify(reactiveValueOperations).get(testKey);
        verify(mcpConfigAdapter).convertLangChain4jToStandard(any());
        verify(reactiveValueOperations).set(eq(testKey), anyString());
    }

    // ==================== 消息变更通知测试 ====================

    @Test
    void testMessageChangeNotification_AddOperation() {
        // 测试ADD操作的消息变更通知

        // 模拟消息监听器接收到ADD消息
        String messageJson = JacksonUtils.writeValueAsString(testAddMessage);

        // 验证消息格式正确
        assertTrue(testAddMessage.isValid());
        assertEquals(McpClientMessage.ActionType.ADD, testAddMessage.getActionType());
        assertEquals(1, testAddMessage.getServerCount());
        assertTrue(testAddMessage.getServerNames().contains("fetch"));

        // 验证消息可以正确序列化和反序列化
        McpClientMessage deserializedMessage = JacksonUtils.readValue(messageJson, McpClientMessage.class);
        assertEquals(testAddMessage.getAction(), deserializedMessage.getAction());
        assertEquals(testAddMessage.getServerCount(), deserializedMessage.getServerCount());
        assertTrue(deserializedMessage.isValid());
    }

    @Test
    void testMessageChangeNotification_SseServerConfig() {
        // 测试你提供的SSE服务器配置格式

        // 验证SSE服务器配置
        StandardMcpConfig.StandardMcpServer fetchServer = testAddMessage.getMcpServers().get("fetch");
        assertNotNull(fetchServer);
        assertEquals("sse", fetchServer.getType());
        assertEquals("https://mcp-51e45dbb-c92a-4569.api-inference.modelscope.net/sse", fetchServer.getUrl());
        assertTrue(fetchServer.getEnabled());
        assertTrue(fetchServer.isValid());
    }

    @Test
    void testMessageChangeNotification_ConfigUpdate() {
        // 测试通过ADD消息更新配置

        // 模拟当前没有配置
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.empty());

        // 模拟保存新配置成功
        when(reactiveValueOperations.set(eq(testKey), anyString())).thenReturn(Mono.just(true));

        // 使用ADD消息中的服务器配置更新全局配置
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig("fetch", sseServer))
            .expectNext(true)
            .verifyComplete();

        verify(reactiveValueOperations).get(testKey);
        verify(reactiveValueOperations).set(eq(testKey), anyString());
    }

    @Test
    void testMessageChangeNotification_BatchUpdate() {
        // 测试批量更新多个服务器配置

        // 创建包含多个服务器的消息
        Map<String, StandardMcpConfig.StandardMcpServer> multipleServers = new HashMap<>();
        multipleServers.put("fetch", sseServer);
        multipleServers.put("weather-service", validServer);

        McpClientMessage batchMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .timestamp(Instant.now().toEpochMilli())
            .mcpServers(multipleServers)
            .action("ADD")
            .build();

        // 验证批量消息
        assertTrue(batchMessage.isValid());
        assertEquals(2, batchMessage.getServerCount());
        assertTrue(batchMessage.getServerNames().contains("fetch"));
        assertTrue(batchMessage.getServerNames().contains("weather-service"));

        // 验证锁键名生成（批量操作）
        String lockKey = batchMessage.generateLockKey();
        assertEquals("mcp:add:batch", lockKey);
    }

    @Test
    void testMessageChangeNotification_SingleServerLockKey() {
        // 测试单个服务器操作的锁键名生成

        String lockKey = testAddMessage.generateLockKey();
        assertEquals("mcp:add:fetch", lockKey);

        // 测试其他操作类型
        McpClientMessage updateMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .mcpServers(sseServers)
            .action("UPDATE")
            .build();

        assertEquals("mcp:update:fetch", updateMessage.generateLockKey());

        McpClientMessage deleteMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .mcpServers(sseServers)
            .action("DELETE")
            .build();

        assertEquals("mcp:delete:fetch", deleteMessage.generateLockKey());
    }

    @Test
    void testMessageChangeNotification_ReloadOperation() {
        // 测试RELOAD操作（不需要mcpServers）

        McpClientMessage reloadMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .timestamp(Instant.now().toEpochMilli())
            .action("RELOAD")
            .build();

        // 验证RELOAD消息
        assertTrue(reloadMessage.isValid());
        assertEquals(McpClientMessage.ActionType.RELOAD, reloadMessage.getActionType());
        assertEquals(0, reloadMessage.getServerCount());
        assertTrue(reloadMessage.isEmpty());

        // 验证锁键名生成（RELOAD操作）
        String lockKey = reloadMessage.generateLockKey();
        assertEquals("mcp:reload:batch", lockKey);
    }

    @Test
    void testMessageChangeNotification_MessageConversion() {
        // 测试消息与GlobalMcpConfig之间的转换

        // 从消息转换为GlobalMcpConfig
        GlobalMcpConfig globalConfig = testAddMessage.toGlobalMcpConfig();
        assertNotNull(globalConfig);
        assertEquals(testAddMessage.getTimestamp(), globalConfig.getLastModified());
        assertEquals(testAddMessage.getMcpServers(), globalConfig.getMcpServers());

        // 从GlobalMcpConfig创建消息
        McpClientMessage convertedMessage = McpClientMessage.fromGlobalMcpConfig(
            globalConfig, McpClientMessage.ActionType.UPDATE);
        assertNotNull(convertedMessage);
        assertEquals("UPDATE", convertedMessage.getAction());
        assertEquals(globalConfig.getMcpServers(), convertedMessage.getMcpServers());
        assertTrue(convertedMessage.isValid());
    }

    @Test
    void testMessageChangeNotification_InvalidMessages() {
        // 测试无效消息的处理

        // 空消息ID
        McpClientMessage emptyMessageIdMessage = McpClientMessage.builder()
            .messageId("")
            .action("ADD")
            .mcpServers(sseServers)
            .build();
        assertFalse(emptyMessageIdMessage.isValid());

        // 无效操作类型
        McpClientMessage invalidActionMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .action("INVALID")
            .mcpServers(sseServers)
            .build();
        assertFalse(invalidActionMessage.isValid());

        // ADD操作缺少服务器配置
        McpClientMessage noServersMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .action("ADD")
            .build();
        assertFalse(noServersMessage.isValid());
    }

    @Test
    void testMessageChangeNotification_EnabledServersFilter() {
        // 测试启用服务器的过滤功能

        // 创建包含禁用服务器的配置
        StandardMcpConfig.StandardMcpServer disabledServer = StandardMcpConfig.StandardMcpServer.builder()
            .type("stdio")
            .command("python")
            .args(java.util.Arrays.asList("-m", "disabled_mcp"))
            .enabled(false)  // 禁用
            .timeout(30)
            .build();

        Map<String, StandardMcpConfig.StandardMcpServer> mixedServers = new HashMap<>();
        mixedServers.put("fetch", sseServer);  // 启用
        mixedServers.put("disabled-service", disabledServer);  // 禁用

        McpClientMessage mixedMessage = McpClientMessage.builder()
            .messageId(UUID.randomUUID().toString())
            .mcpServers(mixedServers)
            .action("ADD")
            .build();

        // 验证启用服务器过滤
        Map<String, StandardMcpConfig.StandardMcpServer> enabledServers = mixedMessage.getEnabledServers();
        assertEquals(1, enabledServers.size());
        assertTrue(enabledServers.containsKey("fetch"));
        assertFalse(enabledServers.containsKey("disabled-service"));
    }

    // ==================== Redis监听器集成测试 ====================

    @Test
    void testRedisListenerIntegration_MessagePublishing() {
        // 测试Redis消息发布和监听器集成

        // 模拟Redis消息发布
        String messageJson = JacksonUtils.writeValueAsString(testAddMessage);

        // 验证消息可以被正确发布到Redis频道
        assertNotNull(messageJson);
        assertTrue(messageJson.contains("\"action\":\"ADD\""));
        assertTrue(messageJson.contains("\"fetch\""));
        assertTrue(messageJson.contains("\"type\":\"sse\""));
        assertTrue(messageJson.contains("https://mcp-51e45dbb-c92a-4569.api-inference.modelscope.net/sse"));

        // 验证消息可以被正确解析
        McpClientMessage parsedMessage = JacksonUtils.readValue(messageJson, McpClientMessage.class);
        assertTrue(parsedMessage.isValid());
        assertEquals(testAddMessage.getAction(), parsedMessage.getAction());
        assertEquals(testAddMessage.getServerCount(), parsedMessage.getServerCount());
    }

    @Test
    void testRedisListenerIntegration_ConfigSynchronization() {
        // 测试配置同步功能

        // 模拟通过ADD消息触发配置更新
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.empty());
        when(reactiveValueOperations.set(eq(testKey), anyString())).thenReturn(Mono.just(true));

        // 执行配置更新
        StepVerifier.create(mcpGlobalConfigManager.updateServerConfig("fetch", sseServer))
            .expectNext(true)
            .verifyComplete();

        // 验证配置已保存到Redis
        verify(reactiveValueOperations).set(eq(testKey), anyString());

        // 验证可以重新加载配置
        String savedConfigJson = JacksonUtils.writeValueAsString(
            GlobalMcpConfig.builder()
                .lastModified(Instant.now().toEpochMilli())
                .mcpServers(sseServers)
                .build()
        );
        when(reactiveValueOperations.get(testKey)).thenReturn(Mono.just(savedConfigJson));

        StepVerifier.create(mcpGlobalConfigManager.loadGlobalConfig())
            .expectNextMatches(config ->
                config.getServerCount() == 1 &&
                config.getMcpServers().containsKey("fetch"))
            .verifyComplete();
    }
}
