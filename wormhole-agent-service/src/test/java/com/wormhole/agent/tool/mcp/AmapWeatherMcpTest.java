package com.wormhole.agent.tool.mcp;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.tool.core.model.ToolChainContext;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 高德地图MCP天气工具测试
 * 专门测试maps_weather工具的功能
 */
@Slf4j
class AmapWeatherMcpTest extends BaseTest {

    @Autowired
    private McpToolProvider mcpToolProvider;

    private static final String AMAP_CLIENT_NAME = "amap-maps";
    private static final String WEATHER_TOOL_NAME = "maps_weather";

    @Test
    void testAmapClientAvailability() {
        log.info("=== 测试高德地图MCP客户端可用性 ===");
        
        // 检查高德地图客户端是否可用
        boolean isAvailable = mcpToolProvider.isClientAvailable(AMAP_CLIENT_NAME);
        log.info("高德地图客户端 '{}' 可用性: {}", AMAP_CLIENT_NAME, isAvailable);
        
        if (!isAvailable) {
            log.warn("高德地图MCP客户端不可用，请检查配置");
            return; // 如果客户端不可用，跳过后续测试
        }
        
        // 获取高德地图客户端的预加载工具
        List<OpenAiTool> amapTools = mcpToolProvider.getPreloadedTools(AMAP_CLIENT_NAME);
        assertNotNull(amapTools, "高德地图工具列表不应该为null");
        
        log.info("高德地图客户端提供 {} 个工具", amapTools.size());
        
        // 打印所有可用工具
        log.info("高德地图可用工具列表:");
        for (int i = 0; i < amapTools.size(); i++) {
            OpenAiTool tool = amapTools.get(i);
            log.info("  {}. {} - {}", 
                    i + 1, 
                    tool.getFunction().getName(),
                    tool.getFunction().getDescription());
        }
        
        assertTrue(amapTools.size() > 0, "高德地图应该提供至少一个工具");
    }

    @Test
    void testWeatherToolAvailability() {
        log.info("=== 测试maps_weather工具可用性 ===");
        
        if (!mcpToolProvider.isClientAvailable(AMAP_CLIENT_NAME)) {
            log.warn("高德地图MCP客户端不可用，跳过天气工具测试");
            return;
        }
        
        List<OpenAiTool> amapTools = mcpToolProvider.getPreloadedTools(AMAP_CLIENT_NAME);
        
        // 查找maps_weather工具
        OpenAiTool weatherTool = amapTools.stream()
                .filter(tool -> WEATHER_TOOL_NAME.equals(tool.getFunction().getName()))
                .findFirst()
                .orElse(null);
        
        if (weatherTool == null) {
            log.warn("未找到maps_weather工具，可用工具:");
            amapTools.forEach(tool -> 
                log.warn("  - {}", tool.getFunction().getName()));
            fail("maps_weather工具不可用");
        }
        
        log.info("✅ 找到maps_weather工具");
        log.info("工具描述: {}", weatherTool.getFunction().getDescription());
        
        // 验证工具参数
        if (weatherTool.getFunction().getParameters() != null && 
            weatherTool.getFunction().getParameters().getProperties() != null) {
            
            var properties = weatherTool.getFunction().getParameters().getProperties();
            log.info("工具参数数量: {}", properties.size());
            
            properties.forEach((paramName, paramInfo) -> {
                log.info("  参数: {} | 类型: {} | 描述: {}", 
                        paramName, 
                        paramInfo.getType(), 
                        paramInfo.getDescription());
            });
            
            // 验证必需的city参数
            assertTrue(properties.containsKey("city"), "maps_weather工具应该有city参数");
        }
    }

    @Test
    void testWeatherToolExecution() {
        log.info("=== 测试maps_weather工具执行 ===");
        
        if (!mcpToolProvider.isClientAvailable(AMAP_CLIENT_NAME)) {
            log.warn("高德地图MCP客户端不可用，跳过天气工具执行测试");
            return;
        }
        
        // 创建工具链上下文
        ToolChainContext context = new ToolChainContext();
        
        // 预先建立工具与客户端的映射关系
        context.getMcpToolClientMap().put(WEATHER_TOOL_NAME, AMAP_CLIENT_NAME);
        
        // 测试不同城市的天气查询
        String[] testCities = {"北京", "上海", "深圳", "杭州", "成都"};
        
        for (String city : testCities) {
            log.info("--- 查询 {} 的天气 ---", city);
            
            try {
                // 准备工具参数
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("city", city);
                
                log.info("调用参数: {}", arguments);
                
                // 执行工具
                String result = mcpToolProvider.executeMcpTool(WEATHER_TOOL_NAME, arguments, context)
                        .block(); // 阻塞等待结果
                
                assertNotNull(result, city + "的天气查询结果不应该为null");
                assertFalse(result.trim().isEmpty(), city + "的天气查询结果不应该为空");
                
                log.info("✅ {} 天气查询成功", city);
                log.info("天气信息: {}", result);
                
                // 验证结果包含基本天气信息
                assertTrue(result.contains("天气") || result.contains("温度") || result.contains("weather") || 
                          result.contains("temperature") || result.contains("°"), 
                          "结果应该包含天气相关信息");
                
            } catch (Exception e) {
                log.error("❌ {} 天气查询失败: {}", city, e.getMessage(), e);
                // 不让单个城市的失败影响整个测试
            }
            
            // 添加延迟避免API限流
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    @Test
    void testWeatherToolWithAdcode() {
        log.info("=== 测试maps_weather工具使用adcode ===");
        
        if (!mcpToolProvider.isClientAvailable(AMAP_CLIENT_NAME)) {
            log.warn("高德地图MCP客户端不可用，跳过adcode测试");
            return;
        }
        
        ToolChainContext context = new ToolChainContext();
        context.getMcpToolClientMap().put(WEATHER_TOOL_NAME, AMAP_CLIENT_NAME);
        
        // 测试使用adcode查询天气
        Map<String, String> testAdcodes = new HashMap<>();
        testAdcodes.put("110000", "北京市");
        testAdcodes.put("310000", "上海市");
        testAdcodes.put("440300", "深圳市");
        testAdcodes.put("330100", "杭州市");
        
        for (Map.Entry<String, String> entry : testAdcodes.entrySet()) {
            String adcode = entry.getKey();
            String cityName = entry.getValue();
            
            log.info("--- 使用adcode {} 查询 {} 的天气 ---", adcode, cityName);
            
            try {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("city", adcode);
                
                String result = mcpToolProvider.executeMcpTool(WEATHER_TOOL_NAME, arguments, context)
                        .block();
                
                assertNotNull(result, cityName + "的天气查询结果不应该为null");
                log.info("✅ {} (adcode: {}) 天气查询成功", cityName, adcode);
                log.info("天气信息: {}", result);
                
            } catch (Exception e) {
                log.error("❌ {} (adcode: {}) 天气查询失败: {}", cityName, adcode, e.getMessage());
            }
            
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    @Test
    void testWeatherToolErrorHandling() {
        log.info("=== 测试maps_weather工具错误处理 ===");
        
        if (!mcpToolProvider.isClientAvailable(AMAP_CLIENT_NAME)) {
            log.warn("高德地图MCP客户端不可用，跳过错误处理测试");
            return;
        }
        
        ToolChainContext context = new ToolChainContext();
        context.getMcpToolClientMap().put(WEATHER_TOOL_NAME, AMAP_CLIENT_NAME);
        
        // 测试无效城市名称
        String[] invalidCities = {"不存在的城市", "", "123456789", "invalid_city"};
        
        for (String invalidCity : invalidCities) {
            log.info("--- 测试无效城市: '{}' ---", invalidCity);
            
            try {
                Map<String, Object> arguments = new HashMap<>();
                arguments.put("city", invalidCity);
                
                String result = mcpToolProvider.executeMcpTool(WEATHER_TOOL_NAME, arguments, context)
                        .block();
                
                log.info("无效城市 '{}' 的查询结果: {}", invalidCity, result);
                
                // 即使是无效城市，也应该有某种响应（可能是错误信息）
                assertNotNull(result, "即使是无效城市，也应该有响应");
                
            } catch (Exception e) {
                log.info("无效城市 '{}' 查询异常（预期行为）: {}", invalidCity, e.getMessage());
                // 这是预期的行为，不算测试失败
            }
        }
        
        // 测试缺少参数
        log.info("--- 测试缺少city参数 ---");
        try {
            Map<String, Object> emptyArguments = new HashMap<>();
            
            String result = mcpToolProvider.executeMcpTool(WEATHER_TOOL_NAME, emptyArguments, context)
                    .block();
            
            log.info("缺少参数的查询结果: {}", result);
            
        } catch (Exception e) {
            log.info("缺少参数查询异常（预期行为）: {}", e.getMessage());
        }
    }

    @Test
    void testMcpIntegrationStatus() {
        log.info("=== 测试MCP集成状态 ===");
        
        McpToolProvider.McpStatus status = mcpToolProvider.getStatus();
        assertNotNull(status, "MCP状态不应该为null");
        
        log.info("MCP集成状态:");
        log.info("  启用状态: {}", status.isEnabled());
        log.info("  总客户端数: {}", status.getTotalClients());
        log.info("  总工具数: {}", status.getTotalTools());
        log.info("  可用客户端: {}", status.getAvailableClients());
        
        // 检查高德地图客户端状态
        if (status.getAvailableClients().contains(AMAP_CLIENT_NAME)) {
            Integer amapToolCount = status.getClientToolCounts().get(AMAP_CLIENT_NAME);
            log.info("  高德地图工具数: {}", amapToolCount);
            assertTrue(amapToolCount != null && amapToolCount > 0, 
                    "高德地图应该提供至少一个工具");
        } else {
            log.warn("高德地图客户端不在可用客户端列表中");
        }
    }
}
