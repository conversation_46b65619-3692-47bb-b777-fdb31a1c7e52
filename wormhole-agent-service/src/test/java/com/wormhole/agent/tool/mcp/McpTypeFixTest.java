package com.wormhole.agent.tool.mcp;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.model.openai.OpenAiFunction;
import com.wormhole.agent.model.openai.OpenAiTool;
import com.wormhole.agent.core.constant.AiConstant;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试类型转换修复
 */
@Slf4j
class McpTypeFixTest extends BaseTest {

    @Test
    void testOpenAiToolParameterTypes() {
        log.info("=== 测试OpenAiTool参数类型 ===");
        
        // 创建测试用的PropertiesInfo
        Map<String, OpenAiFunction.PropertiesInfo> properties = new HashMap<>();
        
        OpenAiFunction.PropertiesInfo pathParam = OpenAiFunction.PropertiesInfo.builder()
                .type("string")
                .description("文件路径参数")
                .build();
        
        OpenAiFunction.PropertiesInfo countParam = OpenAiFunction.PropertiesInfo.builder()
                .type("integer")
                .description("数量参数")
                .build();
        
        properties.put("path", pathParam);
        properties.put("count", countParam);
        
        // 创建OpenAiParameters
        OpenAiFunction.OpenAiParameters parameters = OpenAiFunction.OpenAiParameters.builder()
                .type("object")
                .properties(properties)
                .build();
        
        // 创建OpenAiFunction
        OpenAiFunction function = OpenAiFunction.builder()
                .name("test-function")
                .description("测试函数")
                .parameters(parameters)
                .build();
        
        // 创建OpenAiTool
        OpenAiTool tool = OpenAiTool.builder()
                .type(AiConstant.FUNCTION)
                .function(function)
                .build();
        
        // 验证类型
        assertNotNull(tool.getFunction());
        assertNotNull(tool.getFunction().getParameters());
        assertNotNull(tool.getFunction().getParameters().getProperties());
        
        Map<String, OpenAiFunction.PropertiesInfo> toolProperties = tool.getFunction().getParameters().getProperties();
        assertEquals(2, toolProperties.size());
        
        // 验证参数类型
        OpenAiFunction.PropertiesInfo pathProperty = toolProperties.get("path");
        assertNotNull(pathProperty);
        assertEquals("string", pathProperty.getType());
        assertEquals("文件路径参数", pathProperty.getDescription());
        
        OpenAiFunction.PropertiesInfo countProperty = toolProperties.get("count");
        assertNotNull(countProperty);
        assertEquals("integer", countProperty.getType());
        assertEquals("数量参数", countProperty.getDescription());
        
        log.info("✅ OpenAiTool参数类型测试通过");
        
        // 测试遍历参数
        log.info("参数列表:");
        for (Map.Entry<String, OpenAiFunction.PropertiesInfo> entry : toolProperties.entrySet()) {
            String paramName = entry.getKey();
            OpenAiFunction.PropertiesInfo paramInfo = entry.getValue();
            
            log.info("  参数: {} | 类型: {} | 描述: {}", 
                    paramName, 
                    paramInfo.getType(), 
                    paramInfo.getDescription());
        }
        
        log.info("✅ 参数遍历测试通过");
    }
    
    @Test
    void testParameterTypeExtraction() {
        log.info("=== 测试参数类型提取 ===");
        
        // 创建McpIntegrationTest实例来测试私有方法
        McpIntegrationTest mcpTest = new McpIntegrationTest();
        
        // 通过反射调用私有方法进行测试
        try {
            var extractMethod = McpIntegrationTest.class.getDeclaredMethod("extractParameterType", OpenAiFunction.PropertiesInfo.class);
            extractMethod.setAccessible(true);
            
            // 测试正常情况
            OpenAiFunction.PropertiesInfo normalParam = OpenAiFunction.PropertiesInfo.builder()
                    .type("string")
                    .description("这是一个测试参数")
                    .build();
            
            String result1 = (String) extractMethod.invoke(mcpTest, normalParam);
            assertEquals("string (这是一个测试参数)", result1);
            log.info("正常参数提取结果: {}", result1);
            
            // 测试长描述
            OpenAiFunction.PropertiesInfo longDescParam = OpenAiFunction.PropertiesInfo.builder()
                    .type("integer")
                    .description("这是一个非常长的描述，用来测试描述截断功能，应该会被截断到50个字符以内")
                    .build();
            
            String result2 = (String) extractMethod.invoke(mcpTest, longDescParam);
            assertTrue(result2.contains("..."));
            log.info("长描述参数提取结果: {}", result2);
            
            // 测试null情况
            String result3 = (String) extractMethod.invoke(mcpTest, (OpenAiFunction.PropertiesInfo) null);
            assertEquals("unknown", result3);
            log.info("null参数提取结果: {}", result3);
            
            log.info("✅ 参数类型提取测试通过");
            
        } catch (Exception e) {
            log.error("反射调用失败", e);
            fail("参数类型提取测试失败: " + e.getMessage());
        }
    }
}
