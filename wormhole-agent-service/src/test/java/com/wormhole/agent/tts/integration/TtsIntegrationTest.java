package com.wormhole.agent.tts.integration;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.response.TtsResponse;
import com.wormhole.agent.tts.service.TtsFacadeService;
import com.wormhole.agent.tts.service.TtsManager;
import com.wormhole.agent.tts.service.TtsResponseService;
import com.wormhole.common.result.Result;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * @date 2025-08-22
 * @description TTS集成测试类
 */
@SpringBootTest
@ActiveProfiles("test") // 使用测试配置文件
class TtsIntegrationTest extends BaseTest {

    @Resource
    private TtsFacadeService ttsFacadeService;

    @Resource
    private TtsManager ttsManager;

    @Resource
    private TtsResponseService ttsResponseService;

    private TtsRequest testRequest;

    @BeforeEach
    void setUp() {
        testRequest = TtsRequest.builder()
                .text("这是一个集成测试文本")
                .voice("zh_female_xiaoxin_moon_bigtts")
                .format("mp3")
                .speed(1.0)
                .emotion("normal")
                .language("zh")
                .build();
    }

    @Test
    @DisplayName("集成测试：完整的TTS合成流程")
    void testCompleteTtsFlow() {
        // 测试获取可用提供商
        Mono<Result<List<String>>> providersResult = ttsFacadeService.getAvailableProviders();
        
        StepVerifier.create(providersResult)
                .expectNextMatches(result -> {
                    assertTrue(result.isSuccess());
                    assertNotNull(result.getData());
                    assertFalse(result.getData().isEmpty());
                    return true;
                })
                .verifyComplete();

        // 测试默认提供商合成
        Mono<TtsResponse> synthesisResult = ttsFacadeService.synthesize(testRequest);
        
        StepVerifier.create(synthesisResult)
                .expectNextMatches(response -> {
                    // 根据实际配置，可能成功或失败
                    assertNotNull(response);
                    assertNotNull(response.getMessage());
                    return true;
                })
                .verifyComplete();
    }

    @Test
    @DisplayName("集成测试：TtsManager服务发现")
    void testTtsManagerServiceDiscovery() {
        List<String> availableProviders = ttsManager.getAvailableProviders();
        
        assertNotNull(availableProviders);
        // 至少应该有豆包提供商（如果配置正确）
        // 由于测试环境可能没有正确的API密钥，这里主要测试结构
    }

    @Test
    @DisplayName("集成测试：错误处理")
    void testErrorHandling() {
        TtsRequest invalidRequest = TtsRequest.builder()
                .text("") // 空文本
                .build();

        Mono<TtsResponse> result = ttsFacadeService.synthesize(invalidRequest);
        
        StepVerifier.create(result)
                .expectErrorMatches(throwable -> 
                    throwable.getMessage().contains("文本内容不能为空"))
                .verify();
    }

    @Test
    @DisplayName("集成测试：长文本处理")
    void testLongTextHandling() {
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 250; i++) { // 250字符，不超过300限制
            longText.append("测");
        }
        
        TtsRequest longTextRequest = TtsRequest.builder()
                .text(longText.toString())
                .format("mp3")
                .build();

        Mono<TtsResponse> result = ttsFacadeService.synthesize(longTextRequest);
        
        StepVerifier.create(result)
                .expectNextMatches(response -> {
                    assertNotNull(response);
                    return true;
                })
                .verifyComplete();
    }

    @Test
    @DisplayName("集成测试：多种音频格式")
    void testMultipleAudioFormats() {
        String[] formats = {"mp3", "wav", "pcm"};
        
        for (String format : formats) {
            TtsRequest formatRequest = TtsRequest.builder()
                    .text("测试" + format + "格式")
                    .format(format)
                    .build();

            Mono<TtsResponse> result = ttsFacadeService.synthesize(formatRequest);
            
            StepVerifier.create(result)
                    .expectNextMatches(response -> {
                        assertNotNull(response);
                        return true;
                    })
                    .verifyComplete();
        }
    }

    @Test
    @DisplayName("集成测试：并发请求处理")
    void testConcurrentRequests() {
        int concurrentCount = 5;
        Mono<TtsResponse>[] requests = new Mono[concurrentCount];
        
        for (int i = 0; i < concurrentCount; i++) {
            TtsRequest request = TtsRequest.builder()
                    .text("并发测试文本" + i)
                    .format("mp3")
                    .build();
            requests[i] = ttsFacadeService.synthesize(request);
        }

        StepVerifier.create(Mono.when(requests))
                .expectComplete()
                .verify();
    }

    @Test
    @DisplayName("集成测试：响应服务创建音频响应")
    void testResponseServiceAudioCreation() {
        Mono<org.springframework.http.ResponseEntity<byte[]>> result = 
                ttsResponseService.createAudioResponse(testRequest);
        
        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> {
                    assertNotNull(responseEntity);
                    // 响应状态可能是200（成功）或400（失败，如果没有正确配置API）
                    assertTrue(
                        responseEntity.getStatusCode().value() == 200 || 
                        responseEntity.getStatusCode().value() == 400 ||
                        responseEntity.getStatusCode().value() == 500
                    );
                    return true;
                })
                .verifyComplete();
    }

    @Test
    @DisplayName("集成测试：响应服务创建流式响应")
    void testResponseServiceStreamCreation() {
        Mono<org.springframework.http.ResponseEntity<reactor.core.publisher.Flux<org.springframework.core.io.buffer.DataBuffer>>> result = 
                ttsResponseService.createStreamResponse(testRequest);
        
        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> {
                    assertNotNull(responseEntity);
                    // 响应状态可能是200（成功）或400/500（失败）
                    assertTrue(
                        responseEntity.getStatusCode().value() == 200 || 
                        responseEntity.getStatusCode().value() == 400 ||
                        responseEntity.getStatusCode().value() == 500
                    );
                    return true;
                })
                .verifyComplete();
    }

    @Test
    @DisplayName("集成测试：不同语言支持")
    void testMultipleLanguages() {
        String[] languages = {"zh", "en", "ja"};
        String[] texts = {"你好世界", "Hello World", "こんにちは世界"};
        
        for (int i = 0; i < languages.length; i++) {
            TtsRequest langRequest = TtsRequest.builder()
                    .text(texts[i])
                    .language(languages[i])
                    .format("mp3")
                    .build();

            Mono<TtsResponse> result = ttsFacadeService.synthesize(langRequest);
            
            StepVerifier.create(result)
                    .expectNextMatches(response -> {
                        assertNotNull(response);
                        return true;
                    })
                    .verifyComplete();
        }
    }
}