package com.wormhole.agent.tts.validation;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.client.chat.params.TtsRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

/**
 * <AUTHOR>
 * @date 2025-08-22
 * @description TTS请求验证器测试类
 */
class TtsRequestValidatorTest extends BaseTest {

    private TtsRequestValidator validator;

    @BeforeEach
    void setUp() {
        validator = new TtsRequestValidator();
    }

    @Test
    @DisplayName("测试有效请求验证通过")
    void testValidRequest() {
        TtsRequest validRequest = TtsRequest.builder()
                .text("这是一个有效的测试文本")
                .voice("zh_female_xiaoxin_moon_bigtts")
                .format("mp3")
                .build();

        assertDoesNotThrow(() -> validator.validateRequest(validRequest));
    }

    @Test
    @DisplayName("测试空请求验证失败")
    void testNullRequest() {
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class, 
                () -> validator.validateRequest(null)
        );
        
        assertEquals("请求不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试空文本验证失败")
    void testEmptyText() {
        TtsRequest emptyTextRequest = TtsRequest.builder()
                .text("")
                .build();

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class, 
                () -> validator.validateRequest(emptyTextRequest)
        );
        
        assertEquals("文本内容不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试空白文本验证失败")
    void testBlankText() {
        TtsRequest blankTextRequest = TtsRequest.builder()
                .text("   ")
                .build();

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class, 
                () -> validator.validateRequest(blankTextRequest)
        );
        
        assertEquals("文本内容不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试文本长度超限验证失败")
    void testTextTooLong() {
        StringBuilder longText = new StringBuilder();
        for (int i = 0; i < 350; i++) { // 超过300字符限制
            longText.append("a");
        }
        
        TtsRequest longTextRequest = TtsRequest.builder()
                .text(longText.toString())
                .build();

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class, 
                () -> validator.validateRequest(longTextRequest)
        );
        
        assertEquals("文本长度不能超过300字符", exception.getMessage());
    }

    @Test
    @DisplayName("测试边界值文本长度验证通过")
    void testBoundaryTextLength() {
        StringBuilder boundaryText = new StringBuilder();
        for (int i = 0; i < 300; i++) { // 恰好300字符
            boundaryText.append("a");
        }
        
        TtsRequest boundaryRequest = TtsRequest.builder()
                .text(boundaryText.toString())
                .build();

        assertDoesNotThrow(() -> validator.validateRequest(boundaryRequest));
    }

    @Test
    @DisplayName("测试最小有效文本验证通过")
    void testMinimumValidText() {
        TtsRequest minRequest = TtsRequest.builder()
                .text("a") // 最小有效文本
                .build();

        assertDoesNotThrow(() -> validator.validateRequest(minRequest));
    }

    @Test
    @DisplayName("测试中文文本验证通过")
    void testChineseText() {
        TtsRequest chineseRequest = TtsRequest.builder()
                .text("这是一段中文测试文本，包含标点符号！")
                .build();

        assertDoesNotThrow(() -> validator.validateRequest(chineseRequest));
    }

    @Test
    @DisplayName("测试包含特殊字符的文本验证通过")
    void testSpecialCharactersText() {
        TtsRequest specialCharsRequest = TtsRequest.builder()
                .text("Hello, World! @#$%^&*()_+-={}[]|\\:;\"'<>?,./ 你好世界")
                .build();

        assertDoesNotThrow(() -> validator.validateRequest(specialCharsRequest));
    }

    @Test
    @DisplayName("测试数字文本验证通过")
    void testNumericText() {
        TtsRequest numericRequest = TtsRequest.builder()
                .text("12345678901234567890")
                .build();

        assertDoesNotThrow(() -> validator.validateRequest(numericRequest));
    }

    @Test
    @DisplayName("测试多行文本验证")
    void testMultilineText() {
        TtsRequest multilineRequest = TtsRequest.builder()
                .text("第一行文本\n第二行文本\r\n第三行文本")
                .build();

        assertDoesNotThrow(() -> validator.validateRequest(multilineRequest));
    }

    @Test
    @DisplayName("测试只包含换行符的文本验证失败")
    void testOnlyNewlineText() {
        TtsRequest newlineRequest = TtsRequest.builder()
                .text("\n\r\n\r")
                .build();

        // 因为StringUtils.hasText()会认为只包含空白字符的字符串为空
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class, 
                () -> validator.validateRequest(newlineRequest)
        );
        
        assertEquals("文本内容不能为空", exception.getMessage());
    }

    @Test
    @DisplayName("测试Tab字符文本验证失败")
    void testTabOnlyText() {
        TtsRequest tabRequest = TtsRequest.builder()
                .text("\t\t\t")
                .build();

        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class, 
                () -> validator.validateRequest(tabRequest)
        );
        
        assertEquals("文本内容不能为空", exception.getMessage());
    }
}