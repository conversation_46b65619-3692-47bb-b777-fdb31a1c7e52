package com.wormhole.agent.tts.service;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.response.TtsResponse;
import com.wormhole.common.result.Result;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025-08-22
 * @description TTS门面服务测试类
 */
class TtsFacadeServiceTest extends BaseTest {

    @Resource
    private TtsManager ttsManager;

    @Resource
    private TtsFacadeService ttsFacadeService;

    @Test
    @DisplayName("测试默认提供商合成")
    void testSynthesize() {
        TtsRequest validRequest = TtsRequest.builder()
                .text("谢康晨好帅啊")
                .voice("BV001_streaming")
                .format("mp3")
                .speed(1.0)
                .build();

        // when(ttsManager.textToSpeech(validRequest).doOnNext(r -> System.out.println("1. TTS合成结果: " + JacksonUtils.writeValueAsString(r))));

        Mono<TtsResponse> result = ttsFacadeService.synthesize(validRequest);

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    @DisplayName("测试指定提供商合成")
    void testSynthesizeWithProvider() {
        String provider = "doubao";

        TtsRequest validRequest = TtsRequest.builder()
                .text("测试文本")
                .voice("zh_female_xiaoxin_moon_bigtts")
                .format("mp3")
                .speed(1.0)
                .build();

        when(ttsManager.textToSpeech(provider, validRequest).doOnNext(r -> System.out.println("1. TTS合成结果: " + JacksonUtils.writeValueAsString(r))));

        Mono<TtsResponse> result = ttsFacadeService.synthesizeWithProvider(provider, validRequest)
                .doOnNext(r -> System.out.println("2. TTS合成结果: " + JacksonUtils.writeValueAsString(r)));

        StepVerifier.create(result)
                .verifyComplete();
    }

    @Test
    @DisplayName("测试获取可用提供商")
    void testGetAvailableProviders() {
        List<String> providers = Arrays.asList("doubao", "azure");
        when(ttsManager.getAvailableProviders()).thenReturn(providers);

        Mono<Result<List<String>>> result = ttsFacadeService.getAvailableProviders();

        StepVerifier.create(result)
                .expectNextMatches(r -> 
                    r.isSuccess() && 
                    r.getData().contains("doubao") && 
                    r.getData().contains("azure"))
                .verifyComplete();
    }


    @Test
    @DisplayName("测试Null请求处理")
    void testNullRequest() {
        Mono<TtsResponse> result = ttsFacadeService.synthesize(null);

        StepVerifier.create(result)
                .expectErrorMatches(throwable -> 
                    throwable instanceof NullPointerException)
                .verify();
    }
}