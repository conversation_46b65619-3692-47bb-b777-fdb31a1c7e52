package com.wormhole.agent.tts.service;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.response.TtsResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2025-08-22
 * @description TTS响应服务测试类
 */
class TtsResponseServiceTest extends BaseTest {

    private TtsResponseService ttsResponseService;
    private TtsManager mockTtsManager;

    private TtsRequest validRequest;
    private TtsResponse successResponse;
    private TtsResponse failureResponse;

    @BeforeEach
    void setUp() {
        ttsResponseService = new TtsResponseService();
        mockTtsManager = mock(TtsManager.class);
        
        // 使用反射注入mock对象
        ReflectionTestUtils.setField(ttsResponseService, "ttsManager", mockTtsManager);

        validRequest = TtsRequest.builder()
                .text("测试文本")
                .format("mp3")
                .build();

        successResponse = TtsResponse.builder()
                .success(true)
                .message("TTS转换成功")
                .audioData("test audio data".getBytes())
                .audioFormat("mp3")
                .requestId("test-123")
                .build();

        failureResponse = TtsResponse.builder()
                .success(false)
                .message("TTS转换失败")
                .build();
    }

    @Test
    @DisplayName("测试创建字节数组响应 - 成功")
    void testCreateAudioResponseSuccess() {
        when(mockTtsManager.textToSpeech(any(TtsRequest.class)))
                .thenReturn(Mono.just(successResponse));

        Mono<ResponseEntity<byte[]>> result = ttsResponseService.createAudioResponse(validRequest);

        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> {
                    assertEquals(200, responseEntity.getStatusCode().value());
                    assertEquals("audio/mpeg", responseEntity.getHeaders().getContentType().toString());
                    assertNotNull(responseEntity.getBody());
                    return true;
                })
                .verifyComplete();
    }

    @Test
    @DisplayName("测试创建字节数组响应 - 失败")
    void testCreateAudioResponseFailure() {
        when(mockTtsManager.textToSpeech(any(TtsRequest.class)))
                .thenReturn(Mono.just(failureResponse));

        Mono<ResponseEntity<byte[]>> result = ttsResponseService.createAudioResponse(validRequest);

        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> 
                    responseEntity.getStatusCode().value() == 400)
                .verifyComplete();
    }

    @Test
    @DisplayName("测试创建字节数组响应 - 指定提供商")
    void testCreateAudioResponseWithProvider() {
        String provider = "doubao";
        when(mockTtsManager.textToSpeech(eq(provider), any(TtsRequest.class)))
                .thenReturn(Mono.just(successResponse));

        Mono<ResponseEntity<byte[]>> result = ttsResponseService.createAudioResponse(provider, validRequest);

        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> 
                    responseEntity.getStatusCode().value() == 200)
                .verifyComplete();
    }

    @Test
    @DisplayName("测试创建流式响应 - 成功")
    void testCreateStreamResponseSuccess() {
        when(mockTtsManager.textToSpeech(any(TtsRequest.class)))
                .thenReturn(Mono.just(successResponse));

        Mono<ResponseEntity<Flux<DataBuffer>>> result = ttsResponseService.createStreamResponse(validRequest);

        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> {
                    assertEquals(200, responseEntity.getStatusCode().value());
                    assertEquals("audio/mpeg", responseEntity.getHeaders().getContentType().toString());
                    assertNotNull(responseEntity.getBody());
                    return true;
                })
                .verifyComplete();
    }

    @Test
    @DisplayName("测试创建流式响应 - 失败")
    void testCreateStreamResponseFailure() {
        when(mockTtsManager.textToSpeech(any(TtsRequest.class)))
                .thenReturn(Mono.just(failureResponse));

        Mono<ResponseEntity<Flux<DataBuffer>>> result = ttsResponseService.createStreamResponse(validRequest);

        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> 
                    responseEntity.getStatusCode().value() == 400)
                .verifyComplete();
    }

    @Test
    @DisplayName("测试创建流式响应 - 指定提供商")
    void testCreateStreamResponseWithProvider() {
        String provider = "doubao";
        when(mockTtsManager.textToSpeech(eq(provider), any(TtsRequest.class)))
                .thenReturn(Mono.just(successResponse));

        Mono<ResponseEntity<Flux<DataBuffer>>> result = ttsResponseService.createStreamResponse(provider, validRequest);

        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> 
                    responseEntity.getStatusCode().value() == 200)
                .verifyComplete();
    }

    @Test
    @DisplayName("测试不同音频格式的媒体类型")
    void testDifferentAudioFormats() {
        String[] formats = {"mp3", "wav", "pcm", "ogg"};
        String[] expectedTypes = {"audio/mpeg", "audio/wav", "audio/pcm", "audio/ogg"};

        for (int i = 0; i < formats.length; i++) {
            TtsResponse response = TtsResponse.builder()
                    .success(true)
                    .audioData("test data".getBytes())
                    .audioFormat(formats[i])
                    .build();

            when(mockTtsManager.textToSpeech(any(TtsRequest.class)))
                    .thenReturn(Mono.just(response));

            Mono<ResponseEntity<byte[]>> result = ttsResponseService.createAudioResponse(validRequest);
            
            final int index = i;
            StepVerifier.create(result)
                    .expectNextMatches(responseEntity -> 
                        expectedTypes[index].equals(responseEntity.getHeaders().getContentType().toString()))
                    .verifyComplete();
        }
    }

    @Test
    @DisplayName("测试服务异常处理")
    void testServiceException() {
        when(mockTtsManager.textToSpeech(any(TtsRequest.class)))
                .thenReturn(Mono.error(new RuntimeException("服务异常")));

        Mono<ResponseEntity<byte[]>> result = ttsResponseService.createAudioResponse(validRequest);

        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> 
                    responseEntity.getStatusCode().value() == 500)
                .verifyComplete();
    }

    @Test
    @DisplayName("测试空音频数据处理")
    void testNullAudioData() {
        TtsResponse responseWithNullData = TtsResponse.builder()
                .success(true)
                .message("成功但无音频数据")
                .audioData(null)
                .build();

        when(mockTtsManager.textToSpeech(any(TtsRequest.class)))
                .thenReturn(Mono.just(responseWithNullData));

        Mono<ResponseEntity<byte[]>> result = ttsResponseService.createAudioResponse(validRequest);

        StepVerifier.create(result)
                .expectNextMatches(responseEntity -> 
                    responseEntity.getStatusCode().value() == 400)
                .verifyComplete();
    }

    @Test
    @DisplayName("测试DataBuffer流创建")
    void testDataBufferFluxCreation() {
        byte[] largeData = new byte[20000]; // 20KB数据，会被分成多个8KB块
        for (int i = 0; i < largeData.length; i++) {
            largeData[i] = (byte) (i % 256);
        }

        TtsResponse responseWithLargeData = TtsResponse.builder()
                .success(true)
                .audioData(largeData)
                .audioFormat("mp3")
                .build();

        when(mockTtsManager.textToSpeech(any(TtsRequest.class)))
                .thenReturn(Mono.just(responseWithLargeData));

        Mono<ResponseEntity<Flux<DataBuffer>>> result = ttsResponseService.createStreamResponse(validRequest);

        StepVerifier.create(result.flatMapMany(ResponseEntity::getBody))
                .expectNextCount(3) // 20KB应该被分成3个块（8KB + 8KB + 4KB）
                .verifyComplete();
    }
}