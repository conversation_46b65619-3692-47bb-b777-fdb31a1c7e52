//package com.wormhole.agent.langchain4j;
//
//import com.wormhole.agent.Application;
//import com.wormhole.agent.nacos.config.TemplateEnum;
//import com.wormhole.agent.nacos.listener.TemplateService;
//import dev.langchain4j.agent.tool.ToolExecutionRequest;
//import dev.langchain4j.data.message.AiMessage;
//import dev.langchain4j.data.message.ChatMessage;
//import dev.langchain4j.data.message.SystemMessage;
//import dev.langchain4j.data.message.UserMessage;
//import dev.langchain4j.model.chat.ChatLanguageModel;
//import dev.langchain4j.model.input.PromptTemplate;
//import dev.langchain4j.rag.query.Metadata;
//import dev.langchain4j.rag.query.Query;
//import dev.langchain4j.rag.query.transformer.CompressingQueryTransformer;
//import dev.langchain4j.rag.query.transformer.DefaultQueryTransformer;
//import dev.langchain4j.rag.query.transformer.ExpandingQueryTransformer;
//import dev.langchain4j.rag.query.transformer.QueryTransformer;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.ActiveProfiles;
//
//import java.util.ArrayList;
//import java.util.Collection;
//import java.util.List;
//
//import static java.util.Arrays.asList;
//import static java.util.Collections.emptyList;
//import static org.assertj.core.api.Assertions.assertThat;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.verifyNoInteractions;
//
//@SpringBootTest(classes = {Application.class})
//@ActiveProfiles("local")
//@Slf4j
//public class Langchain4jTest {
//    @Autowired
//    private ChatLanguageModel model;
//    @Autowired
//    private TemplateService templateService;
//
//
//    @Test
//    void chat(){
//        String chat = model.chat("say hello world!");
//        System.out.println(chat);
//    }
//    @Test
//    void chat_v2(){
////
////        ChatRequest build = ChatRequest.builder().parameters(ChatRequestParameters.builder().toolSpecifications(ToolSpecification.builder().description("").build()).build())..build();
////        ChatResponse chat = model.chat(build);
////        System.out.println(chat);
//    }
//
//    @Test
//    void should_compress_query_and_chat_memory_into_single_query(){
//        PromptTemplate promptTemplate = PromptTemplate.from(templateService.getTemplate(TemplateEnum.langchain_system_prompt)
//                );
//        List<ChatMessage> chatMemory = asList(
//                SystemMessage.from("Be polite"), // this message will be ignored
//                UserMessage.from("北京天气怎么样"),
//                AiMessage.from("天气晴朗"),
//                AiMessage.from(ToolExecutionRequest.builder() // this message will be ignored
//                        .id("12345")
//                        .name("current_time")
//                        .arguments("{}")
//                        .build())
//        );
//        UserMessage userMessage = UserMessage.from("天津呢");
//        Metadata metadata = Metadata.from(userMessage, "default", chatMemory);
//
//        Query query = Query.from(userMessage.singleText(), metadata);
//
//        String expectedCompressedQuery = "天津天气情况如何？";
//
//        CompressingQueryTransformer transformer = new CompressingQueryTransformer(model,promptTemplate);
//        // when
//        Collection<Query> queries = transformer.transform(query);
//
//        // then
//        assertThat(queries).containsExactly(Query.from(expectedCompressedQuery, metadata));
//
//    }
//
//    @Test
//    void should_not_compress_when_empty_chat_memory() {
//
//        // given
//        List<ChatMessage> chatMemory = emptyList();
//
//
//        UserMessage userMessage = UserMessage.from("Hello");
//        Metadata metadata = Metadata.from(userMessage, "default", chatMemory);
//
//        Query query = Query.from(userMessage.singleText(), metadata);
//
//        ChatLanguageModel model = mock(ChatLanguageModel.class);
//        CompressingQueryTransformer transformer = new CompressingQueryTransformer(model);
//
//        // when
//        Collection<Query> queries = transformer.transform(query);
//
//        // then
//        assertThat(queries).containsExactly(query);
//
//        verifyNoInteractions(model);
//    }
//
//    @Test
//    void should_using_custom() {
//        // given
//        List<ChatMessage> chatMemory = new ArrayList<>();
//        chatMemory.add(SystemMessage.from("Be polite"));
//        PromptTemplate promptTemplate = PromptTemplate.from(
//                "Rewrite the following query {{query}} to make it more suitable for RAG-based information retrieval");
//
//        UserMessage userMessage = UserMessage.from("猜猜我现在心里正在想的一个随机数字是多少，范围在 1 - 100 之间");
//        Metadata metadata = Metadata.from(userMessage, "default", chatMemory);
//
//        Query query = Query.from(userMessage.singleText(), metadata);
//
//        ChatLanguageModel model = mock(ChatLanguageModel.class);
//        CompressingQueryTransformer transformer = new CompressingQueryTransformer(model,promptTemplate);
//        // when
//        Collection<Query> queries = transformer.transform(query);
//        // then
//        assertThat(queries).containsExactly(query);
//
//        verifyNoInteractions(model);
//    }
//
//    @Test
//    void should_expand_query_with_custom_N() {
//
//        // given
//        int n = 5;
//
//        QueryTransformer transformer = new ExpandingQueryTransformer(model, n);
//
//        Query query = Query.from("query");
//
//        // when
//        Collection<Query> queries = transformer.transform(query);
//        System.out.println(queries);
//    }
//    @Test
//    void should_return_same_query() {
//        PromptTemplate promptTemplate = PromptTemplate.from(
//                "Rewrite the following query {{query}} to make it more suitable for RAG-based information retrieval");
//
//        // given
//        QueryTransformer transformer = new DefaultQueryTransformer();
//        Query query = Query.from("query");
//
//        // when
//        Collection<Query> transformed = transformer.transform(query);
//
//        // then
//        assertThat(transformed).containsExactly(query);
//    }
//
//
//    @Test
//    void should_compress_query_and_chat_memory_into_single_query_using_custom_prompt_template(){
//       // “应该使用自定义提示模板将查询和聊天记忆压缩为单个查询”
//        // given
//        PromptTemplate promptTemplate = PromptTemplate.from(
//                "Given the following conversation: {{chatMemory}} reformulate the following query: {{query}}");
//
//        List<ChatMessage> chatMemory = asList(
//                UserMessage.from("Tell me about Klaus Heisler"),
//                AiMessage.from("He is a cool guy")
//        );
//        UserMessage userMessage = UserMessage.from("How old is he?");
//        Metadata metadata = Metadata.from(userMessage, "default", chatMemory);
//        Query query = Query.from(userMessage.text(), metadata);
//
//        String expectedCompressedQuery = "How old is Klaus Heisler?";
//
//        CompressingQueryTransformer transformer = new CompressingQueryTransformer(model, promptTemplate);
//
//        // when
//        Collection<Query> queries = transformer.transform(query);
//
//        // then
//        assertThat(queries).containsExactly(Query.from(expectedCompressedQuery, metadata));
//
//    }
//
//    @Test
//    void should_compress_query_and_chat_memory_into_single_query_using_custom_prompt_template_builder() {
//
//        // given
//        PromptTemplate promptTemplate = PromptTemplate.from(
//                "Given the following conversation: {{chatMemory}} reformulate the following query: {{query}}");
//
//        List<ChatMessage> chatMemory = asList(
//                UserMessage.from("Tell me about Klaus Heisler"),
//                AiMessage.from("He is a cool guy")
//        );
//        UserMessage userMessage = UserMessage.from("How old is he?");
//        Metadata metadata = Metadata.from(userMessage, "default", chatMemory);
//        Query query = Query.from(userMessage.text(), metadata);
//
//        String expectedCompressedQuery = "How old is Klaus Heisler?";
//
//        CompressingQueryTransformer transformer = CompressingQueryTransformer.builder()
//                .chatLanguageModel(model)
//                .promptTemplate(promptTemplate)
//                .build();
//
//        // when
//        Collection<Query> queries = transformer.transform(query);
//
//        // then
//        assertThat(queries).containsExactly(Query.from(expectedCompressedQuery, metadata));
//    }
//
//}
//
