package com.wormhole.agent.util;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


class MarkdownTableUtilsTest {
    @Test
    public void testConvertSearchResultsToMarkdownTable() {
        // 准备测试数据 - 模拟搜索结果
        List<Map<String, Object>> searchResults = new ArrayList<>();

        // 结果1
        Map<String, Object> result1 = new HashMap<>();
        result1.put("排名", 1);
        result1.put("Distance", 0.92154897);
        result1.put("检索方式", "keyword");
        result1.put("标题", "人工智能在医疗领域的创新应用");
        result1.put("内容摘要", "本文探讨了AI在医疗诊断、药物研发和医疗影像分析中的应用案例，以及如何通过深度学习算法提高诊断准确率...");
        result1.put("internalId", "doc12345"); // 这是一个我们不想显示的内部字段
        searchResults.add(result1);

        // 结果2
        Map<String, Object> result2 = new HashMap<>();
        result2.put("排名", 2);
        result2.put("Distance", 0.87632451);
        result2.put("检索方式", "vector");
        result2.put("标题", "智能助手：改变日常生活的AI技术");
        result2.put("内容摘要", "从Siri到ChatGPT，智能助手正在改变我们的日常交互方式。本文分析了主流智能助手的技术原理和用户体验差异...");
        result2.put("internalId", "doc67890");
        searchResults.add(result2);

        // 结果3
        Map<String, Object> result3 = new HashMap<>();
        result3.put("排名", 3);
        result3.put("Distance", 0.81097562);
        result3.put("检索方式", "keyword");
        result3.put("标题", "企业如何实施AI转型战略");
        result3.put("内容摘要", "本文提供了企业AI转型的实用框架，包括数据基础设施建设、团队能力培养和业务流程重构三个关键步骤...");
        result3.put("internalId", "doc24680");
        searchResults.add(result3);

        // 定义表格显示的列
        List<String> headers = Arrays.asList("排名", "Distance", "检索方式", "标题", "内容摘要");

        // 测试方法1：使用预定义的headers
        String markdownTable1 = MarkdownTableUtils.convertToMarkdownTable(searchResults, headers);
        System.out.println("方法1结果：\n" + markdownTable1);

        // 验证方法1结果
        Assertions.assertTrue(markdownTable1.contains("| 排名 | Distance | 检索方式 | 标题 | 内容摘要 |"));
        Assertions.assertTrue(markdownTable1.contains("| 1 | 0.92154897 | keyword | 人工智能在医疗领域的创新应用 |"));
        Assertions.assertFalse(markdownTable1.contains("internalId"));

        // 测试方法2：带标题和级别
        String title = "搜索结果列表示例\n查询：人工智能应用";
        String markdownTable2 = MarkdownTableUtils.convertToMarkdownTable(title, 2, searchResults, headers);
        System.out.println("方法2结果：\n" + markdownTable2);

        // 验证方法2结果
        Assertions.assertTrue(markdownTable2.contains("## 搜索结果列表示例\n查询：人工智能应用"));
        Assertions.assertTrue(markdownTable2.contains("| 排名 | Distance | 检索方式 | 标题 | 内容摘要 |"));

        // 测试方法3：使用回退headers和隐藏字段
        String markdownTable3 = MarkdownTableUtils.convertToMarkdownTable(searchResults, headers);
        System.out.println("方法3结果：\n" + markdownTable3);

        // 验证方法3结果
        Assertions.assertTrue(markdownTable3.contains("| 排名 | Distance | 检索方式 | 标题 | 内容摘要 |"));
        Assertions.assertFalse(markdownTable3.contains("internalId"));

        // 测试边界情况：空数据列表
        String emptyResult = MarkdownTableUtils.convertToMarkdownTable(title, 2, new ArrayList<>(), headers);
        Assertions.assertEquals(StringUtils.EMPTY, emptyResult);

        // 测试边界情况：空标题
        String noTitleResult = MarkdownTableUtils.convertToMarkdownTable(null, 2, searchResults, headers);
        Assertions.assertFalse(noTitleResult.contains("#"));
        Assertions.assertTrue(noTitleResult.contains("| 排名 | Distance | 检索方式 | 标题 | 内容摘要 |"));
    }
}