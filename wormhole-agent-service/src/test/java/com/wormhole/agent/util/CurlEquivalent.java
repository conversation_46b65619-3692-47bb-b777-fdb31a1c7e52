package com.wormhole.agent.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

public class CurlEquivalent {

    public static void main(String[] args) throws IOException {
        URL url = new URL("https://segment.jina.ai/");
        HttpURLConnection con = (HttpURLConnection) url.openConnection();
        con.setRequestMethod("POST");
        con.setRequestProperty("Content-Type", "application/json");
        con.setRequestProperty("Authorization", "Bearer jina_7eb8e66540674f3098dc992007b82ecbgG2Em5LGW18CZ8N9EtdKsPYG76Ot");
        con.setDoOutput(true);

        String jsonInputString = "{\n" +
                "    \"content\": \"我爱北京天安门\n" +
                "    \"return_tokens\": true,\n" +
                "    \"return_chunks\": true,\n" +
                "    \"max_chunk_length\": 2\n" +
                "}";

        try(OutputStream os = con.getOutputStream()) {
            byte[] input = jsonInputString.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        try(BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"))) {
            StringBuilder response = new StringBuilder();
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
            System.out.println(response.toString());
        }
    }
}

