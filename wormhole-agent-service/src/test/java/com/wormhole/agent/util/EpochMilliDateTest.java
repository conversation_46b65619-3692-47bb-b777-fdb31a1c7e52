package com.wormhole.agent.util;

import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.json.JsonData;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.Application;
import com.wormhole.agent.core.util.Querys;
import com.wormhole.agent.knowledge.core.es.KnowledgeChunkService;
import com.wormhole.common.util.BeanUtils;
import com.wormhole.common.util.DateParsePatterns;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.LocalDateTimeUtils;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.test.context.ActiveProfiles;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * EpochMilliDateTest
 *
 * <AUTHOR>
 * @version 2024/12/21
 */
@ActiveProfiles("local")
@SpringBootTest(classes = {Application.class})
public class EpochMilliDateTest {

    @Resource
    private KnowledgeChunkService knowledgeChunkService;

    @Resource
    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    @Test
    void testDate() {
        LocalDateTime date = LocalDateTimeUtils.parseDateTime("2024-01-09 19:19:53");
        long epochMilli = LocalDateTimeUtils.toEpochMilli(date);
        System.out.println(epochMilli);
        // no result
        // Query query = Querys.range("date", JsonData.of("2024-01-09T19:19:53Z"), JsonData.of("2024-01-09T19:19:53Z"));

        // ok
        // Query query = Querys.range("date", JsonData.of(epochMilli), JsonData.of(epochMilli));

        // ok
        Query query = Querys.range("date", JsonData.of("2024-01-09T19:19:53+08:00"), JsonData.of("2024-01-09T19:19:53+08:00"));
        NativeQuery nativeQuery = NativeQuery.builder().withQuery(query).build();
        System.out.println(nativeQuery.getQuery().toString());
        List<TestDateIndex> testDateIndexList = reactiveElasticsearchTemplate.search(nativeQuery, TestDateIndex.class)
                .map(searchHit -> {
                    TestDateIndex content = searchHit.getContent();
                    System.out.println(content.getDate());
                    return content;
                })
                .collectList()
                .block();

        List<TestDateInfo> testDateInfos = testDateIndexList.stream()
                .map(index -> BeanUtils.copy(index, TestDateInfo.class))
                .collect(Collectors.toList());

        System.out.println("testDateInfos:\n" + JacksonUtils.writeValuePretty(testDateInfos));
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Document(indexName = "my-index-000002")
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class TestDateIndex {

        @Id
        private String id;

        @Field(type = FieldType.Date)
        private Instant date;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class TestDateInfo {

        private String id;
        @JsonFormat(pattern = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_DASH)
        private Instant date;
    }

}
