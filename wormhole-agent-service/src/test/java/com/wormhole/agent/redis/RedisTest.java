package com.wormhole.agent.redis;

import com.wormhole.agent.Application;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.ReactiveRedisTemplate;

import java.time.Duration;

@SpringBootTest(classes = Application.class)
class RedisTest {

    @Resource
    ReactiveRedisTemplate reactiveRedisTemplate;

    @Test
    void save() {
        reactiveRedisTemplate.opsForValue().set("demo-test", "first", Duration.ofMinutes(1)).block();
    }

    @Test
    void get() {
        reactiveRedisTemplate.opsForValue().get("demo-test").doOnNext(System.out::println).block();
    }

    @Test
    void delete() {
        reactiveRedisTemplate.opsForValue().delete("demo-test").doOnNext(System.out::println).block();
    }

}