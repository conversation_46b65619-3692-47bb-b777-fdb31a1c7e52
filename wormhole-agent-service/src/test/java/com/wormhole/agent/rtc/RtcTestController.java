package com.wormhole.agent.rtc;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.rtc.handler.command.CommandHandlerFactory;
import com.wormhole.agent.rtc.handler.event.EventHandleFactory;
import com.wormhole.channel.consts.MessageBody;
import com.wormhole.channel.consts.event.RoomCreateEvent;
import com.wormhole.channel.consts.helper.RtcHelper;
import com.wormhole.channel.consts.helper.RtcHttpHelper;
import com.wormhole.channel.consts.message.ClientMessageData;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.ReactiveStringRedisTemplate;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * @author: joker.liu
 * @date: 2025/4/1
 * @Description:
 */
public class RtcTestController extends BaseTest {

    private static final Logger log = LoggerFactory.getLogger(RtcTestController.class);
    @Resource
    private EventHandleFactory eventHandleFactory;

    @Resource
    private CommandHandlerFactory commandHandlerFactory;

    // 请求智能体

    // 客户挂断

    // 转人工
    @Test
    public void transferToHuman() {

        // "{payload=MessageBody(action=transfer_to_human, timestamp=1743652150802, data={device_id=ED9816CE61AD4652AB83294A025527C8, hotel_code=KYAGGJ, room_id=KYAGGJ_1111121_ED9816CE61AD4652AB83294A025527C8, room_no=1111121, type=room, user_id=room_ED9816CE61AD4652AB83294A025527C8}), traceId='57c3743160d6442586c9e0b531d223ce', timestamp=1743652150858}";
        MessageBody messageBody = new MessageBody();
        messageBody.setAction("transfer_to_human");
        messageBody.setTimestamp("1743652150802");
        ClientMessageData data = new ClientMessageData();
        data.setDeviceId("ED9816CE61AD4652AB83294A025527C8");
        data.setTimestamp("1743652150802");
        messageBody.setData(data);
        commandHandlerFactory.getEventHandler(messageBody.getAction()).handle(messageBody).subscribe();
    }

    // 接听请求
    public void accept() {
        // {payload=MessageBody(action=accept, timestamp=1743494274041, data={device_id=9BB2B0B5876D49D69FCAD0E1A28E52B9, hotel_code=KYAGGJ, room_id=KYAGGJ_0_9BB2B0B5876D49D69FCAD0E1A28E52B9, room_no=0, type=front, user_id=front_9BB2B0B5876D49D69FCAD0E1A28E52B9}),}



    }

    // 房间生成

    // 取消

    // 呼叫超时

    // 拒绝接听

    // 挂断

    @Test
    public void test() {

        // {payload=MessageBody(action=RoomCreate, timestamp=1743495406909, data={room_id=KYAGGJ_1111121_ED9816CE61AD4652AB83294A025527C8, timestamp=1743495406909}), traceId='20d80eaf015948c1ad5d69cbf777e783', timestamp=1743495407492}

        MessageBody messageBody = new MessageBody();
        messageBody.setAction("RoomCreate");
        messageBody.setTimestamp("1743495406909");

        RoomCreateEvent data = new RoomCreateEvent();
        data.setRoomId("KYAGGJ_1111121_ED9816CE61AD4652AB83294A025527C8");
        data.setTimestamp(1743495406909L);

        messageBody.setData(data);

        eventHandleFactory.getEventHandler("RoomCreate").handle(messageBody).subscribe();

    }

    @Test
    public void testHangUp() {

        // {payload=MessageBody(action=hang_up, timestamp=1743494274041, data={device_id=9BB2B0B5876D49D69FCAD0E1A28E52B9, hotel_code=KYAGGJ, room_id=KYAGGJ_0_9BB2B0B5876D49D69FCAD0E1A28E52B9, room_no=0, type=front, user_id=front_9BB2B0B5876D49D69FCAD0E1A28E52B9}), traceId='a376f28737484770aab72378d5392e11', timestamp=1743494274425}

        MessageBody messageBody = new MessageBody();
        messageBody.setAction("cancel");
        messageBody.setTimestamp("1743494274041");
        ClientMessageData data = new ClientMessageData();
        data.setDeviceId("ED9816CE61AD4652AB83294A025527C8");
        data.setTimestamp("1743494274425");
        messageBody.setData(data);

        commandHandlerFactory.getEventHandler(messageBody.getAction()).handle(messageBody).subscribe();

    }

    @Test
    public void startAgent() {

        MessageBody messageBody = new MessageBody();
        messageBody.setAction("token_req");
        messageBody.setTimestamp("1743501682411");
        ClientMessageData data = new ClientMessageData();
        data.setDeviceId("8476C6CC-7824-4D90-AF53-087953615BD91");
        data.setTimestamp("1743494274425");
        data.setRoomId("KYAGGJ_1111121_ED9816CE61AD4652AB83294A025527C8");
        messageBody.setData(data);

        commandHandlerFactory.getEventHandler(messageBody.getAction()).handle(messageBody).subscribe();

        messageBody.setAction("init_check");
        messageBody.setTimestamp("1743501682411");
        data.setDeviceId("8476C6CC-7824-4D90-AF53-087953615BD91");
        data.setTimestamp("1743494274425");
        data.setRoomId("KYAGGJ_1111121_ED9816CE61AD4652AB83294A025527C8");
        messageBody.setData(data);

        commandHandlerFactory.getEventHandler(messageBody.getAction()).handle(messageBody).subscribe();

    }

    @Resource
    private RtcHelper rtcHelper;

    @Test
    public void destroy() {
        rtcHelper.dismissRoomAsync("KYAGGJ_147258_ED9816CE61AD4652AB83294A025527C8_1744684986167")
                .flatMap(result -> {
                    log.info("dismiss room result:{}", result);
                    return Mono.just(true);
                }).onErrorResume(e -> {
                    log.error("Failed to dismiss room", e);
                    return Mono.just(false);
                }).subscribe();
    }

    @Resource
    private RtcHttpHelper rtcHttpHelper;

    @Resource
    private ReactiveStringRedisTemplate reactiveStringRedisTemplate;

    @Test
    public void test111() {

        List<String> deviceIds = List.of("111", "222");

        reactiveStringRedisTemplate.opsForSet().members("wormhole:rtc:online:device:test")
                        .collectList()
                                .flatMap(devices -> {
                                    System.out.println("=========>" + devices);
                                    return Mono.just(devices);
                                }).subscribe();

        Flux.fromIterable(deviceIds)
                        .flatMap(deviceId -> reactiveStringRedisTemplate.opsForSet().isMember("wormhole:rtc:online:device:test", deviceId))
                                .collectList().subscribe(results -> log.info("=========>" + results));


        reactiveStringRedisTemplate.opsForSet().isMember("wormhole:rtc:online:device:test", "111")
                .flatMap(isOnlineMap -> {
                    System.out.println("=========>" + isOnlineMap);
                    return Mono.just(isOnlineMap);
                }).subscribe();

    }



}
