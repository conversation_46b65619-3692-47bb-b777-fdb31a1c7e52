package com.wormhole.agent.log.service;

import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.wormhole.agent.Application;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.core.model.bot.BotInfo;
import com.wormhole.agent.core.model.bot.BotModeEnum;
import com.wormhole.agent.core.model.chat.ChatType;
import com.wormhole.agent.core.util.Querys;
import com.wormhole.agent.log.enums.ExecutionStatType;
import com.wormhole.agent.log.index.ChatAccessLogIndex;
import com.wormhole.agent.client.chat.params.query.BotDebugInfoQuery;
import com.wormhole.agent.log.stat.ExecutionStat;
import com.wormhole.agent.log.stat.ExecutionStatManager;
import com.wormhole.agent.client.chat.response.BotDebugInfoResponse;
import com.wormhole.agent.client.chat.response.vo.ExecutionStatVO;
import com.wormhole.common.util.IdUtils;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.client.elc.NativeQuery;
import org.springframework.data.elasticsearch.client.elc.ReactiveElasticsearchTemplate;
import org.springframework.test.context.ActiveProfiles;
import reactor.test.StepVerifier;

@Slf4j
@SpringBootTest(classes = {Application.class})
@Configuration(proxyBeanMethods = false)
@ActiveProfiles("dev")
class ChatAccessChatLogServiceTest {
    @Autowired
    private ChatAccessLogService chatAccessLogService;
    @Resource
    private ReactiveElasticsearchTemplate reactiveElasticsearchTemplate;

    @Test
    void saveChatAccessLog() {
        String traceId = IdUtils.generateId();
        ChatContext chatContext = new ChatContext();
        chatContext.setTraceId(traceId);
        chatContext.setChatType(ChatType.AGENT);
        chatContext.setClientReqId("test-client-req-id");
        chatContext.setUserId("test-account-id");
        chatContext.setBotCode("test-bot-code");
        BotInfo botInfoDTO = new BotInfo();

        botInfoDTO.setBotMode(BotModeEnum.LLM_AGENT.getValue());
        chatContext.setBotInfo(botInfoDTO);
        ExecutionStatManager executionStatManager = chatContext.getExecutionStatManager();
        // 模拟多个操作的开始和结束，并计算每个阶段的耗时
        simulateOperation(executionStatManager, ExecutionStatType.chat_access, "User input data");
        simulateOperation(executionStatManager, ExecutionStatType.llm_summary, "LLM processing data");
        simulateOperation(executionStatManager, ExecutionStatType.smart_intent, "Smart intent processing data");
        simulateOperation(executionStatManager, ExecutionStatType.read_memory, "Search knowledge data");
        simulateOperation(executionStatManager, ExecutionStatType.llm_suggest, "LLM processing suggest");

        // 结束
        executionStatManager.end(ExecutionStatType.chat_access, "User input data");

        log.info("traceId: {}", traceId);

        chatAccessLogService.saveChatAccessLog(chatContext).block();

        // 构建查询
        BotDebugInfoQuery query = new BotDebugInfoQuery();

        query.setTraceId(traceId);
        StepVerifier.create(chatAccessLogService.query(query)
                        .doOnSubscribe(subscription -> log.info("Executing search query..."))
                        .doOnNext(searchHit -> log.info("Search result: {}", searchHit))
                )
                .expectNextMatches(chatAccessLogVO -> {
                    log.info("chatAccessLogVO: {}", chatAccessLogVO);
                    return true;
                })
                .verifyComplete();

    }


    @Test
    void query() {
        // 构建查询
        Query query = Querys.bool(t ->
                t.must(Querys.term("trace_id", "67ab2be75e773e2d83fb3dc9fd3f2962")) // 确保查询字段为 keyword 类型
        );
        NativeQuery nativeQuery = NativeQuery.builder().withQuery(query).build();

        // 打印查询内容
        log.info("Generated query: {}", nativeQuery.getQuery());

        // 执行查询并验证结果
        StepVerifier.create(
                        reactiveElasticsearchTemplate.search(nativeQuery, ChatAccessLogIndex.class)
                                .doOnSubscribe(subscription -> log.info("Executing search query..."))
                                .doOnNext(searchHit -> log.info("Search result: {}", searchHit))
                                .doOnComplete(() -> log.info("Search completed with no results"))
                                .map(searchHit -> {
                                    ChatAccessLogIndex source = searchHit.getContent();
                                    BotDebugInfoResponse target = new BotDebugInfoResponse();
                                    target.setAnswer(source.getAnswer());
                                    target.setBotCode(source.getBotCode());
                                    target.setBotMode(source.getBotMode());
                                    target.setChatCompletionId(source.getChatCompletionId());
                                    target.setChatType(source.getChatType());
                                    target.setClientReqId(source.getClientReqId());
                                    target.setConversationId(source.getConversationId());
                                    target.setQuestion(source.getQuestion());
                                    target.setTraceId(source.getTraceId());
                                    target.setUserId(source.getUserId());
                                    target.setElapsedMs(source.getElapsedMs());

                                    target.setExecutionStatList(JSONArray.parseArray(source.getExecutionStatList(), ExecutionStatVO.class));
                                    log.info("source: {}", JacksonUtils.writeValueAsString(target));
                                    return target;
                                })
                )
                .expectNextMatches(botDebugInfoResponse -> {
                    log.info("chatAccessLogVO: {}", botDebugInfoResponse.toString());
                    // 这里可以添加断言逻辑，例如验证具体字段
                    return true;
                })
                .verifyComplete();
    }


    private static void simulateOperation(ExecutionStatManager executionStatManager, ExecutionStatType type, Object input) {
        // 开始记录操作
        executionStatManager.start(type, input);

        try {
            Thread.sleep(100 + (long) (Math.random() * 900)); // 模拟100ms到1000ms的处理时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.info("Operation was interrupted");
        }

        // 记录操作结束时间，并提供输出数据
        if (ExecutionStatType.chat_access.equals(type)) {
            return;
        }
        String output = "Processed data for " + type;
        executionStatManager.end(type, output);
        ExecutionStat statData = executionStatManager.getStatData(type);
        statData.setContextValue("test" + type.toString(), "test");
    }
}