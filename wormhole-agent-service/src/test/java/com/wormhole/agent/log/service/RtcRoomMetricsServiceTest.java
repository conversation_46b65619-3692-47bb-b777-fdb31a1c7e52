package com.wormhole.agent.log.service;

import com.wormhole.agent.Application;
import com.wormhole.agent.client.chat.params.query.RtcRoomInfoQuery;
import com.wormhole.agent.log.index.RtcRoomMetricsIndex;
import com.wormhole.agent.log.vo.RtcRoomEvent;
import com.wormhole.agent.log.vo.RtcRoomLlmMetrics;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.common.util.LocalDateTimeUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Configuration;
import org.springframework.test.context.ActiveProfiles;
import reactor.test.StepVerifier;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Slf4j
@SpringBootTest(classes = {Application.class})
@Configuration(proxyBeanMethods = false)
@ActiveProfiles("dev")
class RtcRoomMetricsServiceTest {
    @Autowired
    private RtcRoomMetricsService rtcRoomMetricsService;

    @Test
    void saveChatAccessLog() {
        String id = UUID.randomUUID().toString();
        // 准备测试数据
        long currentTime = System.currentTimeMillis();

        // 创建第一个事件
        RtcRoomEvent event1 = RtcRoomEvent.builder()
                .roundId(1L)
                .type(1L)
                .runStage("stage1")
                .eventTime(currentTime)
                .build();

        // 创建第二个事件，时间晚于第一个事件
        RtcRoomEvent event2 = RtcRoomEvent.builder()
                .roundId(1L)
                .type(2L)
                .runStage("stage2")
                .eventTime(currentTime + 500) // 500毫秒后
                .build();

        // 创建第三个事件，属于第二轮
        RtcRoomEvent event3 = RtcRoomEvent.builder()
                .roundId(2L)
                .type(1L)
                .runStage("stage1")
                .eventTime(currentTime + 1000) // 1000毫秒后
                .build();

        // 创建第四个事件，属于第二轮
        RtcRoomEvent event4 = RtcRoomEvent.builder()
                .roundId(2L)
                .type(2L)
                .runStage("stage2")
                .eventTime(currentTime + 1500) // 1500毫秒后
                .build();

        List<RtcRoomEvent> events = new ArrayList<>();
        events.add(event1);
        events.add(event2);
        events.add(event3);
        events.add(event4);

        // 创建LLM指标
        RtcRoomLlmMetrics llmMetrics = new RtcRoomLlmMetrics();
        llmMetrics.setTraceId("trace-123");
        llmMetrics.setConversationId("conv-456");
        llmMetrics.setClientReqId("req-789");
        llmMetrics.setStartTime(currentTime + 200); // 开始于第一轮中间
        llmMetrics.setEndTime(currentTime + 700);   // 结束于第一轮之后
        llmMetrics.setTotalElapsedMs(500L);

        // 创建LLM子节点
        List<RtcRoomLlmMetrics.Node> nodes = new ArrayList<>();
        RtcRoomLlmMetrics.Node node1 = new RtcRoomLlmMetrics.Node();
        node1.setName("解析请求");
        node1.setStartTime(currentTime + 200);
        node1.setEndTime(currentTime + 300);
        node1.setElapsedMs(100L);

        RtcRoomLlmMetrics.Node node2 = new RtcRoomLlmMetrics.Node();
        node2.setName("模型推理");
        node2.setStartTime(currentTime + 300);
        node2.setEndTime(currentTime + 600);
        node2.setElapsedMs(300L);

        RtcRoomLlmMetrics.Node node3 = new RtcRoomLlmMetrics.Node();
        node3.setName("后处理");
        node3.setStartTime(currentTime + 600);
        node3.setEndTime(currentTime + 700);
        node3.setElapsedMs(100L);

        // 添加子节点
        nodes.add(node1);
        nodes.add(node2);
        nodes.add(node3);
        llmMetrics.setNodes(nodes);

        // 构建RtcRoomMetricsIndex对象
        RtcRoomMetricsIndex index = RtcRoomMetricsIndex.builder()
                .timestamp(LocalDateTimeUtils.nowToInstant())
                .roomId(id)     // 兼容可能的字段名
                .appId("5121451")
                .elapsedMs(2000L)  // 总耗时2000毫秒
                .sessionStartTime(Instant.ofEpochMilli(currentTime))
                .sessionEndTime(Instant.ofEpochMilli(currentTime + 2000))
                .sessionStartTime(LocalDateTimeUtils.nowToInstant())
                .sessionEndTime(LocalDateTimeUtils.nowToInstant())
                .events(JacksonUtils.writeValueAsString(events))
                .llmMetrics(JacksonUtils.writeValueAsString(Arrays.asList(llmMetrics)))
                .build();

        // 构建查询
        RtcRoomInfoQuery query = new RtcRoomInfoQuery();
        query.setRtcRoomId(id);
        StepVerifier.create(rtcRoomMetricsService.save(index).then(
                rtcRoomMetricsService.query(query)

                        .doOnSubscribe(subscription -> log.info("Executing search query..."))
                        .doOnNext(searchHit -> log.info("Search result: {}", searchHit)))
                )
                .expectNextMatches(chatAccessLogVO -> {
                    log.info("RtcRoomMetricsIndex: {}", chatAccessLogVO);
                    return true;
                })
                .verifyComplete();

    }
    @Test
    public void query(){
    // 构建查询
    RtcRoomInfoQuery query = new RtcRoomInfoQuery();

    query.setRtcRoomId("90e6f8d9-c5ab-4cae-9ab0-a42330d40bcd");
    StepVerifier.create(rtcRoomMetricsService.queryFlame(query)
                            .doOnSubscribe(subscription -> log.info("Executing search query..."))
                            .doOnNext(searchHit -> log.info("Search result: {}", searchHit))
            )
            .expectNextMatches(chatAccessLogVO -> {
                log.info("RtcRoomMetricsIndex: {}", JacksonUtils.writeValueAsString(chatAccessLogVO));
                return true;
            })
            .verifyComplete();
}

}