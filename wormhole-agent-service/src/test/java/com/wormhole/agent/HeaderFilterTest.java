package com.wormhole.agent;

import com.wormhole.boot.filter.HeaderContextFilter;
import com.wormhole.common.constant.HeaderConstant;
import org.junit.jupiter.api.Test;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

public class HeaderFilterTest extends BaseTest{

    @Test
    void testFilterWithSource() {
        // 模拟请求
        MockServerHttpRequest request = MockServerHttpRequest.get("/test")
                .header(HeaderConstant.USER_ID, "12345")
                .header(HeaderConstant.USERNAME, "john_doe")
                .header(HeaderConstant.DEVICE_ID, "device_123")
                .header(HeaderConstant.SOURCE, "web")
                .build();
        MockServerWebExchange exchange = MockServerWebExchange.from(request);

        // 测试过滤器
        HeaderContextFilter filter = new HeaderContextFilter();
        StepVerifier.create(filter.filter(exchange, chain -> Mono.empty()))
                .verifyComplete();
    }
}
