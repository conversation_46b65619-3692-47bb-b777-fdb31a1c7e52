
package com.wormhole.agent.service;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.AgentChatParams;
import com.wormhole.agent.core.model.chat.ChatType;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

class ChatServiceTest extends BaseTest {

    @Resource
    private ChatService chatService;

    @Test
    void testChatCompletions() {
        AgentChatParams agentChatParams = AgentChatParams.builder()
                .botCode("hotel_search_bot")
                .content("详细介绍一下第6个点")
                .userId("123456")
                .conversationId("123456")
                .build();
        ChatContext chatContext = ChatContext.builder()
                .chatParams(agentChatParams)
                .chatType(ChatType.AGENT)
                .build();
        chatService.chatCompletions(chatContext).blockLast();
    }

    @Test
    void testChatCompletionsText() {
        AgentChatParams agentChatParams = AgentChatParams.builder()
                .userId("1234567")
                .botCode("bot_28486bac40c831ab20f292e0df7f8dd6")
                .content("深圳有什么好玩的")
                .clientReqId("clientReqId")
                .conversationId("chatId")
                .build();
        ChatContext chatContext = ChatContext.builder()
                .chatParams(agentChatParams)
                .userId("1234567")
                .username("username")
                .source("source")
                .chatType(ChatType.AGENT)
                .build();
        chatService.chatCompletions(chatContext).blockLast();
    }

}