package com.wormhole.agent.service;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.chat.model.ChatContext;
import com.wormhole.agent.client.chat.params.AgentChatParams;
import com.wormhole.agent.core.model.chat.ChatType;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @date 2025/2/18 15:01
 */
public class SuggestServiceTest extends BaseTest {

    @Resource
    private ChatService chatService;

    @Test
    void testSuggest() {
        ChatContext chatContext = ChatContext.builder()
                .chatParams(AgentChatParams.builder()
                        .userId("123456")
                        .botCode("doubao_pro_256k")
                        .content("深圳有什么好玩的")
                        .conversationId("chatId")
                        .clientReqId("clientReqId")
                        .build())
                .chatType(ChatType.AGENT)
                .build();
        chatService.chatCompletions(chatContext).blockLast();
    }
}
