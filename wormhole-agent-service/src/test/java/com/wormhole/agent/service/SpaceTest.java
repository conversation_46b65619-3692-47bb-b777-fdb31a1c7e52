package com.wormhole.agent.service;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.query.BotQueryCondition;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * @author: joker.liu
 * @date: 2025/4/18
 * @Description:
 */
public class SpaceTest extends BaseTest {

    @Resource
    private SpaceService spaceService;

    @Test
    public void test() {


        spaceService.getInSpaceUserList("user_068db844d31aa7b63049da38414c5d5f")
                .doOnNext(str -> System.out.println("========>" + str))
                .subscribe();

    }

}
