package com.wormhole.agent.amap;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.amap.qo.WeatherInfoQO;
import com.wormhole.agent.amap.vo.PlaceAroundVO;
import com.wormhole.agent.amap.vo.WeatherResponse;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.poi.req.KeywordMatchReq;
import com.wormhole.poi.req.PlaceAroundReq;
import com.wormhole.poi.resp.PlaceAroundResp;
import com.wormhole.poi.service.AmapService;
import jakarta.annotation.Resource;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Mono;

import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2025/2/20 15:29
 */
public class AmapPoiServiceTest extends BaseTest {

    @Resource
    private AmapDemoService amapDemoService;

    @Resource
    private AmapService amapService;
@Resource
private AmapPoiService amapPoiService;

    @Test
    void testQueryPlaceAroundForFood() {
        PlaceAroundResp block = amapDemoService.queryPlaceAround("桂湾地铁站", false).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }

    @Test
    void testQueryWeather() {
        WeatherInfoQO qo = new WeatherInfoQO();
        qo.setCity("440305");
        qo.setExtensions("");
        String block = amapPoiService.queryWeather(qo).block();
        System.out.println(block);
    }

    @Test
    void testQueryAround() {
        PlaceAroundReq qo = new PlaceAroundReq();
        qo.setLocation("113.92,22.49");
//        qo.setRadius( null);
//        qo.setTypes("050000");
//        qo.setSortrule(null);
        qo.setKeywords("广粥味");
        PlaceAroundResp block = amapService.queryPlaceAround(qo).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }
    @Test
    void testPoi()  {

        KeywordMatchReq keywordMatchReq = new KeywordMatchReq();
        keywordMatchReq.setKeywords("深圳");
        PlaceAroundResp block = amapService.queryKeywordMatchPlace(keywordMatchReq).block();
        System.out.println(JacksonUtils.writeValueAsString(block));
    }



}
