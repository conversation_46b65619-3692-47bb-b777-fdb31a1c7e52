package com.wormhole.agent.workflow.node;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.ConditionInputs;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import com.wormhole.tarot.AndCond;
import com.wormhole.tarot.ConditionDesc;
import com.wormhole.tarot.OrCond;
import com.wormhole.tarot.api.Cond;
import com.wormhole.tarot.core.CondParser;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.data.elasticsearch.core.ResourceUtil;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
class ConditionalBranchNodeExecutorTest {

    @Test
    void test() {
        String json = ResourceUtil.readFileFromClasspath("com/wormhole/agent/workflow/data/condition.json");
        Workflow workflow = Workflow.fromJson(json);
        Node node = workflow.getNodeById("102910");
        ConditionInputs inputs = WorkflowUtils.getInputs(node.getData().getInputs(), ConditionInputs.class);

        WorkflowContext workflowContext = WorkflowContext.builder()
                .workflowCode(workflow.getWorkflowDefinition().getWorkflowCode())
                .workflow(workflow)
                .build();
        NodeExecutionInfo nodeExecutionInfo = new NodeExecutionInfo();
        workflowContext.recordNodeExecution(workflow.getStartNode().getId(), nodeExecutionInfo);
        nodeExecutionInfo.setOutput(ImmutableMap.of(WorkflowConstant.USER_INPUT, "123"));

        AtomicInteger counter = new AtomicInteger(0);
        inputs.getBranches().stream().forEach(branch -> {
            ConditionInputs.Condition condition = branch.getCondition();

            // 表达式列表
            List<ConditionDesc> conditionDescList = Lists.newArrayList();
            // 左边量的值
            Map<String, Object> contextMap = Maps.newHashMap();
            condition.getConditions().stream()
                    .forEach(conditionItem -> {
                        Node.Input leftInput = conditionItem.getLeft().getInput();

                        // 表达式
                        ConditionDesc conditionDesc = new ConditionDesc();
                        // 从左边量读取
                        conditionDesc.setKey(WorkflowUtils.getRefJsonPath(leftInput));// $.header.DeviceId
                        conditionDesc.setType(leftInput.getType());// string
                        // 操作符
                        conditionDesc.setOperator(conditionItem.getOperator());// 123
                        // 从右变量读取。右变量可能是ref或者输入
                        ConditionInputs.ConditionSide right = conditionItem.getRight();
                        conditionDesc.setValue(WorkflowUtils.getRightValue(right.getInput(), contextMap));
                        conditionDescList.add(conditionDesc);

                        // 变量值
                        String refKey = WorkflowUtils.getRefJsonKey(leftInput);
                        Object contentValue = WorkflowUtils.getContentValue(leftInput, workflowContext);
                        contextMap.put(refKey, contentValue);
                    });

            Integer logic = condition.getLogic();
            List<Cond> conds = CondParser.parseCondList(conditionDescList);
            Cond cond;
            if (logic == 1) {
                cond = new OrCond(conds);
            } else {
                cond = new AndCond(conds);
            }
            boolean check = cond.check(contextMap);

            log.info("branch={}, size={}, checkResult={}", counter.incrementAndGet(), condition.getConditions().size(), check);
            log.info(JacksonUtils.writeValuePretty(cond));
        });
    }

}