package com.wormhole.agent.workflow.node;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.wormhole.agent.workflow.Workflow;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.model.Node;
import com.wormhole.agent.workflow.model.NodeExecutionInfo;
import com.wormhole.agent.workflow.model.WorkflowConstant;
import com.wormhole.agent.workflow.model.inputs.CodeInputs;
import com.wormhole.agent.workflow.script.groovy.GroovyScriptExecutor;
import com.wormhole.agent.workflow.util.WorkflowUtils;
import com.wormhole.common.util.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.elasticsearch.core.ResourceUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
class CodeNodeExecutorTest {
    private static final Logger LOGGER = LoggerFactory.getLogger(GroovyScriptExecutor.class.getName());

    @Test
    void test() {
        // 读取json文件
        String json = ResourceUtil.readFileFromClasspath("com/wormhole/agent/workflow/data/code-groovy.json");
        //初始化工作流
        Workflow workflow = Workflow.fromJson(json);
        WorkflowContext workflowContext = WorkflowContext.builder()
                .workflow(workflow)
                .workflowCode(workflow.getWorkflowDefinition().getWorkflowCode())
                .build();
        NodeExecutionInfo nodeExecutionInfo = new NodeExecutionInfo();
        workflowContext.recordNodeExecution(workflow.getStartNode().getId(), nodeExecutionInfo);
        Map<String, Object> outputMap = new HashMap<>();
        outputMap.put(WorkflowConstant.USER_INPUT, "123");
        outputMap.put("name", "chen");
        outputMap.put("number", 18);
        workflowContext.setInitialInput(outputMap);
        // 开始节点赋值
        nodeExecutionInfo.setOutput(outputMap);
        log.info("workflow start outputMap:{}", outputMap);

        Node node = workflow.getNodeById("151684");
        CodeInputs inputs = WorkflowUtils.getInputs(node.getData().getInputs(), CodeInputs.class);
        log.info("code 没inputParams inputs:{}", inputs);
//        Map<String, Object> inputParametersValue = WorkflowUtils.getInputParametersValue(node.getData().getInputs(), workflowContext);
//        log.info("inputParametersValue:{}", inputParametersValue);
        String scriptName = workflowContext.getExecutionId() + "_" + node.getId();
        GroovyScriptExecutor groovyScriptExecutor = new GroovyScriptExecutor();
        groovyScriptExecutor.addComponent("log", LOGGER);

        groovyScriptExecutor.updateScript(scriptName, inputs.getCode());
        // 1、解析脚本中的变量，放入context
        String botUserInput = WorkflowUtils.getBotUserInput(workflowContext);
        log.info("botUserInput :{}", botUserInput);
        // 执行一个有方法的脚本
        Map<String, Object> templateData = WorkflowUtils.getInputParametersValue(inputs, workflowContext);
        log.info("没参数 templateData:{}", templateData);
        Object runResult = groovyScriptExecutor.runScript(scriptName, outputMap);
        // 输出定义
        Map<String, Object> runResultMap = JSON.parseObject(JacksonUtils.writeValueAsString(runResult), Map.class);
        final Map<String, Object> resultMap = Maps.newHashMap();

        List<Node.Output> outputs = node.getData().getOutputs();
        for (Node.Output output : outputs) {
            String outputName = output.getName();
            resultMap.put(outputName, runResultMap.get(outputName));
        }
        log.info("resultMap :{}", resultMap);

    }

}