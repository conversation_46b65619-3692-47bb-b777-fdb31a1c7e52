package com.wormhole.agent.workflow.debug;

import com.wormhole.agent.BaseTest;
import com.wormhole.agent.client.chat.params.WorkflowParams;
import com.wormhole.agent.workflow.WorkflowContext;
import com.wormhole.agent.workflow.service.WorkflowService;
import com.wormhole.common.constant.Constants;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import reactor.core.publisher.Flux;

import java.util.Map;

public class WorkflowDebugTest extends BaseTest {
    @Resource
    private WorkflowService workflowService;
    @Test
    void test() {
        WorkflowParams workflowParams = new WorkflowParams();
        workflowParams.setDebug(false);
        workflowParams.setWorkflowCode("workflow_5ee3360188b7a09b4c8bb2181383be1f");
        workflowParams.setInitialInput(Map.of("USER_INPUT", "gpt是啥"));

        String debugCode = workflowParams.isDebug() ? workflowParams.getWorkflowCode() + Constants.DEBUG_SUFFIX : workflowParams.getWorkflowCode();
        WorkflowContext workflowContext = WorkflowContext.builder()
                .workflowCode(debugCode)
                .initialInput(workflowParams.getInitialInput())
                .build();
        Flux<WorkflowContext> flux = Flux.defer(() -> workflowService.executeWorkflow(workflowContext));
        flux.next().map(WorkflowContext::getNodeExecutionInfoMap).subscribe(System.out::println);
    }
}
