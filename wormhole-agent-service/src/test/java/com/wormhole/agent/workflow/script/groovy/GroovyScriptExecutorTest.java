package com.wormhole.agent.workflow.script.groovy;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Slf4j
class GroovyScriptExecutorTest {

    public static void main(String[] args) {
        String scriptName = "demo";
        String scriptContent = """
                def str = "hello " + (Objects.nonNull(ctx?.name) ? ctx?.name : "test");
                log.info(str)
                Map<String, Object> params = new HashMap<>();
                params.put("key0", str + "!"+ max(100, ctx?.number));
                return params;
                
                def max(int a, int b) {
                    return Math.max(a, b)
                }
                """;
        GroovyScriptExecutor executor = new GroovyScriptExecutor();
        executor.addComponent("log", log);

        executor.updateScript(scriptName, scriptContent);

        try {
            // 执行一个有方法的脚本
            Map<String, Object> context = new HashMap<>();
            context.put("number", 42);
            Object o = executor.runScript(scriptName, context);
            System.out.println(o);
        } catch (Exception e) {
            log.error("Error executing script: " + e.getMessage());
        }
    }

}