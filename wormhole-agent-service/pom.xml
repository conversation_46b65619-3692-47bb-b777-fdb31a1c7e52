<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.wormhole</groupId>
        <artifactId>wormhole-agent</artifactId>
        <version>1.1.12</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.agent</groupId>
    <artifactId>wormhole-agent-service</artifactId>
    <version>1.1.12</version>

    <name>wormhole-agent-service</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-hotelds-api-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-trace</artifactId>
        </dependency>
        <!-- Wormhole -->
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-agent-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-agent-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-agent-ai</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-agent-knowledge</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-poi-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-mq-starter</artifactId>
            <version>1.1.3-test-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-channel-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-common</artifactId>
        </dependency>

        <!-- spring-->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        </dependency>

        <!-- Spring AI-->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-pdf-document-reader</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-tika-document-reader</artifactId>
        </dependency>

        <!-- LangChain4j MCP -->
        <dependency>
            <groupId>dev.langchain4j</groupId>
            <artifactId>langchain4j-mcp</artifactId>
            <version>1.3.0-beta9</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>wormhole-agent</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
            <testResource>
                <directory>src/test/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
