branch:
  - buttons:
      - name: 查看服务状态dev
        description: 查看服务状态dev
        event: web_trigger_deploy_status_dev
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 备注
            required: false # 是否必填
            type: input
      - name: 重启服务dev
        description: 重启服务dev
        event: web_trigger_deploy_restart_dev
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 备注
            required: false # 是否必填
            type: input
      - name: 重启服务test
        description: 重启服务test
        event: web_trigger_deploy_restart_test
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 备注
            required: false # 是否必填
            type: input
      - name: 重启服务prod
        description: 重启服务prod
        event: web_trigger_deploy_restart_prod
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 备注
            required: false # 是否必填
            type: input
      - name: 构建镜像并部署海外intl-dev环境
        description: 构建镜像并部署到海外intl-dev环境
        event: web_trigger_build_deploy_intl_dev
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 分支名
            required: false # 是否必填
            type: input

      - name: 构建镜像并部署海外intl-test环境
        description: 构建镜像并部署到海外intl-test环境
        event: web_trigger_build_deploy_intl_test
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 分支名
            required: false # 是否必填
            type: input
      - name: 构建镜像并部署国内dev环境
        description: 构建镜像并部署到dev环境
        event: web_trigger_build_deploy_dev
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 分支名
            required: false # 是否必填
            type: input

      - name: 构建镜像并部署国内test环境
        description: 构建镜像并部署到test环境
        event: web_trigger_build_deploy_test
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 分支名
            required: false # 是否必填
            type: input
      
      - name: ☆office办公环境发布请去tag中部署☆
        description: ☆office办公环境发布请去tag中部署☆
        event: web_trigger_build_deploy_office_dev
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 此处不支持直接发布office办公环境,请去对应分支tag中部署
            required: true # 是否必填
            type: input
#
#      - name: office--构建镜像并部署国内test环境
#        description: 构建镜像并部署到test环境
#        event: web_trigger_build_deploy_office_test
#        inputs:
#          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
#          target_branch:
#            # 输入框
#            description: 分支名
#            required: false # 是否必填
#            type: input


      - name: ☆prod生产环境发布请去tag中部署☆
        description: ☆prod生产环境发布请去tag中部署☆
        event: web_trigger_deploy_restart_dev
        inputs:
          # 目前支持以下三种格式：输入框（input）、多行文本输入框（textarea）、下拉选择框（select），其中 key 值（var1、var2、var3）为环境变量名
          target_branch:
            # 输入框
            description: 此处不支持直接发布prod生产环境,请去对应分支tag中部署
            required: true # 是否必填
            type: input

    