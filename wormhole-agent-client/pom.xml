<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wormhole-agent</artifactId>
        <groupId>com.wormhole</groupId>
        <version>1.1.12</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>wormhole-agent-client</artifactId>
    <version>1.1.12</version>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-agent-openai</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-agent-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wormhole</groupId>
            <artifactId>wormhole-agent-knowledge</artifactId>
        </dependency>
    </dependencies>
</project>