package com.wormhole.agent.client.chat.config;

import cn.hutool.extra.spring.SpringUtil;
import com.wormhole.agent.client.chat.constant.AgentApiConstant;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.reactive.function.client.WebClient;

@Configuration
public class AgentApiConfig {


    @Bean(AgentApiConstant.AGENT_API_WEB_CLIENT)
    public WebClient rtcWebClient(Environment environment) {
        return WebClient.builder().baseUrl(getIngress())
                .build();
    }


    private String getIngress() {
        String DEV_INGRESS = "http://wormhole-agent.delonix.dev";
        String TEST_INGRESS = "http://wormhole-agent.delonix.test";
        String PROD_INGRESS = "http://wormhole-agent.delonix.prod";
        String INTL_INGRESS = "http://wormhole-agent.delonix-intl.prod";
        String TASK_INGRESS = "http://wormhole-agent.delonix.task";

        String activeProfile = SpringUtil.getActiveProfile();

        return switch (activeProfile) {
            case "test" -> TEST_INGRESS;
            case "prod" -> PROD_INGRESS;
            case "intl-prod" -> INTL_INGRESS;
            case "task" -> TASK_INGRESS;
            default -> DEV_INGRESS;
        };

    }


}
