package com.wormhole.agent.client.chat.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.model.openai.OpenAiTool;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WorkflowInfoResponse {
    /**
     * 工作流id
     */
    private String workflowCode;
    /**
     * 工作流名称
     */
    private String workflowName;
    /**
     * 工作流开始节点的tool定义
     */
    private OpenAiTool tool;
}
