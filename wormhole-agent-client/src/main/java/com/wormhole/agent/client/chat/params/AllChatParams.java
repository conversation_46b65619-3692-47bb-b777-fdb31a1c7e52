package com.wormhole.agent.client.chat.params;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import com.wormhole.agent.model.openai.OpenAiResponseFormat;
import com.wormhole.agent.model.openai.OpenAiStreamOptions;
import com.wormhole.agent.model.openai.OpenAiTool;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * LlmChatParams
 *
 * <AUTHOR>
 * @version 2024/12/9
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class AllChatParams extends ChatParams {
    /**
     * botCode
     */
    private String botCode;
    /**
     * 用户输入
     */
    private String content;
    /**
     * 是否调试
     */
    private boolean isDebug = false;

    /**
     * 模型提供厂商
     */
    private String modelProvider;
    /**
     * 最近对话轮数
     */
    @Builder.Default
    private int recentRound = 3;
    /**
     * A list of messages comprising the conversation so far. Example Python code.
     */
    private List<OpenAiChatMessage> messages;

    /**
     * ID of the model to use. See the model endpoint compatibility table for details on which models work with the Chat API.
     */
    private String model;

    /**
     * Number between -2.0 and 2.0. Positive values penalize new tokens based on their existing frequency in the text so far, decreasing the model's likelihood to repeat the same line verbatim.
     */
    private Double frequencyPenalty;

    /**
     * Accepts a JSON object that maps tokens (specified by their token ID in the tokenizer) to an associated bias value from -100 to 100. Mathematically, the bias is added to the logits generated by the model prior to sampling. The exact effect will vary per model, but values between -1 and 1 should decrease or increase likelihood of selection; values like -100 or 100 should result in a ban or exclusive selection of the relevant token.
     */
    private Map<String, Integer> logitBias;

    /**
     * Whether to return log probabilities of the output tokens or not. If true, returns the log probabilities of each output token returned in the content of message.
     */
    private Boolean logprobs;

    /**
     * An integer between 0 and 20 specifying the number of most likely tokens to return at each token position, each with an associated log probability. logprobs must be set to true if this parameter is used.
     */
    private Integer topLogprobs;

    /**
     * The maximum number of tokens that can be generated in the chat completion.
     */
    private Integer maxTokens;

    /**
     * How many chat completion choices to generate for each input message. Note that you will be charged based on the number of generated tokens across all of the choices. Keep n as 1 to minimize costs.
     */
    private Integer n;

    /**
     * Number between -2.0 and 2.0. Positive values penalize new tokens based on whether they appear in the text so far, increasing the model's likelihood to talk about new topics.
     */
    private Double presencePenalty;

    /**
     * 指定模型必须输出的格式的对象。
     * 设置为 { "type": "json_object" } 启用 JSON 模式，这保证模型生成的消息是有效的 JSON。
     * 重要提示：使用 JSON 模式时，您还必须通过系统或用户消息指示模型自行生成 JSON。如果没有这个，模型可能会生成无休止的空白流，直到生成达到令牌限制，从而导致延迟增加并出现“卡住”请求。
     * 另请注意，如果 finish_reason="length" ，则消息内容可能会被部分截断，这表明生成超出 max_tokens 或会话超出最大上下文长度。
     * type 类型 Defaults to text 默认为文本，Must be one of text or json_object.
     */
    private OpenAiResponseFormat responseFormat;

    /**
     * This feature is in Beta. If specified, our system will make a best effort to sample deterministically, such that repeated requests with the same seed and parameters should return the same result. Determinism is not guaranteed, and you should refer to the system_fingerprint response parameter to monitor changes in the backend.
     */
    private Integer seed;

    /**
     * Specifies the latency tier to use for processing the request. This parameter is relevant for customers subscribed to the scale tier service:
     * <p>
     * If set to 'auto', and the Project is Scale tier enabled, the system will utilize scale tier credits until they are exhausted.
     * If set to 'auto', and the Project is not Scale tier enabled, the request will be processed using the default service tier with a lower uptime SLA and no latency guarentee.
     * If set to 'default', the request will be processed using the default service tier with a lower uptime SLA and no latency guarentee.
     * When not set, the default behavior is 'auto'.
     * When this parameter is set, the response body will include the service_tier utilized.
     */
    private String serviceTier;

    /**
     * Up to 4 sequences where the API will stop generating further tokens.
     */
    private String stop;

    /**
     * If set, partial message deltas will be sent, like in ChatGPT. Tokens will be sent as data-only server-sent events as they become available, with the stream terminated by a data: [DONE] message. Example Python code.
     */
    @Builder.Default
    private Boolean stream = false;

    /**
     * Options for streaming response. Only set this when you set stream: true.
     */
    private OpenAiStreamOptions streamOptions;

    /**
     * What sampling temperature to use, between 0 and 2. Higher values like 0.8 will make the output more random, while lower values like 0.2 will make it more focused and deterministic.
     * <p>
     * We generally recommend altering this or top_p but not both.
     */
    private Double temperature;

    /**
     * An alternative to sampling with temperature, called nucleus sampling, where the model considers the results of the tokens with top_p probability mass. So 0.1 means only the tokens comprising the top 10% probability mass are considered.
     * <p>
     * We generally recommend altering this or temperature but not both.
     */
    private Double topP;

    /**
     * A list of tool enabled on the assistant. There can be a maximum of 128 tools per assistant. Tools can be of types code_interpreter, retrieval, or function
     */
    private List<OpenAiTool> tools;

    /**
     * Controls which (if any) tool is called by the model. none means the model will not call any tool and instead generates a message. auto means the model can pick between generating a message or calling one or more tools. required means the model must call one or more tools. Specifying a particular tool via {"type": "function", "function": {"name": "my_function"}} forces the model to call that tool.
     * none 表示模型不会调用任何工具，而是生成一条消息。
     * auto 表示模型可以在生成消息或调用一个或多个工具之间进行选择。
     * required 表示模型必须调用一个或多个工具。
     */
    private String toolChoice;

    /**
     * Whether to enable parallel function calling during tool use.
     */
    private Boolean parallelToolCalls;

    /**
     * A unique identifier representing your end-user, which can help OpenAI to monitor and detect abuse. Learn more.
     */
    private String user;


}
