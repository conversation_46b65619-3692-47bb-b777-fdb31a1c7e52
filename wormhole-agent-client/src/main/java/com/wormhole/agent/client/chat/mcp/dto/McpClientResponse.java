package com.wormhole.agent.client.chat.mcp.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Map;

/**
 * MCP客户端响应数据模型
 * 用于REST API的响应数据
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class McpClientResponse {
    
    /**
     * 客户端名称
     */
    @JsonProperty("client_name")
    private String clientName;
    
    /**
     * 服务状态
     */
    private String status;
    
    /**
     * 状态描述信息
     */
    @JsonProperty("status_message")
    private String statusMessage;
    
    /**
     * 可用工具数量
     */
    @JsonProperty("tool_count")
    private Integer toolCount;
    
    /**
     * 最后健康检查时间
     */
    @JsonProperty("last_health_check")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant lastHealthCheck;
    
    /**
     * 扩展元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 错误信息（当状态为ERROR时）
     */
    @JsonProperty("error_message")
    private String errorMessage;
    
    /**
     * 从McpServiceStatus创建响应对象
     */
    public static McpClientResponse fromServiceStatus(McpServiceStatus status) {
        if (status == null) {
            return null;
        }
        
        return McpClientResponse.builder()
            .clientName(status.getClientName())
            .status(status.getStatus() != null ? status.getStatus().getCode() : null)
            .statusMessage(status.getStatusMessage())
            .toolCount(status.getToolCount())
            .lastHealthCheck(status.getLastHealthCheck())
            .metadata(status.getMetadata())
            .errorMessage(status.getErrorMessage())
            .build();
    }
    
    /**
     * 获取状态摘要
     */
    @JsonIgnore
    public String getStatusSummary() {
        return String.format("%s - %s (工具数: %d)", 
            clientName, 
            statusMessage != null ? statusMessage : "未知状态", 
            toolCount != null ? toolCount : 0);
    }
    
    /**
     * 检查是否为活跃状态
     */
    @JsonIgnore
    public boolean isActive() {
        return "active".equals(status);
    }

}
