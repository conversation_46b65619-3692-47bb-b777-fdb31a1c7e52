package com.wormhole.agent.client.chat.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.bot.ModelInfo;
import com.wormhole.agent.core.model.bot.PromptInfo;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BotInfoResponse {
    /**
     * 客户端clientReqId
     */
    private String clientReqId;
    /**
     * userId
     */
    private String userId;
    /**
     * 会话id
     */
    private String conversationId;
    /**
     * botCode
     */
    private String botCode;
    /**
     * Bot名称
     */
    private String name;

    /**
     * 工作流列表
     */
    private List<WorkflowInfoResponse> workflowList;

    /**
     * 模型信息
     */
    private ModelInfo modelInfo;
    /**
     * 提示信息
     */
    private PromptInfo promptInfo;


}
