package com.wormhole.agent.client.chat.response.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.context.DefaultContext;
import com.wormhole.common.util.DateParsePatterns;
import lombok.*;


/**
 * ExecutionStat VO
 * 执行统计类，用于记录执行的相关信息。
 *
 * <AUTHOR>
 * @Date 2024/12/12 14:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class ExecutionStatVO extends DefaultContext {
    /**
     * 执行类型。
     */
    private String type;
    /**
     * 执行类型名称。
     */
    private String typeName;

    /**
     * 执行的输入数据。
     */
    private Object input;

    /**
     * 执行的输出数据。
     */
    private Object output;
    private Object nodeStatInfo;

    /**
     * 执行的开始时间。
     */
    @JSONField(format = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS)
    private String startTime;


    /**
     * 执行的结束时间。
     */
    @JSONField(format = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS)
    private String endTime;

    /**
     * 执行的耗时（毫秒）。
     * <p>计算方式：结束时间 - 开始时间</p>
     */
    @Builder.Default
    private long elapsedMs = -1L;

    /**
     * 执行状态。
     * <p>可能的状态包括：成功、失败、进行中</p>
     */
    private String status;

    /**
     * 记录执行过程中发生的异常信息。
     */
    private String errorMsg;

    /**
     * 记录执行过程中发生的堆栈信息。
     */
    private String stackTrace;

    /**
     * 层级1代表整个聊天会话，2中间执行，3内部以此叠加
     */
    private int level;
    private int sort;

}