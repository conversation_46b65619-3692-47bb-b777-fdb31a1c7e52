package com.wormhole.agent.client.chat.response;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;

/**
 * 通用火焰图类
 * <p>
 * 包含根节点和全局元数据，支持不同类型的火焰图
 * </p>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FlameGraph {
    /** 根节点 */
    private FlameNode root;

    /** 全局元数据 */
    private Map<String, Object> metadata;

    /** 火焰图标题 */
    private String title;

    /** 创建时间戳 */
    private long timestamp;

    /**
     * 火焰图类型枚举
     */
    public enum Type {
        /** 基于采样的火焰图，value表示采样次数 */
        SAMPLING,

        /** 基于跟踪的火焰图，使用时间戳 */
        TRACING
    }

    /** 火焰图类型 */
    private Type type;

    /**
     * 带根节点的构造函数
     *
     * @param root 根节点
     */
    public FlameGraph(FlameNode root) {
        this.root = root;
        this.metadata = new HashMap<>();
        this.timestamp = System.currentTimeMillis();
        this.type = Type.SAMPLING;
    }

    /**
     * 完整构造函数
     *
     * @param title 标题
     * @param root 根节点
     * @param type 火焰图类型
     */
    public FlameGraph(String title, FlameNode root, Type type) {
        this.title = title;
        this.root = root;
        this.type = type;
        this.metadata = new HashMap<>();
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 计算总值（对于采样类型）或总时间（对于跟踪类型）
     *
     * @return 总值或总时间
     */
    public long calculateTotal() {
        if (root != null) {
            if (type == Type.SAMPLING) {
                return calculateNodeValue(root);
            } else {
                return calculateNodeTime(root);
            }
        }
        return 0;
    }

    /**
     * 计算节点的总值（递归）
     *
     * @param node 节点
     * @return 总值
     */
    private long calculateNodeValue(FlameNode node) {
        if (node.getChildren() == null || node.getChildren().isEmpty()) {
            return node.getValue();
        }

        long total = 0;
        for (FlameNode child : node.getChildren()) {
            total += calculateNodeValue(child);
        }
        return total;
    }

    /**
     * 计算节点的总时间（递归）
     *
     * @param node 节点
     * @return 总时间
     */
    private long calculateNodeTime(FlameNode node) {
        if (node.getChildren() == null || node.getChildren().isEmpty()) {
            return node.getDuration();
        }

        long total = 0;
        for (FlameNode child : node.getChildren()) {
            total += calculateNodeTime(child);
        }
        return total;
    }

    /**
     * 添加全局元数据
     *
     * @param key 键
     * @param value 值
     */
    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }
}
