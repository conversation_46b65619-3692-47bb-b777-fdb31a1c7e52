package com.wormhole.agent.client.chat.mcp.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Map;

/**
 * MCP服务状态信息
 * 用于跟踪MCP客户端的运行状态和健康信息
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class McpServiceStatus {
    
    /**
     * 客户端名称
     */
    @JsonProperty("client_name")
    private String clientName;
    
    /**
     * 服务状态
     */
    private ServiceStatus status;
    
    /**
     * 状态描述信息
     */
    @JsonProperty("status_message")
    private String statusMessage;
    
    /**
     * 服务创建时间
     */
    @JsonProperty("created_at")
    private Instant createdAt;
    
    /**
     * 最后更新时间
     */
    @JsonProperty("updated_at")
    private Instant updatedAt;
    
    /**
     * 最后健康检查时间
     */
    @JsonProperty("last_health_check")
    private Instant lastHealthCheck;

    /**
     * 可用工具数量
     */
    @JsonProperty("tool_count")
    private Integer toolCount;
    
    /**
     * 传输方式
     */
    private String transport;
    
    /**
     * 服务端点信息
     */
    private String endpoint;
    
    /**
     * 扩展元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 错误信息（当状态为ERROR时）
     */
    @JsonProperty("error_message")
    private String errorMessage;
    
    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        /**
         * 初始化中
         */
        INITIALIZING("initializing", "服务正在初始化"),
        
        /**
         * 活跃状态
         */
        ACTIVE("active", "服务正常运行"),
        
        /**
         * 非活跃状态
         */
        INACTIVE("inactive", "服务已停止"),
        
        /**
         * 错误状态
         */
        ERROR("error", "服务发生错误"),
        
        /**
         * 正在停止
         */
        STOPPING("stopping", "服务正在停止");
        
        private final String code;
        private final String description;
        
        ServiceStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        /**
         * 从代码获取状态
         */
        public static ServiceStatus fromCode(String code) {
            for (ServiceStatus status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            throw new IllegalArgumentException("未知的服务状态代码: " + code);
        }
    }

    /**
     * 创建初始化状态
     */
    public static McpServiceStatus initializing(String clientName) {
        return McpServiceStatus.builder()
            .clientName(clientName)
            .status(ServiceStatus.INITIALIZING)
            .statusMessage("正在初始化MCP客户端")
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .toolCount(0)
            .build();
    }
    
    /**
     * 创建活跃状态
     */
    public static McpServiceStatus active(String clientName, String transport, String endpoint, int toolCount) {
        return McpServiceStatus.builder()
            .clientName(clientName)
            .status(ServiceStatus.ACTIVE)
            .statusMessage("MCP客户端运行正常")
            .createdAt(Instant.now())
            .updatedAt(Instant.now())
            .lastHealthCheck(Instant.now())
            .toolCount(toolCount)
            .transport(transport)
            .endpoint(endpoint)
            .build();
    }
    
    /**
     * 创建错误状态
     */
    public static McpServiceStatus error(String clientName, String errorMessage) {
        return McpServiceStatus.builder()
            .clientName(clientName)
            .status(ServiceStatus.ERROR)
            .statusMessage("MCP客户端发生错误")
            .updatedAt(Instant.now())
            .errorMessage(errorMessage)
            .build();
    }
    
    /**
     * 更新状态
     */
    public void updateStatus(ServiceStatus newStatus, String message) {
        this.status = newStatus;
        this.statusMessage = message;
        this.updatedAt = Instant.now();
    }

    /**
     * 设置错误信息
     */
    public void setError(String errorMessage) {
        this.status = ServiceStatus.ERROR;
        this.errorMessage = errorMessage;
        this.statusMessage = "服务发生错误: " + errorMessage;
        this.updatedAt = Instant.now();
    }
    
    /**
     * 检查是否为活跃状态
     */
    @JsonIgnore
    public boolean isActive() {
        return ServiceStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 检查是否健康
     */
    @JsonIgnore
    public boolean isHealthy() {
        return ServiceStatus.ACTIVE.equals(this.status);
    }
    
    /**
     * 获取状态摘要
     */
    public String getStatusSummary() {
        return String.format("%s - %s (工具数: %d)", 
            clientName, 
            status.getDescription(), 
            toolCount != null ? toolCount : 0);
    }
}
