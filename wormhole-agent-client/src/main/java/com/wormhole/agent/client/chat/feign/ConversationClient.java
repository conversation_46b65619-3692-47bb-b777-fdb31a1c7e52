package com.wormhole.agent.client.chat.feign;

import com.wormhole.agent.client.chat.params.CreateConversationReq;
import com.wormhole.agent.client.chat.params.UserConversationVO;
import com.wormhole.common.result.Result;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = "wormhole-agent")
public interface ConversationClient {

    @PostMapping("/conversations/create")
    Mono<Result<UserConversationVO>> create(@RequestBody CreateConversationReq req);

}
