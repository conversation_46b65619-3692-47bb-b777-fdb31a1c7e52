package com.wormhole.agent.client.chat.params;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.core.model.bot.*;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;

import java.time.LocalDateTime;
import java.util.*;

/**
 * 用户会话实体
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserConversationVO {
    /**
     * 主键ID
     */
    @Id
    private Long id;
    /**
     *  创建时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;


    /**
     *  用户id
     * */
    private String userId;

    /**
     *  创建人姓名
     * */
    private String createdByName;
    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 智能体编码
     */
    private String botCode;
    /**
     * 智能体名称
     */
    private String botName;
    /**
     * 来源
     */
    private String source;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 酒店编码
     */
    private String hotelCode;
    /**
     * 位置编码
     */
    private String positionCode;
    /**
     * 会话标题
     */
    private String title;

    /**
     * 描述
     */
    private String content;
    /**
     * 开场白
     */
    private OnboardingInfo onboardingInfo;
    /**
     * 图形信息
     */
    private ObjectInfo imageJson;

    /**
     * 拓展配置信息
     */
    private Map<String, Object> extMap;
}