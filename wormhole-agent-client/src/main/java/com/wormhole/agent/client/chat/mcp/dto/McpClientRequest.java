package com.wormhole.agent.client.chat.mcp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * MCP客户端请求数据模型
 * 用于REST API的请求参数
 *
 * <AUTHOR>
 * @version 2025-07-16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class McpClientRequest {
    
    /**
     * 客户端名称（必填）
     */
    private String name;
    
    /**
     * 传输方式（必填）：stdio 或 sse
     */
    private String transport;
    
    /**
     * 服务状态：enabled 或 disabled
     */
    @Builder.Default
    private String status = "enabled";
    
    /**
     * 描述信息
     */
    private String description;
    
    /**
     * SSE传输方式的URL（当transport=sse时必填）
     */
    private String url;
    
    /**
     * STDIO传输方式的命令（当transport=stdio时必填）
     */
    private List<String> command;
    
    /**
     * 工具执行超时时间（秒）
     */
    @JsonProperty("timeout_seconds")
    @Builder.Default
    private Long timeoutSeconds = 30L;
    
    /**
     * 是否记录事件日志
     */
    @JsonProperty("log_events")
    @Builder.Default
    private Boolean logEvents = false;
    
    /**
     * 扩展配置
     */
    private Map<String, Object> metadata;

    
    /**
     * 验证请求参数
     */
    public void validate() {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("客户端名称不能为空");
        }
        
        if (transport == null || transport.trim().isEmpty()) {
            throw new IllegalArgumentException("传输方式不能为空");
        }
        
        if ("sse".equalsIgnoreCase(transport)) {
            if (url == null || url.trim().isEmpty()) {
                throw new IllegalArgumentException("SSE传输方式需要配置URL");
            }
        } else if ("stdio".equalsIgnoreCase(transport)) {
            if (command == null || command.isEmpty()) {
                throw new IllegalArgumentException("STDIO传输方式需要配置命令");
            }
        } else {
            throw new IllegalArgumentException("不支持的传输方式: " + transport);
        }
        
        if (timeoutSeconds != null && timeoutSeconds <= 0) {
            throw new IllegalArgumentException("超时时间必须大于0");
        }
    }
}
