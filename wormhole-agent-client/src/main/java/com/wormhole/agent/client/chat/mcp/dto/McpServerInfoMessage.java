package com.wormhole.agent.client.chat.mcp.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.model.openai.OpenAiTool;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

/**
 * MCP服务器信息消息数据结构
 * 用于Redis pub/sub频道 mcp:server:info 的消息格式
 * 向wormhole-platform推送MCP服务器状态信息
 *
 * <AUTHOR>
 * @version 2025-07-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class McpServerInfoMessage {
    
    /**
     * 消息唯一标识符，用于去重
     */
    @Builder.Default
    private String messageId = UUID.randomUUID().toString();
    
    /**
     * 消息时间戳
     */
    @Builder.Default
    private long timestamp = Instant.now().toEpochMilli();
    
    /**
     * MCP服务器名称
     */
    private String mcpServerName;
    
    /**
     * MCP服务状态信息
     */
    private McpServiceStatus mcpServiceStatus;

    /**
     * 操作类型：INIT, ADD, UPDATE, DELETE
     */
    private String type;
    
    /**
     * 操作类型枚举
     */
    public enum MessageType {
        /**
         * 初始化 - 系统启动时发布所有MCP服务器信息
         */
        INIT("INIT", "初始化MCP服务器信息"),
        
        /**
         * 添加 - 新增MCP服务器
         */
        ADD("ADD", "添加MCP服务器"),
        
        /**
         * 更新 - 更新MCP服务器信息
         */
        UPDATE("UPDATE", "更新MCP服务器信息"),
        
        /**
         * 删除 - 删除MCP服务器
         */
        DELETE("DELETE", "删除MCP服务器"),
        
        /**
         * 错误 - MCP服务器连接或初始化失败
         */
        ERROR("ERROR", "MCP服务器错误");
        
        private final String code;
        private final String description;
        
        MessageType(String code, String description) {
            this.code = code;
            this.description = description;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getDescription() {
            return description;
        }
        
        /**
         * 从代码获取消息类型
         */
        public static MessageType fromCode(String code) {
            for (MessageType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            throw new IllegalArgumentException("未知的消息类型代码: " + code);
        }
    }
    
    /**
     * 验证消息的有效性
     */
    @JsonIgnore
    public boolean isValid() {
        // 检查基本字段
        if (messageId == null || messageId.trim().isEmpty()) {
            return false;
        }
        
        if (mcpServerName == null || mcpServerName.trim().isEmpty()) {
            return false;
        }
        
        if (type == null || type.trim().isEmpty()) {
            return false;
        }
        
        // 验证操作类型是否有效
        try {
            MessageType.fromCode(type);
        } catch (IllegalArgumentException e) {
            return false;
        }
        
        // DELETE和ERROR操作不需要工具信息，但需要状态信息（除了DELETE可以不需要状态）
        if (MessageType.DELETE.getCode().equals(type)) {
            return true; // DELETE可以没有状态信息
        }
        
        // 其他操作需要状态信息
        if (mcpServiceStatus == null) {
            return false;
        }

        return true;
    }
    
    /**
     * 创建初始化消息
     */
    public static McpServerInfoMessage createInitMessage(String serverName, McpServiceStatus status, List<OpenAiTool> tools) {
        return McpServerInfoMessage.builder()
                .mcpServerName(serverName)
                .mcpServiceStatus(status)
                .type(MessageType.INIT.getCode())
                .build();
    }
    
    /**
     * 创建添加消息
     */
    public static McpServerInfoMessage createAddMessage(String serverName, McpServiceStatus status, List<OpenAiTool> tools) {
        return McpServerInfoMessage.builder()
                .mcpServerName(serverName)
                .mcpServiceStatus(status)
                .type(MessageType.ADD.getCode())
                .build();
    }
    
    /**
     * 创建更新消息
     */
    public static McpServerInfoMessage createUpdateMessage(String serverName, McpServiceStatus status, List<OpenAiTool> tools) {
        return McpServerInfoMessage.builder()
                .mcpServerName(serverName)
                .mcpServiceStatus(status)
                .type(MessageType.UPDATE.getCode())
                .build();
    }
    
    /**
     * 创建删除消息
     */
    public static McpServerInfoMessage createDeleteMessage(String serverName, McpServiceStatus status) {
        return McpServerInfoMessage.builder()
                .mcpServerName(serverName)
                .mcpServiceStatus(status)
                .type(MessageType.DELETE.getCode())
                .build();
    }
    
    /**
     * 创建错误消息
     */
    public static McpServerInfoMessage createErrorMessage(String serverName, McpServiceStatus status, String errorMessage) {
        return McpServerInfoMessage.builder()
                .mcpServerName(serverName)
                .mcpServiceStatus(status)
                .type(MessageType.ERROR.getCode())
                .build();
    }

    /**
     * 检查是否为空消息
     */
    @JsonIgnore
    public boolean isEmpty() {
        return mcpServerName == null || mcpServerName.trim().isEmpty();
    }
    
    /**
     * 获取消息摘要
     */
    @JsonIgnore
    public String getSummary() {
        return String.format("MCP服务器信息[%s] - %s",
            mcpServerName, 
            type);
    }
}
