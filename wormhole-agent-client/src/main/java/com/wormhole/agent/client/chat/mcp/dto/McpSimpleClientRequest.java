package com.wormhole.agent.client.chat.mcp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MCP客户端简化请求数据模型
 * 用于只需要客户端名称的REST API请求参数
 * 适用于删除、查询状态、获取工具列表、缓存管理、健康检查等操作
 *
 * <AUTHOR>
 * @version 2025-07-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class McpSimpleClientRequest {
    
    /**
     * 客户端名称（必填）
     * 用于标识要操作的MCP客户端
     */
    private String clientName;
    
    /**
     * 验证请求参数
     * 
     * @throws IllegalArgumentException 当客户端名称为空时抛出异常
     */
    public void validate() {
        if (clientName == null || clientName.trim().isEmpty()) {
            throw new IllegalArgumentException("客户端名称不能为空");
        }
    }
}
