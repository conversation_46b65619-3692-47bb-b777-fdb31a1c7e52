package com.wormhole.agent.client.chat.params.query;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * ChatAccessLogQuery 类用于封装聊天访问日志的查询条件。
 * <p>
 * 该类的实例可以作为查询参数，用于过滤或筛选聊天访问日志记录。
 * 每个字段对应一个查询条件，支持精确匹配或模糊匹配。
 * </p>
 */

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BotDebugInfoQuery {

    /**
     * 跟踪 ID，用于唯一标识一次完整的请求调用链。
     * <p>
     * 通常用于分布式系统中的调用链追踪，
     * 可帮助开发者追踪请求在系统中经过的服务或模块。
     * </p>
     */
    private String traceId;

    /**
     * 查询关键字，用于模糊搜索与聊天内容相关的信息。
     * <p>
     * 支持对聊天消息、系统日志或其他内容的模糊匹配查询。
     * </p>
     */
    private String keyword;

    /**
     * 机器人代码，用于标识特定的聊天机器人。
     * <p>
     * 适用于多机器人系统场景，用于区分不同的机器人实例或类型。
     * 示例值：B123，BOT_001。
     * </p>
     */
    private String botCode;

    /**
     * 聊天补全请求 ID，用于标识特定的聊天补全请求。
     * <p>
     * 该字段通常用于跟踪聊天过程中调用生成式 AI 模型的请求。
     * 示例值：CHAT_COMPLETIONS_123456。
     * </p>
     */
    private String chatCompletionsId;

    /**
     * 客户端请求 ID，用于标识客户端发起的具体请求。
     * <p>
     * 该字段可用于调试客户端与服务端的交互，排查单个请求的详细信息。
     * 示例值：CLIENT_REQ_ABC123。
     * </p>
     */
    private String clientReqId;

    /**
     * 账户 ID，用于关联用户信息。
     * <p>
     * 该字段通常表示用户的唯一标识，用于区分不同的用户。
     * 示例值：USER_001、ACCOUNT_123456。
     * </p>
     */
    private String userId;

    /**
     * 会话 ID，用于标识同一会话的消息。
     * <p>
     * 在一个聊天会话中，所有消息都会共享相同的会话 ID。
     * 示例值：CONV_123456789。
     * </p>
     */
    private String conversationId;

    // 新增时间范围查询参数
    private String startTimeBegin;
    private String startTimeEnd;
}
