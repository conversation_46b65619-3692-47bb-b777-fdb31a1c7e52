package com.wormhole.agent.client.chat.params.query;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class PluginExecuteReq {
   private  String pluginCode;
   private String pluginToolCode;
   private Map<String, Object> inputValueMap;
}
