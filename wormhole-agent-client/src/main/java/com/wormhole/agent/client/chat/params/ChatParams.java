package com.wormhole.agent.client.chat.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.wormhole.agent.core.model.chat.ChatInputType;
import com.wormhole.agent.core.model.chat.ChatProtocol;
import com.wormhole.agent.core.model.chat.ChatResponseMode;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Duration;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/30 22:17
 **/
@Data
@SuperBuilder
@NoArgsConstructor
public class ChatParams {

    @JsonIgnore
    @Builder.Default
    private Duration timeout = Duration.ofMinutes(1L);
    /**
     * 由客户端生成，在websocket场景用于区分流中的消息来自哪个请求
     */
    private String clientReqId;
    /**
     * 用户账户id
     */
    private String userId;
    /**
     * 对话ID，用于管理智能体记忆。首次对话有云端生成返回给客户端，后续请求客户端需带上此ID
     */
    private String conversationId;

    /**
     * text/voice_input/voice_call
     */
    @Builder.Default
    private String chatInputType = ChatInputType.TEXT.getValue();
    /**
     * stream/push
     */
    @Builder.Default
    private String chatResponseMode = ChatResponseMode.STREAM.getValue();
    /**
     * json/protobuf
     */
    @Builder.Default
    private String chatProtocol = ChatProtocol.JSON.getValue();
    /**
     * 智能体中定义的变量
     */
    private Map<String, Object> payload;

}
