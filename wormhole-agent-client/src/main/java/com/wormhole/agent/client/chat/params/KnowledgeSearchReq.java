package com.wormhole.agent.client.chat.params;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.knowledge.params.KnowledgeSearchParams;
import com.wormhole.agent.model.openai.OpenAiChatMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class KnowledgeSearchReq {
    private KnowledgeSearchParams.SearchInput searchInput;
    /**
     * 外部传入的参数
     */
    private KnowledgeSearchParams knowledgeSearchParams;
    /**
     * 聊天上下文
     */
    private List<OpenAiChatMessage> recentMessageList;
}
