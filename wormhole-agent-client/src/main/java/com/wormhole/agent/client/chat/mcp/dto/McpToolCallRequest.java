package com.wormhole.agent.client.chat.mcp.dto;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;


/**
 * MCP工具调用请求对象
 * 用于封装向MCP服务器发起工具调用的请求参数
 * <AUTHOR>
 * @version 2025-07-22
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class McpToolCallRequest {
    /**
     * MCP服务器名称/标识
     * 用于路由到具体的MCP服务实例
     * 例如: "file-server", "database-server", "api-gateway"
     */
    private String mcpServerName;

    /**
     * 工具名称
     * 对应MCP服务器中注册的具体工具方法
     * 例如: "read_file", "execute_query", "send_request"
     */
    private String toolName;

    /**
     * 工具调用参数
     * key-value形式的参数映射，支持嵌套对象
     * 参数类型由具体工具的schema定义决定
     */
    private Map<String, Object> arguments;
}
