package com.wormhole.agent.client.chat.response;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.util.DateParsePatterns;
import com.wormhole.common.util.LocalDateTimeUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用火焰图节点类
 * <p>
 * 适用于各种性能分析、调用栈可视化场景，支持基于采样和基于跟踪的火焰图
 * </p>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class FlameNode {
    /** 节点名称 */
    private String name;

    /** 子节点列表 */
    private List<FlameNode> children;

    /** 开始时间戳 */
    private long startTime;

    private String startTimeFormatted;

    /** 结束时间戳 */
    private long endTime;

    private String endTimeFormatted;

    /**
     * 节点值
     * <p>对于采样类型火焰图，表示采样次数；对于其他类型可以自定义含义</p>
     */
    private long value;

    /**
     * 节点类型
     * <p>如"java"、"native"、"io"、"database"等</p>
     */
    private String type;

    /** 节点元数据 */
    private Map<String, Object> metadata;

    /**
     * 基本构造函数 - 适用于基于采样的火焰图
     *
     * @param name 节点名称
     * @param value 节点值（采样次数）
     */
    public FlameNode(String name, long value) {
        this.name = name;
        this.value = value;
        this.children = new ArrayList<>();
        this.metadata = new HashMap<>();
    }

    /**
     * 时间构造函数 - 适用于基于跟踪的火焰图
     *
     * @param name 节点名称
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     */
    public FlameNode(String name, long startTime, long endTime) {
        this.name = name;
        this.startTime = startTime;
        this.endTime = endTime;
        this.value = 1; // 默认值为1，表示一次执行
        this.children = new ArrayList<>();
        this.metadata = new HashMap<>();
    }

    /**
     * 完整构造函数 - 同时指定时间和值
     *
     * @param name 节点名称
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param value 节点值
     */
    public FlameNode(String name, long startTime, long endTime, long value) {
        this.name = name;
        this.startTime = startTime;
        this.startTimeFormatted= LocalDateTimeUtils.formatTimestamp(startTime, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS);
        this.endTime = endTime;
        this.endTimeFormatted = LocalDateTimeUtils.formatTimestamp(endTime, DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS);
        this.value = value;
        this.children = new ArrayList<>();
        this.metadata = new HashMap<>();
    }

    /**
     * 获取节点持续时间
     *
     * @return 持续时间（毫秒）
     */
    public long getDuration() {
        if (endTime > 0 && startTime > 0) {
            return endTime - startTime;
        }
        return 0;
    }

    /**
     * 添加子节点
     *
     * @param child 子节点
     */
    public void addChild(FlameNode child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
    }

    /**
     * 添加元数据
     *
     * @param key 键
     * @param value 值
     */
    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new HashMap<>();
        }
        this.metadata.put(key, value);
    }
}
