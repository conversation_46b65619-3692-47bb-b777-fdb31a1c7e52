package com.wormhole.agent.client.chat.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.agent.client.chat.response.vo.ExecutionStatVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * ChatAccessLogVO
 *
 * <AUTHOR>
 * @version 2024/12/24
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class BotDebugInfoResponse {

    /**
     * 对应索引中的 "start_time"
     */
    private String startTime;

    /**
     * 对应索引中的 "end_time"
     */
    private String endTime;

    /**
     * 对应索引中的 "elapsed_ms"
     */
    private Long elapsedMs;

    /**
     * 对应索引中的 "trace_id"
     */
    private String traceId;

    /**
     * 对应索引中的 "account_id"
     */
    private String userId;

    /**
     * 对应索引中的 "conversation_id"
     */
    private String conversationId;

    /**
     * 对应索引中的 "chat_completion_id"
     */
    private String chatCompletionId;

    /**
     * 对应索引中的 "client_req_id"
     */
    private String clientReqId;

    /**
     * 对应索引中的 "chat_type"
     */
    private String chatType;

    /**
     * 对应索引中的 "bot_code"
     */
    private String botCode;

    /**
     * 对应索引中的 "question"
     */
    private String question;

    /**
     * 对应索引中的 "answer"
     */
    private String answer;


    /**
     * 对应索引中的 "bot_model"
     */
    private String botMode;

    /**
     * 对应索引中的 "error_msg"
     */
    private String errorMsg;

    /**
     * 对应索引中的 "execution_stat_list"（禁用索引）
     */
    private List<ExecutionStatVO> executionStatList;

}