package com.wormhole.agent.client.chat.client;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.wormhole.agent.client.chat.constant.AgentApiConstant;
import com.wormhole.agent.client.chat.params.KnowledgeSearchReq;
import com.wormhole.agent.client.chat.params.TtsRequest;
import com.wormhole.agent.client.chat.params.query.BotInfoQuery;
import com.wormhole.agent.client.chat.response.BotInfoResponse;
import com.wormhole.agent.client.chat.response.TtsResponse;
import com.wormhole.agent.knowledge.search.KnowledgeSearchContext;
import com.wormhole.common.exception.BusinessException;
import com.wormhole.common.result.PageResult;
import com.wormhole.common.result.Result;
import com.wormhole.common.result.ResultCode;
import com.wormhole.common.util.JacksonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/22
 */
@ConditionalOnClass(WebClient.class)
@Component
@Slf4j
public class AgentApiClient {

    @Resource(name = AgentApiConstant.AGENT_API_WEB_CLIENT)
    private WebClient webClient;

    public Mono<BotInfoResponse> getBotInfo(BotInfoQuery req) {
        return webClientPost("/bot/get_bot_info", null, req, new TypeReference<BotInfoResponse>() {
        });
    }

    public Mono<Result<KnowledgeSearchContext>> kbSearch(@RequestBody KnowledgeSearchReq req) {
        return webClientPost("/knowledge/search", null, req, new TypeReference<Result<KnowledgeSearchContext>>() {
        });
    }

    // ==================== TTS相关接口 ====================

    /**
     * 文本转语音 - 使用默认提供商
     */
    public Mono<Result<TtsResponse>> ttsSynthesize(@RequestBody TtsRequest req) {
        return webClientPost("/tts/synthesize", null, req, new TypeReference<Result<TtsResponse>>() {
        });
    }

    /**
     * 文本转语音 - 指定提供商
     */
    public Mono<Result<TtsResponse>> ttsSynthesizeWithProvider(String provider, @RequestBody TtsRequest req) {
        return webClientPost("/tts/synthesize/" + provider, null, req, new TypeReference<Result<TtsResponse>>() {
        });
    }

    /**
     * 文本转语音 - 直接返回音频字节数组
     */
    public Mono<ResponseEntity<byte[]>> ttsSynthesizeAudio(@RequestBody TtsRequest req) {
        return webClientPostForAudio("/tts/synthesize/audio", null, req);
    }

    /**
     * 文本转语音 - 指定提供商直接返回音频字节数组
     */
    public Mono<ResponseEntity<byte[]>> ttsSynthesizeAudioWithProvider(String provider, @RequestBody TtsRequest req) {
        return webClientPostForAudio("/tts/synthesize/" + provider + "/audio", null, req);
    }

    /**
     * 文本转语音 - 流式音频传输
     */
    public Mono<ResponseEntity<Flux<DataBuffer>>> ttsSynthesizeStream(@RequestBody TtsRequest req) {
        return webClientPostForStream("/tts/synthesize/stream", null, req);
    }

    /**
     * 文本转语音 - 指定提供商流式音频传输
     */
    public Mono<ResponseEntity<Flux<DataBuffer>>> ttsSynthesizeStreamWithProvider(String provider, @RequestBody TtsRequest req) {
        return webClientPostForStream("/tts/synthesize/" + provider + "/stream", null, req);
    }

    /**
     * 获取可用的TTS提供商列表
     */
    public Mono<Result<List<String>>> ttsGetAvailableProviders() {
        return webClientGet("/tts/providers", null, new TypeReference<Result<List<String>>>() {
        });
    }

    public <T> Mono<T> webClientPost(String url, Map<String, String> paramMap, Object body, TypeReference<T> typeReference) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (CollectionUtil.isNotEmpty(paramMap)) {
            paramMap.forEach(params::add);
        }
        return webClient.post()
                .uri(userBuilder -> userBuilder.path(url).queryParams(params).build())
                .bodyValue(Optional.ofNullable(body).orElse(new HashMap<>()))
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR))
                                )
                )
                .bodyToMono(new ParameterizedTypeReference<T>() {
                })
                .doOnNext(res -> log.info("webClientPost for: url {} params {} body {} resp {}", url, JacksonUtils.writeValueAsString(paramMap),
                        JacksonUtils.writeValueAsString(body), JacksonUtils.writeValueAsString(res)))
                .doOnError(e -> log.error("webClientPost request failed url {} params {} body {}", url, JacksonUtils.writeValueAsString(paramMap), JacksonUtils.writeValueAsString(body), e))
                .onErrorResume(error -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, error)))
                .flatMap(result -> {
                    T resultData = JacksonUtils.convertValue(result, typeReference);
                    return Mono.just(resultData); // 使用 justOrEmpty 避免空指针
                })
                .switchIfEmpty(Mono.empty());
    }

    /**
     * WebClient GET请求
     */
    public <T> Mono<T> webClientGet(String url, Map<String, String> paramMap, TypeReference<T> typeReference) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (CollectionUtil.isNotEmpty(paramMap)) {
            paramMap.forEach(params::add);
        }
        return webClient.get()
                .uri(userBuilder -> userBuilder.path(url).queryParams(params).build())
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR))
                                )
                )
                .bodyToMono(new ParameterizedTypeReference<T>() {
                })
                .doOnNext(res -> log.info("webClientGet for: url {} params {} resp {}", url, JacksonUtils.writeValueAsString(paramMap),
                        JacksonUtils.writeValueAsString(res)))
                .doOnError(e -> log.error("webClientGet request failed url {} params {}", url, JacksonUtils.writeValueAsString(paramMap), e))
                .onErrorResume(error -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, error)))
                .flatMap(result -> {
                    T resultData = JacksonUtils.convertValue(result, typeReference);
                    return Mono.just(resultData);
                })
                .switchIfEmpty(Mono.empty());
    }

    /**
     * WebClient POST请求 - 返回音频字节数组
     */
    public Mono<ResponseEntity<byte[]>> webClientPostForAudio(String url, Map<String, String> paramMap, Object body) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (CollectionUtil.isNotEmpty(paramMap)) {
            paramMap.forEach(params::add);
        }
        return webClient.post()
                .uri(userBuilder -> userBuilder.path(url).queryParams(params).build())
                .bodyValue(Optional.ofNullable(body).orElse(new HashMap<>()))
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR))
                                )
                )
                .toEntity(byte[].class)
                .doOnNext(res -> log.info("webClientPostForAudio for: url {} params {} body {} status {}", url, 
                        JacksonUtils.writeValueAsString(paramMap), JacksonUtils.writeValueAsString(body), res.getStatusCode()))
                .doOnError(e -> log.error("webClientPostForAudio request failed url {} params {} body {}", url, 
                        JacksonUtils.writeValueAsString(paramMap), JacksonUtils.writeValueAsString(body), e))
                .onErrorResume(error -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, error)));
    }

    /**
     * WebClient POST请求 - 返回流式数据
     */
    public Mono<ResponseEntity<Flux<DataBuffer>>> webClientPostForStream(String url, Map<String, String> paramMap, Object body) {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        if (CollectionUtil.isNotEmpty(paramMap)) {
            paramMap.forEach(params::add);
        }
        return webClient.post()
                .uri(userBuilder -> userBuilder.path(url).queryParams(params).build())
                .bodyValue(Optional.ofNullable(body).orElse(new HashMap<>()))
                .retrieve()
                .onStatus(HttpStatusCode::isError, response ->
                        response.bodyToMono(String.class)
                                .flatMap(errorBody ->
                                        Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR))
                                )
                )
                .toEntityFlux(DataBuffer.class)
                .doOnNext(res -> log.info("webClientPostForStream for: url {} params {} body {} status {}", url, 
                        JacksonUtils.writeValueAsString(paramMap), JacksonUtils.writeValueAsString(body), res.getStatusCode()))
                .doOnError(e -> log.error("webClientPostForStream request failed url {} params {} body {}", url, 
                        JacksonUtils.writeValueAsString(paramMap), JacksonUtils.writeValueAsString(body), e))
                .onErrorResume(error -> Mono.error(new BusinessException(ResultCode.INTERNAL_SERVER_ERROR, error)));
    }
}
