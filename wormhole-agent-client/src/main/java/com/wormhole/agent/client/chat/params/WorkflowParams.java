package com.wormhole.agent.client.chat.params;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;


/**
 * <AUTHOR>
 * @Date 2025/01/16
 **/
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class WorkflowParams extends ChatParams {

    /**
     * workflowCode
     */
    private String workflowCode;
    /**
     * 变量
     */
    private Map<String, Object> initialInput;
    /**
     * 是否调试
     */
    private boolean isDebug = true;
    @Builder.Default
    private Boolean stream = false;
    /**
     * 节点id
     */
    private String nodeId;

}
