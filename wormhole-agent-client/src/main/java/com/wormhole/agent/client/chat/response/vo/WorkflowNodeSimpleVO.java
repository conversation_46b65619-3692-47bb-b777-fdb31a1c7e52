package com.wormhole.agent.client.chat.response.vo;

import com.alibaba.fastjson.PropertyNamingStrategy;
import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.wormhole.common.util.DateParsePatterns;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * WorkflowNodeSimpleVO
 * 执行统计类，用于记录执行的相关信息。
 *
 * <AUTHOR>
 * @Date 2024/12/12 14:17
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
@JSONType(naming = PropertyNamingStrategy.SnakeCase)
public class WorkflowNodeSimpleVO  {
    private String id;
    /**
     * 执行类型。
     */
    private String type;
    /**
     * 执行类型名称。
     */
    private String typeName;
    private String name;
    private String icon;

    /**
     * 执行的输入数据。
     */
    private Object input;

    /**
     * 执行的输出数据。
     */
    private Object output;

    /**
     * 执行的开始时间。
     */
    @JsonFormat(pattern = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS)
    private String startTime;
    private long start;

    /**
     * 执行的结束时间。
     */
    @JsonFormat(pattern = DateParsePatterns.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS)
    private String endTime;
    private long end;

    /**
     * 执行的耗时（毫秒）。
     * <p>计算方式：结束时间 - 开始时间</p>
     */
    @Builder.Default
    private long elapsedMs = -1L;

    /**
     * 执行状态。
     * <p>可能的状态包括：成功、失败、进行中</p>
     */
    private String status;

    /**
     * 记录执行过程中发生的异常信息。
     */
    private String errorMsg;

    /**
     * 记录执行过程中发生的堆栈信息。
     */
    private String stackTrace;

    private Object modelRequestInfo;

    private Object llmInvocationRecords;

    private Object context;
}