package com.wormhole.agent.client.chat.params;


import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class CreateConversationReq {
    private String botCode;
    private boolean debug = false;

    private String deviceId;
    private String hotelCode;

    private String hotelName;

    private String positionCode;
}
